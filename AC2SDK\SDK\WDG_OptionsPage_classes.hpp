﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_OptionsPage

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_OptionsPage.WDG_OptionsPage_C
// 0x0090 (0x05C0 - 0x0530)
class UWDG_OptionsPage_C final : public UOptionsPage
{
public:
	class UWidgetAnimation*                       ShowroomFade;                                      // 0x0530(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       PageFade;                                          // 0x0538(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWDG_FooterButton_C*                    Back;                                              // 0x0540(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 bgImage;                                           // 0x0548(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_OptionsGenericPanel_C*             ControlsPanel;                                     // 0x0550(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Footer_C*                          Footer;                                            // 0x0558(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_OptionsGenericPanel_C*             GeneralPanel;                                      // 0x0560(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Header_C*                          Header;                                            // 0x0568(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_OptionsGenericPanel_C*             HUDPanel;                                          // 0x0570(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_OptionsGenericPanel_C*             InfoCreditsPanel;                                  // 0x0578(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_OptionsGenericPanel_C*             InternetPanel;                                     // 0x0580(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Page_C*                            PageBase;                                          // 0x0588(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_OptionsGenericPanel_C*             RealismPanel;                                      // 0x0590(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_OptionsGenericPanel_C*             SoundPanel;                                        // 0x0598(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_1;                                       // 0x05A0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_2;                                       // 0x05A8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_OptionsGenericPanel_C*             VideoPanel;                                        // 0x05B0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomLogo_C*                    WDG_ShowroomLogo;                                  // 0x05B8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_OptionsPage_C">();
	}
	static class UWDG_OptionsPage_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_OptionsPage_C>();
	}
};
static_assert(alignof(UWDG_OptionsPage_C) == 0x000008, "Wrong alignment on UWDG_OptionsPage_C");
static_assert(sizeof(UWDG_OptionsPage_C) == 0x0005C0, "Wrong size on UWDG_OptionsPage_C");
static_assert(offsetof(UWDG_OptionsPage_C, ShowroomFade) == 0x000530, "Member 'UWDG_OptionsPage_C::ShowroomFade' has a wrong offset!");
static_assert(offsetof(UWDG_OptionsPage_C, PageFade) == 0x000538, "Member 'UWDG_OptionsPage_C::PageFade' has a wrong offset!");
static_assert(offsetof(UWDG_OptionsPage_C, Back) == 0x000540, "Member 'UWDG_OptionsPage_C::Back' has a wrong offset!");
static_assert(offsetof(UWDG_OptionsPage_C, bgImage) == 0x000548, "Member 'UWDG_OptionsPage_C::bgImage' has a wrong offset!");
static_assert(offsetof(UWDG_OptionsPage_C, ControlsPanel) == 0x000550, "Member 'UWDG_OptionsPage_C::ControlsPanel' has a wrong offset!");
static_assert(offsetof(UWDG_OptionsPage_C, Footer) == 0x000558, "Member 'UWDG_OptionsPage_C::Footer' has a wrong offset!");
static_assert(offsetof(UWDG_OptionsPage_C, GeneralPanel) == 0x000560, "Member 'UWDG_OptionsPage_C::GeneralPanel' has a wrong offset!");
static_assert(offsetof(UWDG_OptionsPage_C, Header) == 0x000568, "Member 'UWDG_OptionsPage_C::Header' has a wrong offset!");
static_assert(offsetof(UWDG_OptionsPage_C, HUDPanel) == 0x000570, "Member 'UWDG_OptionsPage_C::HUDPanel' has a wrong offset!");
static_assert(offsetof(UWDG_OptionsPage_C, InfoCreditsPanel) == 0x000578, "Member 'UWDG_OptionsPage_C::InfoCreditsPanel' has a wrong offset!");
static_assert(offsetof(UWDG_OptionsPage_C, InternetPanel) == 0x000580, "Member 'UWDG_OptionsPage_C::InternetPanel' has a wrong offset!");
static_assert(offsetof(UWDG_OptionsPage_C, PageBase) == 0x000588, "Member 'UWDG_OptionsPage_C::PageBase' has a wrong offset!");
static_assert(offsetof(UWDG_OptionsPage_C, RealismPanel) == 0x000590, "Member 'UWDG_OptionsPage_C::RealismPanel' has a wrong offset!");
static_assert(offsetof(UWDG_OptionsPage_C, SoundPanel) == 0x000598, "Member 'UWDG_OptionsPage_C::SoundPanel' has a wrong offset!");
static_assert(offsetof(UWDG_OptionsPage_C, TextBlock_1) == 0x0005A0, "Member 'UWDG_OptionsPage_C::TextBlock_1' has a wrong offset!");
static_assert(offsetof(UWDG_OptionsPage_C, TextBlock_2) == 0x0005A8, "Member 'UWDG_OptionsPage_C::TextBlock_2' has a wrong offset!");
static_assert(offsetof(UWDG_OptionsPage_C, VideoPanel) == 0x0005B0, "Member 'UWDG_OptionsPage_C::VideoPanel' has a wrong offset!");
static_assert(offsetof(UWDG_OptionsPage_C, WDG_ShowroomLogo) == 0x0005B8, "Member 'UWDG_OptionsPage_C::WDG_ShowroomLogo' has a wrong offset!");

}

