﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SeasonSelector

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "Engine_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SeasonSelector.WDG_SeasonSelector_C
// 0x0140 (0x07A8 - 0x0668)
class UWDG_SeasonSelector_C final : public UGenericBarItem
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0668(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       LogoChange;                                        // 0x0670(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	class UWidgetAnimation*                       Pulse;                                             // 0x0678(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       Opacity;                                           // 0x0680(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       Scale;                                             // 0x0688(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 BGTLogo;                                           // 0x0690(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 BlancPainLogo;                                     // 0x0698(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 GT2Logo;                                           // 0x06A0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Gt4Logo;                                           // 0x06A8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 HoverImageBox;                                     // 0x06B0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 IcgtLogo;                                          // 0x06B8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image;                                             // 0x06C0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_63;                                          // 0x06C8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgLeftSlant;                                      // 0x06D0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgLeftSlantNormal;                                // 0x06D8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgRightSlant;                                     // 0x06E0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgRightSlantNormal;                               // 0x06E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWidgetSwitcher*                        logoSwitcher;                                      // 0x06F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           MainCanvas;                                        // 0x06F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             MainSlot;                                          // 0x0700(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 NoLogo;                                            // 0x0708(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           WhiteText;                                         // 0x0710(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class FName                                   FontTypeFace;                                      // 0x0718(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         MinDesiredWidth;                                   // 0x0720(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	int32                                         FontSize;                                          // 0x0724(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          IsToggleButton;                                    // 0x0728(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_729[0x3];                                      // 0x0729(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FMargin                                LabelPadding;                                      // 0x072C(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn)
	bool                                          Selected;                                          // 0x073C(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	uint8                                         Pad_73D[0x3];                                      // 0x073D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           StoredNormalColor;                                 // 0x0740(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           ToggledTextColor;                                  // 0x0750(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          IsToggled;                                         // 0x0760(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	uint8                                         Pad_761[0x7];                                      // 0x0761(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TMulticastInlineDelegate<void()>              OnUnfocus;                                         // 0x0768(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void(class UWDG_SeasonSelector_C* Sender, bool IsToggled)> OnToggled;   // 0x0778(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	struct FLinearColor                           ToggledColor;                                      // 0x0788(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         SlantWidth;                                        // 0x0798(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          ToggleOnOnly;                                      // 0x079C(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	ESeasonType                                   SeasonType;                                        // 0x079D(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_79E[0x2];                                      // 0x079E(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class AAcMenuGameMode*                        GameMode;                                          // 0x07A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_SeasonSelector(int32 EntryPoint);
	void OnSeasonChanged_Event_0(ESeasonType new_season);
	void ForceRestyle();
	void Toggled();
	void Construct();
	void OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent);
	void BP_MouseOver();
	void BP_MouseLeave();
	void PreConstruct(bool IsDesignTime);
	void BP_UpdateActivity(bool Active);
	void SetTitleText(const class FText& text);
	void SetToggled(bool IsToggledOn, bool ForceTextColor, bool TriggerEvent);
	void SetNormalColor();
	void PulseAnimation();
	void SetSlantImageWidth(class UImage* SlantImage);
	void ResetAnimation();
	void SetSeason(ESeasonType Season);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SeasonSelector_C">();
	}
	static class UWDG_SeasonSelector_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SeasonSelector_C>();
	}
};
static_assert(alignof(UWDG_SeasonSelector_C) == 0x000008, "Wrong alignment on UWDG_SeasonSelector_C");
static_assert(sizeof(UWDG_SeasonSelector_C) == 0x0007A8, "Wrong size on UWDG_SeasonSelector_C");
static_assert(offsetof(UWDG_SeasonSelector_C, UberGraphFrame) == 0x000668, "Member 'UWDG_SeasonSelector_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, LogoChange) == 0x000670, "Member 'UWDG_SeasonSelector_C::LogoChange' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, Pulse) == 0x000678, "Member 'UWDG_SeasonSelector_C::Pulse' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, Opacity) == 0x000680, "Member 'UWDG_SeasonSelector_C::Opacity' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, Scale) == 0x000688, "Member 'UWDG_SeasonSelector_C::Scale' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, BGTLogo) == 0x000690, "Member 'UWDG_SeasonSelector_C::BGTLogo' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, BlancPainLogo) == 0x000698, "Member 'UWDG_SeasonSelector_C::BlancPainLogo' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, GT2Logo) == 0x0006A0, "Member 'UWDG_SeasonSelector_C::GT2Logo' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, Gt4Logo) == 0x0006A8, "Member 'UWDG_SeasonSelector_C::Gt4Logo' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, HoverImageBox) == 0x0006B0, "Member 'UWDG_SeasonSelector_C::HoverImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, IcgtLogo) == 0x0006B8, "Member 'UWDG_SeasonSelector_C::IcgtLogo' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, Image) == 0x0006C0, "Member 'UWDG_SeasonSelector_C::Image' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, Image_63) == 0x0006C8, "Member 'UWDG_SeasonSelector_C::Image_63' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, imgLeftSlant) == 0x0006D0, "Member 'UWDG_SeasonSelector_C::imgLeftSlant' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, imgLeftSlantNormal) == 0x0006D8, "Member 'UWDG_SeasonSelector_C::imgLeftSlantNormal' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, imgRightSlant) == 0x0006E0, "Member 'UWDG_SeasonSelector_C::imgRightSlant' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, imgRightSlantNormal) == 0x0006E8, "Member 'UWDG_SeasonSelector_C::imgRightSlantNormal' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, logoSwitcher) == 0x0006F0, "Member 'UWDG_SeasonSelector_C::logoSwitcher' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, MainCanvas) == 0x0006F8, "Member 'UWDG_SeasonSelector_C::MainCanvas' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, MainSlot) == 0x000700, "Member 'UWDG_SeasonSelector_C::MainSlot' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, NoLogo) == 0x000708, "Member 'UWDG_SeasonSelector_C::NoLogo' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, WhiteText) == 0x000710, "Member 'UWDG_SeasonSelector_C::WhiteText' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, FontTypeFace) == 0x000718, "Member 'UWDG_SeasonSelector_C::FontTypeFace' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, MinDesiredWidth) == 0x000720, "Member 'UWDG_SeasonSelector_C::MinDesiredWidth' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, FontSize) == 0x000724, "Member 'UWDG_SeasonSelector_C::FontSize' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, IsToggleButton) == 0x000728, "Member 'UWDG_SeasonSelector_C::IsToggleButton' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, LabelPadding) == 0x00072C, "Member 'UWDG_SeasonSelector_C::LabelPadding' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, Selected) == 0x00073C, "Member 'UWDG_SeasonSelector_C::Selected' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, StoredNormalColor) == 0x000740, "Member 'UWDG_SeasonSelector_C::StoredNormalColor' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, ToggledTextColor) == 0x000750, "Member 'UWDG_SeasonSelector_C::ToggledTextColor' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, IsToggled) == 0x000760, "Member 'UWDG_SeasonSelector_C::IsToggled' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, OnUnfocus) == 0x000768, "Member 'UWDG_SeasonSelector_C::OnUnfocus' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, OnToggled) == 0x000778, "Member 'UWDG_SeasonSelector_C::OnToggled' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, ToggledColor) == 0x000788, "Member 'UWDG_SeasonSelector_C::ToggledColor' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, SlantWidth) == 0x000798, "Member 'UWDG_SeasonSelector_C::SlantWidth' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, ToggleOnOnly) == 0x00079C, "Member 'UWDG_SeasonSelector_C::ToggleOnOnly' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, SeasonType) == 0x00079D, "Member 'UWDG_SeasonSelector_C::SeasonType' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonSelector_C, GameMode) == 0x0007A0, "Member 'UWDG_SeasonSelector_C::GameMode' has a wrong offset!");

}

