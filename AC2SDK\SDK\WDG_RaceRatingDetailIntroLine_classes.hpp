﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RaceRatingDetailIntroLine

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RaceRatingDetailIntroLine.WDG_RaceRatingDetailIntroLine_C
// 0x0008 (0x0270 - 0x0268)
class UWDG_RaceRatingDetailIntroLine_C final : public URatingDetailIntroLine
{
public:
	class UImage*                                 imgIndicator;                                      // 0x0268(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RaceRatingDetailIntroLine_C">();
	}
	static class UWDG_RaceRatingDetailIntroLine_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RaceRatingDetailIntroLine_C>();
	}
};
static_assert(alignof(UWDG_RaceRatingDetailIntroLine_C) == 0x000008, "Wrong alignment on UWDG_RaceRatingDetailIntroLine_C");
static_assert(sizeof(UWDG_RaceRatingDetailIntroLine_C) == 0x000270, "Wrong size on UWDG_RaceRatingDetailIntroLine_C");
static_assert(offsetof(UWDG_RaceRatingDetailIntroLine_C, imgIndicator) == 0x000268, "Member 'UWDG_RaceRatingDetailIntroLine_C::imgIndicator' has a wrong offset!");

}

