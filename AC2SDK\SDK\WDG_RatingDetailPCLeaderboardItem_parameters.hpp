﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RatingDetailPCLeaderboardItem

#include "Basic.hpp"

#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_RatingDetailPCLeaderboardItem.WDG_RatingDetailPCLeaderboardItem_C.SetData
// 0x00E0 (0x00E0 - 0x0000)
struct WDG_RatingDetailPCLeaderboardItem_C_SetData final
{
public:
	struct FOnlineServicesLeaderboardRank         Rank;                                              // 0x0000(0x0014)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
	float                                         CallFunc_Multiply_IntFloat_ReturnValue;            // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_IntToString_ReturnValue;             // 0x0018(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	int32                                         CallFunc_FCeil_ReturnValue;                        // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2C[0x4];                                       // 0x002C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Concat_StrStr_ReturnValue;                // 0x0030(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_IntToString_ReturnValue_1;           // 0x0040(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x0050(0x0018)()
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_1;              // 0x0068(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_IntToText_ReturnValue;               // 0x0078(0x0018)()
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_1;          // 0x0090(0x0018)()
	class FString                                 CallFunc_Conv_IntToString_ReturnValue_2;           // 0x00A8(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_2;              // 0x00B8(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_2;          // 0x00C8(0x0018)()
};
static_assert(alignof(WDG_RatingDetailPCLeaderboardItem_C_SetData) == 0x000008, "Wrong alignment on WDG_RatingDetailPCLeaderboardItem_C_SetData");
static_assert(sizeof(WDG_RatingDetailPCLeaderboardItem_C_SetData) == 0x0000E0, "Wrong size on WDG_RatingDetailPCLeaderboardItem_C_SetData");
static_assert(offsetof(WDG_RatingDetailPCLeaderboardItem_C_SetData, Rank) == 0x000000, "Member 'WDG_RatingDetailPCLeaderboardItem_C_SetData::Rank' has a wrong offset!");
static_assert(offsetof(WDG_RatingDetailPCLeaderboardItem_C_SetData, CallFunc_Multiply_IntFloat_ReturnValue) == 0x000014, "Member 'WDG_RatingDetailPCLeaderboardItem_C_SetData::CallFunc_Multiply_IntFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RatingDetailPCLeaderboardItem_C_SetData, CallFunc_Conv_IntToString_ReturnValue) == 0x000018, "Member 'WDG_RatingDetailPCLeaderboardItem_C_SetData::CallFunc_Conv_IntToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RatingDetailPCLeaderboardItem_C_SetData, CallFunc_FCeil_ReturnValue) == 0x000028, "Member 'WDG_RatingDetailPCLeaderboardItem_C_SetData::CallFunc_FCeil_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RatingDetailPCLeaderboardItem_C_SetData, CallFunc_Concat_StrStr_ReturnValue) == 0x000030, "Member 'WDG_RatingDetailPCLeaderboardItem_C_SetData::CallFunc_Concat_StrStr_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RatingDetailPCLeaderboardItem_C_SetData, CallFunc_Conv_IntToString_ReturnValue_1) == 0x000040, "Member 'WDG_RatingDetailPCLeaderboardItem_C_SetData::CallFunc_Conv_IntToString_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_RatingDetailPCLeaderboardItem_C_SetData, CallFunc_Conv_StringToText_ReturnValue) == 0x000050, "Member 'WDG_RatingDetailPCLeaderboardItem_C_SetData::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RatingDetailPCLeaderboardItem_C_SetData, CallFunc_Concat_StrStr_ReturnValue_1) == 0x000068, "Member 'WDG_RatingDetailPCLeaderboardItem_C_SetData::CallFunc_Concat_StrStr_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_RatingDetailPCLeaderboardItem_C_SetData, CallFunc_Conv_IntToText_ReturnValue) == 0x000078, "Member 'WDG_RatingDetailPCLeaderboardItem_C_SetData::CallFunc_Conv_IntToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RatingDetailPCLeaderboardItem_C_SetData, CallFunc_Conv_StringToText_ReturnValue_1) == 0x000090, "Member 'WDG_RatingDetailPCLeaderboardItem_C_SetData::CallFunc_Conv_StringToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_RatingDetailPCLeaderboardItem_C_SetData, CallFunc_Conv_IntToString_ReturnValue_2) == 0x0000A8, "Member 'WDG_RatingDetailPCLeaderboardItem_C_SetData::CallFunc_Conv_IntToString_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_RatingDetailPCLeaderboardItem_C_SetData, CallFunc_Concat_StrStr_ReturnValue_2) == 0x0000B8, "Member 'WDG_RatingDetailPCLeaderboardItem_C_SetData::CallFunc_Concat_StrStr_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_RatingDetailPCLeaderboardItem_C_SetData, CallFunc_Conv_StringToText_ReturnValue_2) == 0x0000C8, "Member 'WDG_RatingDetailPCLeaderboardItem_C_SetData::CallFunc_Conv_StringToText_ReturnValue_2' has a wrong offset!");

}

