﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_Rating

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_Rating.WDG_Rating_C
// 0x0008 (0x07C8 - 0x07C0)
class UWDG_Rating_C final : public URatingWidget
{
public:
	class UImage*                                 img_error;                                         // 0x07C0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_Rating_C">();
	}
	static class UWDG_Rating_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_Rating_C>();
	}
};
static_assert(alignof(UWDG_Rating_C) == 0x000008, "Wrong alignment on UWDG_Rating_C");
static_assert(sizeof(UWDG_Rating_C) == 0x0007C8, "Wrong size on UWDG_Rating_C");
static_assert(offsetof(UWDG_Rating_C, img_error) == 0x0007C0, "Member 'UWDG_Rating_C::img_error' has a wrong offset!");

}

