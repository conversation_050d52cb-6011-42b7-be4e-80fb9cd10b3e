﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RatingDetailCN

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RatingDetailCN.WDG_RatingDetailCN_C
// 0x0010 (0x0628 - 0x0618)
class UWDG_RatingDetailCN_C final : public URatingCNDetail
{
public:
	class UImage*                                 imgBackground;                                     // 0x0618(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SimpleChartWrapper_C*              WDG_SimpleChartWrapper;                            // 0x0620(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSki<PERSON>, NoD<PERSON>ructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RatingDetailCN_C">();
	}
	static class UWDG_RatingDetailCN_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RatingDetailCN_C>();
	}
};
static_assert(alignof(UWDG_RatingDetailCN_C) == 0x000008, "Wrong alignment on UWDG_RatingDetailCN_C");
static_assert(sizeof(UWDG_RatingDetailCN_C) == 0x000628, "Wrong size on UWDG_RatingDetailCN_C");
static_assert(offsetof(UWDG_RatingDetailCN_C, imgBackground) == 0x000618, "Member 'UWDG_RatingDetailCN_C::imgBackground' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailCN_C, WDG_SimpleChartWrapper) == 0x000620, "Member 'UWDG_RatingDetailCN_C::WDG_SimpleChartWrapper' has a wrong offset!");

}

