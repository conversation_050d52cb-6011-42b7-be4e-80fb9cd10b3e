﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ViewOptionsPage

#include "Basic.hpp"

#include "WDG_ViewOptionsPage_classes.hpp"
#include "WDG_ViewOptionsPage_parameters.hpp"


namespace SDK
{

// Function WDG_ViewOptionsPage.WDG_ViewOptionsPage_C.ExecuteUbergraph_WDG_ViewOptionsPage
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ViewOptionsPage_C::ExecuteUbergraph_WDG_ViewOptionsPage(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ViewOptionsPage_C", "ExecuteUbergraph_WDG_ViewOptionsPage");

	Params::WDG_ViewOptionsPage_C_ExecuteUbergraph_WDG_ViewOptionsPage Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ViewOptionsPage.WDG_ViewOptionsPage_C.BP_StartPage
// (Event, Public, BlueprintEvent)

void UWDG_ViewOptionsPage_C::BP_StartPage()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ViewOptionsPage_C", "BP_StartPage");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ViewOptionsPage.WDG_ViewOptionsPage_C.BndEvt__popupWarning_K2Node_ComponentBoundEvent_1_OnNo__DelegateSignature
// (BlueprintEvent)

void UWDG_ViewOptionsPage_C::BndEvt__popupWarning_K2Node_ComponentBoundEvent_1_OnNo__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ViewOptionsPage_C", "BndEvt__popupWarning_K2Node_ComponentBoundEvent_1_OnNo__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ViewOptionsPage.WDG_ViewOptionsPage_C.BndEvt__WDG_BasicMessagePopup_K2Node_ComponentBoundEvent_0_OnYes__DelegateSignature
// (BlueprintEvent)

void UWDG_ViewOptionsPage_C::BndEvt__WDG_BasicMessagePopup_K2Node_ComponentBoundEvent_0_OnYes__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ViewOptionsPage_C", "BndEvt__WDG_BasicMessagePopup_K2Node_ComponentBoundEvent_0_OnYes__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ViewOptionsPage.WDG_ViewOptionsPage_C.OnUnsavedWarning
// (Event, Protected, BlueprintCallable, BlueprintEvent)

void UWDG_ViewOptionsPage_C::OnUnsavedWarning()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ViewOptionsPage_C", "OnUnsavedWarning");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ViewOptionsPage.WDG_ViewOptionsPage_C.OnSettingsChanged
// (Event, Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    SameAsOldSettings                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ViewOptionsPage_C::OnSettingsChanged(bool SameAsOldSettings)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ViewOptionsPage_C", "OnSettingsChanged");

	Params::WDG_ViewOptionsPage_C_OnSettingsChanged Parms{};

	Parms.SameAsOldSettings = SameAsOldSettings;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ViewOptionsPage.WDG_ViewOptionsPage_C.BP_OnMenuNavigationPreview
// (Event, Public, BlueprintEvent)
// Parameters:
// const EControllerActionType             Input                                                  (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    isReleased                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ViewOptionsPage_C::BP_OnMenuNavigationPreview(const EControllerActionType Input, bool isReleased)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ViewOptionsPage_C", "BP_OnMenuNavigationPreview");

	Params::WDG_ViewOptionsPage_C_BP_OnMenuNavigationPreview Parms{};

	Parms.Input = Input;
	Parms.isReleased = isReleased;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ViewOptionsPage.WDG_ViewOptionsPage_C.BndEvt__MainSelector_K2Node_ComponentBoundEvent_2_OnChange__DelegateSignature
// (BlueprintEvent)
// Parameters:
// int32                                   New_Value                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ViewOptionsPage_C::BndEvt__MainSelector_K2Node_ComponentBoundEvent_2_OnChange__DelegateSignature(int32 New_Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ViewOptionsPage_C", "BndEvt__MainSelector_K2Node_ComponentBoundEvent_2_OnChange__DelegateSignature");

	Params::WDG_ViewOptionsPage_C_BndEvt__MainSelector_K2Node_ComponentBoundEvent_2_OnChange__DelegateSignature Parms{};

	Parms.New_Value = New_Value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ViewOptionsPage.WDG_ViewOptionsPage_C.OnPreviewKeyDown
// (Event, Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FKeyEvent&                 InKeyEvent                                             (BlueprintVisible, BlueprintReadOnly, Parm)
// struct FEventReply                      ReturnValue                                            (Parm, OutParm, ReturnParm)

struct FEventReply UWDG_ViewOptionsPage_C::OnPreviewKeyDown(const struct FGeometry& MyGeometry, const struct FKeyEvent& InKeyEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ViewOptionsPage_C", "OnPreviewKeyDown");

	Params::WDG_ViewOptionsPage_C_OnPreviewKeyDown Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InKeyEvent = std::move(InKeyEvent);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}

}

