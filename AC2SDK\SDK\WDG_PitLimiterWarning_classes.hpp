﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PitLimiterWarning

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_PitLimiterWarning.WDG_PitLimiterWarning_C
// 0x0048 (0x06A0 - 0x0658)
class UWDG_PitLimiterWarning_C final : public UAcRaceWidgetBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0658(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UTextBlock*                             GearWarning;                                       // 0x0660(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             SpeedWarning;                                      // 0x0668(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtTimer;                                          // 0x0670(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                WarningBorder;                                     // 0x0678(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	struct FTimerHandle                           DebounceTimer;                                     // 0x0680(0x0008)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	float                                         TimeDelay;                                         // 0x0688(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         DefaultTimeDelay;                                  // 0x068C(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<class FString>                         ShortPitEntryTracks;                               // 0x0690(0x0010)(Edit, BlueprintVisible)

public:
	void ExecuteUbergraph_WDG_PitLimiterWarning(int32 EntryPoint);
	void OnStartWidget(class UAcGameInstance* GameInstance, class AAcRaceGameMode* raceGameMode, class ACarAvatar* CarAvatar, const struct FHUDOptions& HUDOptions);
	void OnHudTick(const struct FRaceHUDState& State);
	void Destruct();
	void warningTimer();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_PitLimiterWarning_C">();
	}
	static class UWDG_PitLimiterWarning_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_PitLimiterWarning_C>();
	}
};
static_assert(alignof(UWDG_PitLimiterWarning_C) == 0x000008, "Wrong alignment on UWDG_PitLimiterWarning_C");
static_assert(sizeof(UWDG_PitLimiterWarning_C) == 0x0006A0, "Wrong size on UWDG_PitLimiterWarning_C");
static_assert(offsetof(UWDG_PitLimiterWarning_C, UberGraphFrame) == 0x000658, "Member 'UWDG_PitLimiterWarning_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_PitLimiterWarning_C, GearWarning) == 0x000660, "Member 'UWDG_PitLimiterWarning_C::GearWarning' has a wrong offset!");
static_assert(offsetof(UWDG_PitLimiterWarning_C, SpeedWarning) == 0x000668, "Member 'UWDG_PitLimiterWarning_C::SpeedWarning' has a wrong offset!");
static_assert(offsetof(UWDG_PitLimiterWarning_C, txtTimer) == 0x000670, "Member 'UWDG_PitLimiterWarning_C::txtTimer' has a wrong offset!");
static_assert(offsetof(UWDG_PitLimiterWarning_C, WarningBorder) == 0x000678, "Member 'UWDG_PitLimiterWarning_C::WarningBorder' has a wrong offset!");
static_assert(offsetof(UWDG_PitLimiterWarning_C, DebounceTimer) == 0x000680, "Member 'UWDG_PitLimiterWarning_C::DebounceTimer' has a wrong offset!");
static_assert(offsetof(UWDG_PitLimiterWarning_C, TimeDelay) == 0x000688, "Member 'UWDG_PitLimiterWarning_C::TimeDelay' has a wrong offset!");
static_assert(offsetof(UWDG_PitLimiterWarning_C, DefaultTimeDelay) == 0x00068C, "Member 'UWDG_PitLimiterWarning_C::DefaultTimeDelay' has a wrong offset!");
static_assert(offsetof(UWDG_PitLimiterWarning_C, ShortPitEntryTracks) == 0x000690, "Member 'UWDG_PitLimiterWarning_C::ShortPitEntryTracks' has a wrong offset!");

}

