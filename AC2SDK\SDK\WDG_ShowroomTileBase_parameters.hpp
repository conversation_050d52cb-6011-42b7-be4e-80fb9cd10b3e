﻿#pragma once

/*
* SDK generated by Du<PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomTileBase

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"
#include "Engine_structs.hpp"
#include "SlateCore_structs.hpp"
#include "UMG_structs.hpp"
#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.ExecuteUbergraph_WDG_ShowroomTileBase
// 0x08C0 (0x08C0 - 0x0000)
struct WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0004(0x0001)(ZeroConstructor, Is<PERSON>lainOldData, NoD<PERSON>ru<PERSON>, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_6[0x2];                                        // 0x0006(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FFocusEvent                            K2Node_Event_InFocusEvent_1;                       // 0x0008(0x0008)(NoDestructor)
	struct FFocusEvent                            K2Node_Event_InFocusEvent;                         // 0x0010(0x0008)(NoDestructor)
	struct FModelInfo                             K2Node_CustomEvent_ModelInfo;                      // 0x0018(0x01A8)()
	class FText                                   CallFunc_Conv_IntToText_ReturnValue;               // 0x01C0(0x0018)()
	class FText                                   CallFunc_Conv_NameToText_ReturnValue;              // 0x01D8(0x0018)()
	struct FSlateColor                            K2Node_MakeStruct_SlateColor;                      // 0x01F0(0x0028)()
	struct FPointerEvent                          K2Node_Event_MouseEvent_1;                         // 0x0218(0x0070)(ConstParm)
	struct FGeometry                              K2Node_Event_MyGeometry;                           // 0x0288(0x0038)(IsPlainOldData, NoDestructor)
	struct FPointerEvent                          K2Node_Event_MouseEvent;                           // 0x02C0(0x0070)(ConstParm)
	struct FTeamInfo                              K2Node_CustomEvent_TeamInfo;                       // 0x0330(0x0038)()
	class FName                                   CallFunc_Conv_StringToName_ReturnValue;            // 0x0368(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0370(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x0374(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_IntToString_ReturnValue;             // 0x0378(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x0388(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_389[0x3];                                      // 0x0389(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class FName                                   CallFunc_Conv_StringToName_ReturnValue_1;          // 0x038C(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_394[0x4];                                      // 0x0394(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTeamTemplate                          CallFunc_GetDataTableRowFromName_OutRow;           // 0x0398(0x0130)()
	bool                                          CallFunc_GetDataTableRowFromName_ReturnValue;      // 0x04C8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_4C9[0x7];                                      // 0x04C9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x04D0(0x0018)()
	struct FCarInfo                               K2Node_CustomEvent_CarInfo;                        // 0x04E8(0x00E0)()
	class FName                                   K2Node_CustomEvent_CarKey;                         // 0x05C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Conv_ByteToInt_ReturnValue;               // 0x05D0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_5D4[0x4];                                      // 0x05D4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FDriverInfo                            K2Node_CustomEvent_DriverInfo;                     // 0x05D8(0x00F0)()
	class FName                                   K2Node_CustomEvent_DriverKey;                      // 0x06C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_IsCustom;                       // 0x06D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_6D1[0x7];                                      // 0x06D1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_PadNumberWithZeroesAlways_ReturnValue;    // 0x06D8(0x0018)()
	int32                                         Temp_int_Array_Index_Variable;                     // 0x06F0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESeasonType                                   CallFunc_Array_Get_Item;                           // 0x06F4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsSeasonGTWC_ReturnValue;                 // 0x06F5(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_6F6[0x2];                                      // 0x06F6(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_SeasonToText_YearText;                    // 0x06F8(0x0018)()
	class FString                                 CallFunc_Conv_TextToString_ReturnValue;            // 0x0710(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_1;          // 0x0720(0x0018)()
	struct FLinearColor                           CallFunc_GetNormalColor_NormalTextColor;           // 0x0738(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_1;                    // 0x0748(0x0028)()
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue;              // 0x0770(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0778(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate;              // 0x077C(0x0010)(ZeroConstructor, NoDestructor)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x078C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_78D[0x3];                                      // 0x078D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0790(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0794(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_795[0x3];                                      // 0x0795(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue_1;            // 0x0798(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_GenericMappedLabel_C*              CallFunc_Create_ReturnValue;                       // 0x07A0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue;                     // 0x07A8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x07B0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_7B1[0x3];                                      // 0x07B1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x07B4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_2;            // 0x07B8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_7B9[0x7];                                      // 0x07B9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue;             // 0x07C0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValidSoftObjectReference_ReturnValue;   // 0x07C8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_7C9[0x7];                                      // 0x07C9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_2;          // 0x07D0(0x0018)()
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_3;          // 0x07E8(0x0018)()
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData;              // 0x0800(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData_1;            // 0x0840(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	TArray<struct FFormatArgumentData>            K2Node_MakeArray_Array;                            // 0x0880(0x0010)(ReferenceParm)
	class FText                                   CallFunc_Format_ReturnValue;                       // 0x0890(0x0018)()
	int32                                         CallFunc_Conv_ByteToInt_ReturnValue_1;             // 0x08A8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x08AC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_8AD[0x3];                                      // 0x08AD(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable;                                 // 0x08B0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_3;            // 0x08B4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_8B5[0x3];                                      // 0x08B5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Percent_IntInt_ReturnValue;               // 0x08B8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x08BC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase) == 0x000008, "Wrong alignment on WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase");
static_assert(sizeof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase) == 0x0008C0, "Wrong size on WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, EntryPoint) == 0x000000, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_MakeLiteralByte_ReturnValue) == 0x000004, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, K2Node_Event_IsDesignTime) == 0x000005, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, K2Node_Event_InFocusEvent_1) == 0x000008, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::K2Node_Event_InFocusEvent_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, K2Node_Event_InFocusEvent) == 0x000010, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::K2Node_Event_InFocusEvent' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, K2Node_CustomEvent_ModelInfo) == 0x000018, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::K2Node_CustomEvent_ModelInfo' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_Conv_IntToText_ReturnValue) == 0x0001C0, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_Conv_IntToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_Conv_NameToText_ReturnValue) == 0x0001D8, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_Conv_NameToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, K2Node_MakeStruct_SlateColor) == 0x0001F0, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::K2Node_MakeStruct_SlateColor' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, K2Node_Event_MouseEvent_1) == 0x000218, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::K2Node_Event_MouseEvent_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, K2Node_Event_MyGeometry) == 0x000288, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::K2Node_Event_MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, K2Node_Event_MouseEvent) == 0x0002C0, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::K2Node_Event_MouseEvent' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, K2Node_CustomEvent_TeamInfo) == 0x000330, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::K2Node_CustomEvent_TeamInfo' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_Conv_StringToName_ReturnValue) == 0x000368, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_Conv_StringToName_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_Array_Length_ReturnValue) == 0x000370, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_Array_Length_ReturnValue_1) == 0x000374, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_Conv_IntToString_ReturnValue) == 0x000378, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_Conv_IntToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_Greater_IntInt_ReturnValue) == 0x000388, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_Conv_StringToName_ReturnValue_1) == 0x00038C, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_Conv_StringToName_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_GetDataTableRowFromName_OutRow) == 0x000398, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_GetDataTableRowFromName_OutRow' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_GetDataTableRowFromName_ReturnValue) == 0x0004C8, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_GetDataTableRowFromName_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_Conv_StringToText_ReturnValue) == 0x0004D0, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, K2Node_CustomEvent_CarInfo) == 0x0004E8, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::K2Node_CustomEvent_CarInfo' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, K2Node_CustomEvent_CarKey) == 0x0005C8, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::K2Node_CustomEvent_CarKey' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_Conv_ByteToInt_ReturnValue) == 0x0005D0, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_Conv_ByteToInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, K2Node_CustomEvent_DriverInfo) == 0x0005D8, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::K2Node_CustomEvent_DriverInfo' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, K2Node_CustomEvent_DriverKey) == 0x0006C8, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::K2Node_CustomEvent_DriverKey' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, K2Node_CustomEvent_IsCustom) == 0x0006D0, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::K2Node_CustomEvent_IsCustom' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_PadNumberWithZeroesAlways_ReturnValue) == 0x0006D8, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_PadNumberWithZeroesAlways_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, Temp_int_Array_Index_Variable) == 0x0006F0, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_Array_Get_Item) == 0x0006F4, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_IsSeasonGTWC_ReturnValue) == 0x0006F5, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_IsSeasonGTWC_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_SeasonToText_YearText) == 0x0006F8, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_SeasonToText_YearText' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_Conv_TextToString_ReturnValue) == 0x000710, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_Conv_TextToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_Conv_StringToText_ReturnValue_1) == 0x000720, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_Conv_StringToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_GetNormalColor_NormalTextColor) == 0x000738, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_GetNormalColor_NormalTextColor' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, K2Node_MakeStruct_SlateColor_1) == 0x000748, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::K2Node_MakeStruct_SlateColor_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_GetOwningPlayer_ReturnValue) == 0x000770, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_GetOwningPlayer_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, Temp_int_Loop_Counter_Variable) == 0x000778, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, K2Node_CreateDelegate_OutputDelegate) == 0x00077C, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_Less_IntInt_ReturnValue) == 0x00078C, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_Add_IntInt_ReturnValue) == 0x000790, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000794, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_GetOwningPlayer_ReturnValue_1) == 0x000798, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_GetOwningPlayer_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_Create_ReturnValue) == 0x0007A0, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_AddChild_ReturnValue) == 0x0007A8, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_AddChild_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_IsValid_ReturnValue) == 0x0007B0, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_Add_IntInt_ReturnValue_1) == 0x0007B4, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_MakeLiteralByte_ReturnValue_2) == 0x0007B8, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_MakeLiteralByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_SlotAsCanvasSlot_ReturnValue) == 0x0007C0, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_SlotAsCanvasSlot_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_IsValidSoftObjectReference_ReturnValue) == 0x0007C8, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_IsValidSoftObjectReference_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_Conv_StringToText_ReturnValue_2) == 0x0007D0, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_Conv_StringToText_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_Conv_StringToText_ReturnValue_3) == 0x0007E8, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_Conv_StringToText_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, K2Node_MakeStruct_FormatArgumentData) == 0x000800, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::K2Node_MakeStruct_FormatArgumentData' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, K2Node_MakeStruct_FormatArgumentData_1) == 0x000840, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::K2Node_MakeStruct_FormatArgumentData_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, K2Node_MakeArray_Array) == 0x000880, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_Format_ReturnValue) == 0x000890, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_Format_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_Conv_ByteToInt_ReturnValue_1) == 0x0008A8, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_Conv_ByteToInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, K2Node_SwitchEnum_CmpSuccess) == 0x0008AC, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, Temp_int_Variable) == 0x0008B0, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_MakeLiteralByte_ReturnValue_3) == 0x0008B4, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_MakeLiteralByte_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_Percent_IntInt_ReturnValue) == 0x0008B8, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_Percent_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x0008BC, "Member 'WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.UpdateDriver
// 0x0100 (0x0100 - 0x0000)
struct WDG_ShowroomTileBase_C_UpdateDriver final
{
public:
	struct FDriverInfo                            DriverInfo_0;                                      // 0x0000(0x00F0)(BlueprintVisible, BlueprintReadOnly, Parm)
	class FName                                   DriverKey;                                         // 0x00F0(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          isCustom;                                          // 0x00F8(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomTileBase_C_UpdateDriver) == 0x000008, "Wrong alignment on WDG_ShowroomTileBase_C_UpdateDriver");
static_assert(sizeof(WDG_ShowroomTileBase_C_UpdateDriver) == 0x000100, "Wrong size on WDG_ShowroomTileBase_C_UpdateDriver");
static_assert(offsetof(WDG_ShowroomTileBase_C_UpdateDriver, DriverInfo_0) == 0x000000, "Member 'WDG_ShowroomTileBase_C_UpdateDriver::DriverInfo_0' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_UpdateDriver, DriverKey) == 0x0000F0, "Member 'WDG_ShowroomTileBase_C_UpdateDriver::DriverKey' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_UpdateDriver, isCustom) == 0x0000F8, "Member 'WDG_ShowroomTileBase_C_UpdateDriver::isCustom' has a wrong offset!");

// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.UpdateCar
// 0x00E8 (0x00E8 - 0x0000)
struct WDG_ShowroomTileBase_C_UpdateCar final
{
public:
	struct FCarInfo                               CarInfo_0;                                         // 0x0000(0x00E0)(BlueprintVisible, BlueprintReadOnly, Parm)
	class FName                                   CarKey;                                            // 0x00E0(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomTileBase_C_UpdateCar) == 0x000008, "Wrong alignment on WDG_ShowroomTileBase_C_UpdateCar");
static_assert(sizeof(WDG_ShowroomTileBase_C_UpdateCar) == 0x0000E8, "Wrong size on WDG_ShowroomTileBase_C_UpdateCar");
static_assert(offsetof(WDG_ShowroomTileBase_C_UpdateCar, CarInfo_0) == 0x000000, "Member 'WDG_ShowroomTileBase_C_UpdateCar::CarInfo_0' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_UpdateCar, CarKey) == 0x0000E0, "Member 'WDG_ShowroomTileBase_C_UpdateCar::CarKey' has a wrong offset!");

// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.UpdateTeam
// 0x0038 (0x0038 - 0x0000)
struct WDG_ShowroomTileBase_C_UpdateTeam final
{
public:
	struct FTeamInfo                              TeamInfo_0;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_ShowroomTileBase_C_UpdateTeam) == 0x000008, "Wrong alignment on WDG_ShowroomTileBase_C_UpdateTeam");
static_assert(sizeof(WDG_ShowroomTileBase_C_UpdateTeam) == 0x000038, "Wrong size on WDG_ShowroomTileBase_C_UpdateTeam");
static_assert(offsetof(WDG_ShowroomTileBase_C_UpdateTeam, TeamInfo_0) == 0x000000, "Member 'WDG_ShowroomTileBase_C_UpdateTeam::TeamInfo_0' has a wrong offset!");

// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.OnMouseEnter
// 0x00A8 (0x00A8 - 0x0000)
struct WDG_ShowroomTileBase_C_OnMouseEnter final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FPointerEvent                          MouseEvent;                                        // 0x0038(0x0070)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_ShowroomTileBase_C_OnMouseEnter) == 0x000008, "Wrong alignment on WDG_ShowroomTileBase_C_OnMouseEnter");
static_assert(sizeof(WDG_ShowroomTileBase_C_OnMouseEnter) == 0x0000A8, "Wrong size on WDG_ShowroomTileBase_C_OnMouseEnter");
static_assert(offsetof(WDG_ShowroomTileBase_C_OnMouseEnter, MyGeometry) == 0x000000, "Member 'WDG_ShowroomTileBase_C_OnMouseEnter::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_OnMouseEnter, MouseEvent) == 0x000038, "Member 'WDG_ShowroomTileBase_C_OnMouseEnter::MouseEvent' has a wrong offset!");

// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.OnMouseLeave
// 0x0070 (0x0070 - 0x0000)
struct WDG_ShowroomTileBase_C_OnMouseLeave final
{
public:
	struct FPointerEvent                          MouseEvent;                                        // 0x0000(0x0070)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_ShowroomTileBase_C_OnMouseLeave) == 0x000008, "Wrong alignment on WDG_ShowroomTileBase_C_OnMouseLeave");
static_assert(sizeof(WDG_ShowroomTileBase_C_OnMouseLeave) == 0x000070, "Wrong size on WDG_ShowroomTileBase_C_OnMouseLeave");
static_assert(offsetof(WDG_ShowroomTileBase_C_OnMouseLeave, MouseEvent) == 0x000000, "Member 'WDG_ShowroomTileBase_C_OnMouseLeave::MouseEvent' has a wrong offset!");

// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.UpdateModel
// 0x01A8 (0x01A8 - 0x0000)
struct WDG_ShowroomTileBase_C_UpdateModel final
{
public:
	struct FModelInfo                             ModelInfo_0;                                       // 0x0000(0x01A8)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_ShowroomTileBase_C_UpdateModel) == 0x000008, "Wrong alignment on WDG_ShowroomTileBase_C_UpdateModel");
static_assert(sizeof(WDG_ShowroomTileBase_C_UpdateModel) == 0x0001A8, "Wrong size on WDG_ShowroomTileBase_C_UpdateModel");
static_assert(offsetof(WDG_ShowroomTileBase_C_UpdateModel, ModelInfo_0) == 0x000000, "Member 'WDG_ShowroomTileBase_C_UpdateModel::ModelInfo_0' has a wrong offset!");

// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.OnRemovedFromFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomTileBase_C_OnRemovedFromFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_ShowroomTileBase_C_OnRemovedFromFocusPath) == 0x000004, "Wrong alignment on WDG_ShowroomTileBase_C_OnRemovedFromFocusPath");
static_assert(sizeof(WDG_ShowroomTileBase_C_OnRemovedFromFocusPath) == 0x000008, "Wrong size on WDG_ShowroomTileBase_C_OnRemovedFromFocusPath");
static_assert(offsetof(WDG_ShowroomTileBase_C_OnRemovedFromFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_ShowroomTileBase_C_OnRemovedFromFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.OnAddedToFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomTileBase_C_OnAddedToFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_ShowroomTileBase_C_OnAddedToFocusPath) == 0x000004, "Wrong alignment on WDG_ShowroomTileBase_C_OnAddedToFocusPath");
static_assert(sizeof(WDG_ShowroomTileBase_C_OnAddedToFocusPath) == 0x000008, "Wrong size on WDG_ShowroomTileBase_C_OnAddedToFocusPath");
static_assert(offsetof(WDG_ShowroomTileBase_C_OnAddedToFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_ShowroomTileBase_C_OnAddedToFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_ShowroomTileBase_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomTileBase_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_ShowroomTileBase_C_PreConstruct");
static_assert(sizeof(WDG_ShowroomTileBase_C_PreConstruct) == 0x000001, "Wrong size on WDG_ShowroomTileBase_C_PreConstruct");
static_assert(offsetof(WDG_ShowroomTileBase_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_ShowroomTileBase_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.OnMouseButtonUp
// 0x0218 (0x0218 - 0x0000)
struct WDG_ShowroomTileBase_C_OnMouseButtonUp final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FPointerEvent                          MouseEvent;                                        // 0x0038(0x0070)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	struct FEventReply                            ReturnValue;                                       // 0x00A8(0x00B8)(Parm, OutParm, ReturnParm)
	struct FEventReply                            CallFunc_Handled_ReturnValue;                      // 0x0160(0x00B8)()
};
static_assert(alignof(WDG_ShowroomTileBase_C_OnMouseButtonUp) == 0x000008, "Wrong alignment on WDG_ShowroomTileBase_C_OnMouseButtonUp");
static_assert(sizeof(WDG_ShowroomTileBase_C_OnMouseButtonUp) == 0x000218, "Wrong size on WDG_ShowroomTileBase_C_OnMouseButtonUp");
static_assert(offsetof(WDG_ShowroomTileBase_C_OnMouseButtonUp, MyGeometry) == 0x000000, "Member 'WDG_ShowroomTileBase_C_OnMouseButtonUp::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_OnMouseButtonUp, MouseEvent) == 0x000038, "Member 'WDG_ShowroomTileBase_C_OnMouseButtonUp::MouseEvent' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_OnMouseButtonUp, ReturnValue) == 0x0000A8, "Member 'WDG_ShowroomTileBase_C_OnMouseButtonUp::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_OnMouseButtonUp, CallFunc_Handled_ReturnValue) == 0x000160, "Member 'WDG_ShowroomTileBase_C_OnMouseButtonUp::CallFunc_Handled_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.OnMouseButtonDoubleClick
// 0x0218 (0x0218 - 0x0000)
struct WDG_ShowroomTileBase_C_OnMouseButtonDoubleClick final
{
public:
	struct FGeometry                              InMyGeometry;                                      // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FPointerEvent                          InMouseEvent;                                      // 0x0038(0x0070)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	struct FEventReply                            ReturnValue;                                       // 0x00A8(0x00B8)(Parm, OutParm, ReturnParm)
	struct FEventReply                            CallFunc_Handled_ReturnValue;                      // 0x0160(0x00B8)()
};
static_assert(alignof(WDG_ShowroomTileBase_C_OnMouseButtonDoubleClick) == 0x000008, "Wrong alignment on WDG_ShowroomTileBase_C_OnMouseButtonDoubleClick");
static_assert(sizeof(WDG_ShowroomTileBase_C_OnMouseButtonDoubleClick) == 0x000218, "Wrong size on WDG_ShowroomTileBase_C_OnMouseButtonDoubleClick");
static_assert(offsetof(WDG_ShowroomTileBase_C_OnMouseButtonDoubleClick, InMyGeometry) == 0x000000, "Member 'WDG_ShowroomTileBase_C_OnMouseButtonDoubleClick::InMyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_OnMouseButtonDoubleClick, InMouseEvent) == 0x000038, "Member 'WDG_ShowroomTileBase_C_OnMouseButtonDoubleClick::InMouseEvent' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_OnMouseButtonDoubleClick, ReturnValue) == 0x0000A8, "Member 'WDG_ShowroomTileBase_C_OnMouseButtonDoubleClick::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_OnMouseButtonDoubleClick, CallFunc_Handled_ReturnValue) == 0x000160, "Member 'WDG_ShowroomTileBase_C_OnMouseButtonDoubleClick::CallFunc_Handled_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.OnMouseButtonDown
// 0x02D0 (0x02D0 - 0x0000)
struct WDG_ShowroomTileBase_C_OnMouseButtonDown final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FPointerEvent                          MouseEvent;                                        // 0x0038(0x0070)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	struct FEventReply                            ReturnValue;                                       // 0x00A8(0x00B8)(Parm, OutParm, ReturnParm)
	struct FEventReply                            CallFunc_Handled_ReturnValue;                      // 0x0160(0x00B8)()
	struct FEventReply                            CallFunc_OnMouseButtonDown_ReturnValue;            // 0x0218(0x00B8)()
};
static_assert(alignof(WDG_ShowroomTileBase_C_OnMouseButtonDown) == 0x000008, "Wrong alignment on WDG_ShowroomTileBase_C_OnMouseButtonDown");
static_assert(sizeof(WDG_ShowroomTileBase_C_OnMouseButtonDown) == 0x0002D0, "Wrong size on WDG_ShowroomTileBase_C_OnMouseButtonDown");
static_assert(offsetof(WDG_ShowroomTileBase_C_OnMouseButtonDown, MyGeometry) == 0x000000, "Member 'WDG_ShowroomTileBase_C_OnMouseButtonDown::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_OnMouseButtonDown, MouseEvent) == 0x000038, "Member 'WDG_ShowroomTileBase_C_OnMouseButtonDown::MouseEvent' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_OnMouseButtonDown, ReturnValue) == 0x0000A8, "Member 'WDG_ShowroomTileBase_C_OnMouseButtonDown::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_OnMouseButtonDown, CallFunc_Handled_ReturnValue) == 0x000160, "Member 'WDG_ShowroomTileBase_C_OnMouseButtonDown::CallFunc_Handled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileBase_C_OnMouseButtonDown, CallFunc_OnMouseButtonDown_ReturnValue) == 0x000218, "Member 'WDG_ShowroomTileBase_C_OnMouseButtonDown::CallFunc_OnMouseButtonDown_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.SetSelected
// 0x0001 (0x0001 - 0x0000)
struct WDG_ShowroomTileBase_C_SetSelected final
{
public:
	bool                                          IsSelected_0;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomTileBase_C_SetSelected) == 0x000001, "Wrong alignment on WDG_ShowroomTileBase_C_SetSelected");
static_assert(sizeof(WDG_ShowroomTileBase_C_SetSelected) == 0x000001, "Wrong size on WDG_ShowroomTileBase_C_SetSelected");
static_assert(offsetof(WDG_ShowroomTileBase_C_SetSelected, IsSelected_0) == 0x000000, "Member 'WDG_ShowroomTileBase_C_SetSelected::IsSelected_0' has a wrong offset!");

// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.GetNormalColor
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomTileBase_C_GetNormalColor final
{
public:
	struct FLinearColor                           NormalTextColor_0;                                 // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomTileBase_C_GetNormalColor) == 0x000004, "Wrong alignment on WDG_ShowroomTileBase_C_GetNormalColor");
static_assert(sizeof(WDG_ShowroomTileBase_C_GetNormalColor) == 0x000010, "Wrong size on WDG_ShowroomTileBase_C_GetNormalColor");
static_assert(offsetof(WDG_ShowroomTileBase_C_GetNormalColor, NormalTextColor_0) == 0x000000, "Member 'WDG_ShowroomTileBase_C_GetNormalColor::NormalTextColor_0' has a wrong offset!");

}

