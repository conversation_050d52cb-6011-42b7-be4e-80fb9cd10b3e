﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RatingDetailCp

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RatingDetailCp.WDG_RatingDetailCP_C
// 0x0010 (0x0630 - 0x0620)
class UWDG_RatingDetailCP_C final : public URatingCPDetail
{
public:
	class UImage*                                 imgBackground;                                     // 0x0620(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SimpleChartWrapper_C*              WDG_SimpleChartWrapper;                            // 0x0628(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoD<PERSON>ru<PERSON>, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RatingDetailCP_C">();
	}
	static class UWDG_RatingDetailCP_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RatingDetailCP_C>();
	}
};
static_assert(alignof(UWDG_RatingDetailCP_C) == 0x000008, "Wrong alignment on UWDG_RatingDetailCP_C");
static_assert(sizeof(UWDG_RatingDetailCP_C) == 0x000630, "Wrong size on UWDG_RatingDetailCP_C");
static_assert(offsetof(UWDG_RatingDetailCP_C, imgBackground) == 0x000620, "Member 'UWDG_RatingDetailCP_C::imgBackground' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailCP_C, WDG_SimpleChartWrapper) == 0x000628, "Member 'UWDG_RatingDetailCP_C::WDG_SimpleChartWrapper' has a wrong offset!");

}

