﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RaceRatingDetailIntro

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RaceRatingDetailIntro.WDG_RaceRatingDetailIntro_C
// 0x0008 (0x0278 - 0x0270)
class UWDG_RaceRatingDetailIntro_C final : public URatingDetailIntroItem
{
public:
	class UWDG_RaceRatingDetailIntroLine_C*       WDG_RaceRatingDetailIntroLine;                     // 0x0270(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RaceRatingDetailIntro_C">();
	}
	static class UWDG_RaceRatingDetailIntro_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RaceRatingDetailIntro_C>();
	}
};
static_assert(alignof(UWDG_RaceRatingDetailIntro_C) == 0x000008, "Wrong alignment on UWDG_RaceRatingDetailIntro_C");
static_assert(sizeof(UWDG_RaceRatingDetailIntro_C) == 0x000278, "Wrong size on UWDG_RaceRatingDetailIntro_C");
static_assert(offsetof(UWDG_RaceRatingDetailIntro_C, WDG_RaceRatingDetailIntroLine) == 0x000270, "Member 'UWDG_RaceRatingDetailIntro_C::WDG_RaceRatingDetailIntroLine' has a wrong offset!");

}

