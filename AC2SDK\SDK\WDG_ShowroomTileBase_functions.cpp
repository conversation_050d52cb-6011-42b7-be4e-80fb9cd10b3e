﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomTileBase

#include "Basic.hpp"

#include "WDG_ShowroomTileBase_classes.hpp"
#include "WDG_ShowroomTileBase_parameters.hpp"


namespace SDK
{

// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.ExecuteUbergraph_WDG_ShowroomTileBase
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomTileBase_C::ExecuteUbergraph_WDG_ShowroomTileBase(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileBase_C", "ExecuteUbergraph_WDG_ShowroomTileBase");

	Params::WDG_ShowroomTileBase_C_ExecuteUbergraph_WDG_ShowroomTileBase Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.UpdateDriver
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FDriverInfo&               DriverInfo_0                                           (BlueprintVisible, BlueprintReadOnly, Parm)
// class FName                             DriverKey                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    isCustom                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ShowroomTileBase_C::UpdateDriver(const struct FDriverInfo& DriverInfo_0, class FName DriverKey, bool isCustom)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileBase_C", "UpdateDriver");

	Params::WDG_ShowroomTileBase_C_UpdateDriver Parms{};

	Parms.DriverInfo_0 = std::move(DriverInfo_0);
	Parms.DriverKey = DriverKey;
	Parms.isCustom = isCustom;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.UpdateCar
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FCarInfo&                  CarInfo_0                                              (BlueprintVisible, BlueprintReadOnly, Parm)
// class FName                             CarKey                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomTileBase_C::UpdateCar(const struct FCarInfo& CarInfo_0, class FName CarKey)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileBase_C", "UpdateCar");

	Params::WDG_ShowroomTileBase_C_UpdateCar Parms{};

	Parms.CarInfo_0 = std::move(CarInfo_0);
	Parms.CarKey = CarKey;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.UpdateTeam
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FTeamInfo&                 TeamInfo_0                                             (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_ShowroomTileBase_C::UpdateTeam(const struct FTeamInfo& TeamInfo_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileBase_C", "UpdateTeam");

	Params::WDG_ShowroomTileBase_C_UpdateTeam Parms{};

	Parms.TeamInfo_0 = std::move(TeamInfo_0);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.OnMouseEnter
// (BlueprintCosmetic, Event, Public, HasOutParams, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FPointerEvent&             MouseEvent                                             (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_ShowroomTileBase_C::OnMouseEnter(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileBase_C", "OnMouseEnter");

	Params::WDG_ShowroomTileBase_C_OnMouseEnter Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.MouseEvent = std::move(MouseEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.OnMouseLeave
// (BlueprintCosmetic, Event, Public, HasOutParams, BlueprintEvent)
// Parameters:
// const struct FPointerEvent&             MouseEvent                                             (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_ShowroomTileBase_C::OnMouseLeave(const struct FPointerEvent& MouseEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileBase_C", "OnMouseLeave");

	Params::WDG_ShowroomTileBase_C_OnMouseLeave Parms{};

	Parms.MouseEvent = std::move(MouseEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.UpdateModel
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FModelInfo&                ModelInfo_0                                            (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_ShowroomTileBase_C::UpdateModel(const struct FModelInfo& ModelInfo_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileBase_C", "UpdateModel");

	Params::WDG_ShowroomTileBase_C_UpdateModel Parms{};

	Parms.ModelInfo_0 = std::move(ModelInfo_0);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.OnItemForwardEvent
// (BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomTileBase_C::OnItemForwardEvent()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileBase_C", "OnItemForwardEvent");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_ShowroomTileBase_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileBase_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.OnRemovedFromFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_ShowroomTileBase_C::OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileBase_C", "OnRemovedFromFocusPath");

	Params::WDG_ShowroomTileBase_C_OnRemovedFromFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.OnAddedToFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_ShowroomTileBase_C::OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileBase_C", "OnAddedToFocusPath");

	Params::WDG_ShowroomTileBase_C_OnAddedToFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ShowroomTileBase_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileBase_C", "PreConstruct");

	Params::WDG_ShowroomTileBase_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.OnMouseButtonUp
// (BlueprintCosmetic, Event, Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FPointerEvent&             MouseEvent                                             (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// struct FEventReply                      ReturnValue                                            (Parm, OutParm, ReturnParm)

struct FEventReply UWDG_ShowroomTileBase_C::OnMouseButtonUp(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileBase_C", "OnMouseButtonUp");

	Params::WDG_ShowroomTileBase_C_OnMouseButtonUp Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.MouseEvent = std::move(MouseEvent);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.OnMouseButtonDoubleClick
// (BlueprintCosmetic, Event, Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 InMyGeometry                                           (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FPointerEvent&             InMouseEvent                                           (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// struct FEventReply                      ReturnValue                                            (Parm, OutParm, ReturnParm)

struct FEventReply UWDG_ShowroomTileBase_C::OnMouseButtonDoubleClick(const struct FGeometry& InMyGeometry, const struct FPointerEvent& InMouseEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileBase_C", "OnMouseButtonDoubleClick");

	Params::WDG_ShowroomTileBase_C_OnMouseButtonDoubleClick Parms{};

	Parms.InMyGeometry = std::move(InMyGeometry);
	Parms.InMouseEvent = std::move(InMouseEvent);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.OnMouseButtonDown
// (BlueprintCosmetic, Event, Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FPointerEvent&             MouseEvent                                             (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// struct FEventReply                      ReturnValue                                            (Parm, OutParm, ReturnParm)

struct FEventReply UWDG_ShowroomTileBase_C::OnMouseButtonDown(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileBase_C", "OnMouseButtonDown");

	Params::WDG_ShowroomTileBase_C_OnMouseButtonDown Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.MouseEvent = std::move(MouseEvent);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.SetSelected
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    IsSelected_0                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ShowroomTileBase_C::SetSelected(bool IsSelected_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileBase_C", "SetSelected");

	Params::WDG_ShowroomTileBase_C_SetSelected Parms{};

	Parms.IsSelected_0 = IsSelected_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileBase.WDG_ShowroomTileBase_C.GetNormalColor
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// struct FLinearColor*                    NormalTextColor_0                                      (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomTileBase_C::GetNormalColor(struct FLinearColor* NormalTextColor_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileBase_C", "GetNormalColor");

	Params::WDG_ShowroomTileBase_C_GetNormalColor Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (NormalTextColor_0 != nullptr)
		*NormalTextColor_0 = std::move(Parms.NormalTextColor_0);
}

}

