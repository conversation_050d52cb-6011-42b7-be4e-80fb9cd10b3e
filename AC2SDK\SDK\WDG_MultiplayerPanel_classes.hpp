﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_MultiplayerPanel

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_MultiplayerPanel.WDG_MultiplayerPanel_C
// 0x0068 (0x0650 - 0x05E8)
class UWDG_MultiplayerPanel_C final : public UMultiplayerPanel
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E8(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       OpacityAnim;                                       // 0x05F0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       ScaleAnim;                                         // 0x05F8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UNamedSlot*                             DriverOnlineSlot;                                  // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 HoverImageBox;                                     // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             Multiplayer_lbl;                                   // 0x0610(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 NormalImageBox;                                    // 0x0618(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             OnLineNumSlot;                                     // 0x0620(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             ServersNumSlot;                                    // 0x0628(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             ServersSlot;                                       // 0x0630(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UThrobber*                              Throbber_385;                                      // 0x0638(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtSearching;                                      // 0x0640(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           WhiteText;                                         // 0x0648(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_MultiplayerPanel(int32 EntryPoint);
	void PreConstruct(bool IsDesignTime);
	void BP_MouseOver();
	void BP_MouseLeave();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_MultiplayerPanel_C">();
	}
	static class UWDG_MultiplayerPanel_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_MultiplayerPanel_C>();
	}
};
static_assert(alignof(UWDG_MultiplayerPanel_C) == 0x000008, "Wrong alignment on UWDG_MultiplayerPanel_C");
static_assert(sizeof(UWDG_MultiplayerPanel_C) == 0x000650, "Wrong size on UWDG_MultiplayerPanel_C");
static_assert(offsetof(UWDG_MultiplayerPanel_C, UberGraphFrame) == 0x0005E8, "Member 'UWDG_MultiplayerPanel_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPanel_C, OpacityAnim) == 0x0005F0, "Member 'UWDG_MultiplayerPanel_C::OpacityAnim' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPanel_C, ScaleAnim) == 0x0005F8, "Member 'UWDG_MultiplayerPanel_C::ScaleAnim' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPanel_C, DriverOnlineSlot) == 0x000600, "Member 'UWDG_MultiplayerPanel_C::DriverOnlineSlot' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPanel_C, HoverImageBox) == 0x000608, "Member 'UWDG_MultiplayerPanel_C::HoverImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPanel_C, Multiplayer_lbl) == 0x000610, "Member 'UWDG_MultiplayerPanel_C::Multiplayer_lbl' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPanel_C, NormalImageBox) == 0x000618, "Member 'UWDG_MultiplayerPanel_C::NormalImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPanel_C, OnLineNumSlot) == 0x000620, "Member 'UWDG_MultiplayerPanel_C::OnLineNumSlot' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPanel_C, ServersNumSlot) == 0x000628, "Member 'UWDG_MultiplayerPanel_C::ServersNumSlot' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPanel_C, ServersSlot) == 0x000630, "Member 'UWDG_MultiplayerPanel_C::ServersSlot' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPanel_C, Throbber_385) == 0x000638, "Member 'UWDG_MultiplayerPanel_C::Throbber_385' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPanel_C, txtSearching) == 0x000640, "Member 'UWDG_MultiplayerPanel_C::txtSearching' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPanel_C, WhiteText) == 0x000648, "Member 'UWDG_MultiplayerPanel_C::WhiteText' has a wrong offset!");

}

