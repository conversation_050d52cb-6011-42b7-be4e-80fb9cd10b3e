﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_MPQuickjoinSlotCPInviteSlot

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_MPQuickjoinSlotCPInviteSlot.WDG_MPQuickjoinSlotCPInviteSlot_C
// 0x0268 (0x0848 - 0x05E0)
class UWDG_MPQuickjoinSlotCPInviteSlot_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       PulseOverlay;                                      // 0x05E8(0x0008)(BlueprintVisible, BlueprintReadO<PERSON><PERSON>, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDest<PERSON>ctor, HasGetValueTypeHash)
	class UWidgetAnimation*                       AnimScale;                                         // 0x05F0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       AnimOpacity;                                       // 0x05F8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UBorder*                                borderWaitingIndicator;                            // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 BottomHighlight;                                   // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                contentPanelBorder;                                // 0x0610(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           EventTypeCanvas;                                   // 0x0618(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 HoverImageBox;                                     // 0x0620(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgDivisor;                                        // 0x0628(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgEvent;                                          // 0x0630(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgEventColor;                                     // 0x0638(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgInfoColor;                                      // 0x0640(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 LeftHighlight;                                     // 0x0648(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 NormalImageBox;                                    // 0x0650(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 RightHighlight;                                    // 0x0658(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UThrobber*                              Throbber_385;                                      // 0x0660(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 TopHighlight;                                      // 0x0668(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtAdditionalInfo;                                 // 0x0670(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtCountdown;                                      // 0x0678(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtElement01;                                      // 0x0680(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtElement02;                                      // 0x0688(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtElement03;                                      // 0x0690(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtElement05;                                      // 0x0698(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtElement07;                                      // 0x06A0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtElement08;                                      // 0x06A8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtErrorMessage;                                   // 0x06B0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtEventHeader;                                    // 0x06B8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtFreePracticeLength;                             // 0x06C0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtMedalsOrTRLabel;                                // 0x06C8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtQualifyLength;                                  // 0x06D0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtRaceLength;                                     // 0x06D8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtRegion;                                         // 0x06E0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtRequirementSafetyRating;                        // 0x06E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtRequirementTrackMedals;                         // 0x06F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtSearching;                                      // 0x06F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtServerType;                                     // 0x0700(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtTrackName;                                      // 0x0708(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	struct FSlateColor                            RedRatingColor;                                    // 0x0710(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance)
	struct FSlateColor                            WhiteRatingColor;                                  // 0x0738(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance)
	struct FOnlineServicesCPInvitationState       cpInviteState;                                     // 0x0760(0x0098)(Edit, BlueprintVisible, DisableEditOnInstance)
	class FText                                   bannedXDaysStringFormat;                           // 0x07F8(0x0018)(Edit, BlueprintVisible, DisableEditOnInstance)
	class FText                                   trackRequirementMedalsText;                        // 0x0810(0x0018)(Edit, BlueprintVisible, DisableEditOnInstance)
	class FText                                   trackRequirementRatingText;                        // 0x0828(0x0018)(Edit, BlueprintVisible, DisableEditOnInstance)
	bool                                          IsInErrorState;                                    // 0x0840(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)

public:
	void ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot(int32 EntryPoint);
	void OnSetInviteState();
	void OnManualDataRefresh();
	void BP_MouseLeave();
	void BP_MouseOver();
	void Tick(const struct FGeometry& MyGeometry, float InDeltaTime);
	void Construct();
	void SetInviteState(const struct FOnlineServicesCPInvitationState& inviteState, const class FText& waitingIndicatorText);
	void GetMinutesRemainingText(const struct FDateTime& SessionEndUtc, class FText* SessionEndText);
	void UpdateTimer(bool* TimeUp);
	void SetCountdownOrErrorMessage(bool isError, const class FText& text, const struct FLinearColor& BackgroundColor, const struct FLinearColor& ForegroundColor_0, bool UsePulseAnimation);
	void FormatBanMessage(int32 Days, class FText* text);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_MPQuickjoinSlotCPInviteSlot_C">();
	}
	static class UWDG_MPQuickjoinSlotCPInviteSlot_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_MPQuickjoinSlotCPInviteSlot_C>();
	}
};
static_assert(alignof(UWDG_MPQuickjoinSlotCPInviteSlot_C) == 0x000008, "Wrong alignment on UWDG_MPQuickjoinSlotCPInviteSlot_C");
static_assert(sizeof(UWDG_MPQuickjoinSlotCPInviteSlot_C) == 0x000848, "Wrong size on UWDG_MPQuickjoinSlotCPInviteSlot_C");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, PulseOverlay) == 0x0005E8, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::PulseOverlay' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, AnimScale) == 0x0005F0, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::AnimScale' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, AnimOpacity) == 0x0005F8, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::AnimOpacity' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, borderWaitingIndicator) == 0x000600, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::borderWaitingIndicator' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, BottomHighlight) == 0x000608, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::BottomHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, contentPanelBorder) == 0x000610, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::contentPanelBorder' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, EventTypeCanvas) == 0x000618, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::EventTypeCanvas' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, HoverImageBox) == 0x000620, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::HoverImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, imgDivisor) == 0x000628, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::imgDivisor' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, imgEvent) == 0x000630, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::imgEvent' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, imgEventColor) == 0x000638, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::imgEventColor' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, imgInfoColor) == 0x000640, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::imgInfoColor' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, LeftHighlight) == 0x000648, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::LeftHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, NormalImageBox) == 0x000650, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::NormalImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, RightHighlight) == 0x000658, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::RightHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, Throbber_385) == 0x000660, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::Throbber_385' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, TopHighlight) == 0x000668, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::TopHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, txtAdditionalInfo) == 0x000670, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::txtAdditionalInfo' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, txtCountdown) == 0x000678, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::txtCountdown' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, txtElement01) == 0x000680, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::txtElement01' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, txtElement02) == 0x000688, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::txtElement02' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, txtElement03) == 0x000690, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::txtElement03' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, txtElement05) == 0x000698, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::txtElement05' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, txtElement07) == 0x0006A0, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::txtElement07' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, txtElement08) == 0x0006A8, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::txtElement08' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, txtErrorMessage) == 0x0006B0, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::txtErrorMessage' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, txtEventHeader) == 0x0006B8, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::txtEventHeader' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, txtFreePracticeLength) == 0x0006C0, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::txtFreePracticeLength' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, txtMedalsOrTRLabel) == 0x0006C8, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::txtMedalsOrTRLabel' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, txtQualifyLength) == 0x0006D0, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::txtQualifyLength' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, txtRaceLength) == 0x0006D8, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::txtRaceLength' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, txtRegion) == 0x0006E0, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::txtRegion' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, txtRequirementSafetyRating) == 0x0006E8, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::txtRequirementSafetyRating' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, txtRequirementTrackMedals) == 0x0006F0, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::txtRequirementTrackMedals' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, txtSearching) == 0x0006F8, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::txtSearching' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, txtServerType) == 0x000700, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::txtServerType' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, txtTrackName) == 0x000708, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::txtTrackName' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, RedRatingColor) == 0x000710, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::RedRatingColor' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, WhiteRatingColor) == 0x000738, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::WhiteRatingColor' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, cpInviteState) == 0x000760, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::cpInviteState' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, bannedXDaysStringFormat) == 0x0007F8, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::bannedXDaysStringFormat' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, trackRequirementMedalsText) == 0x000810, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::trackRequirementMedalsText' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, trackRequirementRatingText) == 0x000828, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::trackRequirementRatingText' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlotCPInviteSlot_C, IsInErrorState) == 0x000840, "Member 'UWDG_MPQuickjoinSlotCPInviteSlot_C::IsInErrorState' has a wrong offset!");

}

