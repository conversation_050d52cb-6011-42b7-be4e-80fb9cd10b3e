﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PenaltyIndicator

#include "Basic.hpp"

#include "WDG_PenaltyIndicator_classes.hpp"
#include "WDG_PenaltyIndicator_parameters.hpp"


namespace SDK
{

// Function WDG_PenaltyIndicator.WDG_PenaltyIndicator_C.ExecuteUbergraph_WDG_PenaltyIndicator
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_PenaltyIndicator_C::ExecuteUbergraph_WDG_PenaltyIndicator(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PenaltyIndicator_C", "ExecuteUbergraph_WDG_PenaltyIndicator");

	Params::WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PenaltyIndicator.WDG_PenaltyIndicator_C.Tick
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// float                                   InDeltaTime                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_PenaltyIndicator_C::Tick(const struct FGeometry& MyGeometry, float InDeltaTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PenaltyIndicator_C", "Tick");

	Params::WDG_PenaltyIndicator_C_Tick Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InDeltaTime = InDeltaTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PenaltyIndicator.WDG_PenaltyIndicator_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_PenaltyIndicator_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PenaltyIndicator_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_PenaltyIndicator.WDG_PenaltyIndicator_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_PenaltyIndicator_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PenaltyIndicator_C", "PreConstruct");

	Params::WDG_PenaltyIndicator_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PenaltyIndicator.WDG_PenaltyIndicator_C.SetPenalty
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// EPenaltyType                            PenaltyType_0                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// uint8                                   PenaltyWeight_0                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_PenaltyIndicator_C::SetPenalty(EPenaltyType PenaltyType_0, uint8 PenaltyWeight_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PenaltyIndicator_C", "SetPenalty");

	Params::WDG_PenaltyIndicator_C_SetPenalty Parms{};

	Parms.PenaltyType_0 = PenaltyType_0;
	Parms.PenaltyWeight_0 = PenaltyWeight_0;

	UObject::ProcessEvent(Func, &Parms);
}

}

