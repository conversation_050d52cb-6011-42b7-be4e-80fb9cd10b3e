﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_UI_Rating_Entry

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_UI_Rating_Entry.WDG_UI_Rating_Entry_C
// 0x0010 (0x0608 - 0x05F8)
class UWDG_UI_Rating_Entry_C final : public URatingSelectionPanel
{
public:
	class UImage*                                 background;                                        // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 lockedForeground;                                  // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_UI_Rating_Entry_C">();
	}
	static class UWDG_UI_Rating_Entry_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_UI_Rating_Entry_C>();
	}
};
static_assert(alignof(UWDG_UI_Rating_Entry_C) == 0x000008, "Wrong alignment on UWDG_UI_Rating_Entry_C");
static_assert(sizeof(UWDG_UI_Rating_Entry_C) == 0x000608, "Wrong size on UWDG_UI_Rating_Entry_C");
static_assert(offsetof(UWDG_UI_Rating_Entry_C, background) == 0x0005F8, "Member 'UWDG_UI_Rating_Entry_C::background' has a wrong offset!");
static_assert(offsetof(UWDG_UI_Rating_Entry_C, lockedForeground) == 0x000600, "Member 'UWDG_UI_Rating_Entry_C::lockedForeground' has a wrong offset!");

}

