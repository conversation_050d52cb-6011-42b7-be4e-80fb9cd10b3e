﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SimpleColumnChartColumn

#include "Basic.hpp"

#include "UMG_structs.hpp"
#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_SimpleColumnChartColumn.WDG_SimpleColumnChartColumn_C.ExecuteUbergraph_WDG_SimpleColumnChartColumn
// 0x0014 (0x0014 - 0x0000)
struct WDG_SimpleColumnChartColumn_C_ExecuteUbergraph_WDG_SimpleColumnChartColumn final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FFocusEvent                            K2Node_Event_InFocusEvent_1;                       // 0x0004(0x0008)(NoDestructor)
	struct FFocusEvent                            K2Node_Event_InFocusEvent;                         // 0x000C(0x0008)(NoDestructor)
};
static_assert(alignof(WDG_SimpleColumnChartColumn_C_ExecuteUbergraph_WDG_SimpleColumnChartColumn) == 0x000004, "Wrong alignment on WDG_SimpleColumnChartColumn_C_ExecuteUbergraph_WDG_SimpleColumnChartColumn");
static_assert(sizeof(WDG_SimpleColumnChartColumn_C_ExecuteUbergraph_WDG_SimpleColumnChartColumn) == 0x000014, "Wrong size on WDG_SimpleColumnChartColumn_C_ExecuteUbergraph_WDG_SimpleColumnChartColumn");
static_assert(offsetof(WDG_SimpleColumnChartColumn_C_ExecuteUbergraph_WDG_SimpleColumnChartColumn, EntryPoint) == 0x000000, "Member 'WDG_SimpleColumnChartColumn_C_ExecuteUbergraph_WDG_SimpleColumnChartColumn::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChartColumn_C_ExecuteUbergraph_WDG_SimpleColumnChartColumn, K2Node_Event_InFocusEvent_1) == 0x000004, "Member 'WDG_SimpleColumnChartColumn_C_ExecuteUbergraph_WDG_SimpleColumnChartColumn::K2Node_Event_InFocusEvent_1' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChartColumn_C_ExecuteUbergraph_WDG_SimpleColumnChartColumn, K2Node_Event_InFocusEvent) == 0x00000C, "Member 'WDG_SimpleColumnChartColumn_C_ExecuteUbergraph_WDG_SimpleColumnChartColumn::K2Node_Event_InFocusEvent' has a wrong offset!");

// Function WDG_SimpleColumnChartColumn.WDG_SimpleColumnChartColumn_C.OnRemovedFromFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_SimpleColumnChartColumn_C_OnRemovedFromFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_SimpleColumnChartColumn_C_OnRemovedFromFocusPath) == 0x000004, "Wrong alignment on WDG_SimpleColumnChartColumn_C_OnRemovedFromFocusPath");
static_assert(sizeof(WDG_SimpleColumnChartColumn_C_OnRemovedFromFocusPath) == 0x000008, "Wrong size on WDG_SimpleColumnChartColumn_C_OnRemovedFromFocusPath");
static_assert(offsetof(WDG_SimpleColumnChartColumn_C_OnRemovedFromFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_SimpleColumnChartColumn_C_OnRemovedFromFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_SimpleColumnChartColumn.WDG_SimpleColumnChartColumn_C.OnAddedToFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_SimpleColumnChartColumn_C_OnAddedToFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_SimpleColumnChartColumn_C_OnAddedToFocusPath) == 0x000004, "Wrong alignment on WDG_SimpleColumnChartColumn_C_OnAddedToFocusPath");
static_assert(sizeof(WDG_SimpleColumnChartColumn_C_OnAddedToFocusPath) == 0x000008, "Wrong size on WDG_SimpleColumnChartColumn_C_OnAddedToFocusPath");
static_assert(offsetof(WDG_SimpleColumnChartColumn_C_OnAddedToFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_SimpleColumnChartColumn_C_OnAddedToFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_SimpleColumnChartColumn.WDG_SimpleColumnChartColumn_C.SetValue
// 0x0010 (0x0010 - 0x0000)
struct WDG_SimpleColumnChartColumn_C_SetValue final
{
public:
	float                                         barValue;                                          // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         barId;                                             // 0x0004(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          isBarNavigable;                                    // 0x0008(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_9[0x3];                                        // 0x0009(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Subtract_FloatFloat_ReturnValue;          // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SimpleColumnChartColumn_C_SetValue) == 0x000004, "Wrong alignment on WDG_SimpleColumnChartColumn_C_SetValue");
static_assert(sizeof(WDG_SimpleColumnChartColumn_C_SetValue) == 0x000010, "Wrong size on WDG_SimpleColumnChartColumn_C_SetValue");
static_assert(offsetof(WDG_SimpleColumnChartColumn_C_SetValue, barValue) == 0x000000, "Member 'WDG_SimpleColumnChartColumn_C_SetValue::barValue' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChartColumn_C_SetValue, barId) == 0x000004, "Member 'WDG_SimpleColumnChartColumn_C_SetValue::barId' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChartColumn_C_SetValue, isBarNavigable) == 0x000008, "Member 'WDG_SimpleColumnChartColumn_C_SetValue::isBarNavigable' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChartColumn_C_SetValue, CallFunc_Subtract_FloatFloat_ReturnValue) == 0x00000C, "Member 'WDG_SimpleColumnChartColumn_C_SetValue::CallFunc_Subtract_FloatFloat_ReturnValue' has a wrong offset!");

// Function WDG_SimpleColumnChartColumn.WDG_SimpleColumnChartColumn_C.SetBarHighlighted
// 0x0002 (0x0002 - 0x0000)
struct WDG_SimpleColumnChartColumn_C_SetBarHighlighted final
{
public:
	bool                                          isFocused;                                         // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	ESlateVisibility                              ShowDampeningLayer;                                // 0x0001(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SimpleColumnChartColumn_C_SetBarHighlighted) == 0x000001, "Wrong alignment on WDG_SimpleColumnChartColumn_C_SetBarHighlighted");
static_assert(sizeof(WDG_SimpleColumnChartColumn_C_SetBarHighlighted) == 0x000002, "Wrong size on WDG_SimpleColumnChartColumn_C_SetBarHighlighted");
static_assert(offsetof(WDG_SimpleColumnChartColumn_C_SetBarHighlighted, isFocused) == 0x000000, "Member 'WDG_SimpleColumnChartColumn_C_SetBarHighlighted::isFocused' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChartColumn_C_SetBarHighlighted, ShowDampeningLayer) == 0x000001, "Member 'WDG_SimpleColumnChartColumn_C_SetBarHighlighted::ShowDampeningLayer' has a wrong offset!");

}

