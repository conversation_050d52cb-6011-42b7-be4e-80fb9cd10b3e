﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RatingDetailMessageItem

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RatingDetailMessageItem.WDG_RatingDetailMessageItem_C
// 0x0018 (0x0280 - 0x0268)
class UWDG_RatingDetailMessageItem_C final : public URatingDetailMessageItem
{
public:
	class UImage*                                 Image_0;                                           // 0x0268(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_1;                                           // 0x0270(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoD<PERSON>ru<PERSON>, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgSignal;                                         // 0x0278(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RatingDetailMessageItem_C">();
	}
	static class UWDG_RatingDetailMessageItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RatingDetailMessageItem_C>();
	}
};
static_assert(alignof(UWDG_RatingDetailMessageItem_C) == 0x000008, "Wrong alignment on UWDG_RatingDetailMessageItem_C");
static_assert(sizeof(UWDG_RatingDetailMessageItem_C) == 0x000280, "Wrong size on UWDG_RatingDetailMessageItem_C");
static_assert(offsetof(UWDG_RatingDetailMessageItem_C, Image_0) == 0x000268, "Member 'UWDG_RatingDetailMessageItem_C::Image_0' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailMessageItem_C, Image_1) == 0x000270, "Member 'UWDG_RatingDetailMessageItem_C::Image_1' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailMessageItem_C, imgSignal) == 0x000278, "Member 'UWDG_RatingDetailMessageItem_C::imgSignal' has a wrong offset!");

}

