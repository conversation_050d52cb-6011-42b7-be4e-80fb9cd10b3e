﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SpecialEvents

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SpecialEvents.WDG_SpecialEvents_C
// 0x00E8 (0x0E98 - 0x0DB0)
class UWDG_SpecialEvents_C final : public USpecialEvents
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0DB0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       FadeElements;                                      // 0x0DB8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       UnavailablePulse;                                  // 0x0DC0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       LoadingPulse;                                      // 0x0DC8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWDG_FooterButton_C*                    Back;                                              // 0x0DD0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnOldEvents;                                      // 0x0DD8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnRefresh;                                        // 0x0DE0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_StartSessionPanel_C*               btnStart;                                          // 0x0DE8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           canvasListWrapper;                                 // 0x0DF0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SpecialEventItem_C*                currentItem;                                       // 0x0DF8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      CurrentModel;                                      // 0x0E00(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      CurrentTeam;                                       // 0x0E08(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  dummyRouter;                                       // 0x0E10(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      Entry;                                             // 0x0E18(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Footer_C*                          Footer;                                            // 0x0E20(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgLineAbove;                                      // 0x0E28(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SpecialEventList_C*                listSpecialEvents;                                 // 0x0E30(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_DLCWarningPopup_C*                 popupDLC;                                          // 0x0E38(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UScrollBox*                             scrollLeaderboard;                                 // 0x0E40(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class USizeBox*                               sizeListWrapper;                                   // 0x0E48(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWidgetSwitcher*                        switcherDetails;                                   // 0x0E50(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtLoading;                                        // 0x0E58(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtLoadingLeaderboard;                             // 0x0E60(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtNoEvents;                                       // 0x0E68(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           wrapperDetails;                                    // 0x0E70(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           wrapperHelp;                                       // 0x0E78(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	TMulticastInlineDelegate<void()>              OnListPopulated;                                   // 0x0E80(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	bool                                          ViewingPast;                                       // 0x0E90(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	bool                                          IsFadedOut;                                        // 0x0E91(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	uint8                                         Pad_E92[0x2];                                      // 0x0E92(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Lastrank;                                          // 0x0E94(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_SpecialEvents(int32 EntryPoint);
	void BndEvt__popupDLC_K2Node_ComponentBoundEvent_7_OnYes__DelegateSignature();
	void BndEvt__btnOldEvents_K2Node_ComponentBoundEvent_6_OnAcPanelFocusEvent__DelegateSignature(class UAcPanelBase* panel);
	void BndEvt__btnRefresh_K2Node_ComponentBoundEvent_5_OnAcPanelFocusEvent__DelegateSignature(class UAcPanelBase* panel);
	void BndEvt__CurrentModel_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_3_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__btdOldEvents_K2Node_ComponentBoundEvent_2_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__StartSessionPanel_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature();
	void OnPageReady();
	void OnEventSelected(const struct FSpecialEventPreset& Preset, const struct FModelInfo& model_info, const struct FCarInfo& car_info, const struct FTeamInfo& team_info, const struct FCircuitInfo& circuit_info);
	void BndEvt__btnConfirm_K2Node_ComponentBoundEvent_1_OnAcPanelForwardEvent__DelegateSignature();
	void BP_StartPage();
	void OnSpecialEventListPopulated(int32 Count, class UWDG_SpecialEventItem_C* PreviouslySelected);
	void OnSpecialEventItemAdded(class UWDG_SpecialEventItem_C* Item);
	void BndEvt__WDG_GenericBarItem_C_0_K2Node_ComponentBoundEvent_2_OnAcPanelFocusEvent__DelegateSignature(class UAcPanelBase* panel);
	void BndEvt__listSpecialEvents_K2Node_ComponentBoundEvent_1_AddedToFocusPath__DelegateSignature();
	void BP_OnBackward();
	void BndEvt__Back_K2Node_ComponentBoundEvent_0_OnClicked__DelegateSignature();
	void OnLeaderboardRequested(int32 ref_id);
	void OnLeaderboardReceived(const TArray<struct FOnlineServicesHotlapEntry>& entries, const TArray<class FText>& deltas);
	void Populate(TArray<struct FSpecialEventPreset>& SpecialEvents, bool PastEvents);
	void FocusToItem(class UAcPanelBase* Target);
	void GetPresets(bool Debug, bool PastEvents, TArray<struct FSpecialEventPreset>* SpecialEvents);
	void ItemSelected(class UWDG_SpecialEventItem_C* Item, bool ByMouse);
	void SetEventsAvailable(bool IsAvailable);
	void SelectAndFocusListToItem(class UWDG_SpecialEventItem_C* Item);
	void GetSelectedItem(class UWDG_SpecialEventItem_C** SelectedItem);
	class UWidget* DownToSelectedItem(EUINavigation Navigation_0);
	struct FEventReply OnPreviewKeyDown(const struct FGeometry& MyGeometry, const struct FKeyEvent& InKeyEvent);
	void UIVisible();
	void UIHidden();
	class UWidget* LeftToCurrentTeam(EUINavigation Navigation_0);
	void UnfadeUI();
	void SequenceEvent__ENTRYPOINTWDG_SpecialEvents_0();
	void SequenceEvent__ENTRYPOINTWDG_SpecialEvents_1();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SpecialEvents_C">();
	}
	static class UWDG_SpecialEvents_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SpecialEvents_C>();
	}
};
static_assert(alignof(UWDG_SpecialEvents_C) == 0x000008, "Wrong alignment on UWDG_SpecialEvents_C");
static_assert(sizeof(UWDG_SpecialEvents_C) == 0x000E98, "Wrong size on UWDG_SpecialEvents_C");
static_assert(offsetof(UWDG_SpecialEvents_C, UberGraphFrame) == 0x000DB0, "Member 'UWDG_SpecialEvents_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, FadeElements) == 0x000DB8, "Member 'UWDG_SpecialEvents_C::FadeElements' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, UnavailablePulse) == 0x000DC0, "Member 'UWDG_SpecialEvents_C::UnavailablePulse' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, LoadingPulse) == 0x000DC8, "Member 'UWDG_SpecialEvents_C::LoadingPulse' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, Back) == 0x000DD0, "Member 'UWDG_SpecialEvents_C::Back' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, btnOldEvents) == 0x000DD8, "Member 'UWDG_SpecialEvents_C::btnOldEvents' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, btnRefresh) == 0x000DE0, "Member 'UWDG_SpecialEvents_C::btnRefresh' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, btnStart) == 0x000DE8, "Member 'UWDG_SpecialEvents_C::btnStart' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, canvasListWrapper) == 0x000DF0, "Member 'UWDG_SpecialEvents_C::canvasListWrapper' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, currentItem) == 0x000DF8, "Member 'UWDG_SpecialEvents_C::currentItem' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, CurrentModel) == 0x000E00, "Member 'UWDG_SpecialEvents_C::CurrentModel' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, CurrentTeam) == 0x000E08, "Member 'UWDG_SpecialEvents_C::CurrentTeam' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, dummyRouter) == 0x000E10, "Member 'UWDG_SpecialEvents_C::dummyRouter' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, Entry) == 0x000E18, "Member 'UWDG_SpecialEvents_C::Entry' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, Footer) == 0x000E20, "Member 'UWDG_SpecialEvents_C::Footer' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, imgLineAbove) == 0x000E28, "Member 'UWDG_SpecialEvents_C::imgLineAbove' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, listSpecialEvents) == 0x000E30, "Member 'UWDG_SpecialEvents_C::listSpecialEvents' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, popupDLC) == 0x000E38, "Member 'UWDG_SpecialEvents_C::popupDLC' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, scrollLeaderboard) == 0x000E40, "Member 'UWDG_SpecialEvents_C::scrollLeaderboard' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, sizeListWrapper) == 0x000E48, "Member 'UWDG_SpecialEvents_C::sizeListWrapper' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, switcherDetails) == 0x000E50, "Member 'UWDG_SpecialEvents_C::switcherDetails' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, txtLoading) == 0x000E58, "Member 'UWDG_SpecialEvents_C::txtLoading' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, txtLoadingLeaderboard) == 0x000E60, "Member 'UWDG_SpecialEvents_C::txtLoadingLeaderboard' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, txtNoEvents) == 0x000E68, "Member 'UWDG_SpecialEvents_C::txtNoEvents' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, wrapperDetails) == 0x000E70, "Member 'UWDG_SpecialEvents_C::wrapperDetails' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, wrapperHelp) == 0x000E78, "Member 'UWDG_SpecialEvents_C::wrapperHelp' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, OnListPopulated) == 0x000E80, "Member 'UWDG_SpecialEvents_C::OnListPopulated' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, ViewingPast) == 0x000E90, "Member 'UWDG_SpecialEvents_C::ViewingPast' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, IsFadedOut) == 0x000E91, "Member 'UWDG_SpecialEvents_C::IsFadedOut' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEvents_C, Lastrank) == 0x000E94, "Member 'UWDG_SpecialEvents_C::Lastrank' has a wrong offset!");

}

