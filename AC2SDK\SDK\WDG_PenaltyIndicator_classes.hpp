﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PenaltyIndicator

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_PenaltyIndicator.WDG_PenaltyIndicator_C
// 0x0080 (0x02E0 - 0x0260)
class UWDG_PenaltyIndicator_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0260(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UBorder*                                brdBackground;                                     // 0x0268(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           cnvWrapper;                                        // 0x0270(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtLabel;                                          // 0x0278(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	EPenaltyType                                  PenaltyType;                                       // 0x0280(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	uint8                                         PenaltyWeight;                                     // 0x0281(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	uint8                                         Pad_282[0x6];                                      // 0x0282(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	TMap<EPenaltyType, class FText>               MapPenaltyTexts;                                   // 0x0288(0x0050)(Edit, BlueprintVisible, DisableEditOnInstance)
	class AAcRaceGameMode*                        raceGameMode;                                      // 0x02D8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_PenaltyIndicator(int32 EntryPoint);
	void Tick(const struct FGeometry& MyGeometry, float InDeltaTime);
	void Construct();
	void PreConstruct(bool IsDesignTime);
	void SetPenalty(EPenaltyType PenaltyType_0, uint8 PenaltyWeight_0);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_PenaltyIndicator_C">();
	}
	static class UWDG_PenaltyIndicator_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_PenaltyIndicator_C>();
	}
};
static_assert(alignof(UWDG_PenaltyIndicator_C) == 0x000008, "Wrong alignment on UWDG_PenaltyIndicator_C");
static_assert(sizeof(UWDG_PenaltyIndicator_C) == 0x0002E0, "Wrong size on UWDG_PenaltyIndicator_C");
static_assert(offsetof(UWDG_PenaltyIndicator_C, UberGraphFrame) == 0x000260, "Member 'UWDG_PenaltyIndicator_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_PenaltyIndicator_C, brdBackground) == 0x000268, "Member 'UWDG_PenaltyIndicator_C::brdBackground' has a wrong offset!");
static_assert(offsetof(UWDG_PenaltyIndicator_C, cnvWrapper) == 0x000270, "Member 'UWDG_PenaltyIndicator_C::cnvWrapper' has a wrong offset!");
static_assert(offsetof(UWDG_PenaltyIndicator_C, txtLabel) == 0x000278, "Member 'UWDG_PenaltyIndicator_C::txtLabel' has a wrong offset!");
static_assert(offsetof(UWDG_PenaltyIndicator_C, PenaltyType) == 0x000280, "Member 'UWDG_PenaltyIndicator_C::PenaltyType' has a wrong offset!");
static_assert(offsetof(UWDG_PenaltyIndicator_C, PenaltyWeight) == 0x000281, "Member 'UWDG_PenaltyIndicator_C::PenaltyWeight' has a wrong offset!");
static_assert(offsetof(UWDG_PenaltyIndicator_C, MapPenaltyTexts) == 0x000288, "Member 'UWDG_PenaltyIndicator_C::MapPenaltyTexts' has a wrong offset!");
static_assert(offsetof(UWDG_PenaltyIndicator_C, raceGameMode) == 0x0002D8, "Member 'UWDG_PenaltyIndicator_C::raceGameMode' has a wrong offset!");

}

