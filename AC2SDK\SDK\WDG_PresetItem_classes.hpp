﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PresetItem

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_PresetItem.WDG_PresetItem_C
// 0x0120 (0x0700 - 0x05E0)
class UWDG_PresetItem_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UBorder*                                background;                                        // 0x05E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnDel;                                            // 0x05F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnLoad;                                           // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         controls;                                          // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtDate;                                           // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtMain;                                           // 0x0610(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtSecondary;                                      // 0x0618(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWidget*                                FocusedButton;                                     // 0x0620(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TMulticastInlineDelegate<void(class FName Name, int32 uniqueDeviceIndex, class FName ProductId)> OnLoad; // 0x0628(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void(class UWDG_PresetItem_C* Sender, const class FString& Filename)> OnDelete; // 0x0638(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	class AAcMenuGameMode*                        GameMode;                                          // 0x0648(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   LabelText;                                         // 0x0650(0x0018)(Edit, BlueprintVisible, ExposeOnSpawn)
	bool                                          AllowDelete;                                       // 0x0668(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn)
	uint8                                         Pad_669[0x3];                                      // 0x0669(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           NormalColor;                                       // 0x066C(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           NonDeletableColor;                                 // 0x067C(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	int32                                         uniqueDeviceIndex;                                 // 0x068C(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	class FName                                   Name_0;                                            // 0x0690(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	class FName                                   ProductId;                                         // 0x0698(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	class FText                                   DateText;                                          // 0x06A0(0x0018)(Edit, BlueprintVisible, ExposeOnSpawn)
	class FText                                   SecondaryText;                                     // 0x06B8(0x0018)(Edit, BlueprintVisible, ExposeOnSpawn)
	TMulticastInlineDelegate<void(class UWDG_PresetItem_C* Sender, class FName Filename)> OnLoadGeneric; // 0x06D0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void(class FName Filename)> OnDeleteGeneric;                            // 0x06E0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void(bool highlighted)> OnHighlightChanged;                             // 0x06F0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)

public:
	void ExecuteUbergraph_WDG_PresetItem(int32 EntryPoint);
	void BndEvt__btnDel_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__btnPlay_K2Node_ComponentBoundEvent_5_OnAcPanelForwardEvent__DelegateSignature();
	void BP_SetHighlight(bool highlighted);
	void BP_MouseOver();
	void BP_MouseLeave();
	void OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent);
	void OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent);
	void OnMouseFocus_Event_0(class UAcPanelBase* panel, bool mouse_over);
	void OnInitialized();
	void BndEvt__btnDel_K2Node_ComponentBoundEvent_1_OnAcPanelMouseEvent__DelegateSignature(class UAcPanelBase* panel, bool mouse_over);
	void PreConstruct(bool IsDesignTime);
	void Construct();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_PresetItem_C">();
	}
	static class UWDG_PresetItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_PresetItem_C>();
	}
};
static_assert(alignof(UWDG_PresetItem_C) == 0x000008, "Wrong alignment on UWDG_PresetItem_C");
static_assert(sizeof(UWDG_PresetItem_C) == 0x000700, "Wrong size on UWDG_PresetItem_C");
static_assert(offsetof(UWDG_PresetItem_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_PresetItem_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_PresetItem_C, background) == 0x0005E8, "Member 'UWDG_PresetItem_C::background' has a wrong offset!");
static_assert(offsetof(UWDG_PresetItem_C, btnDel) == 0x0005F0, "Member 'UWDG_PresetItem_C::btnDel' has a wrong offset!");
static_assert(offsetof(UWDG_PresetItem_C, btnLoad) == 0x0005F8, "Member 'UWDG_PresetItem_C::btnLoad' has a wrong offset!");
static_assert(offsetof(UWDG_PresetItem_C, controls) == 0x000600, "Member 'UWDG_PresetItem_C::controls' has a wrong offset!");
static_assert(offsetof(UWDG_PresetItem_C, txtDate) == 0x000608, "Member 'UWDG_PresetItem_C::txtDate' has a wrong offset!");
static_assert(offsetof(UWDG_PresetItem_C, txtMain) == 0x000610, "Member 'UWDG_PresetItem_C::txtMain' has a wrong offset!");
static_assert(offsetof(UWDG_PresetItem_C, txtSecondary) == 0x000618, "Member 'UWDG_PresetItem_C::txtSecondary' has a wrong offset!");
static_assert(offsetof(UWDG_PresetItem_C, FocusedButton) == 0x000620, "Member 'UWDG_PresetItem_C::FocusedButton' has a wrong offset!");
static_assert(offsetof(UWDG_PresetItem_C, OnLoad) == 0x000628, "Member 'UWDG_PresetItem_C::OnLoad' has a wrong offset!");
static_assert(offsetof(UWDG_PresetItem_C, OnDelete) == 0x000638, "Member 'UWDG_PresetItem_C::OnDelete' has a wrong offset!");
static_assert(offsetof(UWDG_PresetItem_C, GameMode) == 0x000648, "Member 'UWDG_PresetItem_C::GameMode' has a wrong offset!");
static_assert(offsetof(UWDG_PresetItem_C, LabelText) == 0x000650, "Member 'UWDG_PresetItem_C::LabelText' has a wrong offset!");
static_assert(offsetof(UWDG_PresetItem_C, AllowDelete) == 0x000668, "Member 'UWDG_PresetItem_C::AllowDelete' has a wrong offset!");
static_assert(offsetof(UWDG_PresetItem_C, NormalColor) == 0x00066C, "Member 'UWDG_PresetItem_C::NormalColor' has a wrong offset!");
static_assert(offsetof(UWDG_PresetItem_C, NonDeletableColor) == 0x00067C, "Member 'UWDG_PresetItem_C::NonDeletableColor' has a wrong offset!");
static_assert(offsetof(UWDG_PresetItem_C, uniqueDeviceIndex) == 0x00068C, "Member 'UWDG_PresetItem_C::uniqueDeviceIndex' has a wrong offset!");
static_assert(offsetof(UWDG_PresetItem_C, Name_0) == 0x000690, "Member 'UWDG_PresetItem_C::Name_0' has a wrong offset!");
static_assert(offsetof(UWDG_PresetItem_C, ProductId) == 0x000698, "Member 'UWDG_PresetItem_C::ProductId' has a wrong offset!");
static_assert(offsetof(UWDG_PresetItem_C, DateText) == 0x0006A0, "Member 'UWDG_PresetItem_C::DateText' has a wrong offset!");
static_assert(offsetof(UWDG_PresetItem_C, SecondaryText) == 0x0006B8, "Member 'UWDG_PresetItem_C::SecondaryText' has a wrong offset!");
static_assert(offsetof(UWDG_PresetItem_C, OnLoadGeneric) == 0x0006D0, "Member 'UWDG_PresetItem_C::OnLoadGeneric' has a wrong offset!");
static_assert(offsetof(UWDG_PresetItem_C, OnDeleteGeneric) == 0x0006E0, "Member 'UWDG_PresetItem_C::OnDeleteGeneric' has a wrong offset!");
static_assert(offsetof(UWDG_PresetItem_C, OnHighlightChanged) == 0x0006F0, "Member 'UWDG_PresetItem_C::OnHighlightChanged' has a wrong offset!");

}

