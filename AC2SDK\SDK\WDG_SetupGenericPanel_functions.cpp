﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SetupGenericPanel

#include "Basic.hpp"

#include "WDG_SetupGenericPanel_classes.hpp"
#include "WDG_SetupGenericPanel_parameters.hpp"


namespace SDK
{

// Function WDG_SetupGenericPanel.WDG_SetupGenericPanel_C.ExecuteUbergraph_WDG_SetupGenericPanel
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SetupGenericPanel_C::ExecuteUbergraph_WDG_SetupGenericPanel(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SetupGenericPanel_C", "ExecuteUbergraph_WDG_SetupGenericPanel");

	Params::WDG_SetupGenericPanel_C_ExecuteUbergraph_WDG_SetupGenericPanel Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SetupGenericPanel.WDG_SetupGenericPanel_C.BP_MouseLeave
// (Event, Public, BlueprintEvent)

void UWDG_SetupGenericPanel_C::BP_MouseLeave()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SetupGenericPanel_C", "BP_MouseLeave");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SetupGenericPanel.WDG_SetupGenericPanel_C.BP_MouseOver
// (Event, Public, BlueprintEvent)

void UWDG_SetupGenericPanel_C::BP_MouseOver()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SetupGenericPanel_C", "BP_MouseOver");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SetupGenericPanel.WDG_SetupGenericPanel_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_SetupGenericPanel_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SetupGenericPanel_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}

}

