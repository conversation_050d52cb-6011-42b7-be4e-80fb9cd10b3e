﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RollingStart

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RollingStart.WDG_RollingStart_C
// 0x0298 (0x08F0 - 0x0658)
class UWDG_RollingStart_C final : public UAcRaceWidgetBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0658(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UImage*                                 background;                                        // 0x0660(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 BigGoLeft;                                         // 0x0668(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 BigGoRight;                                        // 0x0670(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                Border_0;                                          // 0x0678(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                Border_2;                                          // 0x0680(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                Border_5;                                          // 0x0688(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 car_acc_l;                                         // 0x0690(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 car_acc_mid;                                       // 0x0698(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 car_acc_r;                                         // 0x06A0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 car_dec_l;                                         // 0x06A8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 car_dec_mid;                                       // 0x06B0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 car_dec_r;                                         // 0x06B8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 car_l;                                             // 0x06C0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 car_mid;                                           // 0x06C8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 car_r;                                             // 0x06D0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           cars;                                              // 0x06D8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 colors;                                            // 0x06E0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 GoLeft;                                            // 0x06E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 GoRight;                                           // 0x06F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_437;                                         // 0x06F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_439;                                         // 0x0700(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             limit1;                                            // 0x0708(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             limit2;                                            // 0x0710(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                Message;                                           // 0x0718(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                Radar;                                             // 0x0720(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             SingleLine;                                        // 0x0728(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             SlowDownAlert;                                     // 0x0730(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                SpeedBar;                                          // 0x0738(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                SpeedFormationWarning;                             // 0x0740(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             SpeedValue;                                        // 0x0748(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             StayLeft;                                          // 0x0750(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             StayRight;                                         // 0x0758(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_7;                                       // 0x0760(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                Warning;                                           // 0x0768(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             WarningCounter;                                    // 0x0770(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class AAcRaceGameMode*                        raceGameMode;                                      // 0x0778(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FRollingStartMessage                   MessageStruct;                                     // 0x0780(0x0130)(Edit, BlueprintVisible, DisableEditOnInstance)
	struct FLinearColor                           red;                                               // 0x08B0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Yellow;                                            // 0x08C0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           green;                                             // 0x08D0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         blink_time;                                        // 0x08E0(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          areCarsVisible;                                    // 0x08E4(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	ERaceSessionPhase                             phase;                                             // 0x08E5(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_8E6[0x2];                                      // 0x08E6(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         convertedSpeed;                                    // 0x08E8(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_RollingStart(int32 EntryPoint);
	void Destruct();
	void OnHudTick(const struct FRaceHUDState& State);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RollingStart_C">();
	}
	static class UWDG_RollingStart_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RollingStart_C>();
	}
};
static_assert(alignof(UWDG_RollingStart_C) == 0x000008, "Wrong alignment on UWDG_RollingStart_C");
static_assert(sizeof(UWDG_RollingStart_C) == 0x0008F0, "Wrong size on UWDG_RollingStart_C");
static_assert(offsetof(UWDG_RollingStart_C, UberGraphFrame) == 0x000658, "Member 'UWDG_RollingStart_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, background) == 0x000660, "Member 'UWDG_RollingStart_C::background' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, BigGoLeft) == 0x000668, "Member 'UWDG_RollingStart_C::BigGoLeft' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, BigGoRight) == 0x000670, "Member 'UWDG_RollingStart_C::BigGoRight' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, Border_0) == 0x000678, "Member 'UWDG_RollingStart_C::Border_0' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, Border_2) == 0x000680, "Member 'UWDG_RollingStart_C::Border_2' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, Border_5) == 0x000688, "Member 'UWDG_RollingStart_C::Border_5' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, car_acc_l) == 0x000690, "Member 'UWDG_RollingStart_C::car_acc_l' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, car_acc_mid) == 0x000698, "Member 'UWDG_RollingStart_C::car_acc_mid' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, car_acc_r) == 0x0006A0, "Member 'UWDG_RollingStart_C::car_acc_r' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, car_dec_l) == 0x0006A8, "Member 'UWDG_RollingStart_C::car_dec_l' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, car_dec_mid) == 0x0006B0, "Member 'UWDG_RollingStart_C::car_dec_mid' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, car_dec_r) == 0x0006B8, "Member 'UWDG_RollingStart_C::car_dec_r' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, car_l) == 0x0006C0, "Member 'UWDG_RollingStart_C::car_l' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, car_mid) == 0x0006C8, "Member 'UWDG_RollingStart_C::car_mid' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, car_r) == 0x0006D0, "Member 'UWDG_RollingStart_C::car_r' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, cars) == 0x0006D8, "Member 'UWDG_RollingStart_C::cars' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, colors) == 0x0006E0, "Member 'UWDG_RollingStart_C::colors' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, GoLeft) == 0x0006E8, "Member 'UWDG_RollingStart_C::GoLeft' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, GoRight) == 0x0006F0, "Member 'UWDG_RollingStart_C::GoRight' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, Image_437) == 0x0006F8, "Member 'UWDG_RollingStart_C::Image_437' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, Image_439) == 0x000700, "Member 'UWDG_RollingStart_C::Image_439' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, limit1) == 0x000708, "Member 'UWDG_RollingStart_C::limit1' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, limit2) == 0x000710, "Member 'UWDG_RollingStart_C::limit2' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, Message) == 0x000718, "Member 'UWDG_RollingStart_C::Message' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, Radar) == 0x000720, "Member 'UWDG_RollingStart_C::Radar' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, SingleLine) == 0x000728, "Member 'UWDG_RollingStart_C::SingleLine' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, SlowDownAlert) == 0x000730, "Member 'UWDG_RollingStart_C::SlowDownAlert' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, SpeedBar) == 0x000738, "Member 'UWDG_RollingStart_C::SpeedBar' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, SpeedFormationWarning) == 0x000740, "Member 'UWDG_RollingStart_C::SpeedFormationWarning' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, SpeedValue) == 0x000748, "Member 'UWDG_RollingStart_C::SpeedValue' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, StayLeft) == 0x000750, "Member 'UWDG_RollingStart_C::StayLeft' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, StayRight) == 0x000758, "Member 'UWDG_RollingStart_C::StayRight' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, TextBlock_7) == 0x000760, "Member 'UWDG_RollingStart_C::TextBlock_7' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, Warning) == 0x000768, "Member 'UWDG_RollingStart_C::Warning' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, WarningCounter) == 0x000770, "Member 'UWDG_RollingStart_C::WarningCounter' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, raceGameMode) == 0x000778, "Member 'UWDG_RollingStart_C::raceGameMode' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, MessageStruct) == 0x000780, "Member 'UWDG_RollingStart_C::MessageStruct' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, red) == 0x0008B0, "Member 'UWDG_RollingStart_C::red' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, Yellow) == 0x0008C0, "Member 'UWDG_RollingStart_C::Yellow' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, green) == 0x0008D0, "Member 'UWDG_RollingStart_C::green' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, blink_time) == 0x0008E0, "Member 'UWDG_RollingStart_C::blink_time' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, areCarsVisible) == 0x0008E4, "Member 'UWDG_RollingStart_C::areCarsVisible' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, phase) == 0x0008E5, "Member 'UWDG_RollingStart_C::phase' has a wrong offset!");
static_assert(offsetof(UWDG_RollingStart_C, convertedSpeed) == 0x0008E8, "Member 'UWDG_RollingStart_C::convertedSpeed' has a wrong offset!");

}

