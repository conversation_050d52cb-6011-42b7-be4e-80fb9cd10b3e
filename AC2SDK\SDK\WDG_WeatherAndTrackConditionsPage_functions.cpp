﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_WeatherAndTrackConditionsPage

#include "Basic.hpp"

#include "WDG_WeatherAndTrackConditionsPage_classes.hpp"
#include "WDG_WeatherAndTrackConditionsPage_parameters.hpp"


namespace SDK
{

// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_WeatherAndTrackConditionsPage_C::ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherAndTrackConditionsPage_C", "ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage");

	Params::WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.Variability Changed
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UGenericSelectorItem*             Source                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_index                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_value                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_WeatherAndTrackConditionsPage_C::Variability_Changed(class UGenericSelectorItem* Source, int32 current_index, int32 current_value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherAndTrackConditionsPage_C", "Variability Changed");

	Params::WDG_WeatherAndTrackConditionsPage_C_Variability_Changed Parms{};

	Parms.Source = Source;
	Parms.current_index = current_index;
	Parms.current_value = current_value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.Dynamic Weather Changed
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UGenericSelectorItem*             Source                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_index                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_value                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_WeatherAndTrackConditionsPage_C::Dynamic_Weather_Changed(class UGenericSelectorItem* Source, int32 current_index, int32 current_value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherAndTrackConditionsPage_C", "Dynamic Weather Changed");

	Params::WDG_WeatherAndTrackConditionsPage_C_Dynamic_Weather_Changed Parms{};

	Parms.Source = Source;
	Parms.current_index = current_index;
	Parms.current_value = current_value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.Ambient Temperature Changed
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UGenericSelectorItem*             Source                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_index                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_value                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_WeatherAndTrackConditionsPage_C::Ambient_Temperature_Changed(class UGenericSelectorItem* Source, int32 current_index, int32 current_value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherAndTrackConditionsPage_C", "Ambient Temperature Changed");

	Params::WDG_WeatherAndTrackConditionsPage_C_Ambient_Temperature_Changed Parms{};

	Parms.Source = Source;
	Parms.current_index = current_index;
	Parms.current_value = current_value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.CustomEvent_0
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UGenericSelectorItem*             Source                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_index                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_value                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_WeatherAndTrackConditionsPage_C::CustomEvent_0(class UGenericSelectorItem* Source, int32 current_index, int32 current_value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherAndTrackConditionsPage_C", "CustomEvent_0");

	Params::WDG_WeatherAndTrackConditionsPage_C_CustomEvent_0 Parms{};

	Parms.Source = Source;
	Parms.current_index = current_index;
	Parms.current_value = current_value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.Weather Variability Changed
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UGenericSelectorItem*             Source                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_index                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_value                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_WeatherAndTrackConditionsPage_C::Weather_Variability_Changed(class UGenericSelectorItem* Source, int32 current_index, int32 current_value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherAndTrackConditionsPage_C", "Weather Variability Changed");

	Params::WDG_WeatherAndTrackConditionsPage_C_Weather_Variability_Changed Parms{};

	Parms.Source = Source;
	Parms.current_index = current_index;
	Parms.current_value = current_value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.BndEvt__sliderStandingWater_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UGenericSelectorItem*             Source                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_index                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_value                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_WeatherAndTrackConditionsPage_C::BndEvt__sliderStandingWater_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature(class UGenericSelectorItem* Source, int32 current_index, int32 current_value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherAndTrackConditionsPage_C", "BndEvt__sliderStandingWater_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature");

	Params::WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderStandingWater_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature Parms{};

	Parms.Source = Source;
	Parms.current_index = current_index;
	Parms.current_value = current_value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.BndEvt__sliderGripLevel_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UGenericSelectorItem*             Source                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_index                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_value                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_WeatherAndTrackConditionsPage_C::BndEvt__sliderGripLevel_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature(class UGenericSelectorItem* Source, int32 current_index, int32 current_value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherAndTrackConditionsPage_C", "BndEvt__sliderGripLevel_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature");

	Params::WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderGripLevel_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature Parms{};

	Parms.Source = Source;
	Parms.current_index = current_index;
	Parms.current_value = current_value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.BndEvt__sliderSurface_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UGenericSelectorItem*             Source                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_index                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_value                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_WeatherAndTrackConditionsPage_C::BndEvt__sliderSurface_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature(class UGenericSelectorItem* Source, int32 current_index, int32 current_value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherAndTrackConditionsPage_C", "BndEvt__sliderSurface_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature");

	Params::WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderSurface_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature Parms{};

	Parms.Source = Source;
	Parms.current_index = current_index;
	Parms.current_value = current_value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.BndEvt__sliderRandomWeather_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UGenericSelectorItem*             Source                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_index                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_value                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_WeatherAndTrackConditionsPage_C::BndEvt__sliderRandomWeather_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature(class UGenericSelectorItem* Source, int32 current_index, int32 current_value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherAndTrackConditionsPage_C", "BndEvt__sliderRandomWeather_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature");

	Params::WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderRandomWeather_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature Parms{};

	Parms.Source = Source;
	Parms.current_index = current_index;
	Parms.current_value = current_value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.BndEvt__WDG_GenericBarItem_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_WeatherAndTrackConditionsPage_C::BndEvt__WDG_GenericBarItem_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherAndTrackConditionsPage_C", "BndEvt__WDG_GenericBarItem_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.Weather Preset Changed
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// EWeatherPresetType                      weatherType                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_WeatherAndTrackConditionsPage_C::Weather_Preset_Changed(EWeatherPresetType weatherType)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherAndTrackConditionsPage_C", "Weather Preset Changed");

	Params::WDG_WeatherAndTrackConditionsPage_C_Weather_Preset_Changed Parms{};

	Parms.weatherType = weatherType;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.BP_OnBackward
// (Event, Public, BlueprintEvent)

void UWDG_WeatherAndTrackConditionsPage_C::BP_OnBackward()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherAndTrackConditionsPage_C", "BP_OnBackward");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.BackButtonClicked
// (BlueprintCallable, BlueprintEvent)

void UWDG_WeatherAndTrackConditionsPage_C::BackButtonClicked()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherAndTrackConditionsPage_C", "BackButtonClicked");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.OnForward
// (BlueprintCallable, BlueprintEvent)

void UWDG_WeatherAndTrackConditionsPage_C::OnForward()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherAndTrackConditionsPage_C", "OnForward");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.CustomEvent_1
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// EWeatherPresetType                      weatherType                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWDG_WeatherTypePanel_C*          Source                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_WeatherAndTrackConditionsPage_C::CustomEvent_1(EWeatherPresetType weatherType, class UWDG_WeatherTypePanel_C* Source)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherAndTrackConditionsPage_C", "CustomEvent_1");

	Params::WDG_WeatherAndTrackConditionsPage_C_CustomEvent_1 Parms{};

	Parms.weatherType = weatherType;
	Parms.Source = Source;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.BP_StartPage
// (Event, Public, BlueprintEvent)

void UWDG_WeatherAndTrackConditionsPage_C::BP_StartPage()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherAndTrackConditionsPage_C", "BP_StartPage");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.CloudRainRules
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    ByCloudCover                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_WeatherAndTrackConditionsPage_C::CloudRainRules(bool ByCloudCover)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherAndTrackConditionsPage_C", "CloudRainRules");

	Params::WDG_WeatherAndTrackConditionsPage_C_CloudRainRules Parms{};

	Parms.ByCloudCover = ByCloudCover;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.WetnessRules
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    ByWetness                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_WeatherAndTrackConditionsPage_C::WetnessRules(bool ByWetness)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherAndTrackConditionsPage_C", "WetnessRules");

	Params::WDG_WeatherAndTrackConditionsPage_C_WetnessRules Parms{};

	Parms.ByWetness = ByWetness;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.FocusOnDynamicSlider
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// EUINavigation                           Navigation_0                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWidget*                          ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

class UWidget* UWDG_WeatherAndTrackConditionsPage_C::FocusOnDynamicSlider(EUINavigation Navigation_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherAndTrackConditionsPage_C", "FocusOnDynamicSlider");

	Params::WDG_WeatherAndTrackConditionsPage_C_FocusOnDynamicSlider Parms{};

	Parms.Navigation_0 = Navigation_0;

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.OnPreviewKeyDown
// (Event, Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FKeyEvent&                 InKeyEvent                                             (BlueprintVisible, BlueprintReadOnly, Parm)
// struct FEventReply                      ReturnValue                                            (Parm, OutParm, ReturnParm)

struct FEventReply UWDG_WeatherAndTrackConditionsPage_C::OnPreviewKeyDown(const struct FGeometry& MyGeometry, const struct FKeyEvent& InKeyEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherAndTrackConditionsPage_C", "OnPreviewKeyDown");

	Params::WDG_WeatherAndTrackConditionsPage_C_OnPreviewKeyDown Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InKeyEvent = std::move(InKeyEvent);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.GetBiasedWetnessPuddles
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// float                                   DryWeight                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// float                                   CapForDry                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// float*                                  MaxWetnessPuddles                                      (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_WeatherAndTrackConditionsPage_C::GetBiasedWetnessPuddles(float DryWeight, float CapForDry, float* MaxWetnessPuddles)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherAndTrackConditionsPage_C", "GetBiasedWetnessPuddles");

	Params::WDG_WeatherAndTrackConditionsPage_C_GetBiasedWetnessPuddles Parms{};

	Parms.DryWeight = DryWeight;
	Parms.CapForDry = CapForDry;

	UObject::ProcessEvent(Func, &Parms);

	if (MaxWetnessPuddles != nullptr)
		*MaxWetnessPuddles = Parms.MaxWetnessPuddles;
}

}

