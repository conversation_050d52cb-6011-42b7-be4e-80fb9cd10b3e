﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RatingDetailCC

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RatingDetailCC.WDG_RatingDetailCC_C
// 0x0010 (0x0638 - 0x0628)
class UWDG_RatingDetailCC_C final : public URatingCCDetail
{
public:
	class UImage*                                 imgBackground;                                     // 0x0628(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SimpleChartWrapper_C*              WDG_SimpleChartWrapper;                            // 0x0630(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoD<PERSON>ru<PERSON>, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RatingDetailCC_C">();
	}
	static class UWDG_RatingDetailCC_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RatingDetailCC_C>();
	}
};
static_assert(alignof(UWDG_RatingDetailCC_C) == 0x000008, "Wrong alignment on UWDG_RatingDetailCC_C");
static_assert(sizeof(UWDG_RatingDetailCC_C) == 0x000638, "Wrong size on UWDG_RatingDetailCC_C");
static_assert(offsetof(UWDG_RatingDetailCC_C, imgBackground) == 0x000628, "Member 'UWDG_RatingDetailCC_C::imgBackground' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailCC_C, WDG_SimpleChartWrapper) == 0x000630, "Member 'UWDG_RatingDetailCC_C::WDG_SimpleChartWrapper' has a wrong offset!");

}

