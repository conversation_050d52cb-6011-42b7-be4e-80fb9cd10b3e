﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ServerStats

#include "Basic.hpp"

#include "WDG_ServerStats_classes.hpp"
#include "WDG_ServerStats_parameters.hpp"


namespace SDK
{

// Function WDG_ServerStats.WDG_ServerStats_C.ExecuteUbergraph_WDG_ServerStats
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ServerStats_C::ExecuteUbergraph_WDG_ServerStats(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ServerStats_C", "ExecuteUbergraph_WDG_ServerStats");

	Params::WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ServerStats.WDG_ServerStats_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ServerStats_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ServerStats_C", "PreConstruct");

	Params::WDG_ServerStats_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ServerStats.WDG_ServerStats_C.OnServerStatsChanged
// (HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FMultiplayerServerStats&   server_stats                                           (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_ServerStats_C::OnServerStatsChanged(const struct FMultiplayerServerStats& server_stats)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ServerStats_C", "OnServerStatsChanged");

	Params::WDG_ServerStats_C_OnServerStatsChanged Parms{};

	Parms.server_stats = std::move(server_stats);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ServerStats.WDG_ServerStats_C.OnStartWidget
// (Event, Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UAcGameInstance*                  GameInstance                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class AAcRaceGameMode*                  raceGameMode                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class ACarAvatar*                       CarAvatar                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FHUDOptions&               HUDOptions                                             (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)

void UWDG_ServerStats_C::OnStartWidget(class UAcGameInstance* GameInstance, class AAcRaceGameMode* raceGameMode, class ACarAvatar* CarAvatar, const struct FHUDOptions& HUDOptions)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ServerStats_C", "OnStartWidget");

	Params::WDG_ServerStats_C_OnStartWidget Parms{};

	Parms.GameInstance = GameInstance;
	Parms.raceGameMode = raceGameMode;
	Parms.CarAvatar = CarAvatar;
	Parms.HUDOptions = std::move(HUDOptions);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ServerStats.WDG_ServerStats_C.Update
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FMultiplayerServerStats&   serverStats                                            (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_ServerStats_C::Update(const struct FMultiplayerServerStats& serverStats)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ServerStats_C", "Update");

	Params::WDG_ServerStats_C_Update Parms{};

	Parms.serverStats = std::move(serverStats);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ServerStats.WDG_ServerStats_C.AddTextToVBox
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UVerticalBox*&                    vbox                                                   (BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ZeroConstructor, InstancedReference, ReferenceParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const class FText&                      text                                                   (BlueprintVisible, BlueprintReadOnly, Parm)
// const struct FSlateColor&               Color                                                  (BlueprintVisible, BlueprintReadOnly, Parm)
// bool                                    isHighlighted                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ServerStats_C::AddTextToVBox(class UVerticalBox*& vbox, const class FText& text, const struct FSlateColor& Color, bool isHighlighted)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ServerStats_C", "AddTextToVBox");

	Params::WDG_ServerStats_C_AddTextToVBox Parms{};

	Parms.vbox = vbox;
	Parms.text = std::move(text);
	Parms.Color = std::move(Color);
	Parms.isHighlighted = isHighlighted;

	UObject::ProcessEvent(Func, &Parms);

	vbox = Parms.vbox;
}


// Function WDG_ServerStats.WDG_ServerStats_C.SetStatValue
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UTextBlock*                       txtBlock                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    LowIsGood                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
// int32                                   WarningColorStarts                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   ErrorColorStarts                                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ServerStats_C::SetStatValue(class UTextBlock* txtBlock, int32 Value, bool LowIsGood, int32 WarningColorStarts, int32 ErrorColorStarts)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ServerStats_C", "SetStatValue");

	Params::WDG_ServerStats_C_SetStatValue Parms{};

	Parms.txtBlock = txtBlock;
	Parms.Value = Value;
	Parms.LowIsGood = LowIsGood;
	Parms.WarningColorStarts = WarningColorStarts;
	Parms.ErrorColorStarts = ErrorColorStarts;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ServerStats.WDG_ServerStats_C.IsWidgetDefinitionEnabled
// (Event, Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UAcGameInstance*                  GameInstance                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FHUDOptions&               HUDOptions                                             (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor)

bool UWDG_ServerStats_C::IsWidgetDefinitionEnabled(class UAcGameInstance* GameInstance, const struct FHUDOptions& HUDOptions)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ServerStats_C", "IsWidgetDefinitionEnabled");

	Params::WDG_ServerStats_C_IsWidgetDefinitionEnabled Parms{};

	Parms.GameInstance = GameInstance;
	Parms.HUDOptions = std::move(HUDOptions);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ServerStats.WDG_ServerStats_C.ScaledNudgeByOffset
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FVector2D&                 currentPosition                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UCanvasPanelSlot*                 Slot_0                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// float                                   HUDScale                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// float                                   Offset                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ServerStats_C::ScaledNudgeByOffset(const struct FVector2D& currentPosition, class UCanvasPanelSlot* Slot_0, float HUDScale, float Offset)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ServerStats_C", "ScaledNudgeByOffset");

	Params::WDG_ServerStats_C_ScaledNudgeByOffset Parms{};

	Parms.currentPosition = std::move(currentPosition);
	Parms.Slot_0 = Slot_0;
	Parms.HUDScale = HUDScale;
	Parms.Offset = Offset;

	UObject::ProcessEvent(Func, &Parms);
}

}

