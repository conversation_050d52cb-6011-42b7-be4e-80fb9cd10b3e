﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RaceRatingItem

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RaceRatingItem.WDG_RaceRatingItem_C
// 0x0028 (0x0320 - 0x02F8)
class UWDG_RaceRatingItem_C final : public URatingItem
{
public:
	class UWidgetAnimation*                       Collapse;                                          // 0x02F8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       Expand;                                            // 0x0300(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 Image_1;                                           // 0x0308(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_2;                                           // 0x0310(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_3;                                           // 0x0318(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RaceRatingItem_C">();
	}
	static class UWDG_RaceRatingItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RaceRatingItem_C>();
	}
};
static_assert(alignof(UWDG_RaceRatingItem_C) == 0x000008, "Wrong alignment on UWDG_RaceRatingItem_C");
static_assert(sizeof(UWDG_RaceRatingItem_C) == 0x000320, "Wrong size on UWDG_RaceRatingItem_C");
static_assert(offsetof(UWDG_RaceRatingItem_C, Collapse) == 0x0002F8, "Member 'UWDG_RaceRatingItem_C::Collapse' has a wrong offset!");
static_assert(offsetof(UWDG_RaceRatingItem_C, Expand) == 0x000300, "Member 'UWDG_RaceRatingItem_C::Expand' has a wrong offset!");
static_assert(offsetof(UWDG_RaceRatingItem_C, Image_1) == 0x000308, "Member 'UWDG_RaceRatingItem_C::Image_1' has a wrong offset!");
static_assert(offsetof(UWDG_RaceRatingItem_C, Image_2) == 0x000310, "Member 'UWDG_RaceRatingItem_C::Image_2' has a wrong offset!");
static_assert(offsetof(UWDG_RaceRatingItem_C, Image_3) == 0x000318, "Member 'UWDG_RaceRatingItem_C::Image_3' has a wrong offset!");

}

