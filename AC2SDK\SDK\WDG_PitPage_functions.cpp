﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PitPage

#include "Basic.hpp"

#include "WDG_PitPage_classes.hpp"
#include "WDG_PitPage_parameters.hpp"


namespace SDK
{

// Function WDG_PitPage.WDG_PitPage_C.ExecuteUbergraph_WDG_PitPage
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_PitPage_C::ExecuteUbergraph_WDG_PitPage(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PitPage_C", "ExecuteUbergraph_WDG_PitPage");

	Params::WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PitPage.WDG_PitPage_C.OnSetShowCircuitName
// (Event, Public, BlueprintEvent)
// Parameters:
// bool                                    fullscreen                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_PitPage_C::OnSetShowCircuitName(bool fullscreen)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PitPage_C", "OnSetShowCircuitName");

	Params::WDG_PitPage_C_OnSetShowCircuitName Parms{};

	Parms.fullscreen = fullscreen;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PitPage.WDG_PitPage_C.OnStartPage
// (Event, Public, BlueprintEvent)

void UWDG_PitPage_C::OnStartPage()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PitPage_C", "OnStartPage");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_PitPage.WDG_PitPage_C.BndEvt__btnToggleFullscreen_K2Node_ComponentBoundEvent_1_OnButtonPressed__DelegateSignature
// (BlueprintEvent)

void UWDG_PitPage_C::BndEvt__btnToggleFullscreen_K2Node_ComponentBoundEvent_1_OnButtonPressed__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PitPage_C", "BndEvt__btnToggleFullscreen_K2Node_ComponentBoundEvent_1_OnButtonPressed__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_PitPage.WDG_PitPage_C.OnSetFullscreen
// (Event, Public, BlueprintEvent)
// Parameters:
// bool                                    fullscreen                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_PitPage_C::OnSetFullscreen(bool fullscreen)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PitPage_C", "OnSetFullscreen");

	Params::WDG_PitPage_C_OnSetFullscreen Parms{};

	Parms.fullscreen = fullscreen;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PitPage.WDG_PitPage_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_PitPage_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PitPage_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_PitPage.WDG_PitPage_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_PitPage_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PitPage_C", "PreConstruct");

	Params::WDG_PitPage_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}

}

