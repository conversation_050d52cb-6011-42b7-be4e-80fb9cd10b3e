﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomPalette

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ShowroomPalette.WDG_ShowroomPalette_C
// 0x0090 (0x0670 - 0x05E0)
class UWDG_ShowroomPalette_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWDG_GenericBarItem_C*                  btnExit;                                           // 0x05E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UScrollBox*                             scrollColors;                                      // 0x05F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWrapBox*                               wrapNormal;                                        // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWrapBox*                               wrapSpecial;                                       // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	TMulticastInlineDelegate<void(int32 Code, class UWDG_ShowroomMaterialColorSelector_C* TargetSelector)> OnColorSelected; // 0x0608(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TArray<struct FSkinColor>                     NormalColors;                                      // 0x0618(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<struct FSkinColor>                     SpecialColors;                                     // 0x0628(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	class UWDG_ShowroomMaterialColorSelector_C*   TargetSelector;                                    // 0x0638(0x0008)(Edit, BlueprintVisible, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<struct FSkinColor>                     AllColors;                                         // 0x0640(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TMulticastInlineDelegate<void(int32 ColorCode, class UWDG_ShowroomMaterialColorSelector_C* TargetSelector)> OnColorFocused; // 0x0650(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void(bool Cancel, class UWDG_ShowroomMaterialColorSelector_C* TargetSelector)> OnClosed; // 0x0660(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)

public:
	void ExecuteUbergraph_WDG_ShowroomPalette(int32 EntryPoint);
	void BndEvt__btnExit_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature(class UWDG_GenericBarItem_C* Sender);
	void BP_OnChildrenBackward(class UAcPanelBase* Child);
	void Populate(bool Special, TArray<struct FSkinColor>& colors);
	class UWDG_ShowroomTileItemHorizontal_C* CreateColorTile(const struct FSkinColor& SkinColor);
	void ColorSelected(class UWDG_ShowroomTileItemHorizontal_C* Sender, int32 Key, const struct FLinearColor& Color);
	void Open(bool for_aux_lights, class UWDG_ShowroomMaterialColorSelector_C* Sender, class UWDG_ShowroomTileItemHorizontal_C** FocusedTile);
	void Close(bool Cancel);
	void ColorFocused(class UWDG_ShowroomTileBase_C* Sender);
	struct FEventReply OnPreviewKeyDown(const struct FGeometry& MyGeometry, const struct FKeyEvent& InKeyEvent);
	void getAllColors();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ShowroomPalette_C">();
	}
	static class UWDG_ShowroomPalette_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ShowroomPalette_C>();
	}
};
static_assert(alignof(UWDG_ShowroomPalette_C) == 0x000008, "Wrong alignment on UWDG_ShowroomPalette_C");
static_assert(sizeof(UWDG_ShowroomPalette_C) == 0x000670, "Wrong size on UWDG_ShowroomPalette_C");
static_assert(offsetof(UWDG_ShowroomPalette_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_ShowroomPalette_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPalette_C, btnExit) == 0x0005E8, "Member 'UWDG_ShowroomPalette_C::btnExit' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPalette_C, scrollColors) == 0x0005F0, "Member 'UWDG_ShowroomPalette_C::scrollColors' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPalette_C, wrapNormal) == 0x0005F8, "Member 'UWDG_ShowroomPalette_C::wrapNormal' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPalette_C, wrapSpecial) == 0x000600, "Member 'UWDG_ShowroomPalette_C::wrapSpecial' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPalette_C, OnColorSelected) == 0x000608, "Member 'UWDG_ShowroomPalette_C::OnColorSelected' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPalette_C, NormalColors) == 0x000618, "Member 'UWDG_ShowroomPalette_C::NormalColors' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPalette_C, SpecialColors) == 0x000628, "Member 'UWDG_ShowroomPalette_C::SpecialColors' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPalette_C, TargetSelector) == 0x000638, "Member 'UWDG_ShowroomPalette_C::TargetSelector' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPalette_C, AllColors) == 0x000640, "Member 'UWDG_ShowroomPalette_C::AllColors' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPalette_C, OnColorFocused) == 0x000650, "Member 'UWDG_ShowroomPalette_C::OnColorFocused' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPalette_C, OnClosed) == 0x000660, "Member 'UWDG_ShowroomPalette_C::OnClosed' has a wrong offset!");

}

