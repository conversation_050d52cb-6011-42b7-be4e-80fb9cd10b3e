﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RollingStart

#include "Basic.hpp"

#include "WDG_RollingStart_classes.hpp"
#include "WDG_RollingStart_parameters.hpp"


namespace SDK
{

// Function WDG_RollingStart.WDG_RollingStart_C.ExecuteUbergraph_WDG_RollingStart
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_RollingStart_C::ExecuteUbergraph_WDG_RollingStart(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_RollingStart_C", "ExecuteUbergraph_WDG_RollingStart");

	Params::WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_RollingStart.WDG_RollingStart_C.Destruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_RollingStart_C::Destruct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_RollingStart_C", "Destruct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_RollingStart.WDG_RollingStart_C.OnHudTick
// (Event, Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FRaceHUDState&             State                                                  (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_RollingStart_C::OnHudTick(const struct FRaceHUDState& State)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_RollingStart_C", "OnHudTick");

	Params::WDG_RollingStart_C_OnHudTick Parms{};

	Parms.State = std::move(State);

	UObject::ProcessEvent(Func, &Parms);
}

}

