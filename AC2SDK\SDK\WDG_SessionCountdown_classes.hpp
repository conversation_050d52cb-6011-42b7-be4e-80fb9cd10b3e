﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SessionCountdown

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"
#include "UMG_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SessionCountdown.WDG_SessionCountdown_C
// 0x0050 (0x06A8 - 0x0658)
class UWDG_SessionCountdown_C final : public UAcRaceWidgetBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0658(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UBorder*                                DoNotQuitBorder;                                   // 0x0660(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_0;                                       // 0x0668(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_1;                                       // 0x0670(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtTimer;                                          // 0x0678(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtType;                                           // 0x0680(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class AAcRaceGameMode*                        raceGameMode;                                      // 0x0688(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          HideInEditor;                                      // 0x0690(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_691[0x7];                                      // 0x0691(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTimerHandle                           TimerHandle;                                       // 0x0698(0x0008)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	bool                                          isOnline;                                          // 0x06A0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)

public:
	void ExecuteUbergraph_WDG_SessionCountdown(int32 EntryPoint);
	void PreConstruct(bool IsDesignTime);
	void Destruct();
	void OnEverySecond();
	void Construct();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SessionCountdown_C">();
	}
	static class UWDG_SessionCountdown_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SessionCountdown_C>();
	}
};
static_assert(alignof(UWDG_SessionCountdown_C) == 0x000008, "Wrong alignment on UWDG_SessionCountdown_C");
static_assert(sizeof(UWDG_SessionCountdown_C) == 0x0006A8, "Wrong size on UWDG_SessionCountdown_C");
static_assert(offsetof(UWDG_SessionCountdown_C, UberGraphFrame) == 0x000658, "Member 'UWDG_SessionCountdown_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SessionCountdown_C, DoNotQuitBorder) == 0x000660, "Member 'UWDG_SessionCountdown_C::DoNotQuitBorder' has a wrong offset!");
static_assert(offsetof(UWDG_SessionCountdown_C, TextBlock_0) == 0x000668, "Member 'UWDG_SessionCountdown_C::TextBlock_0' has a wrong offset!");
static_assert(offsetof(UWDG_SessionCountdown_C, TextBlock_1) == 0x000670, "Member 'UWDG_SessionCountdown_C::TextBlock_1' has a wrong offset!");
static_assert(offsetof(UWDG_SessionCountdown_C, txtTimer) == 0x000678, "Member 'UWDG_SessionCountdown_C::txtTimer' has a wrong offset!");
static_assert(offsetof(UWDG_SessionCountdown_C, txtType) == 0x000680, "Member 'UWDG_SessionCountdown_C::txtType' has a wrong offset!");
static_assert(offsetof(UWDG_SessionCountdown_C, raceGameMode) == 0x000688, "Member 'UWDG_SessionCountdown_C::raceGameMode' has a wrong offset!");
static_assert(offsetof(UWDG_SessionCountdown_C, HideInEditor) == 0x000690, "Member 'UWDG_SessionCountdown_C::HideInEditor' has a wrong offset!");
static_assert(offsetof(UWDG_SessionCountdown_C, TimerHandle) == 0x000698, "Member 'UWDG_SessionCountdown_C::TimerHandle' has a wrong offset!");
static_assert(offsetof(UWDG_SessionCountdown_C, isOnline) == 0x0006A0, "Member 'UWDG_SessionCountdown_C::isOnline' has a wrong offset!");

}

