﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomCustomCarOptions

#include "Basic.hpp"

#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.ExecuteUbergraph_WDG_ShowroomCustomCarOptions
// 0x0128 (0x0128 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_MaterialKey_5;          // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, <PERSON>D<PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_MaterialKey_4;          // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_MaterialKey_3;          // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_MaterialKey_2;          // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_MaterialKey_1;          // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomMaterialColorSelector_C*   K2Node_ComponentBoundEvent_Sender_5;               // 0x0018(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomMaterialColorSelector_C*   K2Node_ComponentBoundEvent_Sender_4;               // 0x0020(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomPalette_C*                 CallFunc_OpenColorPalette_colorPalette;            // 0x0028(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomPalette_C*                 CallFunc_OpenColorPalette_colorPalette_1;          // 0x0030(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomMaterialColorSelector_C*   K2Node_ComponentBoundEvent_Sender_3;               // 0x0038(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomMaterialColorSelector_C*   K2Node_ComponentBoundEvent_Sender_2;               // 0x0040(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomPalette_C*                 CallFunc_OpenColorPalette_colorPalette_2;          // 0x0048(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomPalette_C*                 CallFunc_OpenColorPalette_colorPalette_3;          // 0x0050(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomMaterialColorSelector_C*   K2Node_ComponentBoundEvent_Sender_1;               // 0x0058(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomMaterialColorSelector_C*   K2Node_ComponentBoundEvent_Sender;                 // 0x0060(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomPalette_C*                 CallFunc_OpenColorPalette_colorPalette_4;          // 0x0068(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomPalette_C*                 CallFunc_OpenColorPalette_colorPalette_5;          // 0x0070(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_Code;                   // 0x0078(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_7C[0x4];                                       // 0x007C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomMaterialColorSelector_C*   K2Node_ComponentBoundEvent_TargetSelector_2;       // 0x0080(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_ColorCode;              // 0x0088(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_8C[0x4];                                       // 0x008C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomMaterialColorSelector_C*   K2Node_ComponentBoundEvent_TargetSelector_1;       // 0x0090(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_ComponentBoundEvent_Cancel;                 // 0x0098(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_99[0x7];                                       // 0x0099(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomMaterialColorSelector_C*   K2Node_ComponentBoundEvent_TargetSelector;         // 0x00A0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ECarModelType                                 K2Node_CustomEvent_CarModel;                       // 0x00A8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_A9[0x7];                                       // 0x00A9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UGenericSelectorItem*                   K2Node_ComponentBoundEvent_source_4;               // 0x00B0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_current_index_4;        // 0x00B8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_current_value_4;        // 0x00BC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_Conv_IntToByte_ReturnValue;               // 0x00C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C1[0x7];                                       // 0x00C1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UGenericSelectorItem*                   K2Node_ComponentBoundEvent_source_3;               // 0x00C8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_current_index_3;        // 0x00D0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_current_value_3;        // 0x00D4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_MaterialKey;            // 0x00D8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_Conv_IntToByte_ReturnValue_1;             // 0x00DC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_DD[0x3];                                       // 0x00DD(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UGenericSelectorItem*                   K2Node_ComponentBoundEvent_source_2;               // 0x00E0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_current_index_2;        // 0x00E8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_current_value_2;        // 0x00EC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_Conv_IntToByte_ReturnValue_2;             // 0x00F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_GetValidValue_ReturnValue;                // 0x00F1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_F2[0x6];                                       // 0x00F2(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UGenericSelectorItem*                   K2Node_ComponentBoundEvent_source_1;               // 0x00F8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_current_index_1;        // 0x0100(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_current_value_1;        // 0x0104(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_Conv_IntToByte_ReturnValue_3;             // 0x0108(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_GetValidValue_ReturnValue_1;              // 0x0109(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_10A[0x6];                                      // 0x010A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UGenericSelectorItem*                   K2Node_ComponentBoundEvent_source;                 // 0x0110(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_current_index;          // 0x0118(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_current_value;          // 0x011C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_Conv_IntToByte_ReturnValue_4;             // 0x0120(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions) == 0x000128, "Wrong size on WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, EntryPoint) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_MaterialKey_5) == 0x000004, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_MaterialKey_5' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_MaterialKey_4) == 0x000008, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_MaterialKey_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_MaterialKey_3) == 0x00000C, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_MaterialKey_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_MaterialKey_2) == 0x000010, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_MaterialKey_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_MaterialKey_1) == 0x000014, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_MaterialKey_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_Sender_5) == 0x000018, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_Sender_5' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_Sender_4) == 0x000020, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_Sender_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, CallFunc_OpenColorPalette_colorPalette) == 0x000028, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::CallFunc_OpenColorPalette_colorPalette' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, CallFunc_OpenColorPalette_colorPalette_1) == 0x000030, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::CallFunc_OpenColorPalette_colorPalette_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_Sender_3) == 0x000038, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_Sender_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_Sender_2) == 0x000040, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_Sender_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, CallFunc_OpenColorPalette_colorPalette_2) == 0x000048, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::CallFunc_OpenColorPalette_colorPalette_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, CallFunc_OpenColorPalette_colorPalette_3) == 0x000050, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::CallFunc_OpenColorPalette_colorPalette_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_Sender_1) == 0x000058, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_Sender_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_Sender) == 0x000060, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_Sender' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, CallFunc_OpenColorPalette_colorPalette_4) == 0x000068, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::CallFunc_OpenColorPalette_colorPalette_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, CallFunc_OpenColorPalette_colorPalette_5) == 0x000070, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::CallFunc_OpenColorPalette_colorPalette_5' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_Code) == 0x000078, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_Code' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_TargetSelector_2) == 0x000080, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_TargetSelector_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_ColorCode) == 0x000088, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_ColorCode' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_TargetSelector_1) == 0x000090, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_TargetSelector_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_Cancel) == 0x000098, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_Cancel' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_TargetSelector) == 0x0000A0, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_TargetSelector' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_CustomEvent_CarModel) == 0x0000A8, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_CustomEvent_CarModel' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_source_4) == 0x0000B0, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_source_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_current_index_4) == 0x0000B8, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_current_index_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_current_value_4) == 0x0000BC, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_current_value_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, CallFunc_Conv_IntToByte_ReturnValue) == 0x0000C0, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::CallFunc_Conv_IntToByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_source_3) == 0x0000C8, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_source_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_current_index_3) == 0x0000D0, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_current_index_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_current_value_3) == 0x0000D4, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_current_value_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_MaterialKey) == 0x0000D8, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_MaterialKey' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, CallFunc_Conv_IntToByte_ReturnValue_1) == 0x0000DC, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::CallFunc_Conv_IntToByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_source_2) == 0x0000E0, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_source_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_current_index_2) == 0x0000E8, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_current_index_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_current_value_2) == 0x0000EC, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_current_value_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, CallFunc_Conv_IntToByte_ReturnValue_2) == 0x0000F0, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::CallFunc_Conv_IntToByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, CallFunc_GetValidValue_ReturnValue) == 0x0000F1, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::CallFunc_GetValidValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_source_1) == 0x0000F8, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_source_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_current_index_1) == 0x000100, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_current_index_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_current_value_1) == 0x000104, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_current_value_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, CallFunc_Conv_IntToByte_ReturnValue_3) == 0x000108, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::CallFunc_Conv_IntToByte_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, CallFunc_GetValidValue_ReturnValue_1) == 0x000109, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::CallFunc_GetValidValue_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_source) == 0x000110, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_source' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_current_index) == 0x000118, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_current_index' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, K2Node_ComponentBoundEvent_current_value) == 0x00011C, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::K2Node_ComponentBoundEvent_current_value' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions, CallFunc_Conv_IntToByte_ReturnValue_4) == 0x000120, "Member 'WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions::CallFunc_Conv_IntToByte_ReturnValue_4' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__sliderBanner_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_BndEvt__sliderBanner_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature final
{
public:
	class UGenericSelectorItem*                   Source;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_index;                                     // 0x0008(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_value;                                     // 0x000C(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderBanner_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_BndEvt__sliderBanner_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderBanner_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature) == 0x000010, "Wrong size on WDG_ShowroomCustomCarOptions_C_BndEvt__sliderBanner_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderBanner_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature, Source) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__sliderBanner_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature::Source' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderBanner_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature, current_index) == 0x000008, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__sliderBanner_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature::current_index' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderBanner_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature, current_value) == 0x00000C, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__sliderBanner_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature::current_value' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__sliderCup_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_BndEvt__sliderCup_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature final
{
public:
	class UGenericSelectorItem*                   Source;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_index;                                     // 0x0008(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_value;                                     // 0x000C(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderCup_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_BndEvt__sliderCup_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderCup_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature) == 0x000010, "Wrong size on WDG_ShowroomCustomCarOptions_C_BndEvt__sliderCup_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderCup_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature, Source) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__sliderCup_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature::Source' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderCup_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature, current_index) == 0x000008, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__sliderCup_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature::current_index' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderCup_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature, current_value) == 0x00000C, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__sliderCup_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature::current_value' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__sliderNationality_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_BndEvt__sliderNationality_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature final
{
public:
	class UGenericSelectorItem*                   Source;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_index;                                     // 0x0008(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_value;                                     // 0x000C(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderNationality_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_BndEvt__sliderNationality_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderNationality_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature) == 0x000010, "Wrong size on WDG_ShowroomCustomCarOptions_C_BndEvt__sliderNationality_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderNationality_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature, Source) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__sliderNationality_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature::Source' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderNationality_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature, current_index) == 0x000008, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__sliderNationality_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature::current_index' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderNationality_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature, current_value) == 0x00000C, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__sliderNationality_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature::current_value' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__selectorRim1_K2Node_ComponentBoundEvent_7_OnMaterialChanged__DelegateSignature
// 0x0004 (0x0004 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim1_K2Node_ComponentBoundEvent_7_OnMaterialChanged__DelegateSignature final
{
public:
	int32                                         MaterialKey;                                       // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim1_K2Node_ComponentBoundEvent_7_OnMaterialChanged__DelegateSignature) == 0x000004, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim1_K2Node_ComponentBoundEvent_7_OnMaterialChanged__DelegateSignature");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim1_K2Node_ComponentBoundEvent_7_OnMaterialChanged__DelegateSignature) == 0x000004, "Wrong size on WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim1_K2Node_ComponentBoundEvent_7_OnMaterialChanged__DelegateSignature");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim1_K2Node_ComponentBoundEvent_7_OnMaterialChanged__DelegateSignature, MaterialKey) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim1_K2Node_ComponentBoundEvent_7_OnMaterialChanged__DelegateSignature::MaterialKey' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__sliderSponsor_K2Node_ComponentBoundEvent_8_SelectorChanged__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_BndEvt__sliderSponsor_K2Node_ComponentBoundEvent_8_SelectorChanged__DelegateSignature final
{
public:
	class UGenericSelectorItem*                   Source;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_index;                                     // 0x0008(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_value;                                     // 0x000C(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderSponsor_K2Node_ComponentBoundEvent_8_SelectorChanged__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_BndEvt__sliderSponsor_K2Node_ComponentBoundEvent_8_SelectorChanged__DelegateSignature");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderSponsor_K2Node_ComponentBoundEvent_8_SelectorChanged__DelegateSignature) == 0x000010, "Wrong size on WDG_ShowroomCustomCarOptions_C_BndEvt__sliderSponsor_K2Node_ComponentBoundEvent_8_SelectorChanged__DelegateSignature");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderSponsor_K2Node_ComponentBoundEvent_8_SelectorChanged__DelegateSignature, Source) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__sliderSponsor_K2Node_ComponentBoundEvent_8_SelectorChanged__DelegateSignature::Source' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderSponsor_K2Node_ComponentBoundEvent_8_SelectorChanged__DelegateSignature, current_index) == 0x000008, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__sliderSponsor_K2Node_ComponentBoundEvent_8_SelectorChanged__DelegateSignature::current_index' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderSponsor_K2Node_ComponentBoundEvent_8_SelectorChanged__DelegateSignature, current_value) == 0x00000C, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__sliderSponsor_K2Node_ComponentBoundEvent_8_SelectorChanged__DelegateSignature::current_value' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__sliderTemplate_K2Node_ComponentBoundEvent_7_SelectorChanged__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_BndEvt__sliderTemplate_K2Node_ComponentBoundEvent_7_SelectorChanged__DelegateSignature final
{
public:
	class UGenericSelectorItem*                   Source;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_index;                                     // 0x0008(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_value;                                     // 0x000C(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderTemplate_K2Node_ComponentBoundEvent_7_SelectorChanged__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_BndEvt__sliderTemplate_K2Node_ComponentBoundEvent_7_SelectorChanged__DelegateSignature");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderTemplate_K2Node_ComponentBoundEvent_7_SelectorChanged__DelegateSignature) == 0x000010, "Wrong size on WDG_ShowroomCustomCarOptions_C_BndEvt__sliderTemplate_K2Node_ComponentBoundEvent_7_SelectorChanged__DelegateSignature");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderTemplate_K2Node_ComponentBoundEvent_7_SelectorChanged__DelegateSignature, Source) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__sliderTemplate_K2Node_ComponentBoundEvent_7_SelectorChanged__DelegateSignature::Source' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderTemplate_K2Node_ComponentBoundEvent_7_SelectorChanged__DelegateSignature, current_index) == 0x000008, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__sliderTemplate_K2Node_ComponentBoundEvent_7_SelectorChanged__DelegateSignature::current_index' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__sliderTemplate_K2Node_ComponentBoundEvent_7_SelectorChanged__DelegateSignature, current_value) == 0x00000C, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__sliderTemplate_K2Node_ComponentBoundEvent_7_SelectorChanged__DelegateSignature::current_value' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.OnModelUpdate
// 0x0001 (0x0001 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_OnModelUpdate final
{
public:
	ECarModelType                                 CarModel;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_OnModelUpdate) == 0x000001, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_OnModelUpdate");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_OnModelUpdate) == 0x000001, "Wrong size on WDG_ShowroomCustomCarOptions_C_OnModelUpdate");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_OnModelUpdate, CarModel) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_OnModelUpdate::CarModel' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__colorPalette_K2Node_ComponentBoundEvent_14_OnClosed__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_14_OnClosed__DelegateSignature final
{
public:
	bool                                          Cancel;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomMaterialColorSelector_C*   TargetSelector;                                    // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_14_OnClosed__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_14_OnClosed__DelegateSignature");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_14_OnClosed__DelegateSignature) == 0x000010, "Wrong size on WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_14_OnClosed__DelegateSignature");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_14_OnClosed__DelegateSignature, Cancel) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_14_OnClosed__DelegateSignature::Cancel' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_14_OnClosed__DelegateSignature, TargetSelector) == 0x000008, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_14_OnClosed__DelegateSignature::TargetSelector' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__colorPalette_K2Node_ComponentBoundEvent_13_OnColorFocused__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_13_OnColorFocused__DelegateSignature final
{
public:
	int32                                         ColorCode;                                         // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomMaterialColorSelector_C*   TargetSelector;                                    // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_13_OnColorFocused__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_13_OnColorFocused__DelegateSignature");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_13_OnColorFocused__DelegateSignature) == 0x000010, "Wrong size on WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_13_OnColorFocused__DelegateSignature");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_13_OnColorFocused__DelegateSignature, ColorCode) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_13_OnColorFocused__DelegateSignature::ColorCode' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_13_OnColorFocused__DelegateSignature, TargetSelector) == 0x000008, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_13_OnColorFocused__DelegateSignature::TargetSelector' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__colorPalette_K2Node_ComponentBoundEvent_6_OnColorSelected__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_6_OnColorSelected__DelegateSignature final
{
public:
	int32                                         Code;                                              // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomMaterialColorSelector_C*   TargetSelector;                                    // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_6_OnColorSelected__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_6_OnColorSelected__DelegateSignature");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_6_OnColorSelected__DelegateSignature) == 0x000010, "Wrong size on WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_6_OnColorSelected__DelegateSignature");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_6_OnColorSelected__DelegateSignature, Code) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_6_OnColorSelected__DelegateSignature::Code' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_6_OnColorSelected__DelegateSignature, TargetSelector) == 0x000008, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_6_OnColorSelected__DelegateSignature::TargetSelector' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_5_OnSelectColor__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_5_OnSelectColor__DelegateSignature final
{
public:
	class UWDG_ShowroomMaterialColorSelector_C*   Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_5_OnSelectColor__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_5_OnSelectColor__DelegateSignature");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_5_OnSelectColor__DelegateSignature) == 0x000008, "Wrong size on WDG_ShowroomCustomCarOptions_C_BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_5_OnSelectColor__DelegateSignature");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_5_OnSelectColor__DelegateSignature, Sender) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_5_OnSelectColor__DelegateSignature::Sender' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__selectorRim2_K2Node_ComponentBoundEvent_4_OnSelectColor__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim2_K2Node_ComponentBoundEvent_4_OnSelectColor__DelegateSignature final
{
public:
	class UWDG_ShowroomMaterialColorSelector_C*   Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim2_K2Node_ComponentBoundEvent_4_OnSelectColor__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim2_K2Node_ComponentBoundEvent_4_OnSelectColor__DelegateSignature");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim2_K2Node_ComponentBoundEvent_4_OnSelectColor__DelegateSignature) == 0x000008, "Wrong size on WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim2_K2Node_ComponentBoundEvent_4_OnSelectColor__DelegateSignature");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim2_K2Node_ComponentBoundEvent_4_OnSelectColor__DelegateSignature, Sender) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim2_K2Node_ComponentBoundEvent_4_OnSelectColor__DelegateSignature::Sender' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__selectorRim1_K2Node_ComponentBoundEvent_3_OnSelectColor__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim1_K2Node_ComponentBoundEvent_3_OnSelectColor__DelegateSignature final
{
public:
	class UWDG_ShowroomMaterialColorSelector_C*   Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim1_K2Node_ComponentBoundEvent_3_OnSelectColor__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim1_K2Node_ComponentBoundEvent_3_OnSelectColor__DelegateSignature");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim1_K2Node_ComponentBoundEvent_3_OnSelectColor__DelegateSignature) == 0x000008, "Wrong size on WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim1_K2Node_ComponentBoundEvent_3_OnSelectColor__DelegateSignature");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim1_K2Node_ComponentBoundEvent_3_OnSelectColor__DelegateSignature, Sender) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim1_K2Node_ComponentBoundEvent_3_OnSelectColor__DelegateSignature::Sender' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_2_OnSelectColor__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_2_OnSelectColor__DelegateSignature final
{
public:
	class UWDG_ShowroomMaterialColorSelector_C*   Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_2_OnSelectColor__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_2_OnSelectColor__DelegateSignature");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_2_OnSelectColor__DelegateSignature) == 0x000008, "Wrong size on WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_2_OnSelectColor__DelegateSignature");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_2_OnSelectColor__DelegateSignature, Sender) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_2_OnSelectColor__DelegateSignature::Sender' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_1_OnSelectColor__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_1_OnSelectColor__DelegateSignature final
{
public:
	class UWDG_ShowroomMaterialColorSelector_C*   Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_1_OnSelectColor__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_1_OnSelectColor__DelegateSignature");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_1_OnSelectColor__DelegateSignature) == 0x000008, "Wrong size on WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_1_OnSelectColor__DelegateSignature");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_1_OnSelectColor__DelegateSignature, Sender) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_1_OnSelectColor__DelegateSignature::Sender' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_0_OnSelectColor__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_0_OnSelectColor__DelegateSignature final
{
public:
	class UWDG_ShowroomMaterialColorSelector_C*   Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_0_OnSelectColor__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_0_OnSelectColor__DelegateSignature");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_0_OnSelectColor__DelegateSignature) == 0x000008, "Wrong size on WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_0_OnSelectColor__DelegateSignature");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_0_OnSelectColor__DelegateSignature, Sender) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_0_OnSelectColor__DelegateSignature::Sender' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_12_OnMaterialChanged__DelegateSignature
// 0x0004 (0x0004 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_12_OnMaterialChanged__DelegateSignature final
{
public:
	int32                                         MaterialKey;                                       // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_12_OnMaterialChanged__DelegateSignature) == 0x000004, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_12_OnMaterialChanged__DelegateSignature");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_12_OnMaterialChanged__DelegateSignature) == 0x000004, "Wrong size on WDG_ShowroomCustomCarOptions_C_BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_12_OnMaterialChanged__DelegateSignature");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_12_OnMaterialChanged__DelegateSignature, MaterialKey) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_12_OnMaterialChanged__DelegateSignature::MaterialKey' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_11_OnMaterialChanged__DelegateSignature
// 0x0004 (0x0004 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_11_OnMaterialChanged__DelegateSignature final
{
public:
	int32                                         MaterialKey;                                       // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_11_OnMaterialChanged__DelegateSignature) == 0x000004, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_11_OnMaterialChanged__DelegateSignature");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_11_OnMaterialChanged__DelegateSignature) == 0x000004, "Wrong size on WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_11_OnMaterialChanged__DelegateSignature");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_11_OnMaterialChanged__DelegateSignature, MaterialKey) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_11_OnMaterialChanged__DelegateSignature::MaterialKey' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_10_OnMaterialChanged__DelegateSignature
// 0x0004 (0x0004 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_10_OnMaterialChanged__DelegateSignature final
{
public:
	int32                                         MaterialKey;                                       // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_10_OnMaterialChanged__DelegateSignature) == 0x000004, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_10_OnMaterialChanged__DelegateSignature");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_10_OnMaterialChanged__DelegateSignature) == 0x000004, "Wrong size on WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_10_OnMaterialChanged__DelegateSignature");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_10_OnMaterialChanged__DelegateSignature, MaterialKey) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_10_OnMaterialChanged__DelegateSignature::MaterialKey' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_9_OnMaterialChanged__DelegateSignature
// 0x0004 (0x0004 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_9_OnMaterialChanged__DelegateSignature final
{
public:
	int32                                         MaterialKey;                                       // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_9_OnMaterialChanged__DelegateSignature) == 0x000004, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_9_OnMaterialChanged__DelegateSignature");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_9_OnMaterialChanged__DelegateSignature) == 0x000004, "Wrong size on WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_9_OnMaterialChanged__DelegateSignature");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_9_OnMaterialChanged__DelegateSignature, MaterialKey) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_9_OnMaterialChanged__DelegateSignature::MaterialKey' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__selectorRim2_K2Node_ComponentBoundEvent_8_OnMaterialChanged__DelegateSignature
// 0x0004 (0x0004 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim2_K2Node_ComponentBoundEvent_8_OnMaterialChanged__DelegateSignature final
{
public:
	int32                                         MaterialKey;                                       // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim2_K2Node_ComponentBoundEvent_8_OnMaterialChanged__DelegateSignature) == 0x000004, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim2_K2Node_ComponentBoundEvent_8_OnMaterialChanged__DelegateSignature");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim2_K2Node_ComponentBoundEvent_8_OnMaterialChanged__DelegateSignature) == 0x000004, "Wrong size on WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim2_K2Node_ComponentBoundEvent_8_OnMaterialChanged__DelegateSignature");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim2_K2Node_ComponentBoundEvent_8_OnMaterialChanged__DelegateSignature, MaterialKey) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim2_K2Node_ComponentBoundEvent_8_OnMaterialChanged__DelegateSignature::MaterialKey' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.GetSkinTemplates
// 0x00B0 (0x00B0 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_GetSkinTemplates final
{
public:
	ECarModelType                                 CarModel;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x3];                                        // 0x0001(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_IntToText_ReturnValue;               // 0x0010(0x0018)()
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NotEqual_StrStr_ReturnValue;              // 0x002C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2D[0x3];                                       // 0x002D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Conv_BoolToString_ReturnValue;            // 0x0030(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	TArray<struct FSkinTemplate>                  CallFunc_getModelCustomSkinTemplates_ReturnValue;  // 0x0040(0x0010)(ReferenceParm)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0050(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_54[0x4];                                       // 0x0054(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSkinTemplate                          CallFunc_Array_Get_Item;                           // 0x0058(0x0048)()
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x00A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_A1[0x3];                                       // 0x00A1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x00A4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x00A8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_GetSkinTemplates) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_GetSkinTemplates");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_GetSkinTemplates) == 0x0000B0, "Wrong size on WDG_ShowroomCustomCarOptions_C_GetSkinTemplates");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_GetSkinTemplates, CarModel) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_GetSkinTemplates::CarModel' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_GetSkinTemplates, Temp_int_Array_Index_Variable) == 0x000004, "Member 'WDG_ShowroomCustomCarOptions_C_GetSkinTemplates::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_GetSkinTemplates, Temp_int_Loop_Counter_Variable) == 0x000008, "Member 'WDG_ShowroomCustomCarOptions_C_GetSkinTemplates::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_GetSkinTemplates, CallFunc_Add_IntInt_ReturnValue) == 0x00000C, "Member 'WDG_ShowroomCustomCarOptions_C_GetSkinTemplates::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_GetSkinTemplates, CallFunc_Conv_IntToText_ReturnValue) == 0x000010, "Member 'WDG_ShowroomCustomCarOptions_C_GetSkinTemplates::CallFunc_Conv_IntToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_GetSkinTemplates, CallFunc_Add_IntInt_ReturnValue_1) == 0x000028, "Member 'WDG_ShowroomCustomCarOptions_C_GetSkinTemplates::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_GetSkinTemplates, CallFunc_NotEqual_StrStr_ReturnValue) == 0x00002C, "Member 'WDG_ShowroomCustomCarOptions_C_GetSkinTemplates::CallFunc_NotEqual_StrStr_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_GetSkinTemplates, CallFunc_Conv_BoolToString_ReturnValue) == 0x000030, "Member 'WDG_ShowroomCustomCarOptions_C_GetSkinTemplates::CallFunc_Conv_BoolToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_GetSkinTemplates, CallFunc_getModelCustomSkinTemplates_ReturnValue) == 0x000040, "Member 'WDG_ShowroomCustomCarOptions_C_GetSkinTemplates::CallFunc_getModelCustomSkinTemplates_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_GetSkinTemplates, CallFunc_Array_Length_ReturnValue) == 0x000050, "Member 'WDG_ShowroomCustomCarOptions_C_GetSkinTemplates::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_GetSkinTemplates, CallFunc_Array_Get_Item) == 0x000058, "Member 'WDG_ShowroomCustomCarOptions_C_GetSkinTemplates::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_GetSkinTemplates, CallFunc_Greater_IntInt_ReturnValue) == 0x0000A0, "Member 'WDG_ShowroomCustomCarOptions_C_GetSkinTemplates::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_GetSkinTemplates, CallFunc_Array_Length_ReturnValue_1) == 0x0000A4, "Member 'WDG_ShowroomCustomCarOptions_C_GetSkinTemplates::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_GetSkinTemplates, CallFunc_Less_IntInt_ReturnValue) == 0x0000A8, "Member 'WDG_ShowroomCustomCarOptions_C_GetSkinTemplates::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.updateKeySlider
// 0x0048 (0x0048 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_updateKeySlider final
{
public:
	class UGenericSelectorItem*                   Target;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<int32>                                 SourceArray;                                       // 0x0008(0x0010)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	class FText                                   FirstOptionText;                                   // 0x0018(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm)
	int32                                         Temp_int_Variable;                                 // 0x0030(0x0004)(ConstParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_TextIsEmpty_ReturnValue;                  // 0x0034(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_35[0x3];                                       // 0x0035(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0038(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x003C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_3D[0x3];                                       // 0x003D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Add_ReturnValue;                    // 0x0040(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue_1;                  // 0x0044(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_updateKeySlider) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_updateKeySlider");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_updateKeySlider) == 0x000048, "Wrong size on WDG_ShowroomCustomCarOptions_C_updateKeySlider");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateKeySlider, Target) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_updateKeySlider::Target' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateKeySlider, SourceArray) == 0x000008, "Member 'WDG_ShowroomCustomCarOptions_C_updateKeySlider::SourceArray' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateKeySlider, FirstOptionText) == 0x000018, "Member 'WDG_ShowroomCustomCarOptions_C_updateKeySlider::FirstOptionText' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateKeySlider, Temp_int_Variable) == 0x000030, "Member 'WDG_ShowroomCustomCarOptions_C_updateKeySlider::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateKeySlider, CallFunc_TextIsEmpty_ReturnValue) == 0x000034, "Member 'WDG_ShowroomCustomCarOptions_C_updateKeySlider::CallFunc_TextIsEmpty_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateKeySlider, CallFunc_Array_Length_ReturnValue) == 0x000038, "Member 'WDG_ShowroomCustomCarOptions_C_updateKeySlider::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateKeySlider, CallFunc_Greater_IntInt_ReturnValue) == 0x00003C, "Member 'WDG_ShowroomCustomCarOptions_C_updateKeySlider::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateKeySlider, CallFunc_Array_Add_ReturnValue) == 0x000040, "Member 'WDG_ShowroomCustomCarOptions_C_updateKeySlider::CallFunc_Array_Add_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateKeySlider, CallFunc_Array_Add_ReturnValue_1) == 0x000044, "Member 'WDG_ShowroomCustomCarOptions_C_updateKeySlider::CallFunc_Array_Add_ReturnValue_1' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.getAuxLights
// 0x0018 (0x0018 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_getAuxLights final
{
public:
	ECarModelType                                 CarModel;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<int32>                                 CallFunc_getModelAuxLightKeysAsInt_ReturnValue;    // 0x0008(0x0010)(ReferenceParm)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_getAuxLights) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_getAuxLights");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_getAuxLights) == 0x000018, "Wrong size on WDG_ShowroomCustomCarOptions_C_getAuxLights");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getAuxLights, CarModel) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_getAuxLights::CarModel' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getAuxLights, CallFunc_getModelAuxLightKeysAsInt_ReturnValue) == 0x000008, "Member 'WDG_ShowroomCustomCarOptions_C_getAuxLights::CallFunc_getModelAuxLightKeysAsInt_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.getCarGraphicData
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_getCarGraphicData final
{
public:
	class UAcCarGraphicData*                      CarGraphicData;                                    // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_getCarGraphicData) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_getCarGraphicData");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_getCarGraphicData) == 0x000008, "Wrong size on WDG_ShowroomCustomCarOptions_C_getCarGraphicData");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getCarGraphicData, CarGraphicData) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_getCarGraphicData::CarGraphicData' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.SetCarInfo
// 0x00F8 (0x00F8 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_SetCarInfo final
{
public:
	struct FCarInfo                               CarInfo_0;                                         // 0x0000(0x00E0)(BlueprintVisible, BlueprintReadOnly, Parm)
	int32                                         CallFunc_Conv_ByteToInt_ReturnValue;               // 0x00E0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Conv_ByteToInt_ReturnValue_1;             // 0x00E4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Conv_ByteToInt_ReturnValue_2;             // 0x00E8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Conv_ByteToInt_ReturnValue_3;             // 0x00EC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Conv_ByteToInt_ReturnValue_4;             // 0x00F0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Conv_ByteToInt_ReturnValue_5;             // 0x00F4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_SetCarInfo) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_SetCarInfo");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_SetCarInfo) == 0x0000F8, "Wrong size on WDG_ShowroomCustomCarOptions_C_SetCarInfo");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_SetCarInfo, CarInfo_0) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_SetCarInfo::CarInfo_0' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_SetCarInfo, CallFunc_Conv_ByteToInt_ReturnValue) == 0x0000E0, "Member 'WDG_ShowroomCustomCarOptions_C_SetCarInfo::CallFunc_Conv_ByteToInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_SetCarInfo, CallFunc_Conv_ByteToInt_ReturnValue_1) == 0x0000E4, "Member 'WDG_ShowroomCustomCarOptions_C_SetCarInfo::CallFunc_Conv_ByteToInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_SetCarInfo, CallFunc_Conv_ByteToInt_ReturnValue_2) == 0x0000E8, "Member 'WDG_ShowroomCustomCarOptions_C_SetCarInfo::CallFunc_Conv_ByteToInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_SetCarInfo, CallFunc_Conv_ByteToInt_ReturnValue_3) == 0x0000EC, "Member 'WDG_ShowroomCustomCarOptions_C_SetCarInfo::CallFunc_Conv_ByteToInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_SetCarInfo, CallFunc_Conv_ByteToInt_ReturnValue_4) == 0x0000F0, "Member 'WDG_ShowroomCustomCarOptions_C_SetCarInfo::CallFunc_Conv_ByteToInt_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_SetCarInfo, CallFunc_Conv_ByteToInt_ReturnValue_5) == 0x0000F4, "Member 'WDG_ShowroomCustomCarOptions_C_SetCarInfo::CallFunc_Conv_ByteToInt_ReturnValue_5' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.OnCarInfoUpdated
// 0x0001 (0x0001 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_OnCarInfoUpdated final
{
public:
	bool                                          SetDirty;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_OnCarInfoUpdated) == 0x000001, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_OnCarInfoUpdated");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_OnCarInfoUpdated) == 0x000001, "Wrong size on WDG_ShowroomCustomCarOptions_C_OnCarInfoUpdated");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_OnCarInfoUpdated, SetDirty) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_OnCarInfoUpdated::SetDirty' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.updateMaterialInSelector
// 0x0188 (0x0188 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector final
{
public:
	class UWDG_ShowroomMaterialColorSelector_C*   TargetSelector;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          IsRim;                                             // 0x0008(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_9[0x3];                                        // 0x0009(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_1;                   // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_1;                  // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcCarGraphicData*                      CallFunc_getCarGraphicData_CarGraphicData;         // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TMap<class FName, struct FRimMaterialType>    CallFunc_getRimMaterialTypesWithNames_ReturnValue; // 0x0028(0x0050)()
	TMap<class FName, struct FSkinMaterialType>   CallFunc_getSkinMaterialTypesWithNames_ReturnValue; // 0x0078(0x0050)()
	TArray<struct FRimMaterialType>               CallFunc_Map_Values_Values;                        // 0x00C8(0x0010)(ReferenceParm)
	TArray<class FName>                           CallFunc_Map_Keys_Keys;                            // 0x00D8(0x0010)(ReferenceParm)
	class FName                                   CallFunc_Array_Get_Item;                           // 0x00E8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSkinMaterialType                      CallFunc_Map_Find_Value;                           // 0x00F0(0x0030)()
	bool                                          CallFunc_Map_Find_ReturnValue;                     // 0x0120(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_121[0x7];                                      // 0x0121(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Conv_NameToString_ReturnValue;            // 0x0128(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	float                                         CallFunc_Conv_StringToFloat_ReturnValue;           // 0x0138(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x013C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue;                       // 0x0140(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0144(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_145[0x3];                                      // 0x0145(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class FName>                           CallFunc_Map_Keys_Keys_1;                          // 0x0148(0x0010)(ReferenceParm)
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x0158(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   CallFunc_Array_Get_Item_1;                         // 0x015C(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_164[0x4];                                      // 0x0164(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Conv_NameToString_ReturnValue_1;          // 0x0168(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x0178(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Conv_StringToFloat_ReturnValue_1;         // 0x017C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_1;                // 0x0180(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_181[0x3];                                      // 0x0181(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_FTrunc_ReturnValue_1;                     // 0x0184(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector) == 0x000188, "Wrong size on WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, TargetSelector) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::TargetSelector' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, IsRim) == 0x000008, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::IsRim' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, Temp_int_Loop_Counter_Variable) == 0x00000C, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, CallFunc_Add_IntInt_ReturnValue) == 0x000010, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, Temp_int_Array_Index_Variable) == 0x000014, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, Temp_int_Array_Index_Variable_1) == 0x000018, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::Temp_int_Array_Index_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, Temp_int_Loop_Counter_Variable_1) == 0x00001C, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::Temp_int_Loop_Counter_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, CallFunc_getCarGraphicData_CarGraphicData) == 0x000020, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::CallFunc_getCarGraphicData_CarGraphicData' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, CallFunc_getRimMaterialTypesWithNames_ReturnValue) == 0x000028, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::CallFunc_getRimMaterialTypesWithNames_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, CallFunc_getSkinMaterialTypesWithNames_ReturnValue) == 0x000078, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::CallFunc_getSkinMaterialTypesWithNames_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, CallFunc_Map_Values_Values) == 0x0000C8, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::CallFunc_Map_Values_Values' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, CallFunc_Map_Keys_Keys) == 0x0000D8, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::CallFunc_Map_Keys_Keys' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, CallFunc_Array_Get_Item) == 0x0000E8, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, CallFunc_Map_Find_Value) == 0x0000F0, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::CallFunc_Map_Find_Value' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, CallFunc_Map_Find_ReturnValue) == 0x000120, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::CallFunc_Map_Find_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, CallFunc_Conv_NameToString_ReturnValue) == 0x000128, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::CallFunc_Conv_NameToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, CallFunc_Conv_StringToFloat_ReturnValue) == 0x000138, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::CallFunc_Conv_StringToFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, CallFunc_Array_Length_ReturnValue) == 0x00013C, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, CallFunc_FTrunc_ReturnValue) == 0x000140, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::CallFunc_FTrunc_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, CallFunc_Less_IntInt_ReturnValue) == 0x000144, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, CallFunc_Map_Keys_Keys_1) == 0x000148, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::CallFunc_Map_Keys_Keys_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, CallFunc_Add_IntInt_ReturnValue_1) == 0x000158, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, CallFunc_Array_Get_Item_1) == 0x00015C, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, CallFunc_Conv_NameToString_ReturnValue_1) == 0x000168, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::CallFunc_Conv_NameToString_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, CallFunc_Array_Length_ReturnValue_1) == 0x000178, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, CallFunc_Conv_StringToFloat_ReturnValue_1) == 0x00017C, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::CallFunc_Conv_StringToFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, CallFunc_Less_IntInt_ReturnValue_1) == 0x000180, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::CallFunc_Less_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector, CallFunc_FTrunc_ReturnValue_1) == 0x000184, "Member 'WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector::CallFunc_FTrunc_ReturnValue_1' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.getSponsors
// 0x0020 (0x0020 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_getSponsors final
{
public:
	ECarModelType                                 CarModel;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcCarGraphicData*                      CallFunc_getCarGraphicData_CarGraphicData;         // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<int32>                                 CallFunc_getModelSponsorKeysAsInt_ReturnValue;     // 0x0010(0x0010)(ReferenceParm)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_getSponsors) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_getSponsors");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_getSponsors) == 0x000020, "Wrong size on WDG_ShowroomCustomCarOptions_C_getSponsors");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getSponsors, CarModel) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_getSponsors::CarModel' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getSponsors, CallFunc_getCarGraphicData_CarGraphicData) == 0x000008, "Member 'WDG_ShowroomCustomCarOptions_C_getSponsors::CallFunc_getCarGraphicData_CarGraphicData' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getSponsors, CallFunc_getModelSponsorKeysAsInt_ReturnValue) == 0x000010, "Member 'WDG_ShowroomCustomCarOptions_C_getSponsors::CallFunc_getModelSponsorKeysAsInt_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.selectorMaterialKeyAsName
// 0x0028 (0x0028 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_selectorMaterialKeyAsName final
{
public:
	class UWDG_ShowroomMaterialColorSelector_C*   Selector;                                          // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   Name_0;                                            // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_IntToString_ReturnValue;             // 0x0010(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FName                                   CallFunc_Conv_StringToName_ReturnValue;            // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_selectorMaterialKeyAsName) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_selectorMaterialKeyAsName");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_selectorMaterialKeyAsName) == 0x000028, "Wrong size on WDG_ShowroomCustomCarOptions_C_selectorMaterialKeyAsName");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_selectorMaterialKeyAsName, Selector) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_selectorMaterialKeyAsName::Selector' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_selectorMaterialKeyAsName, Name_0) == 0x000008, "Member 'WDG_ShowroomCustomCarOptions_C_selectorMaterialKeyAsName::Name_0' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_selectorMaterialKeyAsName, CallFunc_Conv_IntToString_ReturnValue) == 0x000010, "Member 'WDG_ShowroomCustomCarOptions_C_selectorMaterialKeyAsName::CallFunc_Conv_IntToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_selectorMaterialKeyAsName, CallFunc_Conv_StringToName_ReturnValue) == 0x000020, "Member 'WDG_ShowroomCustomCarOptions_C_selectorMaterialKeyAsName::CallFunc_Conv_StringToName_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.selectorColorAndMaterial
// 0x0020 (0x0020 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_selectorColorAndMaterial final
{
public:
	class UWDG_ShowroomMaterialColorSelector_C*   Selector;                                          // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         ColorCode;                                         // 0x0008(0x0004)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   MaterialName;                                      // 0x000C(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   CallFunc_selectorMaterialKeyAsName_Name;           // 0x0014(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_selectorColorAndMaterial) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_selectorColorAndMaterial");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_selectorColorAndMaterial) == 0x000020, "Wrong size on WDG_ShowroomCustomCarOptions_C_selectorColorAndMaterial");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_selectorColorAndMaterial, Selector) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_selectorColorAndMaterial::Selector' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_selectorColorAndMaterial, ColorCode) == 0x000008, "Member 'WDG_ShowroomCustomCarOptions_C_selectorColorAndMaterial::ColorCode' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_selectorColorAndMaterial, MaterialName) == 0x00000C, "Member 'WDG_ShowroomCustomCarOptions_C_selectorColorAndMaterial::MaterialName' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_selectorColorAndMaterial, CallFunc_selectorMaterialKeyAsName_Name) == 0x000014, "Member 'WDG_ShowroomCustomCarOptions_C_selectorColorAndMaterial::CallFunc_selectorMaterialKeyAsName_Name' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.OnUpdateCarInfoValues
// 0x0040 (0x0040 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues final
{
public:
	int32                                         CallFunc_selectorColorAndMaterial_ColorCode;       // 0x0000(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   CallFunc_selectorColorAndMaterial_MaterialName;    // 0x0004(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_selectorColorAndMaterial_ColorCode_1;     // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   CallFunc_selectorColorAndMaterial_MaterialName_1;  // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_selectorColorAndMaterial_ColorCode_2;     // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   CallFunc_selectorColorAndMaterial_MaterialName_2;  // 0x001C(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_selectorColorAndMaterial_ColorCode_3;     // 0x0024(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   CallFunc_selectorColorAndMaterial_MaterialName_3;  // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_selectorColorAndMaterial_ColorCode_4;     // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   CallFunc_selectorColorAndMaterial_MaterialName_4;  // 0x0034(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_Conv_IntToByte_ReturnValue;               // 0x003C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues) == 0x000004, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues) == 0x000040, "Wrong size on WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues, CallFunc_selectorColorAndMaterial_ColorCode) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues::CallFunc_selectorColorAndMaterial_ColorCode' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues, CallFunc_selectorColorAndMaterial_MaterialName) == 0x000004, "Member 'WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues::CallFunc_selectorColorAndMaterial_MaterialName' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues, CallFunc_selectorColorAndMaterial_ColorCode_1) == 0x00000C, "Member 'WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues::CallFunc_selectorColorAndMaterial_ColorCode_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues, CallFunc_selectorColorAndMaterial_MaterialName_1) == 0x000010, "Member 'WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues::CallFunc_selectorColorAndMaterial_MaterialName_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues, CallFunc_selectorColorAndMaterial_ColorCode_2) == 0x000018, "Member 'WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues::CallFunc_selectorColorAndMaterial_ColorCode_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues, CallFunc_selectorColorAndMaterial_MaterialName_2) == 0x00001C, "Member 'WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues::CallFunc_selectorColorAndMaterial_MaterialName_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues, CallFunc_selectorColorAndMaterial_ColorCode_3) == 0x000024, "Member 'WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues::CallFunc_selectorColorAndMaterial_ColorCode_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues, CallFunc_selectorColorAndMaterial_MaterialName_3) == 0x000028, "Member 'WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues::CallFunc_selectorColorAndMaterial_MaterialName_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues, CallFunc_selectorColorAndMaterial_ColorCode_4) == 0x000030, "Member 'WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues::CallFunc_selectorColorAndMaterial_ColorCode_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues, CallFunc_selectorColorAndMaterial_MaterialName_4) == 0x000034, "Member 'WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues::CallFunc_selectorColorAndMaterial_MaterialName_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues, CallFunc_Conv_IntToByte_ReturnValue) == 0x00003C, "Member 'WDG_ShowroomCustomCarOptions_C_OnUpdateCarInfoValues::CallFunc_Conv_IntToByte_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.openColorPalette
// 0x0020 (0x0020 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_openColorPalette final
{
public:
	bool                                          for_aux_lights;                                    // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomMaterialColorSelector_C*   Sender;                                            // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomPalette_C*                 colorPalette_0;                                    // 0x0010(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      CallFunc_Open_FocusedTile;                         // 0x0018(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_openColorPalette) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_openColorPalette");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_openColorPalette) == 0x000020, "Wrong size on WDG_ShowroomCustomCarOptions_C_openColorPalette");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_openColorPalette, for_aux_lights) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_openColorPalette::for_aux_lights' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_openColorPalette, Sender) == 0x000008, "Member 'WDG_ShowroomCustomCarOptions_C_openColorPalette::Sender' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_openColorPalette, colorPalette_0) == 0x000010, "Member 'WDG_ShowroomCustomCarOptions_C_openColorPalette::colorPalette_0' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_openColorPalette, CallFunc_Open_FocusedTile) == 0x000018, "Member 'WDG_ShowroomCustomCarOptions_C_openColorPalette::CallFunc_Open_FocusedTile' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.ForEachPaletteWrapper
// 0x0040 (0x0040 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper final
{
public:
	class UPanelWidget*                           Wrapper;                                           // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomMaterialColorSelector_C*   Sender;                                            // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_12[0x2];                                       // 0x0012(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_GetChildrenCount_ReturnValue;             // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable;                                 // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWidget*                                CallFunc_GetChildAt_ReturnValue;                   // 0x0020(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      K2Node_DynamicCast_AsWDG_Showroom_Tile_Item_Horizontal; // 0x0028(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_31[0x3];                                       // 0x0031(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0034(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x0039(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x003A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper) == 0x000040, "Wrong size on WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper, Wrapper) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper::Wrapper' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper, Sender) == 0x000008, "Member 'WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper::Sender' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper, Temp_bool_Variable) == 0x000010, "Member 'WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper, CallFunc_Not_PreBool_ReturnValue) == 0x000011, "Member 'WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper, CallFunc_GetChildrenCount_ReturnValue) == 0x000014, "Member 'WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper::CallFunc_GetChildrenCount_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper, CallFunc_Subtract_IntInt_ReturnValue) == 0x000018, "Member 'WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper, Temp_int_Variable) == 0x00001C, "Member 'WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper, CallFunc_GetChildAt_ReturnValue) == 0x000020, "Member 'WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper::CallFunc_GetChildAt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper, K2Node_DynamicCast_AsWDG_Showroom_Tile_Item_Horizontal) == 0x000028, "Member 'WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper::K2Node_DynamicCast_AsWDG_Showroom_Tile_Item_Horizontal' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper, K2Node_DynamicCast_bSuccess) == 0x000030, "Member 'WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper, CallFunc_Add_IntInt_ReturnValue) == 0x000034, "Member 'WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper, CallFunc_LessEqual_IntInt_ReturnValue) == 0x000038, "Member 'WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x000039, "Member 'WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper, CallFunc_BooleanAND_ReturnValue) == 0x00003A, "Member 'WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.getNationalities
// 0x0098 (0x0098 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_getNationalities final
{
public:
	class UDataTable*                             NationalitiesTable;                                // 0x0000(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_14[0x4];                                       // 0x0014(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<struct FNationality>                   CallFunc_GetNationalitiesSorted_ReturnValue;       // 0x0018(0x0010)(ReferenceParm)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2C[0x4];                                       // 0x002C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FNationality                           CallFunc_Array_Get_Item;                           // 0x0030(0x0060)()
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0090(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_91[0x3];                                       // 0x0091(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Conv_ByteToInt_ReturnValue;               // 0x0094(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_getNationalities) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_getNationalities");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_getNationalities) == 0x000098, "Wrong size on WDG_ShowroomCustomCarOptions_C_getNationalities");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getNationalities, NationalitiesTable) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_getNationalities::NationalitiesTable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getNationalities, Temp_int_Array_Index_Variable) == 0x000008, "Member 'WDG_ShowroomCustomCarOptions_C_getNationalities::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getNationalities, Temp_int_Loop_Counter_Variable) == 0x00000C, "Member 'WDG_ShowroomCustomCarOptions_C_getNationalities::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getNationalities, CallFunc_Add_IntInt_ReturnValue) == 0x000010, "Member 'WDG_ShowroomCustomCarOptions_C_getNationalities::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getNationalities, CallFunc_GetNationalitiesSorted_ReturnValue) == 0x000018, "Member 'WDG_ShowroomCustomCarOptions_C_getNationalities::CallFunc_GetNationalitiesSorted_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getNationalities, CallFunc_Array_Length_ReturnValue) == 0x000028, "Member 'WDG_ShowroomCustomCarOptions_C_getNationalities::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getNationalities, CallFunc_Array_Get_Item) == 0x000030, "Member 'WDG_ShowroomCustomCarOptions_C_getNationalities::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getNationalities, CallFunc_Less_IntInt_ReturnValue) == 0x000090, "Member 'WDG_ShowroomCustomCarOptions_C_getNationalities::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getNationalities, CallFunc_Conv_ByteToInt_ReturnValue) == 0x000094, "Member 'WDG_ShowroomCustomCarOptions_C_getNationalities::CallFunc_Conv_ByteToInt_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.getCups
// 0x0040 (0x0040 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_getCups final
{
public:
	int32                                         CallFunc_MakeLiteralInt_ReturnValue;               // 0x0000(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable;                                 // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_1;                               // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_Conv_IntToByte_ReturnValue;               // 0x000C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D[0x3];                                        // 0x000D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_GetValidValue_ReturnValue;                // 0x0014(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_Conv_IntToByte_ReturnValue_1;             // 0x0015(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_16[0x2];                                       // 0x0016(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Conv_ByteToInt_ReturnValue;               // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_GetEnumeratorValueFromIndex_ReturnValue;  // 0x001C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1D[0x3];                                       // 0x001D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_CupCategoryToText_Text;                   // 0x0020(0x0018)()
	int32                                         CallFunc_Conv_ByteToInt_ReturnValue_1;             // 0x0038(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x003C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_getCups) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_getCups");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_getCups) == 0x000040, "Wrong size on WDG_ShowroomCustomCarOptions_C_getCups");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getCups, CallFunc_MakeLiteralInt_ReturnValue) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_getCups::CallFunc_MakeLiteralInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getCups, Temp_int_Variable) == 0x000004, "Member 'WDG_ShowroomCustomCarOptions_C_getCups::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getCups, Temp_int_Variable_1) == 0x000008, "Member 'WDG_ShowroomCustomCarOptions_C_getCups::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getCups, CallFunc_Conv_IntToByte_ReturnValue) == 0x00000C, "Member 'WDG_ShowroomCustomCarOptions_C_getCups::CallFunc_Conv_IntToByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getCups, CallFunc_Add_IntInt_ReturnValue) == 0x000010, "Member 'WDG_ShowroomCustomCarOptions_C_getCups::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getCups, CallFunc_GetValidValue_ReturnValue) == 0x000014, "Member 'WDG_ShowroomCustomCarOptions_C_getCups::CallFunc_GetValidValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getCups, CallFunc_Conv_IntToByte_ReturnValue_1) == 0x000015, "Member 'WDG_ShowroomCustomCarOptions_C_getCups::CallFunc_Conv_IntToByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getCups, CallFunc_Conv_ByteToInt_ReturnValue) == 0x000018, "Member 'WDG_ShowroomCustomCarOptions_C_getCups::CallFunc_Conv_ByteToInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getCups, CallFunc_GetEnumeratorValueFromIndex_ReturnValue) == 0x00001C, "Member 'WDG_ShowroomCustomCarOptions_C_getCups::CallFunc_GetEnumeratorValueFromIndex_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getCups, CallFunc_CupCategoryToText_Text) == 0x000020, "Member 'WDG_ShowroomCustomCarOptions_C_getCups::CallFunc_CupCategoryToText_Text' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getCups, CallFunc_Conv_ByteToInt_ReturnValue_1) == 0x000038, "Member 'WDG_ShowroomCustomCarOptions_C_getCups::CallFunc_Conv_ByteToInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getCups, CallFunc_Less_IntInt_ReturnValue) == 0x00003C, "Member 'WDG_ShowroomCustomCarOptions_C_getCups::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.getBanners
// 0x0020 (0x0020 - 0x0000)
struct WDG_ShowroomCustomCarOptions_C_getBanners final
{
public:
	ECarModelType                                 CarModel;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcCarGraphicData*                      CallFunc_getCarGraphicData_CarGraphicData;         // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<int32>                                 CallFunc_getModelBannerKeysAsInt_ReturnValue;      // 0x0010(0x0010)(ReferenceParm)
};
static_assert(alignof(WDG_ShowroomCustomCarOptions_C_getBanners) == 0x000008, "Wrong alignment on WDG_ShowroomCustomCarOptions_C_getBanners");
static_assert(sizeof(WDG_ShowroomCustomCarOptions_C_getBanners) == 0x000020, "Wrong size on WDG_ShowroomCustomCarOptions_C_getBanners");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getBanners, CarModel) == 0x000000, "Member 'WDG_ShowroomCustomCarOptions_C_getBanners::CarModel' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getBanners, CallFunc_getCarGraphicData_CarGraphicData) == 0x000008, "Member 'WDG_ShowroomCustomCarOptions_C_getBanners::CallFunc_getCarGraphicData_CarGraphicData' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCustomCarOptions_C_getBanners, CallFunc_getModelBannerKeysAsInt_ReturnValue) == 0x000010, "Member 'WDG_ShowroomCustomCarOptions_C_getBanners::CallFunc_getModelBannerKeysAsInt_ReturnValue' has a wrong offset!");

}

