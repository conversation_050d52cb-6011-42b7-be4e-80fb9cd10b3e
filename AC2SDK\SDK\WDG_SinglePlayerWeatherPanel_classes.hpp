﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SinglePlayerWeatherPanel

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SinglePlayerWeatherPanel.WDG_SinglePlayerWeatherPanel_C
// 0x00E8 (0x06C8 - 0x05E0)
class UWDG_SinglePlayerWeatherPanel_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       Scale;                                             // 0x05E8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       Hover;                                             // 0x05F0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 BaseRed01;                                         // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 BaseRed02;                                         // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 BottomHighlight;                                   // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           CanvasPanel_3;                                     // 0x0610(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           CanvasPanel_4;                                     // 0x0618(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           CanvasPanel_7;                                     // 0x0620(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           CanvasPanel_8;                                     // 0x0628(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           CanvasPanel_9;                                     // 0x0630(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           CanvasPanel_10;                                    // 0x0638(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 HoverImageBox;                                     // 0x0640(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             IconSlot;                                          // 0x0648(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_3;                                           // 0x0650(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_4;                                           // 0x0658(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_5;                                           // 0x0660(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_9;                                           // 0x0668(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgDivisor;                                        // 0x0670(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgEventColor;                                     // 0x0678(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 LeftHighlight;                                     // 0x0680(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 NormalImageBox;                                    // 0x0688(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             PercentSlot;                                       // 0x0690(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 RightHighlight;                                    // 0x0698(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             TextSlot01;                                        // 0x06A0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             TextSlot02;                                        // 0x06A8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             TextSlot03;                                        // 0x06B0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             TitleSlot;                                         // 0x06B8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 TopHighlight;                                      // 0x06C0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_SinglePlayerWeatherPanel(int32 EntryPoint);
	void BP_MouseLeave();
	void BP_MouseOver();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SinglePlayerWeatherPanel_C">();
	}
	static class UWDG_SinglePlayerWeatherPanel_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SinglePlayerWeatherPanel_C>();
	}
};
static_assert(alignof(UWDG_SinglePlayerWeatherPanel_C) == 0x000008, "Wrong alignment on UWDG_SinglePlayerWeatherPanel_C");
static_assert(sizeof(UWDG_SinglePlayerWeatherPanel_C) == 0x0006C8, "Wrong size on UWDG_SinglePlayerWeatherPanel_C");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_SinglePlayerWeatherPanel_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, Scale) == 0x0005E8, "Member 'UWDG_SinglePlayerWeatherPanel_C::Scale' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, Hover) == 0x0005F0, "Member 'UWDG_SinglePlayerWeatherPanel_C::Hover' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, BaseRed01) == 0x0005F8, "Member 'UWDG_SinglePlayerWeatherPanel_C::BaseRed01' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, BaseRed02) == 0x000600, "Member 'UWDG_SinglePlayerWeatherPanel_C::BaseRed02' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, BottomHighlight) == 0x000608, "Member 'UWDG_SinglePlayerWeatherPanel_C::BottomHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, CanvasPanel_3) == 0x000610, "Member 'UWDG_SinglePlayerWeatherPanel_C::CanvasPanel_3' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, CanvasPanel_4) == 0x000618, "Member 'UWDG_SinglePlayerWeatherPanel_C::CanvasPanel_4' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, CanvasPanel_7) == 0x000620, "Member 'UWDG_SinglePlayerWeatherPanel_C::CanvasPanel_7' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, CanvasPanel_8) == 0x000628, "Member 'UWDG_SinglePlayerWeatherPanel_C::CanvasPanel_8' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, CanvasPanel_9) == 0x000630, "Member 'UWDG_SinglePlayerWeatherPanel_C::CanvasPanel_9' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, CanvasPanel_10) == 0x000638, "Member 'UWDG_SinglePlayerWeatherPanel_C::CanvasPanel_10' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, HoverImageBox) == 0x000640, "Member 'UWDG_SinglePlayerWeatherPanel_C::HoverImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, IconSlot) == 0x000648, "Member 'UWDG_SinglePlayerWeatherPanel_C::IconSlot' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, Image_3) == 0x000650, "Member 'UWDG_SinglePlayerWeatherPanel_C::Image_3' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, Image_4) == 0x000658, "Member 'UWDG_SinglePlayerWeatherPanel_C::Image_4' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, Image_5) == 0x000660, "Member 'UWDG_SinglePlayerWeatherPanel_C::Image_5' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, Image_9) == 0x000668, "Member 'UWDG_SinglePlayerWeatherPanel_C::Image_9' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, imgDivisor) == 0x000670, "Member 'UWDG_SinglePlayerWeatherPanel_C::imgDivisor' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, imgEventColor) == 0x000678, "Member 'UWDG_SinglePlayerWeatherPanel_C::imgEventColor' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, LeftHighlight) == 0x000680, "Member 'UWDG_SinglePlayerWeatherPanel_C::LeftHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, NormalImageBox) == 0x000688, "Member 'UWDG_SinglePlayerWeatherPanel_C::NormalImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, PercentSlot) == 0x000690, "Member 'UWDG_SinglePlayerWeatherPanel_C::PercentSlot' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, RightHighlight) == 0x000698, "Member 'UWDG_SinglePlayerWeatherPanel_C::RightHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, TextSlot01) == 0x0006A0, "Member 'UWDG_SinglePlayerWeatherPanel_C::TextSlot01' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, TextSlot02) == 0x0006A8, "Member 'UWDG_SinglePlayerWeatherPanel_C::TextSlot02' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, TextSlot03) == 0x0006B0, "Member 'UWDG_SinglePlayerWeatherPanel_C::TextSlot03' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, TitleSlot) == 0x0006B8, "Member 'UWDG_SinglePlayerWeatherPanel_C::TitleSlot' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerWeatherPanel_C, TopHighlight) == 0x0006C0, "Member 'UWDG_SinglePlayerWeatherPanel_C::TopHighlight' has a wrong offset!");

}

