﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_MultiplayerQuickjoinPage

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "Engine_structs.hpp"
#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.ExecuteUbergraph_WDG_MultiplayerQuickjoinPage
// 0x03B8 (0x03B8 - 0x0000)
struct WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   Temp_name_Variable;                                // 0x0004(0x0008)(ConstParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Is_DLC_Car_Available_ReturnValue;         // 0x000C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_D[0x3];                                        // 0x000D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue;              // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance;             // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_21[0x7];                                       // 0x0021(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UOnlineServices*                        CallFunc_GetOnlineServices_ReturnValue;            // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_31[0x7];                                       // 0x0031(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue_1;            // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance_1;           // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x0048(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_Is_DLC_Car_Available_ReturnValue_1;       // 0x0049(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_4A[0x6];                                       // 0x004A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	TSubclassOf<class UAcPageBase>                CallFunc_Map_Find_Value;                           // 0x0050(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          CallFunc_Map_Find_ReturnValue;                     // 0x0058(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_59[0x7];                                       // 0x0059(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPageBase*                            CallFunc_GoToPage_ReturnValue;                     // 0x0060(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FOnlineServicesMPServerInfo            K2Node_Event_legacy_competition_server;            // 0x0068(0x00B0)(ConstParm)
	struct FOnlineServicesMPQuickjoinPanelInfo    K2Node_Event_quickjoin_info;                       // 0x0118(0x0028)(ConstParm, NoDestructor)
	struct FOnlineServicesCPInvitationState       K2Node_Event_competition_server_invitation;        // 0x0140(0x0098)(ConstParm)
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue_2;            // 0x01D8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance_2;           // 0x01E0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_2;                     // 0x01E8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_1E9[0x3];                                      // 0x01E9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FFocusEvent                            K2Node_Event_InFocusEvent;                         // 0x01EC(0x0008)(NoDestructor)
	uint8                                         Pad_1F4[0x4];                                      // 0x01F4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPageBase*                            CallFunc_GoToPage_ReturnValue_1;                   // 0x01F8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x0200(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_201[0x7];                                      // 0x0201(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue_3;            // 0x0208(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance_3;           // 0x0210(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_3;                     // 0x0218(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_219[0x3];                                      // 0x0219(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate;              // 0x021C(0x0010)(ZeroConstructor, NoDestructor)
	uint8                                         Pad_22C[0x4];                                      // 0x022C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTimerHandle                           CallFunc_K2_SetTimerDelegate_ReturnValue;          // 0x0230(0x0008)(NoDestructor, HasGetValueTypeHash)
	class UAcPageBase*                            CallFunc_GoToPage_ReturnValue_2;                   // 0x0238(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FOnlineServicesCPInvitationState       K2Node_CustomEvent_invitation_update;              // 0x0240(0x0098)(ConstParm)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x02D8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2D9[0x7];                                      // 0x02D9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue;               // 0x02E0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EMPCarGroup                                   K2Node_ComponentBoundEvent_CarGroup_2;             // 0x02E8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2E9[0x7];                                      // 0x02E9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue_1;             // 0x02F0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EMPCarGroup                                   K2Node_ComponentBoundEvent_CarGroup_1;             // 0x02F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x02F9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	EMPCarGroup                                   K2Node_ComponentBoundEvent_CarGroup;               // 0x02FA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2FB[0x5];                                      // 0x02FB(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPanelBase*                           K2Node_ComponentBoundEvent_Panel_3;                // 0x0300(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcPanelBase*                           K2Node_ComponentBoundEvent_Panel_2;                // 0x0308(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcPanelBase*                           K2Node_ComponentBoundEvent_Panel_1;                // 0x0310(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcPanelBase*                           K2Node_ComponentBoundEvent_Panel;                  // 0x0318(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EDisconnectionReason                          K2Node_Event_reason;                               // 0x0320(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess_1;                    // 0x0321(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_322[0x6];                                      // 0x0322(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue_2;             // 0x0328(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_HasFocusedDescendants_ReturnValue;        // 0x0330(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0331(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0332(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_333[0x5];                                      // 0x0333(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0338(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EContentType                                  K2Node_Event_content_type;                         // 0x0340(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_341[0x7];                                      // 0x0341(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationReverse_ReturnValue;         // 0x0348(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue_4;            // 0x0350(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance_4;           // 0x0358(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_4;                     // 0x0360(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_361[0x3];                                      // 0x0361(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate_1;            // 0x0364(0x0010)(ZeroConstructor, NoDestructor)
	uint8                                         Pad_374[0x4];                                      // 0x0374(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPageBase*                            CallFunc_GetCurrentPage_ReturnValue;               // 0x0378(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FTimerHandle                           CallFunc_K2_SetTimerDelegate_ReturnValue_1;        // 0x0380(0x0008)(NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ObjectObject_ReturnValue;      // 0x0388(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsValid_ReturnValue_2;                    // 0x0389(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_38A[0x2];                                      // 0x038A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate_2;            // 0x038C(0x0010)(ZeroConstructor, NoDestructor)
	uint8                                         Pad_39C[0x4];                                      // 0x039C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTimerHandle                           CallFunc_K2_SetTimerDelegate_ReturnValue_2;        // 0x03A0(0x0008)(NoDestructor, HasGetValueTypeHash)
	TDelegate<void(const struct FOnlineServicesCPInvitationState& invitation_update)> K2Node_CreateDelegate_OutputDelegate_3; // 0x03A8(0x0010)(ZeroConstructor, NoDestructor)
};
static_assert(alignof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage) == 0x000008, "Wrong alignment on WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage");
static_assert(sizeof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage) == 0x0003B8, "Wrong size on WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, EntryPoint) == 0x000000, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, Temp_name_Variable) == 0x000004, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::Temp_name_Variable' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_Is_DLC_Car_Available_ReturnValue) == 0x00000C, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_Is_DLC_Car_Available_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_GetGameInstance_ReturnValue) == 0x000010, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_GetGameInstance_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_DynamicCast_AsAc_Game_Instance) == 0x000018, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_DynamicCast_AsAc_Game_Instance' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_DynamicCast_bSuccess) == 0x000020, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_GetOnlineServices_ReturnValue) == 0x000028, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_GetOnlineServices_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_IsValid_ReturnValue) == 0x000030, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_GetGameInstance_ReturnValue_1) == 0x000038, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_GetGameInstance_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_DynamicCast_AsAc_Game_Instance_1) == 0x000040, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_DynamicCast_AsAc_Game_Instance_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_DynamicCast_bSuccess_1) == 0x000048, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_Is_DLC_Car_Available_ReturnValue_1) == 0x000049, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_Is_DLC_Car_Available_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_Map_Find_Value) == 0x000050, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_Map_Find_Value' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_Map_Find_ReturnValue) == 0x000058, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_Map_Find_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_GoToPage_ReturnValue) == 0x000060, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_GoToPage_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_Event_legacy_competition_server) == 0x000068, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_Event_legacy_competition_server' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_Event_quickjoin_info) == 0x000118, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_Event_quickjoin_info' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_Event_competition_server_invitation) == 0x000140, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_Event_competition_server_invitation' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_GetGameInstance_ReturnValue_2) == 0x0001D8, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_GetGameInstance_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_DynamicCast_AsAc_Game_Instance_2) == 0x0001E0, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_DynamicCast_AsAc_Game_Instance_2' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_DynamicCast_bSuccess_2) == 0x0001E8, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_DynamicCast_bSuccess_2' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_Event_InFocusEvent) == 0x0001EC, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_Event_InFocusEvent' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_GoToPage_ReturnValue_1) == 0x0001F8, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_GoToPage_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_IsValid_ReturnValue_1) == 0x000200, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_GetGameInstance_ReturnValue_3) == 0x000208, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_GetGameInstance_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_DynamicCast_AsAc_Game_Instance_3) == 0x000210, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_DynamicCast_AsAc_Game_Instance_3' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_DynamicCast_bSuccess_3) == 0x000218, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_DynamicCast_bSuccess_3' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_CreateDelegate_OutputDelegate) == 0x00021C, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_K2_SetTimerDelegate_ReturnValue) == 0x000230, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_K2_SetTimerDelegate_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_GoToPage_ReturnValue_2) == 0x000238, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_GoToPage_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_CustomEvent_invitation_update) == 0x000240, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_CustomEvent_invitation_update' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x0002D8, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_GetMenuManager_ReturnValue) == 0x0002E0, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_GetMenuManager_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_ComponentBoundEvent_CarGroup_2) == 0x0002E8, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_ComponentBoundEvent_CarGroup_2' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_GetMenuManager_ReturnValue_1) == 0x0002F0, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_GetMenuManager_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_ComponentBoundEvent_CarGroup_1) == 0x0002F8, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_ComponentBoundEvent_CarGroup_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_SwitchEnum_CmpSuccess) == 0x0002F9, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_ComponentBoundEvent_CarGroup) == 0x0002FA, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_ComponentBoundEvent_CarGroup' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_ComponentBoundEvent_Panel_3) == 0x000300, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_ComponentBoundEvent_Panel_3' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_ComponentBoundEvent_Panel_2) == 0x000308, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_ComponentBoundEvent_Panel_2' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_ComponentBoundEvent_Panel_1) == 0x000310, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_ComponentBoundEvent_Panel_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_ComponentBoundEvent_Panel) == 0x000318, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_ComponentBoundEvent_Panel' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_Event_reason) == 0x000320, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_Event_reason' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_SwitchEnum_CmpSuccess_1) == 0x000321, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_SwitchEnum_CmpSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_GetMenuManager_ReturnValue_2) == 0x000328, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_GetMenuManager_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_HasFocusedDescendants_ReturnValue) == 0x000330, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_HasFocusedDescendants_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_Not_PreBool_ReturnValue) == 0x000331, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_BooleanAND_ReturnValue) == 0x000332, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_PlayAnimation_ReturnValue) == 0x000338, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_Event_content_type) == 0x000340, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_Event_content_type' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_PlayAnimationReverse_ReturnValue) == 0x000348, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_PlayAnimationReverse_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_GetGameInstance_ReturnValue_4) == 0x000350, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_GetGameInstance_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_DynamicCast_AsAc_Game_Instance_4) == 0x000358, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_DynamicCast_AsAc_Game_Instance_4' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_DynamicCast_bSuccess_4) == 0x000360, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_DynamicCast_bSuccess_4' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_CreateDelegate_OutputDelegate_1) == 0x000364, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_CreateDelegate_OutputDelegate_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_GetCurrentPage_ReturnValue) == 0x000378, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_GetCurrentPage_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_K2_SetTimerDelegate_ReturnValue_1) == 0x000380, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_K2_SetTimerDelegate_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_EqualEqual_ObjectObject_ReturnValue) == 0x000388, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_EqualEqual_ObjectObject_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_IsValid_ReturnValue_2) == 0x000389, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_IsValid_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_CreateDelegate_OutputDelegate_2) == 0x00038C, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_CreateDelegate_OutputDelegate_2' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, CallFunc_K2_SetTimerDelegate_ReturnValue_2) == 0x0003A0, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::CallFunc_K2_SetTimerDelegate_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage, K2Node_CreateDelegate_OutputDelegate_3) == 0x0003A8, "Member 'WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage::K2Node_CreateDelegate_OutputDelegate_3' has a wrong offset!");

// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.OnSeasonUnavailable
// 0x0001 (0x0001 - 0x0000)
struct WDG_MultiplayerQuickjoinPage_C_OnSeasonUnavailable final
{
public:
	EContentType                                  content_type;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_MultiplayerQuickjoinPage_C_OnSeasonUnavailable) == 0x000001, "Wrong alignment on WDG_MultiplayerQuickjoinPage_C_OnSeasonUnavailable");
static_assert(sizeof(WDG_MultiplayerQuickjoinPage_C_OnSeasonUnavailable) == 0x000001, "Wrong size on WDG_MultiplayerQuickjoinPage_C_OnSeasonUnavailable");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_OnSeasonUnavailable, content_type) == 0x000000, "Member 'WDG_MultiplayerQuickjoinPage_C_OnSeasonUnavailable::content_type' has a wrong offset!");

// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.OnServerDisconnection
// 0x0001 (0x0001 - 0x0000)
struct WDG_MultiplayerQuickjoinPage_C_OnServerDisconnection final
{
public:
	EDisconnectionReason                          reason;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_MultiplayerQuickjoinPage_C_OnServerDisconnection) == 0x000001, "Wrong alignment on WDG_MultiplayerQuickjoinPage_C_OnServerDisconnection");
static_assert(sizeof(WDG_MultiplayerQuickjoinPage_C_OnServerDisconnection) == 0x000001, "Wrong size on WDG_MultiplayerQuickjoinPage_C_OnServerDisconnection");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_OnServerDisconnection, reason) == 0x000000, "Member 'WDG_MultiplayerQuickjoinPage_C_OnServerDisconnection::reason' has a wrong offset!");

// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_13_OnAcPanelFocusEvent__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_13_OnAcPanelFocusEvent__DelegateSignature final
{
public:
	class UAcPanelBase*                           panel;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_13_OnAcPanelFocusEvent__DelegateSignature) == 0x000008, "Wrong alignment on WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_13_OnAcPanelFocusEvent__DelegateSignature");
static_assert(sizeof(WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_13_OnAcPanelFocusEvent__DelegateSignature) == 0x000008, "Wrong size on WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_13_OnAcPanelFocusEvent__DelegateSignature");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_13_OnAcPanelFocusEvent__DelegateSignature, panel) == 0x000000, "Member 'WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_13_OnAcPanelFocusEvent__DelegateSignature::panel' has a wrong offset!");

// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_11_OnAcPanelFocusEvent__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_11_OnAcPanelFocusEvent__DelegateSignature final
{
public:
	class UAcPanelBase*                           panel;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_11_OnAcPanelFocusEvent__DelegateSignature) == 0x000008, "Wrong alignment on WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_11_OnAcPanelFocusEvent__DelegateSignature");
static_assert(sizeof(WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_11_OnAcPanelFocusEvent__DelegateSignature) == 0x000008, "Wrong size on WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_11_OnAcPanelFocusEvent__DelegateSignature");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_11_OnAcPanelFocusEvent__DelegateSignature, panel) == 0x000000, "Member 'WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_11_OnAcPanelFocusEvent__DelegateSignature::panel' has a wrong offset!");

// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_12_OnAcPanelFocusEvent__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_12_OnAcPanelFocusEvent__DelegateSignature final
{
public:
	class UAcPanelBase*                           panel;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_12_OnAcPanelFocusEvent__DelegateSignature) == 0x000008, "Wrong alignment on WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_12_OnAcPanelFocusEvent__DelegateSignature");
static_assert(sizeof(WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_12_OnAcPanelFocusEvent__DelegateSignature) == 0x000008, "Wrong size on WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_12_OnAcPanelFocusEvent__DelegateSignature");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_12_OnAcPanelFocusEvent__DelegateSignature, panel) == 0x000000, "Member 'WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_12_OnAcPanelFocusEvent__DelegateSignature::panel' has a wrong offset!");

// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_10_OnAcPanelFocusEvent__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_10_OnAcPanelFocusEvent__DelegateSignature final
{
public:
	class UAcPanelBase*                           panel;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_10_OnAcPanelFocusEvent__DelegateSignature) == 0x000008, "Wrong alignment on WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_10_OnAcPanelFocusEvent__DelegateSignature");
static_assert(sizeof(WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_10_OnAcPanelFocusEvent__DelegateSignature) == 0x000008, "Wrong size on WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_10_OnAcPanelFocusEvent__DelegateSignature");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_10_OnAcPanelFocusEvent__DelegateSignature, panel) == 0x000000, "Member 'WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_10_OnAcPanelFocusEvent__DelegateSignature::panel' has a wrong offset!");

// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_9_OnCarGroupBlurred__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_MultiplayerQuickjoinPage_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_9_OnCarGroupBlurred__DelegateSignature final
{
public:
	EMPCarGroup                                   CarGroup;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_MultiplayerQuickjoinPage_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_9_OnCarGroupBlurred__DelegateSignature) == 0x000001, "Wrong alignment on WDG_MultiplayerQuickjoinPage_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_9_OnCarGroupBlurred__DelegateSignature");
static_assert(sizeof(WDG_MultiplayerQuickjoinPage_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_9_OnCarGroupBlurred__DelegateSignature) == 0x000001, "Wrong size on WDG_MultiplayerQuickjoinPage_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_9_OnCarGroupBlurred__DelegateSignature");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_9_OnCarGroupBlurred__DelegateSignature, CarGroup) == 0x000000, "Member 'WDG_MultiplayerQuickjoinPage_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_9_OnCarGroupBlurred__DelegateSignature::CarGroup' has a wrong offset!");

// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_8_OnCarGroupFocused__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_MultiplayerQuickjoinPage_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_8_OnCarGroupFocused__DelegateSignature final
{
public:
	EMPCarGroup                                   CarGroup;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_MultiplayerQuickjoinPage_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_8_OnCarGroupFocused__DelegateSignature) == 0x000001, "Wrong alignment on WDG_MultiplayerQuickjoinPage_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_8_OnCarGroupFocused__DelegateSignature");
static_assert(sizeof(WDG_MultiplayerQuickjoinPage_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_8_OnCarGroupFocused__DelegateSignature) == 0x000001, "Wrong size on WDG_MultiplayerQuickjoinPage_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_8_OnCarGroupFocused__DelegateSignature");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_8_OnCarGroupFocused__DelegateSignature, CarGroup) == 0x000000, "Member 'WDG_MultiplayerQuickjoinPage_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_8_OnCarGroupFocused__DelegateSignature::CarGroup' has a wrong offset!");

// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_7_OnCarGroupSelected__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_MultiplayerQuickjoinPage_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_7_OnCarGroupSelected__DelegateSignature final
{
public:
	EMPCarGroup                                   CarGroup;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_MultiplayerQuickjoinPage_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_7_OnCarGroupSelected__DelegateSignature) == 0x000001, "Wrong alignment on WDG_MultiplayerQuickjoinPage_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_7_OnCarGroupSelected__DelegateSignature");
static_assert(sizeof(WDG_MultiplayerQuickjoinPage_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_7_OnCarGroupSelected__DelegateSignature) == 0x000001, "Wrong size on WDG_MultiplayerQuickjoinPage_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_7_OnCarGroupSelected__DelegateSignature");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_7_OnCarGroupSelected__DelegateSignature, CarGroup) == 0x000000, "Member 'WDG_MultiplayerQuickjoinPage_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_7_OnCarGroupSelected__DelegateSignature::CarGroup' has a wrong offset!");

// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.OnCPInviteUpdate
// 0x0098 (0x0098 - 0x0000)
struct WDG_MultiplayerQuickjoinPage_C_OnCPInviteUpdate final
{
public:
	struct FOnlineServicesCPInvitationState       invitation_update;                                 // 0x0000(0x0098)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_MultiplayerQuickjoinPage_C_OnCPInviteUpdate) == 0x000008, "Wrong alignment on WDG_MultiplayerQuickjoinPage_C_OnCPInviteUpdate");
static_assert(sizeof(WDG_MultiplayerQuickjoinPage_C_OnCPInviteUpdate) == 0x000098, "Wrong size on WDG_MultiplayerQuickjoinPage_C_OnCPInviteUpdate");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_OnCPInviteUpdate, invitation_update) == 0x000000, "Member 'WDG_MultiplayerQuickjoinPage_C_OnCPInviteUpdate::invitation_update' has a wrong offset!");

// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.OnRemovedFromFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_MultiplayerQuickjoinPage_C_OnRemovedFromFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_MultiplayerQuickjoinPage_C_OnRemovedFromFocusPath) == 0x000004, "Wrong alignment on WDG_MultiplayerQuickjoinPage_C_OnRemovedFromFocusPath");
static_assert(sizeof(WDG_MultiplayerQuickjoinPage_C_OnRemovedFromFocusPath) == 0x000008, "Wrong size on WDG_MultiplayerQuickjoinPage_C_OnRemovedFromFocusPath");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_OnRemovedFromFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_MultiplayerQuickjoinPage_C_OnRemovedFromFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.UpdateServerInfo
// 0x0170 (0x0170 - 0x0000)
struct WDG_MultiplayerQuickjoinPage_C_UpdateServerInfo final
{
public:
	struct FOnlineServicesMPServerInfo            legacy_competition_server;                         // 0x0000(0x00B0)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	struct FOnlineServicesMPQuickjoinPanelInfo    quickjoin_info;                                    // 0x00B0(0x0028)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)
	struct FOnlineServicesCPInvitationState       competition_server_invitation;                     // 0x00D8(0x0098)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_MultiplayerQuickjoinPage_C_UpdateServerInfo) == 0x000008, "Wrong alignment on WDG_MultiplayerQuickjoinPage_C_UpdateServerInfo");
static_assert(sizeof(WDG_MultiplayerQuickjoinPage_C_UpdateServerInfo) == 0x000170, "Wrong size on WDG_MultiplayerQuickjoinPage_C_UpdateServerInfo");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_UpdateServerInfo, legacy_competition_server) == 0x000000, "Member 'WDG_MultiplayerQuickjoinPage_C_UpdateServerInfo::legacy_competition_server' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_UpdateServerInfo, quickjoin_info) == 0x0000B0, "Member 'WDG_MultiplayerQuickjoinPage_C_UpdateServerInfo::quickjoin_info' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_UpdateServerInfo, competition_server_invitation) == 0x0000D8, "Member 'WDG_MultiplayerQuickjoinPage_C_UpdateServerInfo::competition_server_invitation' has a wrong offset!");

// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.Get_txtStatus_Text_0
// 0x0018 (0x0018 - 0x0000)
struct WDG_MultiplayerQuickjoinPage_C_Get_txtStatus_Text_0 final
{
public:
	class FText                                   ReturnValue;                                       // 0x0000(0x0018)(Parm, OutParm, ReturnParm)
};
static_assert(alignof(WDG_MultiplayerQuickjoinPage_C_Get_txtStatus_Text_0) == 0x000008, "Wrong alignment on WDG_MultiplayerQuickjoinPage_C_Get_txtStatus_Text_0");
static_assert(sizeof(WDG_MultiplayerQuickjoinPage_C_Get_txtStatus_Text_0) == 0x000018, "Wrong size on WDG_MultiplayerQuickjoinPage_C_Get_txtStatus_Text_0");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_Get_txtStatus_Text_0, ReturnValue) == 0x000000, "Member 'WDG_MultiplayerQuickjoinPage_C_Get_txtStatus_Text_0::ReturnValue' has a wrong offset!");

// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.Is DLC Car Available
// 0x0100 (0x0100 - 0x0000)
struct WDG_MultiplayerQuickjoinPage_C_Is_DLC_Car_Available final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue;               // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   CallFunc_GetMultiplayerCar_ReturnValue;            // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FCarInfo                               CallFunc_GetCarInfoByName_Destination;             // 0x0018(0x00E0)()
	bool                                          CallFunc_GetCarInfoByName_ReturnValue;             // 0x00F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_HasAccessToContent_ReturnValue;           // 0x00F9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_MultiplayerQuickjoinPage_C_Is_DLC_Car_Available) == 0x000008, "Wrong alignment on WDG_MultiplayerQuickjoinPage_C_Is_DLC_Car_Available");
static_assert(sizeof(WDG_MultiplayerQuickjoinPage_C_Is_DLC_Car_Available) == 0x000100, "Wrong size on WDG_MultiplayerQuickjoinPage_C_Is_DLC_Car_Available");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_Is_DLC_Car_Available, ReturnValue) == 0x000000, "Member 'WDG_MultiplayerQuickjoinPage_C_Is_DLC_Car_Available::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_Is_DLC_Car_Available, CallFunc_GetMenuManager_ReturnValue) == 0x000008, "Member 'WDG_MultiplayerQuickjoinPage_C_Is_DLC_Car_Available::CallFunc_GetMenuManager_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_Is_DLC_Car_Available, CallFunc_GetMultiplayerCar_ReturnValue) == 0x000010, "Member 'WDG_MultiplayerQuickjoinPage_C_Is_DLC_Car_Available::CallFunc_GetMultiplayerCar_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_Is_DLC_Car_Available, CallFunc_GetCarInfoByName_Destination) == 0x000018, "Member 'WDG_MultiplayerQuickjoinPage_C_Is_DLC_Car_Available::CallFunc_GetCarInfoByName_Destination' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_Is_DLC_Car_Available, CallFunc_GetCarInfoByName_ReturnValue) == 0x0000F8, "Member 'WDG_MultiplayerQuickjoinPage_C_Is_DLC_Car_Available::CallFunc_GetCarInfoByName_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerQuickjoinPage_C_Is_DLC_Car_Available, CallFunc_HasAccessToContent_ReturnValue) == 0x0000F9, "Member 'WDG_MultiplayerQuickjoinPage_C_Is_DLC_Car_Available::CallFunc_HasAccessToContent_ReturnValue' has a wrong offset!");

}

