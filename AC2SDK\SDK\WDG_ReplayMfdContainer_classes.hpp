﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ReplayMfdContainer

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ReplayMfdContainer.WDG_ReplayMfdContainer_C
// 0x0000 (0x07E0 - 0x07E0)
class UWDG_ReplayMfdContainer_C final : public UAcRaceWidgetHost
{
public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ReplayMfdContainer_C">();
	}
	static class UWDG_ReplayMfdContainer_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ReplayMfdContainer_C>();
	}
};
static_assert(alignof(UWDG_ReplayMfdContainer_C) == 0x000008, "Wrong alignment on UWDG_ReplayMfdContainer_C");
static_assert(sizeof(UWDG_ReplayMfdContainer_C) == 0x0007E0, "Wrong size on UWDG_ReplayMfdContainer_C");

}

