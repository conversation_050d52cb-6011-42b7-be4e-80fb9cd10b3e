﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RatingDetailPCLeaderboardSeasonItem

#include "Basic.hpp"

#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RatingDetailPCLeaderboardSeasonItem.WDG_RatingDetailPCLeaderboardSeasonItem_C
// 0x0010 (0x0270 - 0x0260)
class UWDG_RatingDetailPCLeaderboardSeasonItem_C final : public UUserWidget
{
public:
	class UTextBlock*                             txtBlockSeasonNo;                                  // 0x0260(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UVerticalBox*                           vbox_LeaderboardRanks;                             // 0x0268(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void SetSeasonNo(int32 Season);
	void AddLeaderboard(const struct FOnlineServicesLeaderboardRank& Rank);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RatingDetailPCLeaderboardSeasonItem_C">();
	}
	static class UWDG_RatingDetailPCLeaderboardSeasonItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RatingDetailPCLeaderboardSeasonItem_C>();
	}
};
static_assert(alignof(UWDG_RatingDetailPCLeaderboardSeasonItem_C) == 0x000008, "Wrong alignment on UWDG_RatingDetailPCLeaderboardSeasonItem_C");
static_assert(sizeof(UWDG_RatingDetailPCLeaderboardSeasonItem_C) == 0x000270, "Wrong size on UWDG_RatingDetailPCLeaderboardSeasonItem_C");
static_assert(offsetof(UWDG_RatingDetailPCLeaderboardSeasonItem_C, txtBlockSeasonNo) == 0x000260, "Member 'UWDG_RatingDetailPCLeaderboardSeasonItem_C::txtBlockSeasonNo' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailPCLeaderboardSeasonItem_C, vbox_LeaderboardRanks) == 0x000268, "Member 'UWDG_RatingDetailPCLeaderboardSeasonItem_C::vbox_LeaderboardRanks' has a wrong offset!");

}

