﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TimeTableHeader

#include "Basic.hpp"

#include "WDG_TimeTableHeader_classes.hpp"
#include "WDG_TimeTableHeader_parameters.hpp"


namespace SDK
{

// Function WDG_TimeTableHeader.WDG_TimeTableHeader_C.SetSingleCarViewing
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    InBool                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_TimeTableHeader_C::SetSingleCarViewing(bool InBool)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TimeTableHeader_C", "SetSingleCarViewing");

	Params::WDG_TimeTableHeader_C_SetSingleCarViewing Parms{};

	Parms.InBool = InBool;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_TimeTableHeader.WDG_TimeTableHeader_C.SetShowingLaptimes
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    ShowFullTimingInfo                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
// bool                                    ShowTimingInfo                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_TimeTableHeader_C::SetShowingLaptimes(bool ShowFullTimingInfo, bool ShowTimingInfo)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TimeTableHeader_C", "SetShowingLaptimes");

	Params::WDG_TimeTableHeader_C_SetShowingLaptimes Parms{};

	Parms.ShowFullTimingInfo = ShowFullTimingInfo;
	Parms.ShowTimingInfo = ShowTimingInfo;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_TimeTableHeader.WDG_TimeTableHeader_C.SetCombinedLaps
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    IsCombinedLaps                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_TimeTableHeader_C::SetCombinedLaps(bool IsCombinedLaps)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TimeTableHeader_C", "SetCombinedLaps");

	Params::WDG_TimeTableHeader_C_SetCombinedLaps Parms{};

	Parms.IsCombinedLaps = IsCombinedLaps;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_TimeTableHeader.WDG_TimeTableHeader_C.SetShowingHandicap
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    ShowHandicaps                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_TimeTableHeader_C::SetShowingHandicap(bool ShowHandicaps)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TimeTableHeader_C", "SetShowingHandicap");

	Params::WDG_TimeTableHeader_C_SetShowingHandicap Parms{};

	Parms.ShowHandicaps = ShowHandicaps;

	UObject::ProcessEvent(Func, &Parms);
}

}

