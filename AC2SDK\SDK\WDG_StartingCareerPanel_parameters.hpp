﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_StartingCareerPanel

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"
#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_StartingCareerPanel.WDG_StartingCareerPanel_C.ExecuteUbergraph_WDG_StartingCareerPanel
// 0x0028 (0x0028 - 0x0000)
struct WDG_StartingCareerPanel_C_ExecuteUbergraph_WDG_StartingCareerPanel final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_1;              // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_2;              // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_3;              // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_StartingCareerPanel_C_ExecuteUbergraph_WDG_StartingCareerPanel) == 0x000008, "Wrong alignment on WDG_StartingCareerPanel_C_ExecuteUbergraph_WDG_StartingCareerPanel");
static_assert(sizeof(WDG_StartingCareerPanel_C_ExecuteUbergraph_WDG_StartingCareerPanel) == 0x000028, "Wrong size on WDG_StartingCareerPanel_C_ExecuteUbergraph_WDG_StartingCareerPanel");
static_assert(offsetof(WDG_StartingCareerPanel_C_ExecuteUbergraph_WDG_StartingCareerPanel, EntryPoint) == 0x000000, "Member 'WDG_StartingCareerPanel_C_ExecuteUbergraph_WDG_StartingCareerPanel::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_StartingCareerPanel_C_ExecuteUbergraph_WDG_StartingCareerPanel, CallFunc_PlayAnimation_ReturnValue) == 0x000008, "Member 'WDG_StartingCareerPanel_C_ExecuteUbergraph_WDG_StartingCareerPanel::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_StartingCareerPanel_C_ExecuteUbergraph_WDG_StartingCareerPanel, CallFunc_PlayAnimation_ReturnValue_1) == 0x000010, "Member 'WDG_StartingCareerPanel_C_ExecuteUbergraph_WDG_StartingCareerPanel::CallFunc_PlayAnimation_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_StartingCareerPanel_C_ExecuteUbergraph_WDG_StartingCareerPanel, CallFunc_PlayAnimation_ReturnValue_2) == 0x000018, "Member 'WDG_StartingCareerPanel_C_ExecuteUbergraph_WDG_StartingCareerPanel::CallFunc_PlayAnimation_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_StartingCareerPanel_C_ExecuteUbergraph_WDG_StartingCareerPanel, CallFunc_PlayAnimation_ReturnValue_3) == 0x000020, "Member 'WDG_StartingCareerPanel_C_ExecuteUbergraph_WDG_StartingCareerPanel::CallFunc_PlayAnimation_ReturnValue_3' has a wrong offset!");

// Function WDG_StartingCareerPanel.WDG_StartingCareerPanel_C.SetSlotColorText
// 0x0058 (0x0058 - 0x0000)
struct WDG_StartingCareerPanel_C_SetSlotColorText final
{
public:
	struct FLinearColor                           InColorAndOpacity_SpecifiedColor;                  // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSlateColor                            K2Node_MakeStruct_SlateColor;                      // 0x0010(0x0028)(UObjectWrapper)
	class UWidget*                                CallFunc_GetChildAt_ReturnValue;                   // 0x0038(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_HasAnyChildren_ReturnValue;               // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_41[0x7];                                       // 0x0041(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UTextBlock*                             K2Node_DynamicCast_AsText;                         // 0x0048(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0050(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
};
static_assert(alignof(WDG_StartingCareerPanel_C_SetSlotColorText) == 0x000008, "Wrong alignment on WDG_StartingCareerPanel_C_SetSlotColorText");
static_assert(sizeof(WDG_StartingCareerPanel_C_SetSlotColorText) == 0x000058, "Wrong size on WDG_StartingCareerPanel_C_SetSlotColorText");
static_assert(offsetof(WDG_StartingCareerPanel_C_SetSlotColorText, InColorAndOpacity_SpecifiedColor) == 0x000000, "Member 'WDG_StartingCareerPanel_C_SetSlotColorText::InColorAndOpacity_SpecifiedColor' has a wrong offset!");
static_assert(offsetof(WDG_StartingCareerPanel_C_SetSlotColorText, K2Node_MakeStruct_SlateColor) == 0x000010, "Member 'WDG_StartingCareerPanel_C_SetSlotColorText::K2Node_MakeStruct_SlateColor' has a wrong offset!");
static_assert(offsetof(WDG_StartingCareerPanel_C_SetSlotColorText, CallFunc_GetChildAt_ReturnValue) == 0x000038, "Member 'WDG_StartingCareerPanel_C_SetSlotColorText::CallFunc_GetChildAt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_StartingCareerPanel_C_SetSlotColorText, CallFunc_HasAnyChildren_ReturnValue) == 0x000040, "Member 'WDG_StartingCareerPanel_C_SetSlotColorText::CallFunc_HasAnyChildren_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_StartingCareerPanel_C_SetSlotColorText, K2Node_DynamicCast_AsText) == 0x000048, "Member 'WDG_StartingCareerPanel_C_SetSlotColorText::K2Node_DynamicCast_AsText' has a wrong offset!");
static_assert(offsetof(WDG_StartingCareerPanel_C_SetSlotColorText, K2Node_DynamicCast_bSuccess) == 0x000050, "Member 'WDG_StartingCareerPanel_C_SetSlotColorText::K2Node_DynamicCast_bSuccess' has a wrong offset!");

}

