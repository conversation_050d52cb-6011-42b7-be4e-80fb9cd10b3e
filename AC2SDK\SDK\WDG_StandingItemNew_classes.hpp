﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_StandingItemNew

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_StandingItemNew.WDG_StandingItemNew_C
// 0x0018 (0x03E0 - 0x03C8)
class UWDG_StandingItemNew_C final : public URaceStandingItems
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x03C8(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       DriverCar;                                         // 0x03D0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 imgGap;                                            // 0x03D8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_StandingItemNew(int32 EntryPoint);
	void Construct();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_StandingItemNew_C">();
	}
	static class UWDG_StandingItemNew_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_StandingItemNew_C>();
	}
};
static_assert(alignof(UWDG_StandingItemNew_C) == 0x000008, "Wrong alignment on UWDG_StandingItemNew_C");
static_assert(sizeof(UWDG_StandingItemNew_C) == 0x0003E0, "Wrong size on UWDG_StandingItemNew_C");
static_assert(offsetof(UWDG_StandingItemNew_C, UberGraphFrame) == 0x0003C8, "Member 'UWDG_StandingItemNew_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_StandingItemNew_C, DriverCar) == 0x0003D0, "Member 'UWDG_StandingItemNew_C::DriverCar' has a wrong offset!");
static_assert(offsetof(UWDG_StandingItemNew_C, imgGap) == 0x0003D8, "Member 'UWDG_StandingItemNew_C::imgGap' has a wrong offset!");

}

