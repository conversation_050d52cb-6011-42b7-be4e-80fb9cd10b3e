﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_MultiplayerMessage

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_MultiplayerMessage.WDG_MultiplayerMessage_C
// 0x0008 (0x06A0 - 0x0698)
class UWDG_MultiplayerMessage_C final : public UMultiplayerMessageWidget
{
public:
	class UImage*                                 imgTextBackground;                                 // 0x0698(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_MultiplayerMessage_C">();
	}
	static class UWDG_MultiplayerMessage_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_MultiplayerMessage_C>();
	}
};
static_assert(alignof(UWDG_MultiplayerMessage_C) == 0x000008, "Wrong alignment on UWDG_MultiplayerMessage_C");
static_assert(sizeof(UWDG_MultiplayerMessage_C) == 0x0006A0, "Wrong size on UWDG_MultiplayerMessage_C");
static_assert(offsetof(UWDG_MultiplayerMessage_C, imgTextBackground) == 0x000698, "Member 'UWDG_MultiplayerMessage_C::imgTextBackground' has a wrong offset!");

}

