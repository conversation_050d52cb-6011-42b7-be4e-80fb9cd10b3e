﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SimpleColumnChart

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SimpleColumnChart.WDG_SimpleColumnChart_C
// 0x0008 (0x0608 - 0x0600)
class UWDG_SimpleColumnChart_C final : public USimpleColumnChart
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0600(0x0008)(ZeroConstructor, Transient, DuplicateTransient)

public:
	void ExecuteUbergraph_WDG_SimpleColumnChart(int32 EntryPoint);
	void PreConstruct(bool IsDesignTime);
	void AddColumnWithColor(int32 ID, float Value, const struct FLinearColor& Color, bool isColumnNavigable);
	void ClearColumns();
	void AddColumn(int32 ID, float Value, bool isColumnNavigable);
	void WDG_AddSpacer();
	void WDG_AddColumn(class UWDG_SimpleColumnChartColumn_C* col);
	void OnColumnSelectedCallback(class UWDG_SimpleColumnChartColumn_C* selectedColumn);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SimpleColumnChart_C">();
	}
	static class UWDG_SimpleColumnChart_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SimpleColumnChart_C>();
	}
};
static_assert(alignof(UWDG_SimpleColumnChart_C) == 0x000008, "Wrong alignment on UWDG_SimpleColumnChart_C");
static_assert(sizeof(UWDG_SimpleColumnChart_C) == 0x000608, "Wrong size on UWDG_SimpleColumnChart_C");
static_assert(offsetof(UWDG_SimpleColumnChart_C, UberGraphFrame) == 0x000600, "Member 'UWDG_SimpleColumnChart_C::UberGraphFrame' has a wrong offset!");

}

