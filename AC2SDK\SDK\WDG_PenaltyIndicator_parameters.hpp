﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PenaltyIndicator

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "SlateCore_structs.hpp"
#include "Engine_structs.hpp"


namespace SDK::Params
{

// Function WDG_PenaltyIndicator.WDG_PenaltyIndicator_C.ExecuteUbergraph_WDG_PenaltyIndicator
// 0x0060 (0x0060 - 0x0000)
struct WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_5[0x3];                                        // 0x0005(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FGeometry                              K2Node_Event_MyGeometry;                           // 0x0008(0x0038)(IsPlainOldData, NoDestructor)
	float                                         K2Node_Event_InDeltaTime;                          // 0x0040(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_44[0x4];                                       // 0x0044(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class AGameModeBase*                          CallFunc_GetGameMode_ReturnValue;                  // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AAcRaceGameMode*                        K2Node_DynamicCast_AsAc_Race_Game_Mode;            // 0x0050(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0058(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	ERaceSessionPhase                             CallFunc_GetCurrentSessionPhaseUI_ReturnValue;     // 0x0059(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x005A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x005B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator) == 0x000008, "Wrong alignment on WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator");
static_assert(sizeof(WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator) == 0x000060, "Wrong size on WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator");
static_assert(offsetof(WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator, EntryPoint) == 0x000000, "Member 'WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator, K2Node_Event_IsDesignTime) == 0x000004, "Member 'WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator, K2Node_Event_MyGeometry) == 0x000008, "Member 'WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator::K2Node_Event_MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator, K2Node_Event_InDeltaTime) == 0x000040, "Member 'WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator::K2Node_Event_InDeltaTime' has a wrong offset!");
static_assert(offsetof(WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator, CallFunc_GetGameMode_ReturnValue) == 0x000048, "Member 'WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator::CallFunc_GetGameMode_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator, K2Node_DynamicCast_AsAc_Race_Game_Mode) == 0x000050, "Member 'WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator::K2Node_DynamicCast_AsAc_Race_Game_Mode' has a wrong offset!");
static_assert(offsetof(WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator, K2Node_DynamicCast_bSuccess) == 0x000058, "Member 'WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator, CallFunc_GetCurrentSessionPhaseUI_ReturnValue) == 0x000059, "Member 'WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator::CallFunc_GetCurrentSessionPhaseUI_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x00005A, "Member 'WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator, CallFunc_IsValid_ReturnValue) == 0x00005B, "Member 'WDG_PenaltyIndicator_C_ExecuteUbergraph_WDG_PenaltyIndicator::CallFunc_IsValid_ReturnValue' has a wrong offset!");

// Function WDG_PenaltyIndicator.WDG_PenaltyIndicator_C.Tick
// 0x003C (0x003C - 0x0000)
struct WDG_PenaltyIndicator_C_Tick final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	float                                         InDeltaTime;                                       // 0x0038(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_PenaltyIndicator_C_Tick) == 0x000004, "Wrong alignment on WDG_PenaltyIndicator_C_Tick");
static_assert(sizeof(WDG_PenaltyIndicator_C_Tick) == 0x00003C, "Wrong size on WDG_PenaltyIndicator_C_Tick");
static_assert(offsetof(WDG_PenaltyIndicator_C_Tick, MyGeometry) == 0x000000, "Member 'WDG_PenaltyIndicator_C_Tick::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_PenaltyIndicator_C_Tick, InDeltaTime) == 0x000038, "Member 'WDG_PenaltyIndicator_C_Tick::InDeltaTime' has a wrong offset!");

// Function WDG_PenaltyIndicator.WDG_PenaltyIndicator_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_PenaltyIndicator_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_PenaltyIndicator_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_PenaltyIndicator_C_PreConstruct");
static_assert(sizeof(WDG_PenaltyIndicator_C_PreConstruct) == 0x000001, "Wrong size on WDG_PenaltyIndicator_C_PreConstruct");
static_assert(offsetof(WDG_PenaltyIndicator_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_PenaltyIndicator_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WDG_PenaltyIndicator.WDG_PenaltyIndicator_C.SetPenalty
// 0x0098 (0x0098 - 0x0000)
struct WDG_PenaltyIndicator_C_SetPenalty final
{
public:
	EPenaltyType                                  PenaltyType_0;                                     // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         PenaltyWeight_0;                                   // 0x0001(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2[0x2];                                        // 0x0002(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Conv_ByteToInt_ReturnValue;               // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData;              // 0x0008(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0048(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_49[0x7];                                       // 0x0049(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<struct FFormatArgumentData>            K2Node_MakeArray_Array;                            // 0x0050(0x0010)(ReferenceParm)
	class FText                                   CallFunc_Format_ReturnValue;                       // 0x0060(0x0018)()
	class FText                                   CallFunc_Map_Find_Value;                           // 0x0078(0x0018)()
	bool                                          CallFunc_Map_Find_ReturnValue;                     // 0x0090(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_PenaltyIndicator_C_SetPenalty) == 0x000008, "Wrong alignment on WDG_PenaltyIndicator_C_SetPenalty");
static_assert(sizeof(WDG_PenaltyIndicator_C_SetPenalty) == 0x000098, "Wrong size on WDG_PenaltyIndicator_C_SetPenalty");
static_assert(offsetof(WDG_PenaltyIndicator_C_SetPenalty, PenaltyType_0) == 0x000000, "Member 'WDG_PenaltyIndicator_C_SetPenalty::PenaltyType_0' has a wrong offset!");
static_assert(offsetof(WDG_PenaltyIndicator_C_SetPenalty, PenaltyWeight_0) == 0x000001, "Member 'WDG_PenaltyIndicator_C_SetPenalty::PenaltyWeight_0' has a wrong offset!");
static_assert(offsetof(WDG_PenaltyIndicator_C_SetPenalty, CallFunc_Conv_ByteToInt_ReturnValue) == 0x000004, "Member 'WDG_PenaltyIndicator_C_SetPenalty::CallFunc_Conv_ByteToInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PenaltyIndicator_C_SetPenalty, K2Node_MakeStruct_FormatArgumentData) == 0x000008, "Member 'WDG_PenaltyIndicator_C_SetPenalty::K2Node_MakeStruct_FormatArgumentData' has a wrong offset!");
static_assert(offsetof(WDG_PenaltyIndicator_C_SetPenalty, K2Node_SwitchEnum_CmpSuccess) == 0x000048, "Member 'WDG_PenaltyIndicator_C_SetPenalty::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_PenaltyIndicator_C_SetPenalty, K2Node_MakeArray_Array) == 0x000050, "Member 'WDG_PenaltyIndicator_C_SetPenalty::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(WDG_PenaltyIndicator_C_SetPenalty, CallFunc_Format_ReturnValue) == 0x000060, "Member 'WDG_PenaltyIndicator_C_SetPenalty::CallFunc_Format_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PenaltyIndicator_C_SetPenalty, CallFunc_Map_Find_Value) == 0x000078, "Member 'WDG_PenaltyIndicator_C_SetPenalty::CallFunc_Map_Find_Value' has a wrong offset!");
static_assert(offsetof(WDG_PenaltyIndicator_C_SetPenalty, CallFunc_Map_Find_ReturnValue) == 0x000090, "Member 'WDG_PenaltyIndicator_C_SetPenalty::CallFunc_Map_Find_ReturnValue' has a wrong offset!");

}

