﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomCarGroupFilter

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"
#include "CoreUObject_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ShowroomCarGroupFilter.WDG_ShowroomCarGroupFilter_C
// 0x0068 (0x0648 - 0x05E0)
class UWDG_ShowroomCarGroupFilter_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UVerticalBox*                           boxBody;                                           // 0x05E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldD<PERSON>, <PERSON><PERSON>ki<PERSON>, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                brdActiveFilter;                                   // 0x05F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                Indicator;                                         // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtActiveFilter;                                   // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtSizeHolder;                                     // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	ECarGroup                                     activeFilter;                                      // 0x0610(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_611[0x3];                                      // 0x0611(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           OverColor;                                         // 0x0614(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_624[0x4];                                      // 0x0624(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TMulticastInlineDelegate<void(ECarGroup activeFilter)> OnFilterSelected;                         // 0x0628(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TArray<class UWDG_GenericBarItem_C*>          Items;                                             // 0x0638(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, ContainsInstancedReference)

public:
	void ExecuteUbergraph_WDG_ShowroomCarGroupFilter(int32 EntryPoint);
	void OnAfterConstruct();
	void OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent);
	void OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent);
	void OnForward_Event_0();
	void Hide();
	void show();
	void PreConstruct(bool IsDesignTime);
	void UpdateTextLabel();
	void SetActiveFilter(ECarGroup activeFilter_0);
	void ItemSelected(class UWDG_GenericBarItem_C* Item);
	void CreateItem(ECarGroup CarGroup, class UWDG_GenericBarItem_C** Item);
	void Populate(TArray<ECarGroup>& Source);
	void SetSizeHolderIfLonger(const class FText& text);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ShowroomCarGroupFilter_C">();
	}
	static class UWDG_ShowroomCarGroupFilter_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ShowroomCarGroupFilter_C>();
	}
};
static_assert(alignof(UWDG_ShowroomCarGroupFilter_C) == 0x000008, "Wrong alignment on UWDG_ShowroomCarGroupFilter_C");
static_assert(sizeof(UWDG_ShowroomCarGroupFilter_C) == 0x000648, "Wrong size on UWDG_ShowroomCarGroupFilter_C");
static_assert(offsetof(UWDG_ShowroomCarGroupFilter_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_ShowroomCarGroupFilter_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCarGroupFilter_C, boxBody) == 0x0005E8, "Member 'UWDG_ShowroomCarGroupFilter_C::boxBody' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCarGroupFilter_C, brdActiveFilter) == 0x0005F0, "Member 'UWDG_ShowroomCarGroupFilter_C::brdActiveFilter' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCarGroupFilter_C, Indicator) == 0x0005F8, "Member 'UWDG_ShowroomCarGroupFilter_C::Indicator' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCarGroupFilter_C, txtActiveFilter) == 0x000600, "Member 'UWDG_ShowroomCarGroupFilter_C::txtActiveFilter' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCarGroupFilter_C, txtSizeHolder) == 0x000608, "Member 'UWDG_ShowroomCarGroupFilter_C::txtSizeHolder' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCarGroupFilter_C, activeFilter) == 0x000610, "Member 'UWDG_ShowroomCarGroupFilter_C::activeFilter' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCarGroupFilter_C, OverColor) == 0x000614, "Member 'UWDG_ShowroomCarGroupFilter_C::OverColor' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCarGroupFilter_C, OnFilterSelected) == 0x000628, "Member 'UWDG_ShowroomCarGroupFilter_C::OnFilterSelected' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCarGroupFilter_C, Items) == 0x000638, "Member 'UWDG_ShowroomCarGroupFilter_C::Items' has a wrong offset!");

}

