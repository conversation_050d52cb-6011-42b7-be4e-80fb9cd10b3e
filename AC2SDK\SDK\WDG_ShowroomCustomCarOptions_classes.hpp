﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomCustomCarOptions

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C
// 0x0260 (0x0840 - 0x05E0)
class UWDG_ShowroomCustomCarOptions_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWDG_ShowroomPalette_C*                 colorPalette;                                      // 0x05E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSki<PERSON>, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomMaterialColorSelector_C*   selectorAuxLights;                                 // 0x05F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomMaterialColorSelector_C*   selectorRim1;                                      // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomMaterialColorSelector_C*   selectorRim2;                                      // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomMaterialColorSelector_C*   selectorSkin1;                                     // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomMaterialColorSelector_C*   selectorSkin2;                                     // 0x0610(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomMaterialColorSelector_C*   selectorSkin3;                                     // 0x0618(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_HorizontalSlider_C*                sliderBanner;                                      // 0x0620(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_HorizontalSlider_C*                sliderCup;                                         // 0x0628(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_HorizontalSlider_C*                sliderNationality;                                 // 0x0630(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_HorizontalSlider_C*                sliderSponsor;                                     // 0x0638(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_HorizontalSlider_C*                sliderTemplate;                                    // 0x0640(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	TArray<struct FSkinColor>                     NormalColors;                                      // 0x0648(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<struct FSkinColor>                     SpecialColors;                                     // 0x0658(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	struct FCarInfo                               CarInfo;                                           // 0x0668(0x00E0)(Edit, BlueprintVisible)
	TMulticastInlineDelegate<void(const struct FCarInfo& CarInfo)> OnValuesUpdated;                  // 0x0748(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	struct FCarInfo                               StoredCarInfo;                                     // 0x0758(0x00E0)(Edit, BlueprintVisible, DisableEditOnInstance)
	bool                                          SuppressEvents;                                    // 0x0838(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          IsDirty;                                           // 0x0839(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)

public:
	void ExecuteUbergraph_WDG_ShowroomCustomCarOptions(int32 EntryPoint);
	void BndEvt__sliderBanner_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature(class UGenericSelectorItem* Source, int32 current_index, int32 current_value);
	void BndEvt__sliderCup_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature(class UGenericSelectorItem* Source, int32 current_index, int32 current_value);
	void BndEvt__sliderNationality_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature(class UGenericSelectorItem* Source, int32 current_index, int32 current_value);
	void OnAfterConstruct();
	void BndEvt__selectorRim1_K2Node_ComponentBoundEvent_7_OnMaterialChanged__DelegateSignature(int32 MaterialKey);
	void BndEvt__sliderSponsor_K2Node_ComponentBoundEvent_8_SelectorChanged__DelegateSignature(class UGenericSelectorItem* Source, int32 current_index, int32 current_value);
	void BndEvt__sliderTemplate_K2Node_ComponentBoundEvent_7_SelectorChanged__DelegateSignature(class UGenericSelectorItem* Source, int32 current_index, int32 current_value);
	void OnModelUpdate(ECarModelType CarModel);
	void BndEvt__colorPalette_K2Node_ComponentBoundEvent_14_OnClosed__DelegateSignature(bool Cancel, class UWDG_ShowroomMaterialColorSelector_C* TargetSelector);
	void BndEvt__colorPalette_K2Node_ComponentBoundEvent_13_OnColorFocused__DelegateSignature(int32 ColorCode, class UWDG_ShowroomMaterialColorSelector_C* TargetSelector);
	void BndEvt__colorPalette_K2Node_ComponentBoundEvent_6_OnColorSelected__DelegateSignature(int32 Code, class UWDG_ShowroomMaterialColorSelector_C* TargetSelector);
	void BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_5_OnSelectColor__DelegateSignature(class UWDG_ShowroomMaterialColorSelector_C* Sender);
	void BndEvt__selectorRim2_K2Node_ComponentBoundEvent_4_OnSelectColor__DelegateSignature(class UWDG_ShowroomMaterialColorSelector_C* Sender);
	void BndEvt__selectorRim1_K2Node_ComponentBoundEvent_3_OnSelectColor__DelegateSignature(class UWDG_ShowroomMaterialColorSelector_C* Sender);
	void BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_2_OnSelectColor__DelegateSignature(class UWDG_ShowroomMaterialColorSelector_C* Sender);
	void BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_1_OnSelectColor__DelegateSignature(class UWDG_ShowroomMaterialColorSelector_C* Sender);
	void BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_0_OnSelectColor__DelegateSignature(class UWDG_ShowroomMaterialColorSelector_C* Sender);
	void BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_12_OnMaterialChanged__DelegateSignature(int32 MaterialKey);
	void BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_11_OnMaterialChanged__DelegateSignature(int32 MaterialKey);
	void BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_10_OnMaterialChanged__DelegateSignature(int32 MaterialKey);
	void BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_9_OnMaterialChanged__DelegateSignature(int32 MaterialKey);
	void BndEvt__selectorRim2_K2Node_ComponentBoundEvent_8_OnMaterialChanged__DelegateSignature(int32 MaterialKey);
	void GetSkinTemplates(ECarModelType CarModel);
	void updateKeySlider(class UGenericSelectorItem* Target, const TArray<int32>& SourceArray, const class FText& FirstOptionText);
	void getAuxLights(ECarModelType CarModel);
	void getCarGraphicData(class UAcCarGraphicData** CarGraphicData);
	void SetCarInfo(const struct FCarInfo& CarInfo_0);
	void OnCarInfoUpdated(bool SetDirty);
	void updateMaterialInSelector(class UWDG_ShowroomMaterialColorSelector_C* TargetSelector, bool IsRim);
	void getSponsors(ECarModelType CarModel);
	void selectorMaterialKeyAsName(class UWDG_ShowroomMaterialColorSelector_C* Selector, class FName* Name_0);
	void selectorColorAndMaterial(class UWDG_ShowroomMaterialColorSelector_C* Selector, int32* ColorCode, class FName* MaterialName);
	void OnUpdateCarInfoValues();
	void openColorPalette(bool for_aux_lights, class UWDG_ShowroomMaterialColorSelector_C* Sender, class UWDG_ShowroomPalette_C** colorPalette_0);
	void ForEachPaletteWrapper(class UPanelWidget* Wrapper, class UWDG_ShowroomMaterialColorSelector_C* Sender);
	void getNationalities();
	void getCups();
	void getBanners(ECarModelType CarModel);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ShowroomCustomCarOptions_C">();
	}
	static class UWDG_ShowroomCustomCarOptions_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ShowroomCustomCarOptions_C>();
	}
};
static_assert(alignof(UWDG_ShowroomCustomCarOptions_C) == 0x000008, "Wrong alignment on UWDG_ShowroomCustomCarOptions_C");
static_assert(sizeof(UWDG_ShowroomCustomCarOptions_C) == 0x000840, "Wrong size on UWDG_ShowroomCustomCarOptions_C");
static_assert(offsetof(UWDG_ShowroomCustomCarOptions_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_ShowroomCustomCarOptions_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCustomCarOptions_C, colorPalette) == 0x0005E8, "Member 'UWDG_ShowroomCustomCarOptions_C::colorPalette' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCustomCarOptions_C, selectorAuxLights) == 0x0005F0, "Member 'UWDG_ShowroomCustomCarOptions_C::selectorAuxLights' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCustomCarOptions_C, selectorRim1) == 0x0005F8, "Member 'UWDG_ShowroomCustomCarOptions_C::selectorRim1' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCustomCarOptions_C, selectorRim2) == 0x000600, "Member 'UWDG_ShowroomCustomCarOptions_C::selectorRim2' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCustomCarOptions_C, selectorSkin1) == 0x000608, "Member 'UWDG_ShowroomCustomCarOptions_C::selectorSkin1' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCustomCarOptions_C, selectorSkin2) == 0x000610, "Member 'UWDG_ShowroomCustomCarOptions_C::selectorSkin2' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCustomCarOptions_C, selectorSkin3) == 0x000618, "Member 'UWDG_ShowroomCustomCarOptions_C::selectorSkin3' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCustomCarOptions_C, sliderBanner) == 0x000620, "Member 'UWDG_ShowroomCustomCarOptions_C::sliderBanner' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCustomCarOptions_C, sliderCup) == 0x000628, "Member 'UWDG_ShowroomCustomCarOptions_C::sliderCup' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCustomCarOptions_C, sliderNationality) == 0x000630, "Member 'UWDG_ShowroomCustomCarOptions_C::sliderNationality' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCustomCarOptions_C, sliderSponsor) == 0x000638, "Member 'UWDG_ShowroomCustomCarOptions_C::sliderSponsor' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCustomCarOptions_C, sliderTemplate) == 0x000640, "Member 'UWDG_ShowroomCustomCarOptions_C::sliderTemplate' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCustomCarOptions_C, NormalColors) == 0x000648, "Member 'UWDG_ShowroomCustomCarOptions_C::NormalColors' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCustomCarOptions_C, SpecialColors) == 0x000658, "Member 'UWDG_ShowroomCustomCarOptions_C::SpecialColors' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCustomCarOptions_C, CarInfo) == 0x000668, "Member 'UWDG_ShowroomCustomCarOptions_C::CarInfo' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCustomCarOptions_C, OnValuesUpdated) == 0x000748, "Member 'UWDG_ShowroomCustomCarOptions_C::OnValuesUpdated' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCustomCarOptions_C, StoredCarInfo) == 0x000758, "Member 'UWDG_ShowroomCustomCarOptions_C::StoredCarInfo' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCustomCarOptions_C, SuppressEvents) == 0x000838, "Member 'UWDG_ShowroomCustomCarOptions_C::SuppressEvents' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomCustomCarOptions_C, IsDirty) == 0x000839, "Member 'UWDG_ShowroomCustomCarOptions_C::IsDirty' has a wrong offset!");

}

