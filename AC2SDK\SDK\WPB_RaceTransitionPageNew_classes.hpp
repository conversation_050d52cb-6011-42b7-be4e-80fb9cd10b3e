﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WPB_RaceTransitionPageNew

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WPB_RaceTransitionPageNew.WPB_RaceTransitionPageNew_C
// 0x0058 (0x0580 - 0x0528)
class UWPB_RaceTransitionPageNew_C final : public UAcPageBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0528(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       Type_10;                                           // 0x0530(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       Type_01;                                           // 0x0538(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       Type_010;                                          // 0x0540(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 background;                                        // 0x0548(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_354;                                         // 0x0550(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtSavingReplay;                                   // 0x0558(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtVersion;                                        // 0x0560(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class FText                                   txtEAVersion;                                      // 0x0568(0x0018)(Edit, BlueprintVisible, DisableEditOnInstance)

public:
	void ExecuteUbergraph_WPB_RaceTransitionPageNew(int32 EntryPoint);
	void BP_StartPage();
	void Tick(const struct FGeometry& MyGeometry, float InDeltaTime);
	void Construct();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WPB_RaceTransitionPageNew_C">();
	}
	static class UWPB_RaceTransitionPageNew_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWPB_RaceTransitionPageNew_C>();
	}
};
static_assert(alignof(UWPB_RaceTransitionPageNew_C) == 0x000008, "Wrong alignment on UWPB_RaceTransitionPageNew_C");
static_assert(sizeof(UWPB_RaceTransitionPageNew_C) == 0x000580, "Wrong size on UWPB_RaceTransitionPageNew_C");
static_assert(offsetof(UWPB_RaceTransitionPageNew_C, UberGraphFrame) == 0x000528, "Member 'UWPB_RaceTransitionPageNew_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWPB_RaceTransitionPageNew_C, Type_10) == 0x000530, "Member 'UWPB_RaceTransitionPageNew_C::Type_10' has a wrong offset!");
static_assert(offsetof(UWPB_RaceTransitionPageNew_C, Type_01) == 0x000538, "Member 'UWPB_RaceTransitionPageNew_C::Type_01' has a wrong offset!");
static_assert(offsetof(UWPB_RaceTransitionPageNew_C, Type_010) == 0x000540, "Member 'UWPB_RaceTransitionPageNew_C::Type_010' has a wrong offset!");
static_assert(offsetof(UWPB_RaceTransitionPageNew_C, background) == 0x000548, "Member 'UWPB_RaceTransitionPageNew_C::background' has a wrong offset!");
static_assert(offsetof(UWPB_RaceTransitionPageNew_C, Image_354) == 0x000550, "Member 'UWPB_RaceTransitionPageNew_C::Image_354' has a wrong offset!");
static_assert(offsetof(UWPB_RaceTransitionPageNew_C, txtSavingReplay) == 0x000558, "Member 'UWPB_RaceTransitionPageNew_C::txtSavingReplay' has a wrong offset!");
static_assert(offsetof(UWPB_RaceTransitionPageNew_C, txtVersion) == 0x000560, "Member 'UWPB_RaceTransitionPageNew_C::txtVersion' has a wrong offset!");
static_assert(offsetof(UWPB_RaceTransitionPageNew_C, txtEAVersion) == 0x000568, "Member 'UWPB_RaceTransitionPageNew_C::txtEAVersion' has a wrong offset!");

}

