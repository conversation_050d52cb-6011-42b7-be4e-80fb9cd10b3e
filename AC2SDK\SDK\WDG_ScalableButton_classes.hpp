﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ScalableButton

#include "Basic.hpp"

#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ScalableButton.WDG_ScalableButton_C
// 0x0038 (0x0298 - 0x0260)
class UWDG_ScalableButton_C final : public UUserWidget
{
public:
	class UImage*                                 Image_22;                                          // 0x0260(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_23;                                          // 0x0268(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_24;                                          // 0x0270(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_25;                                          // 0x0278(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_383;                                         // 0x0280(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             TitleSlot;                                         // 0x0288(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             vboxSlot;                                          // 0x0290(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ScalableButton_C">();
	}
	static class UWDG_ScalableButton_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ScalableButton_C>();
	}
};
static_assert(alignof(UWDG_ScalableButton_C) == 0x000008, "Wrong alignment on UWDG_ScalableButton_C");
static_assert(sizeof(UWDG_ScalableButton_C) == 0x000298, "Wrong size on UWDG_ScalableButton_C");
static_assert(offsetof(UWDG_ScalableButton_C, Image_22) == 0x000260, "Member 'UWDG_ScalableButton_C::Image_22' has a wrong offset!");
static_assert(offsetof(UWDG_ScalableButton_C, Image_23) == 0x000268, "Member 'UWDG_ScalableButton_C::Image_23' has a wrong offset!");
static_assert(offsetof(UWDG_ScalableButton_C, Image_24) == 0x000270, "Member 'UWDG_ScalableButton_C::Image_24' has a wrong offset!");
static_assert(offsetof(UWDG_ScalableButton_C, Image_25) == 0x000278, "Member 'UWDG_ScalableButton_C::Image_25' has a wrong offset!");
static_assert(offsetof(UWDG_ScalableButton_C, Image_383) == 0x000280, "Member 'UWDG_ScalableButton_C::Image_383' has a wrong offset!");
static_assert(offsetof(UWDG_ScalableButton_C, TitleSlot) == 0x000288, "Member 'UWDG_ScalableButton_C::TitleSlot' has a wrong offset!");
static_assert(offsetof(UWDG_ScalableButton_C, vboxSlot) == 0x000290, "Member 'UWDG_ScalableButton_C::vboxSlot' has a wrong offset!");

}

