﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SpecialEventList

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SpecialEventList.WDG_SpecialEventList_C
// 0x0030 (0x0290 - 0x0260)
class UWDG_SpecialEventList_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0260(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UExtendedScrollbox*                     scrollTiles;                                       // 0x0268(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class USizeBox*                               sizeListWrapper;                                   // 0x0270(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWrapBox*                               tileList;                                          // 0x0278(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	TMulticastInlineDelegate<void()>              AddedToFocusPath;                                  // 0x0280(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)

public:
	void ExecuteUbergraph_WDG_SpecialEventList(int32 EntryPoint);
	void OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SpecialEventList_C">();
	}
	static class UWDG_SpecialEventList_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SpecialEventList_C>();
	}
};
static_assert(alignof(UWDG_SpecialEventList_C) == 0x000008, "Wrong alignment on UWDG_SpecialEventList_C");
static_assert(sizeof(UWDG_SpecialEventList_C) == 0x000290, "Wrong size on UWDG_SpecialEventList_C");
static_assert(offsetof(UWDG_SpecialEventList_C, UberGraphFrame) == 0x000260, "Member 'UWDG_SpecialEventList_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventList_C, scrollTiles) == 0x000268, "Member 'UWDG_SpecialEventList_C::scrollTiles' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventList_C, sizeListWrapper) == 0x000270, "Member 'UWDG_SpecialEventList_C::sizeListWrapper' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventList_C, tileList) == 0x000278, "Member 'UWDG_SpecialEventList_C::tileList' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventList_C, AddedToFocusPath) == 0x000280, "Member 'UWDG_SpecialEventList_C::AddedToFocusPath' has a wrong offset!");

}

