﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SavedGamesPage

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SavedGamesPage.WDG_SavedGamesPage_C
// 0x00A0 (0x05C8 - 0x0528)
class UWDG_SavedGamesPage_C final : public UAcPageBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0528(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       PageFade;                                          // 0x0530(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWDG_FooterButton_C*                    Back;                                              // 0x0538(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 bgImage;                                           // 0x0540(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           Body;                                              // 0x0548(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UScrollBox*                             FileList;                                          // 0x0550(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_BasicMessagePopup_C*               fileOperationPopup;                                // 0x0558(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Footer_C*                          Footer;                                            // 0x0560(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           groupControls;                                     // 0x0568(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Header_C*                          Header;                                            // 0x0570(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Page_C*                            PageBase;                                          // 0x0578(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtNoSaveMessage;                                  // 0x0580(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	TMulticastInlineDelegate<void()>              RefreshFileList;                                   // 0x0588(0x0010)(Edit, BlueprintVisible, ZeroConstructor, BlueprintAssignable, BlueprintCallable)
	class FText                                   MessageText;                                       // 0x0598(0x0018)(Edit, BlueprintVisible, DisableEditOnInstance)
	class FString                                 TargetFileName;                                    // 0x05B0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, HasGetValueTypeHash)
	class UAcPanelBase*                           LastFocusedFilePanel;                              // 0x05C0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_SavedGamesPage(int32 EntryPoint);
	void OnLoad(const struct FAcSaveGameHeader& SaveGame, class UWDG_SaveGameItem_C* Sender);
	void OnDel(const struct FAcSaveGameHeader& SaveGame, class UWDG_SaveGameItem_C* Sender);
	void OnFocusFirstFile();
	void OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent);
	void BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_5_OnYes__DelegateSignature();
	void BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_4_OnNo__DelegateSignature();
	void OnDeleteFile(const class FString& Filename);
	void BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_3_OnShow__DelegateSignature(bool HasConfirmation);
	void BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_2_OnHide__DelegateSignature();
	void OnRefreshFileList();
	void BP_OnBackward();
	void BP_StartPage();
	void BndEvt__Back_K2Node_ComponentBoundEvent_1_OnClicked__DelegateSignature();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SavedGamesPage_C">();
	}
	static class UWDG_SavedGamesPage_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SavedGamesPage_C>();
	}
};
static_assert(alignof(UWDG_SavedGamesPage_C) == 0x000008, "Wrong alignment on UWDG_SavedGamesPage_C");
static_assert(sizeof(UWDG_SavedGamesPage_C) == 0x0005C8, "Wrong size on UWDG_SavedGamesPage_C");
static_assert(offsetof(UWDG_SavedGamesPage_C, UberGraphFrame) == 0x000528, "Member 'UWDG_SavedGamesPage_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SavedGamesPage_C, PageFade) == 0x000530, "Member 'UWDG_SavedGamesPage_C::PageFade' has a wrong offset!");
static_assert(offsetof(UWDG_SavedGamesPage_C, Back) == 0x000538, "Member 'UWDG_SavedGamesPage_C::Back' has a wrong offset!");
static_assert(offsetof(UWDG_SavedGamesPage_C, bgImage) == 0x000540, "Member 'UWDG_SavedGamesPage_C::bgImage' has a wrong offset!");
static_assert(offsetof(UWDG_SavedGamesPage_C, Body) == 0x000548, "Member 'UWDG_SavedGamesPage_C::Body' has a wrong offset!");
static_assert(offsetof(UWDG_SavedGamesPage_C, FileList) == 0x000550, "Member 'UWDG_SavedGamesPage_C::FileList' has a wrong offset!");
static_assert(offsetof(UWDG_SavedGamesPage_C, fileOperationPopup) == 0x000558, "Member 'UWDG_SavedGamesPage_C::fileOperationPopup' has a wrong offset!");
static_assert(offsetof(UWDG_SavedGamesPage_C, Footer) == 0x000560, "Member 'UWDG_SavedGamesPage_C::Footer' has a wrong offset!");
static_assert(offsetof(UWDG_SavedGamesPage_C, groupControls) == 0x000568, "Member 'UWDG_SavedGamesPage_C::groupControls' has a wrong offset!");
static_assert(offsetof(UWDG_SavedGamesPage_C, Header) == 0x000570, "Member 'UWDG_SavedGamesPage_C::Header' has a wrong offset!");
static_assert(offsetof(UWDG_SavedGamesPage_C, PageBase) == 0x000578, "Member 'UWDG_SavedGamesPage_C::PageBase' has a wrong offset!");
static_assert(offsetof(UWDG_SavedGamesPage_C, txtNoSaveMessage) == 0x000580, "Member 'UWDG_SavedGamesPage_C::txtNoSaveMessage' has a wrong offset!");
static_assert(offsetof(UWDG_SavedGamesPage_C, RefreshFileList) == 0x000588, "Member 'UWDG_SavedGamesPage_C::RefreshFileList' has a wrong offset!");
static_assert(offsetof(UWDG_SavedGamesPage_C, MessageText) == 0x000598, "Member 'UWDG_SavedGamesPage_C::MessageText' has a wrong offset!");
static_assert(offsetof(UWDG_SavedGamesPage_C, TargetFileName) == 0x0005B0, "Member 'UWDG_SavedGamesPage_C::TargetFileName' has a wrong offset!");
static_assert(offsetof(UWDG_SavedGamesPage_C, LastFocusedFilePanel) == 0x0005C0, "Member 'UWDG_SavedGamesPage_C::LastFocusedFilePanel' has a wrong offset!");

}

