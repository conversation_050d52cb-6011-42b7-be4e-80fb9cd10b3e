﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomTileItem

#include "Basic.hpp"

#include "WDG_ShowroomTileBase_classes.hpp"
#include "Engine_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "AC2_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ShowroomTileItem.WDG_ShowroomTileItem_C
// 0x00E8 (0x0B98 - 0x0AB0)
class UWDG_ShowroomTileItem_C final : public UWDG_ShowroomTileBase_C
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame_WDG_ShowroomTileItem_C;             // 0x0AB0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UBorder*                                background;                                        // 0x0AB8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, Is<PERSON>lainOldData, RepSki<PERSON>, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         boxGT4;                                            // 0x0AC0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                brdCarGroup;                                       // 0x0AC8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgLeftSlant;                                      // 0x0AD0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWidgetSwitcher*                        switcherFooter;                                    // 0x0AD8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtCarGroup;                                       // 0x0AE0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	TMulticastInlineDelegate<void(class UWDG_ShowroomTileItem_C* Sender, class FName Key)> OnItemForward_0; // 0x0AE8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void(class UWDG_ShowroomTileItem_C* Sender)> OnItemFocused_0;           // 0x0AF8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	struct FLinearColor                           colorST;                                           // 0x0B08(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           colorCup;                                          // 0x0B18(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TMap<class UWDG_CarGroupSelector_C*, struct FLinearColor> CarGroupColorBgr;                      // 0x0B28(0x0050)(Edit, BlueprintVisible, DisableEditOnInstance, ContainsInstancedReference)
	struct FLinearColor                           CarGroupBackgroundColor;                           // 0x0B78(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CarGroupForegroundColor;                           // 0x0B88(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_ShowroomTileItem(int32 EntryPoint);
	void UpdateModel(const struct FModelInfo& ModelInfo_0);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ShowroomTileItem_C">();
	}
	static class UWDG_ShowroomTileItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ShowroomTileItem_C>();
	}
};
static_assert(alignof(UWDG_ShowroomTileItem_C) == 0x000008, "Wrong alignment on UWDG_ShowroomTileItem_C");
static_assert(sizeof(UWDG_ShowroomTileItem_C) == 0x000B98, "Wrong size on UWDG_ShowroomTileItem_C");
static_assert(offsetof(UWDG_ShowroomTileItem_C, UberGraphFrame_WDG_ShowroomTileItem_C) == 0x000AB0, "Member 'UWDG_ShowroomTileItem_C::UberGraphFrame_WDG_ShowroomTileItem_C' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItem_C, background) == 0x000AB8, "Member 'UWDG_ShowroomTileItem_C::background' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItem_C, boxGT4) == 0x000AC0, "Member 'UWDG_ShowroomTileItem_C::boxGT4' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItem_C, brdCarGroup) == 0x000AC8, "Member 'UWDG_ShowroomTileItem_C::brdCarGroup' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItem_C, imgLeftSlant) == 0x000AD0, "Member 'UWDG_ShowroomTileItem_C::imgLeftSlant' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItem_C, switcherFooter) == 0x000AD8, "Member 'UWDG_ShowroomTileItem_C::switcherFooter' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItem_C, txtCarGroup) == 0x000AE0, "Member 'UWDG_ShowroomTileItem_C::txtCarGroup' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItem_C, OnItemForward_0) == 0x000AE8, "Member 'UWDG_ShowroomTileItem_C::OnItemForward_0' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItem_C, OnItemFocused_0) == 0x000AF8, "Member 'UWDG_ShowroomTileItem_C::OnItemFocused_0' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItem_C, colorST) == 0x000B08, "Member 'UWDG_ShowroomTileItem_C::colorST' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItem_C, colorCup) == 0x000B18, "Member 'UWDG_ShowroomTileItem_C::colorCup' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItem_C, CarGroupColorBgr) == 0x000B28, "Member 'UWDG_ShowroomTileItem_C::CarGroupColorBgr' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItem_C, CarGroupBackgroundColor) == 0x000B78, "Member 'UWDG_ShowroomTileItem_C::CarGroupBackgroundColor' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItem_C, CarGroupForegroundColor) == 0x000B88, "Member 'UWDG_ShowroomTileItem_C::CarGroupForegroundColor' has a wrong offset!");

}

