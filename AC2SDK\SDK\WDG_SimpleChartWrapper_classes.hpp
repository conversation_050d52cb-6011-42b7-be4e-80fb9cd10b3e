﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SimpleChartWrapper

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SimpleChartWrapper.WDG_SimpleChartWrapper_C
// 0x0040 (0x0620 - 0x05E0)
class UWDG_SimpleChartWrapper_C final : public UAcPanelBase
{
public:
	class UImage*                                 Image_0;                                           // 0x05E0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_1;                                           // 0x05E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_2;                                           // 0x05F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_3;                                           // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_4;                                           // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_5;                                           // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             NamedSlot_0;                                       // 0x0610(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SimpleAreaChart_C*                 WDG_SimpleAreaChart;                               // 0x0618(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SimpleChartWrapper_C">();
	}
	static class UWDG_SimpleChartWrapper_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SimpleChartWrapper_C>();
	}
};
static_assert(alignof(UWDG_SimpleChartWrapper_C) == 0x000008, "Wrong alignment on UWDG_SimpleChartWrapper_C");
static_assert(sizeof(UWDG_SimpleChartWrapper_C) == 0x000620, "Wrong size on UWDG_SimpleChartWrapper_C");
static_assert(offsetof(UWDG_SimpleChartWrapper_C, Image_0) == 0x0005E0, "Member 'UWDG_SimpleChartWrapper_C::Image_0' has a wrong offset!");
static_assert(offsetof(UWDG_SimpleChartWrapper_C, Image_1) == 0x0005E8, "Member 'UWDG_SimpleChartWrapper_C::Image_1' has a wrong offset!");
static_assert(offsetof(UWDG_SimpleChartWrapper_C, Image_2) == 0x0005F0, "Member 'UWDG_SimpleChartWrapper_C::Image_2' has a wrong offset!");
static_assert(offsetof(UWDG_SimpleChartWrapper_C, Image_3) == 0x0005F8, "Member 'UWDG_SimpleChartWrapper_C::Image_3' has a wrong offset!");
static_assert(offsetof(UWDG_SimpleChartWrapper_C, Image_4) == 0x000600, "Member 'UWDG_SimpleChartWrapper_C::Image_4' has a wrong offset!");
static_assert(offsetof(UWDG_SimpleChartWrapper_C, Image_5) == 0x000608, "Member 'UWDG_SimpleChartWrapper_C::Image_5' has a wrong offset!");
static_assert(offsetof(UWDG_SimpleChartWrapper_C, NamedSlot_0) == 0x000610, "Member 'UWDG_SimpleChartWrapper_C::NamedSlot_0' has a wrong offset!");
static_assert(offsetof(UWDG_SimpleChartWrapper_C, WDG_SimpleAreaChart) == 0x000618, "Member 'UWDG_SimpleChartWrapper_C::WDG_SimpleAreaChart' has a wrong offset!");

}

