﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomCarGroupFilter

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "Engine_structs.hpp"
#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_ShowroomCarGroupFilter.WDG_ShowroomCarGroupFilter_C.ExecuteUbergraph_WDG_ShowroomCarGroupFilter
// 0x0198 (0x0198 - 0x0000)
struct WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0004(0x0004)(ZeroConstructor, Is<PERSON><PERSON>OldD<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate;              // 0x0008(0x0010)(ZeroConstructor, NoDestructor)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsVisible_ReturnValue;                    // 0x0021(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_22[0x2];                                       // 0x0022(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FFocusEvent                            K2Node_Event_InFocusEvent_1;                       // 0x0024(0x0008)(NoDestructor)
	struct FFocusEvent                            K2Node_Event_InFocusEvent;                         // 0x002C(0x0008)(NoDestructor)
	bool                                          CallFunc_IsVisible_ReturnValue_1;                  // 0x0034(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_35[0x3];                                       // 0x0035(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_GenericBarItem_C*                  CallFunc_Array_Get_Item;                           // 0x0038(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   CallFunc_CarGroupToTextFull_Output;                // 0x0040(0x0018)()
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData;              // 0x0058(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	int32                                         CallFunc_Conv_ByteToInt_ReturnValue;               // 0x0098(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_Conv_IntToByte_ReturnValue;               // 0x009C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x009D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_GetValidValue_ReturnValue;                // 0x009E(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_9F[0x1];                                       // 0x009F(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_BoolToText_ReturnValue;              // 0x00A0(0x0018)()
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData_1;            // 0x00B8(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	class FText                                   CallFunc_CarGroupToText_Output;                    // 0x00F8(0x0018)()
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData_2;            // 0x0110(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0150(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_154[0x4];                                      // 0x0154(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<struct FFormatArgumentData>            K2Node_MakeArray_Array;                            // 0x0158(0x0010)(ReferenceParm)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0168(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_169[0x7];                                      // 0x0169(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Format_ReturnValue;                       // 0x0170(0x0018)()
	class FString                                 CallFunc_Conv_TextToString_ReturnValue;            // 0x0188(0x0010)(ZeroConstructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter) == 0x000008, "Wrong alignment on WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter");
static_assert(sizeof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter) == 0x000198, "Wrong size on WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, EntryPoint) == 0x000000, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, Temp_int_Loop_Counter_Variable) == 0x000004, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, K2Node_CreateDelegate_OutputDelegate) == 0x000008, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, CallFunc_Add_IntInt_ReturnValue) == 0x000018, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, Temp_int_Array_Index_Variable) == 0x00001C, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, K2Node_Event_IsDesignTime) == 0x000020, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, CallFunc_IsVisible_ReturnValue) == 0x000021, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::CallFunc_IsVisible_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, K2Node_Event_InFocusEvent_1) == 0x000024, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::K2Node_Event_InFocusEvent_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, K2Node_Event_InFocusEvent) == 0x00002C, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::K2Node_Event_InFocusEvent' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, CallFunc_IsVisible_ReturnValue_1) == 0x000034, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::CallFunc_IsVisible_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, CallFunc_Array_Get_Item) == 0x000038, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, CallFunc_CarGroupToTextFull_Output) == 0x000040, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::CallFunc_CarGroupToTextFull_Output' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, K2Node_MakeStruct_FormatArgumentData) == 0x000058, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::K2Node_MakeStruct_FormatArgumentData' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, CallFunc_Conv_ByteToInt_ReturnValue) == 0x000098, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::CallFunc_Conv_ByteToInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, CallFunc_Conv_IntToByte_ReturnValue) == 0x00009C, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::CallFunc_Conv_IntToByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x00009D, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, CallFunc_GetValidValue_ReturnValue) == 0x00009E, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::CallFunc_GetValidValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, CallFunc_Conv_BoolToText_ReturnValue) == 0x0000A0, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::CallFunc_Conv_BoolToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, K2Node_MakeStruct_FormatArgumentData_1) == 0x0000B8, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::K2Node_MakeStruct_FormatArgumentData_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, CallFunc_CarGroupToText_Output) == 0x0000F8, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::CallFunc_CarGroupToText_Output' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, K2Node_MakeStruct_FormatArgumentData_2) == 0x000110, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::K2Node_MakeStruct_FormatArgumentData_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, CallFunc_Array_Length_ReturnValue) == 0x000150, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, K2Node_MakeArray_Array) == 0x000158, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, CallFunc_Less_IntInt_ReturnValue) == 0x000168, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, CallFunc_Format_ReturnValue) == 0x000170, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::CallFunc_Format_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter, CallFunc_Conv_TextToString_ReturnValue) == 0x000188, "Member 'WDG_ShowroomCarGroupFilter_C_ExecuteUbergraph_WDG_ShowroomCarGroupFilter::CallFunc_Conv_TextToString_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomCarGroupFilter.WDG_ShowroomCarGroupFilter_C.OnRemovedFromFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomCarGroupFilter_C_OnRemovedFromFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_ShowroomCarGroupFilter_C_OnRemovedFromFocusPath) == 0x000004, "Wrong alignment on WDG_ShowroomCarGroupFilter_C_OnRemovedFromFocusPath");
static_assert(sizeof(WDG_ShowroomCarGroupFilter_C_OnRemovedFromFocusPath) == 0x000008, "Wrong size on WDG_ShowroomCarGroupFilter_C_OnRemovedFromFocusPath");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_OnRemovedFromFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_ShowroomCarGroupFilter_C_OnRemovedFromFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_ShowroomCarGroupFilter.WDG_ShowroomCarGroupFilter_C.OnAddedToFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomCarGroupFilter_C_OnAddedToFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_ShowroomCarGroupFilter_C_OnAddedToFocusPath) == 0x000004, "Wrong alignment on WDG_ShowroomCarGroupFilter_C_OnAddedToFocusPath");
static_assert(sizeof(WDG_ShowroomCarGroupFilter_C_OnAddedToFocusPath) == 0x000008, "Wrong size on WDG_ShowroomCarGroupFilter_C_OnAddedToFocusPath");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_OnAddedToFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_ShowroomCarGroupFilter_C_OnAddedToFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_ShowroomCarGroupFilter.WDG_ShowroomCarGroupFilter_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_ShowroomCarGroupFilter_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomCarGroupFilter_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_ShowroomCarGroupFilter_C_PreConstruct");
static_assert(sizeof(WDG_ShowroomCarGroupFilter_C_PreConstruct) == 0x000001, "Wrong size on WDG_ShowroomCarGroupFilter_C_PreConstruct");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_ShowroomCarGroupFilter_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WDG_ShowroomCarGroupFilter.WDG_ShowroomCarGroupFilter_C.UpdateTextLabel
// 0x0018 (0x0018 - 0x0000)
struct WDG_ShowroomCarGroupFilter_C_UpdateTextLabel final
{
public:
	class FText                                   CallFunc_CarGroupToTextFull_Output;                // 0x0000(0x0018)()
};
static_assert(alignof(WDG_ShowroomCarGroupFilter_C_UpdateTextLabel) == 0x000008, "Wrong alignment on WDG_ShowroomCarGroupFilter_C_UpdateTextLabel");
static_assert(sizeof(WDG_ShowroomCarGroupFilter_C_UpdateTextLabel) == 0x000018, "Wrong size on WDG_ShowroomCarGroupFilter_C_UpdateTextLabel");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_UpdateTextLabel, CallFunc_CarGroupToTextFull_Output) == 0x000000, "Member 'WDG_ShowroomCarGroupFilter_C_UpdateTextLabel::CallFunc_CarGroupToTextFull_Output' has a wrong offset!");

// Function WDG_ShowroomCarGroupFilter.WDG_ShowroomCarGroupFilter_C.SetActiveFilter
// 0x0001 (0x0001 - 0x0000)
struct WDG_ShowroomCarGroupFilter_C_SetActiveFilter final
{
public:
	ECarGroup                                     activeFilter_0;                                    // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCarGroupFilter_C_SetActiveFilter) == 0x000001, "Wrong alignment on WDG_ShowroomCarGroupFilter_C_SetActiveFilter");
static_assert(sizeof(WDG_ShowroomCarGroupFilter_C_SetActiveFilter) == 0x000001, "Wrong size on WDG_ShowroomCarGroupFilter_C_SetActiveFilter");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_SetActiveFilter, activeFilter_0) == 0x000000, "Member 'WDG_ShowroomCarGroupFilter_C_SetActiveFilter::activeFilter_0' has a wrong offset!");

// Function WDG_ShowroomCarGroupFilter.WDG_ShowroomCarGroupFilter_C.ItemSelected
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomCarGroupFilter_C_ItemSelected final
{
public:
	class UWDG_GenericBarItem_C*                  Item;                                              // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_Conv_IntToByte_ReturnValue;               // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_GetValidValue_ReturnValue;                // 0x0009(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCarGroupFilter_C_ItemSelected) == 0x000008, "Wrong alignment on WDG_ShowroomCarGroupFilter_C_ItemSelected");
static_assert(sizeof(WDG_ShowroomCarGroupFilter_C_ItemSelected) == 0x000010, "Wrong size on WDG_ShowroomCarGroupFilter_C_ItemSelected");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ItemSelected, Item) == 0x000000, "Member 'WDG_ShowroomCarGroupFilter_C_ItemSelected::Item' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ItemSelected, CallFunc_Conv_IntToByte_ReturnValue) == 0x000008, "Member 'WDG_ShowroomCarGroupFilter_C_ItemSelected::CallFunc_Conv_IntToByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_ItemSelected, CallFunc_GetValidValue_ReturnValue) == 0x000009, "Member 'WDG_ShowroomCarGroupFilter_C_ItemSelected::CallFunc_GetValidValue_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomCarGroupFilter.WDG_ShowroomCarGroupFilter_C.CreateItem
// 0x0058 (0x0058 - 0x0000)
struct WDG_ShowroomCarGroupFilter_C_CreateItem final
{
public:
	ECarGroup                                     CarGroup;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_GenericBarItem_C*                  Item;                                              // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  CallFunc_Create_ReturnValue;                       // 0x0010(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   CallFunc_CarGroupToTextFull_Output;                // 0x0018(0x0018)()
	int32                                         CallFunc_Conv_ByteToInt_ReturnValue;               // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void(class UWDG_GenericBarItem_C* Sender)> K2Node_CreateDelegate_OutputDelegate;       // 0x0034(0x0010)(ZeroConstructor, NoDestructor)
	struct FLinearColor                           Temp_struct_Variable;                              // 0x0044(0x0010)(ConstParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCarGroupFilter_C_CreateItem) == 0x000008, "Wrong alignment on WDG_ShowroomCarGroupFilter_C_CreateItem");
static_assert(sizeof(WDG_ShowroomCarGroupFilter_C_CreateItem) == 0x000058, "Wrong size on WDG_ShowroomCarGroupFilter_C_CreateItem");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_CreateItem, CarGroup) == 0x000000, "Member 'WDG_ShowroomCarGroupFilter_C_CreateItem::CarGroup' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_CreateItem, Item) == 0x000008, "Member 'WDG_ShowroomCarGroupFilter_C_CreateItem::Item' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_CreateItem, CallFunc_Create_ReturnValue) == 0x000010, "Member 'WDG_ShowroomCarGroupFilter_C_CreateItem::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_CreateItem, CallFunc_CarGroupToTextFull_Output) == 0x000018, "Member 'WDG_ShowroomCarGroupFilter_C_CreateItem::CallFunc_CarGroupToTextFull_Output' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_CreateItem, CallFunc_Conv_ByteToInt_ReturnValue) == 0x000030, "Member 'WDG_ShowroomCarGroupFilter_C_CreateItem::CallFunc_Conv_ByteToInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_CreateItem, K2Node_CreateDelegate_OutputDelegate) == 0x000034, "Member 'WDG_ShowroomCarGroupFilter_C_CreateItem::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_CreateItem, Temp_struct_Variable) == 0x000044, "Member 'WDG_ShowroomCarGroupFilter_C_CreateItem::Temp_struct_Variable' has a wrong offset!");

// Function WDG_ShowroomCarGroupFilter.WDG_ShowroomCarGroupFilter_C.Populate
// 0x0058 (0x0058 - 0x0000)
struct WDG_ShowroomCarGroupFilter_C_Populate final
{
public:
	TArray<ECarGroup>                             Source;                                            // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x001C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1D[0x3];                                       // 0x001D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ECarGroup                                     CallFunc_Array_Get_Item;                           // 0x0024(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_25[0x3];                                       // 0x0025(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_GenericBarItem_C*                  CallFunc_CreateItem_Item;                          // 0x0028(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue;                    // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_34[0x4];                                       // 0x0034(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_GenericBarItem_C*                  CallFunc_CreateItem_Item_1;                        // 0x0038(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue_1;                  // 0x0040(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_44[0x4];                                       // 0x0044(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue;                     // 0x0048(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue_1;                   // 0x0050(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomCarGroupFilter_C_Populate) == 0x000008, "Wrong alignment on WDG_ShowroomCarGroupFilter_C_Populate");
static_assert(sizeof(WDG_ShowroomCarGroupFilter_C_Populate) == 0x000058, "Wrong size on WDG_ShowroomCarGroupFilter_C_Populate");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_Populate, Source) == 0x000000, "Member 'WDG_ShowroomCarGroupFilter_C_Populate::Source' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_Populate, Temp_int_Loop_Counter_Variable) == 0x000010, "Member 'WDG_ShowroomCarGroupFilter_C_Populate::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_Populate, CallFunc_Array_Length_ReturnValue) == 0x000014, "Member 'WDG_ShowroomCarGroupFilter_C_Populate::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_Populate, CallFunc_Add_IntInt_ReturnValue) == 0x000018, "Member 'WDG_ShowroomCarGroupFilter_C_Populate::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_Populate, CallFunc_Less_IntInt_ReturnValue) == 0x00001C, "Member 'WDG_ShowroomCarGroupFilter_C_Populate::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_Populate, Temp_int_Array_Index_Variable) == 0x000020, "Member 'WDG_ShowroomCarGroupFilter_C_Populate::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_Populate, CallFunc_Array_Get_Item) == 0x000024, "Member 'WDG_ShowroomCarGroupFilter_C_Populate::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_Populate, CallFunc_CreateItem_Item) == 0x000028, "Member 'WDG_ShowroomCarGroupFilter_C_Populate::CallFunc_CreateItem_Item' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_Populate, CallFunc_Array_Add_ReturnValue) == 0x000030, "Member 'WDG_ShowroomCarGroupFilter_C_Populate::CallFunc_Array_Add_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_Populate, CallFunc_CreateItem_Item_1) == 0x000038, "Member 'WDG_ShowroomCarGroupFilter_C_Populate::CallFunc_CreateItem_Item_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_Populate, CallFunc_Array_Add_ReturnValue_1) == 0x000040, "Member 'WDG_ShowroomCarGroupFilter_C_Populate::CallFunc_Array_Add_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_Populate, CallFunc_AddChild_ReturnValue) == 0x000048, "Member 'WDG_ShowroomCarGroupFilter_C_Populate::CallFunc_AddChild_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_Populate, CallFunc_AddChild_ReturnValue_1) == 0x000050, "Member 'WDG_ShowroomCarGroupFilter_C_Populate::CallFunc_AddChild_ReturnValue_1' has a wrong offset!");

// Function WDG_ShowroomCarGroupFilter.WDG_ShowroomCarGroupFilter_C.SetSizeHolderIfLonger
// 0x0060 (0x0060 - 0x0000)
struct WDG_ShowroomCarGroupFilter_C_SetSizeHolderIfLonger final
{
public:
	class FText                                   text;                                              // 0x0000(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm)
	class FString                                 CallFunc_Conv_TextToString_ReturnValue;            // 0x0018(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	int32                                         CallFunc_Len_ReturnValue;                          // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2C[0x4];                                       // 0x002C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_GetText_ReturnValue;                      // 0x0030(0x0018)()
	class FString                                 CallFunc_Conv_TextToString_ReturnValue_1;          // 0x0048(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	int32                                         CallFunc_Len_ReturnValue_1;                        // 0x0058(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x005C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomCarGroupFilter_C_SetSizeHolderIfLonger) == 0x000008, "Wrong alignment on WDG_ShowroomCarGroupFilter_C_SetSizeHolderIfLonger");
static_assert(sizeof(WDG_ShowroomCarGroupFilter_C_SetSizeHolderIfLonger) == 0x000060, "Wrong size on WDG_ShowroomCarGroupFilter_C_SetSizeHolderIfLonger");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_SetSizeHolderIfLonger, text) == 0x000000, "Member 'WDG_ShowroomCarGroupFilter_C_SetSizeHolderIfLonger::text' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_SetSizeHolderIfLonger, CallFunc_Conv_TextToString_ReturnValue) == 0x000018, "Member 'WDG_ShowroomCarGroupFilter_C_SetSizeHolderIfLonger::CallFunc_Conv_TextToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_SetSizeHolderIfLonger, CallFunc_Len_ReturnValue) == 0x000028, "Member 'WDG_ShowroomCarGroupFilter_C_SetSizeHolderIfLonger::CallFunc_Len_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_SetSizeHolderIfLonger, CallFunc_GetText_ReturnValue) == 0x000030, "Member 'WDG_ShowroomCarGroupFilter_C_SetSizeHolderIfLonger::CallFunc_GetText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_SetSizeHolderIfLonger, CallFunc_Conv_TextToString_ReturnValue_1) == 0x000048, "Member 'WDG_ShowroomCarGroupFilter_C_SetSizeHolderIfLonger::CallFunc_Conv_TextToString_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_SetSizeHolderIfLonger, CallFunc_Len_ReturnValue_1) == 0x000058, "Member 'WDG_ShowroomCarGroupFilter_C_SetSizeHolderIfLonger::CallFunc_Len_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomCarGroupFilter_C_SetSizeHolderIfLonger, CallFunc_Greater_IntInt_ReturnValue) == 0x00005C, "Member 'WDG_ShowroomCarGroupFilter_C_SetSizeHolderIfLonger::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");

}

