﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_VideoOptionsPage

#include "Basic.hpp"

#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_VideoOptionsPage.WDG_VideoOptionsPage_C.ExecuteUbergraph_WDG_VideoOptionsPage
// 0x0108 (0x0108 - 0x0000)
struct WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue;               // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x0014(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_15[0x3];                                       // 0x0015(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_GenericBarItem_C*                  K2Node_ComponentBoundEvent_Sender;                 // 0x0018(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EControllerActionType                         K2Node_Event_input;                                // 0x0020(0x0001)(ConstParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_isReleased;                           // 0x0021(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0022(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0023(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_24[0x4];                                       // 0x0024(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPanelBase*                           K2Node_ComponentBoundEvent_CallingPanel_1;         // 0x0028(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_31[0x7];                                       // 0x0031(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPanelBase*                           K2Node_ComponentBoundEvent_CallingPanel;           // 0x0038(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_ComponentBoundEvent_Cancelled;              // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_41[0x7];                                       // 0x0041(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<struct FFileItem>                      CallFunc_GetCustomPresets_ReturnValue;             // 0x0048(0x0010)(ReferenceParm)
	class FString                                 K2Node_ComponentBoundEvent_Filename_2;             // 0x0058(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          K2Node_ComponentBoundEvent_ExistingFile;           // 0x0068(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_69[0x3];                                       // 0x0069(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class FName                                   K2Node_ComponentBoundEvent_Filename_1;             // 0x006C(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_74[0x4];                                       // 0x0074(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 K2Node_ComponentBoundEvent_DisplayName;            // 0x0078(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_SaveCustomPreset_ReturnValue;             // 0x0088(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_89[0x7];                                       // 0x0089(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Conv_NameToString_ReturnValue;            // 0x0090(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_LoadCustomPreset_ReturnValue;             // 0x00A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_A1[0x7];                                       // 0x00A1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPanelBase*                           K2Node_Event_panelOnFocus;                         // 0x00A8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x00B0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x00B4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<class UWidget*>                        CallFunc_GetAllChildren_ReturnValue;               // 0x00B8(0x0010)(ReferenceParm, ContainsInstancedReference)
	class UWidget*                                CallFunc_Array_Get_Item;                           // 0x00C8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcPanelBase*                           K2Node_DynamicCast_AsAc_Panel_Base;                // 0x00D0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x00D8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_EqualEqual_ObjectObject_ReturnValue;      // 0x00D9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_DA[0x2];                                       // 0x00DA(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x00DC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x00E0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_E1[0x7];                                       // 0x00E1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 K2Node_ComponentBoundEvent_Filename;               // 0x00E8(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_DeleteCustomPreset_ReturnValue;           // 0x00F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_F9[0x7];                                       // 0x00F9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue_1;             // 0x0100(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage) == 0x000008, "Wrong alignment on WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage");
static_assert(sizeof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage) == 0x000108, "Wrong size on WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, EntryPoint) == 0x000000, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, CallFunc_GetMenuManager_ReturnValue) == 0x000008, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::CallFunc_GetMenuManager_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, Temp_int_Array_Index_Variable) == 0x000010, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x000014, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, K2Node_ComponentBoundEvent_Sender) == 0x000018, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::K2Node_ComponentBoundEvent_Sender' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, K2Node_Event_input) == 0x000020, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::K2Node_Event_input' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, K2Node_Event_isReleased) == 0x000021, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::K2Node_Event_isReleased' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, CallFunc_Not_PreBool_ReturnValue) == 0x000022, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000023, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, K2Node_ComponentBoundEvent_CallingPanel_1) == 0x000028, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::K2Node_ComponentBoundEvent_CallingPanel_1' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, CallFunc_BooleanAND_ReturnValue) == 0x000030, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, K2Node_ComponentBoundEvent_CallingPanel) == 0x000038, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::K2Node_ComponentBoundEvent_CallingPanel' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, K2Node_ComponentBoundEvent_Cancelled) == 0x000040, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::K2Node_ComponentBoundEvent_Cancelled' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, CallFunc_GetCustomPresets_ReturnValue) == 0x000048, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::CallFunc_GetCustomPresets_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, K2Node_ComponentBoundEvent_Filename_2) == 0x000058, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::K2Node_ComponentBoundEvent_Filename_2' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, K2Node_ComponentBoundEvent_ExistingFile) == 0x000068, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::K2Node_ComponentBoundEvent_ExistingFile' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, K2Node_ComponentBoundEvent_Filename_1) == 0x00006C, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::K2Node_ComponentBoundEvent_Filename_1' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, K2Node_ComponentBoundEvent_DisplayName) == 0x000078, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::K2Node_ComponentBoundEvent_DisplayName' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, CallFunc_SaveCustomPreset_ReturnValue) == 0x000088, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::CallFunc_SaveCustomPreset_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, CallFunc_Conv_NameToString_ReturnValue) == 0x000090, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::CallFunc_Conv_NameToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, CallFunc_LoadCustomPreset_ReturnValue) == 0x0000A0, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::CallFunc_LoadCustomPreset_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, K2Node_Event_panelOnFocus) == 0x0000A8, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::K2Node_Event_panelOnFocus' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, Temp_int_Loop_Counter_Variable) == 0x0000B0, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, CallFunc_Add_IntInt_ReturnValue) == 0x0000B4, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, CallFunc_GetAllChildren_ReturnValue) == 0x0000B8, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::CallFunc_GetAllChildren_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, CallFunc_Array_Get_Item) == 0x0000C8, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, K2Node_DynamicCast_AsAc_Panel_Base) == 0x0000D0, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::K2Node_DynamicCast_AsAc_Panel_Base' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, K2Node_DynamicCast_bSuccess) == 0x0000D8, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, CallFunc_EqualEqual_ObjectObject_ReturnValue) == 0x0000D9, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::CallFunc_EqualEqual_ObjectObject_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, CallFunc_Array_Length_ReturnValue) == 0x0000DC, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, CallFunc_Less_IntInt_ReturnValue) == 0x0000E0, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, K2Node_ComponentBoundEvent_Filename) == 0x0000E8, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::K2Node_ComponentBoundEvent_Filename' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, CallFunc_DeleteCustomPreset_ReturnValue) == 0x0000F8, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::CallFunc_DeleteCustomPreset_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage, CallFunc_GetMenuManager_ReturnValue_1) == 0x000100, "Member 'WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage::CallFunc_GetMenuManager_ReturnValue_1' has a wrong offset!");

// Function WDG_VideoOptionsPage.WDG_VideoOptionsPage_C.BndEvt__filePanel_K2Node_ComponentBoundEvent_10_OnDeleteUserPreset__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_10_OnDeleteUserPreset__DelegateSignature final
{
public:
	class FString                                 Filename;                                          // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_10_OnDeleteUserPreset__DelegateSignature) == 0x000008, "Wrong alignment on WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_10_OnDeleteUserPreset__DelegateSignature");
static_assert(sizeof(WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_10_OnDeleteUserPreset__DelegateSignature) == 0x000010, "Wrong size on WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_10_OnDeleteUserPreset__DelegateSignature");
static_assert(offsetof(WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_10_OnDeleteUserPreset__DelegateSignature, Filename) == 0x000000, "Member 'WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_10_OnDeleteUserPreset__DelegateSignature::Filename' has a wrong offset!");

// Function WDG_VideoOptionsPage.WDG_VideoOptionsPage_C.OnPanelFocused
// 0x0008 (0x0008 - 0x0000)
struct WDG_VideoOptionsPage_C_OnPanelFocused final
{
public:
	class UAcPanelBase*                           panelOnFocus;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_VideoOptionsPage_C_OnPanelFocused) == 0x000008, "Wrong alignment on WDG_VideoOptionsPage_C_OnPanelFocused");
static_assert(sizeof(WDG_VideoOptionsPage_C_OnPanelFocused) == 0x000008, "Wrong size on WDG_VideoOptionsPage_C_OnPanelFocused");
static_assert(offsetof(WDG_VideoOptionsPage_C_OnPanelFocused, panelOnFocus) == 0x000000, "Member 'WDG_VideoOptionsPage_C_OnPanelFocused::panelOnFocus' has a wrong offset!");

// Function WDG_VideoOptionsPage.WDG_VideoOptionsPage_C.BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature
// 0x0018 (0x0018 - 0x0000)
struct WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature final
{
public:
	class FName                                   Filename;                                          // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 DisplayName;                                       // 0x0008(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature) == 0x000008, "Wrong alignment on WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature");
static_assert(sizeof(WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature) == 0x000018, "Wrong size on WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature");
static_assert(offsetof(WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature, Filename) == 0x000000, "Member 'WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature::Filename' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature, DisplayName) == 0x000008, "Member 'WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature::DisplayName' has a wrong offset!");

// Function WDG_VideoOptionsPage.WDG_VideoOptionsPage_C.BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnSaveUserPreset__DelegateSignature
// 0x0018 (0x0018 - 0x0000)
struct WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnSaveUserPreset__DelegateSignature final
{
public:
	class FString                                 Filename;                                          // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, HasGetValueTypeHash)
	bool                                          ExistingFile;                                      // 0x0010(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnSaveUserPreset__DelegateSignature) == 0x000008, "Wrong alignment on WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnSaveUserPreset__DelegateSignature");
static_assert(sizeof(WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnSaveUserPreset__DelegateSignature) == 0x000018, "Wrong size on WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnSaveUserPreset__DelegateSignature");
static_assert(offsetof(WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnSaveUserPreset__DelegateSignature, Filename) == 0x000000, "Member 'WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnSaveUserPreset__DelegateSignature::Filename' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnSaveUserPreset__DelegateSignature, ExistingFile) == 0x000010, "Member 'WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnSaveUserPreset__DelegateSignature::ExistingFile' has a wrong offset!");

// Function WDG_VideoOptionsPage.WDG_VideoOptionsPage_C.BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_3_OnHide__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_VideoOptionsPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_3_OnHide__DelegateSignature final
{
public:
	class UAcPanelBase*                           CallingPanel;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Cancelled;                                         // 0x0008(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_VideoOptionsPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_3_OnHide__DelegateSignature) == 0x000008, "Wrong alignment on WDG_VideoOptionsPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_3_OnHide__DelegateSignature");
static_assert(sizeof(WDG_VideoOptionsPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_3_OnHide__DelegateSignature) == 0x000010, "Wrong size on WDG_VideoOptionsPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_3_OnHide__DelegateSignature");
static_assert(offsetof(WDG_VideoOptionsPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_3_OnHide__DelegateSignature, CallingPanel) == 0x000000, "Member 'WDG_VideoOptionsPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_3_OnHide__DelegateSignature::CallingPanel' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_3_OnHide__DelegateSignature, Cancelled) == 0x000008, "Member 'WDG_VideoOptionsPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_3_OnHide__DelegateSignature::Cancelled' has a wrong offset!");

// Function WDG_VideoOptionsPage.WDG_VideoOptionsPage_C.BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnShow__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_VideoOptionsPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnShow__DelegateSignature final
{
public:
	class UAcPanelBase*                           CallingPanel;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_VideoOptionsPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnShow__DelegateSignature) == 0x000008, "Wrong alignment on WDG_VideoOptionsPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnShow__DelegateSignature");
static_assert(sizeof(WDG_VideoOptionsPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnShow__DelegateSignature) == 0x000008, "Wrong size on WDG_VideoOptionsPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnShow__DelegateSignature");
static_assert(offsetof(WDG_VideoOptionsPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnShow__DelegateSignature, CallingPanel) == 0x000000, "Member 'WDG_VideoOptionsPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnShow__DelegateSignature::CallingPanel' has a wrong offset!");

// Function WDG_VideoOptionsPage.WDG_VideoOptionsPage_C.BP_OnMenuNavigation
// 0x0002 (0x0002 - 0x0000)
struct WDG_VideoOptionsPage_C_BP_OnMenuNavigation final
{
public:
	EControllerActionType                         Input;                                             // 0x0000(0x0001)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          isReleased;                                        // 0x0001(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_VideoOptionsPage_C_BP_OnMenuNavigation) == 0x000001, "Wrong alignment on WDG_VideoOptionsPage_C_BP_OnMenuNavigation");
static_assert(sizeof(WDG_VideoOptionsPage_C_BP_OnMenuNavigation) == 0x000002, "Wrong size on WDG_VideoOptionsPage_C_BP_OnMenuNavigation");
static_assert(offsetof(WDG_VideoOptionsPage_C_BP_OnMenuNavigation, Input) == 0x000000, "Member 'WDG_VideoOptionsPage_C_BP_OnMenuNavigation::Input' has a wrong offset!");
static_assert(offsetof(WDG_VideoOptionsPage_C_BP_OnMenuNavigation, isReleased) == 0x000001, "Member 'WDG_VideoOptionsPage_C_BP_OnMenuNavigation::isReleased' has a wrong offset!");

// Function WDG_VideoOptionsPage.WDG_VideoOptionsPage_C.BndEvt__btnPresets_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_VideoOptionsPage_C_BndEvt__btnPresets_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature final
{
public:
	class UWDG_GenericBarItem_C*                  Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_VideoOptionsPage_C_BndEvt__btnPresets_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature) == 0x000008, "Wrong alignment on WDG_VideoOptionsPage_C_BndEvt__btnPresets_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature");
static_assert(sizeof(WDG_VideoOptionsPage_C_BndEvt__btnPresets_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature) == 0x000008, "Wrong size on WDG_VideoOptionsPage_C_BndEvt__btnPresets_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature");
static_assert(offsetof(WDG_VideoOptionsPage_C_BndEvt__btnPresets_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature, Sender) == 0x000000, "Member 'WDG_VideoOptionsPage_C_BndEvt__btnPresets_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature::Sender' has a wrong offset!");

}

