﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomMaterialColorSelector

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"
#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.ExecuteUbergraph_WDG_ShowroomMaterialColorSelector
// 0x0060 (0x0060 - 0x0000)
struct WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FMargin                                K2Node_MakeStruct_Margin;                          // 0x0004(0x0010)(ZeroConstructor, Is<PERSON>lainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0014(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0015(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_2;            // 0x0016(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_3;            // 0x0017(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UGenericSelectorItem*                   K2Node_ComponentBoundEvent_source;                 // 0x0020(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_current_index;          // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_current_value;          // 0x002C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      K2Node_ComponentBoundEvent_Sender;                 // 0x0030(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_ColorCode;              // 0x0038(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_ComponentBoundEvent_Color;                  // 0x003C(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4C[0x4];                                       // 0x004C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPanelBase*                           K2Node_ComponentBoundEvent_Panel_1;                // 0x0050(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcPanelBase*                           K2Node_ComponentBoundEvent_Panel;                  // 0x0058(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector) == 0x000008, "Wrong alignment on WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector");
static_assert(sizeof(WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector) == 0x000060, "Wrong size on WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector, EntryPoint) == 0x000000, "Member 'WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector, K2Node_MakeStruct_Margin) == 0x000004, "Member 'WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector::K2Node_MakeStruct_Margin' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector, CallFunc_MakeLiteralByte_ReturnValue) == 0x000014, "Member 'WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000015, "Member 'WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector, CallFunc_MakeLiteralByte_ReturnValue_2) == 0x000016, "Member 'WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector::CallFunc_MakeLiteralByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector, CallFunc_MakeLiteralByte_ReturnValue_3) == 0x000017, "Member 'WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector::CallFunc_MakeLiteralByte_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector, K2Node_Event_IsDesignTime) == 0x000018, "Member 'WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector, K2Node_ComponentBoundEvent_source) == 0x000020, "Member 'WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector::K2Node_ComponentBoundEvent_source' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector, K2Node_ComponentBoundEvent_current_index) == 0x000028, "Member 'WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector::K2Node_ComponentBoundEvent_current_index' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector, K2Node_ComponentBoundEvent_current_value) == 0x00002C, "Member 'WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector::K2Node_ComponentBoundEvent_current_value' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector, K2Node_ComponentBoundEvent_Sender) == 0x000030, "Member 'WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector::K2Node_ComponentBoundEvent_Sender' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector, K2Node_ComponentBoundEvent_ColorCode) == 0x000038, "Member 'WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector::K2Node_ComponentBoundEvent_ColorCode' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector, K2Node_ComponentBoundEvent_Color) == 0x00003C, "Member 'WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector::K2Node_ComponentBoundEvent_Color' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector, K2Node_ComponentBoundEvent_Panel_1) == 0x000050, "Member 'WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector::K2Node_ComponentBoundEvent_Panel_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector, K2Node_ComponentBoundEvent_Panel) == 0x000058, "Member 'WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector::K2Node_ComponentBoundEvent_Panel' has a wrong offset!");

// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.BndEvt__sliderMaterial_K2Node_ComponentBoundEvent_3_OnAcPanelFocusEvent__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomMaterialColorSelector_C_BndEvt__sliderMaterial_K2Node_ComponentBoundEvent_3_OnAcPanelFocusEvent__DelegateSignature final
{
public:
	class UAcPanelBase*                           panel;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomMaterialColorSelector_C_BndEvt__sliderMaterial_K2Node_ComponentBoundEvent_3_OnAcPanelFocusEvent__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomMaterialColorSelector_C_BndEvt__sliderMaterial_K2Node_ComponentBoundEvent_3_OnAcPanelFocusEvent__DelegateSignature");
static_assert(sizeof(WDG_ShowroomMaterialColorSelector_C_BndEvt__sliderMaterial_K2Node_ComponentBoundEvent_3_OnAcPanelFocusEvent__DelegateSignature) == 0x000008, "Wrong size on WDG_ShowroomMaterialColorSelector_C_BndEvt__sliderMaterial_K2Node_ComponentBoundEvent_3_OnAcPanelFocusEvent__DelegateSignature");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_BndEvt__sliderMaterial_K2Node_ComponentBoundEvent_3_OnAcPanelFocusEvent__DelegateSignature, panel) == 0x000000, "Member 'WDG_ShowroomMaterialColorSelector_C_BndEvt__sliderMaterial_K2Node_ComponentBoundEvent_3_OnAcPanelFocusEvent__DelegateSignature::panel' has a wrong offset!");

// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.BndEvt__tileColor_K2Node_ComponentBoundEvent_0_OnAcPanelFocusEvent__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomMaterialColorSelector_C_BndEvt__tileColor_K2Node_ComponentBoundEvent_0_OnAcPanelFocusEvent__DelegateSignature final
{
public:
	class UAcPanelBase*                           panel;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomMaterialColorSelector_C_BndEvt__tileColor_K2Node_ComponentBoundEvent_0_OnAcPanelFocusEvent__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomMaterialColorSelector_C_BndEvt__tileColor_K2Node_ComponentBoundEvent_0_OnAcPanelFocusEvent__DelegateSignature");
static_assert(sizeof(WDG_ShowroomMaterialColorSelector_C_BndEvt__tileColor_K2Node_ComponentBoundEvent_0_OnAcPanelFocusEvent__DelegateSignature) == 0x000008, "Wrong size on WDG_ShowroomMaterialColorSelector_C_BndEvt__tileColor_K2Node_ComponentBoundEvent_0_OnAcPanelFocusEvent__DelegateSignature");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_BndEvt__tileColor_K2Node_ComponentBoundEvent_0_OnAcPanelFocusEvent__DelegateSignature, panel) == 0x000000, "Member 'WDG_ShowroomMaterialColorSelector_C_BndEvt__tileColor_K2Node_ComponentBoundEvent_0_OnAcPanelFocusEvent__DelegateSignature::panel' has a wrong offset!");

// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.BndEvt__Color_K2Node_ComponentBoundEvent_2_OnColorItemForward__DelegateSignature
// 0x0020 (0x0020 - 0x0000)
struct WDG_ShowroomMaterialColorSelector_C_BndEvt__Color_K2Node_ComponentBoundEvent_2_OnColorItemForward__DelegateSignature final
{
public:
	class UWDG_ShowroomTileItemHorizontal_C*      Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         ColorCode_0;                                       // 0x0008(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Color;                                             // 0x000C(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomMaterialColorSelector_C_BndEvt__Color_K2Node_ComponentBoundEvent_2_OnColorItemForward__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomMaterialColorSelector_C_BndEvt__Color_K2Node_ComponentBoundEvent_2_OnColorItemForward__DelegateSignature");
static_assert(sizeof(WDG_ShowroomMaterialColorSelector_C_BndEvt__Color_K2Node_ComponentBoundEvent_2_OnColorItemForward__DelegateSignature) == 0x000020, "Wrong size on WDG_ShowroomMaterialColorSelector_C_BndEvt__Color_K2Node_ComponentBoundEvent_2_OnColorItemForward__DelegateSignature");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_BndEvt__Color_K2Node_ComponentBoundEvent_2_OnColorItemForward__DelegateSignature, Sender) == 0x000000, "Member 'WDG_ShowroomMaterialColorSelector_C_BndEvt__Color_K2Node_ComponentBoundEvent_2_OnColorItemForward__DelegateSignature::Sender' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_BndEvt__Color_K2Node_ComponentBoundEvent_2_OnColorItemForward__DelegateSignature, ColorCode_0) == 0x000008, "Member 'WDG_ShowroomMaterialColorSelector_C_BndEvt__Color_K2Node_ComponentBoundEvent_2_OnColorItemForward__DelegateSignature::ColorCode_0' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_BndEvt__Color_K2Node_ComponentBoundEvent_2_OnColorItemForward__DelegateSignature, Color) == 0x00000C, "Member 'WDG_ShowroomMaterialColorSelector_C_BndEvt__Color_K2Node_ComponentBoundEvent_2_OnColorItemForward__DelegateSignature::Color' has a wrong offset!");

// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.BndEvt__Material_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomMaterialColorSelector_C_BndEvt__Material_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature final
{
public:
	class UGenericSelectorItem*                   Source;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_index;                                     // 0x0008(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_value;                                     // 0x000C(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomMaterialColorSelector_C_BndEvt__Material_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomMaterialColorSelector_C_BndEvt__Material_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature");
static_assert(sizeof(WDG_ShowroomMaterialColorSelector_C_BndEvt__Material_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature) == 0x000010, "Wrong size on WDG_ShowroomMaterialColorSelector_C_BndEvt__Material_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_BndEvt__Material_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature, Source) == 0x000000, "Member 'WDG_ShowroomMaterialColorSelector_C_BndEvt__Material_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature::Source' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_BndEvt__Material_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature, current_index) == 0x000008, "Member 'WDG_ShowroomMaterialColorSelector_C_BndEvt__Material_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature::current_index' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_BndEvt__Material_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature, current_value) == 0x00000C, "Member 'WDG_ShowroomMaterialColorSelector_C_BndEvt__Material_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature::current_value' has a wrong offset!");

// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_ShowroomMaterialColorSelector_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomMaterialColorSelector_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_ShowroomMaterialColorSelector_C_PreConstruct");
static_assert(sizeof(WDG_ShowroomMaterialColorSelector_C_PreConstruct) == 0x000001, "Wrong size on WDG_ShowroomMaterialColorSelector_C_PreConstruct");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_ShowroomMaterialColorSelector_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.SetColor
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomMaterialColorSelector_C_SetColor final
{
public:
	struct FLinearColor                           InColorAndOpacity;                                 // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomMaterialColorSelector_C_SetColor) == 0x000004, "Wrong alignment on WDG_ShowroomMaterialColorSelector_C_SetColor");
static_assert(sizeof(WDG_ShowroomMaterialColorSelector_C_SetColor) == 0x000010, "Wrong size on WDG_ShowroomMaterialColorSelector_C_SetColor");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_SetColor, InColorAndOpacity) == 0x000000, "Member 'WDG_ShowroomMaterialColorSelector_C_SetColor::InColorAndOpacity' has a wrong offset!");

// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.SetColorByCode
// 0x0014 (0x0014 - 0x0000)
struct WDG_ShowroomMaterialColorSelector_C_SetColorByCode final
{
public:
	int32                                         ColorCode_0;                                       // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_SetTileColorByCode_Color;                 // 0x0004(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomMaterialColorSelector_C_SetColorByCode) == 0x000004, "Wrong alignment on WDG_ShowroomMaterialColorSelector_C_SetColorByCode");
static_assert(sizeof(WDG_ShowroomMaterialColorSelector_C_SetColorByCode) == 0x000014, "Wrong size on WDG_ShowroomMaterialColorSelector_C_SetColorByCode");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_SetColorByCode, ColorCode_0) == 0x000000, "Member 'WDG_ShowroomMaterialColorSelector_C_SetColorByCode::ColorCode_0' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_SetColorByCode, CallFunc_SetTileColorByCode_Color) == 0x000004, "Member 'WDG_ShowroomMaterialColorSelector_C_SetColorByCode::CallFunc_SetTileColorByCode_Color' has a wrong offset!");

// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.SetMaterials
// 0x00A0 (0x00A0 - 0x0000)
struct WDG_ShowroomMaterialColorSelector_C_SetMaterials final
{
public:
	TMap<int32, class FText>                      Source_Materials;                                  // 0x0000(0x0050)(BlueprintVisible, BlueprintReadOnly, Parm)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0050(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0054(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0058(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_5C[0x4];                                       // 0x005C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<int32>                                 CallFunc_Map_Keys_Keys;                            // 0x0060(0x0010)(ReferenceParm)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0070(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Get_Item;                           // 0x0074(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0078(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_79[0x7];                                       // 0x0079(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Map_Find_Value;                           // 0x0080(0x0018)()
	bool                                          CallFunc_Map_Find_ReturnValue;                     // 0x0098(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomMaterialColorSelector_C_SetMaterials) == 0x000008, "Wrong alignment on WDG_ShowroomMaterialColorSelector_C_SetMaterials");
static_assert(sizeof(WDG_ShowroomMaterialColorSelector_C_SetMaterials) == 0x0000A0, "Wrong size on WDG_ShowroomMaterialColorSelector_C_SetMaterials");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_SetMaterials, Source_Materials) == 0x000000, "Member 'WDG_ShowroomMaterialColorSelector_C_SetMaterials::Source_Materials' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_SetMaterials, Temp_int_Array_Index_Variable) == 0x000050, "Member 'WDG_ShowroomMaterialColorSelector_C_SetMaterials::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_SetMaterials, Temp_int_Loop_Counter_Variable) == 0x000054, "Member 'WDG_ShowroomMaterialColorSelector_C_SetMaterials::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_SetMaterials, CallFunc_Add_IntInt_ReturnValue) == 0x000058, "Member 'WDG_ShowroomMaterialColorSelector_C_SetMaterials::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_SetMaterials, CallFunc_Map_Keys_Keys) == 0x000060, "Member 'WDG_ShowroomMaterialColorSelector_C_SetMaterials::CallFunc_Map_Keys_Keys' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_SetMaterials, CallFunc_Array_Length_ReturnValue) == 0x000070, "Member 'WDG_ShowroomMaterialColorSelector_C_SetMaterials::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_SetMaterials, CallFunc_Array_Get_Item) == 0x000074, "Member 'WDG_ShowroomMaterialColorSelector_C_SetMaterials::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_SetMaterials, CallFunc_Less_IntInt_ReturnValue) == 0x000078, "Member 'WDG_ShowroomMaterialColorSelector_C_SetMaterials::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_SetMaterials, CallFunc_Map_Find_Value) == 0x000080, "Member 'WDG_ShowroomMaterialColorSelector_C_SetMaterials::CallFunc_Map_Find_Value' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_SetMaterials, CallFunc_Map_Find_ReturnValue) == 0x000098, "Member 'WDG_ShowroomMaterialColorSelector_C_SetMaterials::CallFunc_Map_Find_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.GetSelectedMaterial
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomMaterialColorSelector_C_GetSelectedMaterial final
{
public:
	int32                                         ReturnValue;                                       // 0x0000(0x0004)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetValue_ReturnValue;                     // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomMaterialColorSelector_C_GetSelectedMaterial) == 0x000004, "Wrong alignment on WDG_ShowroomMaterialColorSelector_C_GetSelectedMaterial");
static_assert(sizeof(WDG_ShowroomMaterialColorSelector_C_GetSelectedMaterial) == 0x000008, "Wrong size on WDG_ShowroomMaterialColorSelector_C_GetSelectedMaterial");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_GetSelectedMaterial, ReturnValue) == 0x000000, "Member 'WDG_ShowroomMaterialColorSelector_C_GetSelectedMaterial::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_GetSelectedMaterial, CallFunc_GetValue_ReturnValue) == 0x000004, "Member 'WDG_ShowroomMaterialColorSelector_C_GetSelectedMaterial::CallFunc_GetValue_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.GetSelectedColor
// 0x0004 (0x0004 - 0x0000)
struct WDG_ShowroomMaterialColorSelector_C_GetSelectedColor final
{
public:
	int32                                         ReturnValue;                                       // 0x0000(0x0004)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomMaterialColorSelector_C_GetSelectedColor) == 0x000004, "Wrong alignment on WDG_ShowroomMaterialColorSelector_C_GetSelectedColor");
static_assert(sizeof(WDG_ShowroomMaterialColorSelector_C_GetSelectedColor) == 0x000004, "Wrong size on WDG_ShowroomMaterialColorSelector_C_GetSelectedColor");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_GetSelectedColor, ReturnValue) == 0x000000, "Member 'WDG_ShowroomMaterialColorSelector_C_GetSelectedColor::ReturnValue' has a wrong offset!");

// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.SetMaterial
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomMaterialColorSelector_C_SetMaterial final
{
public:
	int32                                         MaterialKey_0;                                     // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Supress_Event;                                     // 0x0004(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomMaterialColorSelector_C_SetMaterial) == 0x000004, "Wrong alignment on WDG_ShowroomMaterialColorSelector_C_SetMaterial");
static_assert(sizeof(WDG_ShowroomMaterialColorSelector_C_SetMaterial) == 0x000008, "Wrong size on WDG_ShowroomMaterialColorSelector_C_SetMaterial");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_SetMaterial, MaterialKey_0) == 0x000000, "Member 'WDG_ShowroomMaterialColorSelector_C_SetMaterial::MaterialKey_0' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_SetMaterial, Supress_Event) == 0x000004, "Member 'WDG_ShowroomMaterialColorSelector_C_SetMaterial::Supress_Event' has a wrong offset!");

// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.SetMaterialAndColor
// 0x0028 (0x0028 - 0x0000)
struct WDG_ShowroomMaterialColorSelector_C_SetMaterialAndColor final
{
public:
	class FName                                   MaterialKey_0;                                     // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         ColorCode_0;                                       // 0x0008(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C[0x4];                                        // 0x000C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Conv_NameToString_ReturnValue;            // 0x0010(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	float                                         CallFunc_Conv_StringToFloat_ReturnValue;           // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue;                       // 0x0024(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomMaterialColorSelector_C_SetMaterialAndColor) == 0x000008, "Wrong alignment on WDG_ShowroomMaterialColorSelector_C_SetMaterialAndColor");
static_assert(sizeof(WDG_ShowroomMaterialColorSelector_C_SetMaterialAndColor) == 0x000028, "Wrong size on WDG_ShowroomMaterialColorSelector_C_SetMaterialAndColor");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_SetMaterialAndColor, MaterialKey_0) == 0x000000, "Member 'WDG_ShowroomMaterialColorSelector_C_SetMaterialAndColor::MaterialKey_0' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_SetMaterialAndColor, ColorCode_0) == 0x000008, "Member 'WDG_ShowroomMaterialColorSelector_C_SetMaterialAndColor::ColorCode_0' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_SetMaterialAndColor, CallFunc_Conv_NameToString_ReturnValue) == 0x000010, "Member 'WDG_ShowroomMaterialColorSelector_C_SetMaterialAndColor::CallFunc_Conv_NameToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_SetMaterialAndColor, CallFunc_Conv_StringToFloat_ReturnValue) == 0x000020, "Member 'WDG_ShowroomMaterialColorSelector_C_SetMaterialAndColor::CallFunc_Conv_StringToFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomMaterialColorSelector_C_SetMaterialAndColor, CallFunc_FTrunc_ReturnValue) == 0x000024, "Member 'WDG_ShowroomMaterialColorSelector_C_SetMaterialAndColor::CallFunc_FTrunc_ReturnValue' has a wrong offset!");

}

