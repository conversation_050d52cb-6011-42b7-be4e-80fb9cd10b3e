﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ReplayButton

#include "Basic.hpp"

#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ReplayButton.WDG_ReplayButton_C
// 0x0018 (0x0278 - 0x0260)
class UWDG_ReplayButton_C final : public UUserWidget
{
public:
	class UNamedSlot*                             Bottom;                                            // 0x0260(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             Mid;                                               // 0x0268(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             Top;                                               // 0x0270(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ReplayButton_C">();
	}
	static class UWDG_ReplayButton_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ReplayButton_C>();
	}
};
static_assert(alignof(UWDG_ReplayButton_C) == 0x000008, "Wrong alignment on UWDG_ReplayButton_C");
static_assert(sizeof(UWDG_ReplayButton_C) == 0x000278, "Wrong size on UWDG_ReplayButton_C");
static_assert(offsetof(UWDG_ReplayButton_C, Bottom) == 0x000260, "Member 'UWDG_ReplayButton_C::Bottom' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayButton_C, Mid) == 0x000268, "Member 'UWDG_ReplayButton_C::Mid' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayButton_C, Top) == 0x000270, "Member 'UWDG_ReplayButton_C::Top' has a wrong offset!");

}

