﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TyreTempsItemRight

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_TyreTempsItemRight.WDG_TyreTempsItemRight_C
// 0x0010 (0x0318 - 0x0308)
class UWDG_TyreTempsItemRight_C final : public UTyreTemps01ItemWidget
{
public:
	class URetainerBox*                           retainerInner;                                     // 0x0308(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class URetainerBox*                           retainerOuter;                                     // 0x0310(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSki<PERSON>, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_TyreTempsItemRight_C">();
	}
	static class UWDG_TyreTempsItemRight_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_TyreTempsItemRight_C>();
	}
};
static_assert(alignof(UWDG_TyreTempsItemRight_C) == 0x000008, "Wrong alignment on UWDG_TyreTempsItemRight_C");
static_assert(sizeof(UWDG_TyreTempsItemRight_C) == 0x000318, "Wrong size on UWDG_TyreTempsItemRight_C");
static_assert(offsetof(UWDG_TyreTempsItemRight_C, retainerInner) == 0x000308, "Member 'UWDG_TyreTempsItemRight_C::retainerInner' has a wrong offset!");
static_assert(offsetof(UWDG_TyreTempsItemRight_C, retainerOuter) == 0x000310, "Member 'UWDG_TyreTempsItemRight_C::retainerOuter' has a wrong offset!");

}

