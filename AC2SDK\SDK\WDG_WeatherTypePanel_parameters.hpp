﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_WeatherTypePanel

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_WeatherTypePanel.WDG_WeatherTypePanel_C.ExecuteUbergraph_WDG_WeatherTypePanel
// 0x00A8 (0x00A8 - 0x0000)
struct WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate;              // 0x0004(0x0010)(ZeroConstructor, NoDestructor)
	float                                         CallFunc_MakeLiteralFloat_ReturnValue;             // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_1;              // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0028(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_29[0x3];                                       // 0x0029(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FFocusEvent                            K2Node_Event_InFocusEvent_1;                       // 0x002C(0x0008)(NoDestructor)
	struct FFocusEvent                            K2Node_Event_InFocusEvent;                         // 0x0034(0x0008)(NoDestructor)
	bool                                          CallFunc_SetSelected_Changed;                      // 0x003C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_3D[0x3];                                       // 0x003D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Conv_ByteToInt_ReturnValue;               // 0x0040(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_44[0x4];                                       // 0x0044(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Conv_IntToString_ReturnValue;             // 0x0048(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FName                                   CallFunc_Conv_StringToName_ReturnValue;            // 0x0058(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FMeteoInfos                            CallFunc_GetDataTableRowFromName_OutRow;           // 0x0060(0x0040)()
	bool                                          CallFunc_GetDataTableRowFromName_ReturnValue;      // 0x00A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel) == 0x000008, "Wrong alignment on WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel");
static_assert(sizeof(WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel) == 0x0000A8, "Wrong size on WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel");
static_assert(offsetof(WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel, EntryPoint) == 0x000000, "Member 'WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel, K2Node_CreateDelegate_OutputDelegate) == 0x000004, "Member 'WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel, CallFunc_MakeLiteralFloat_ReturnValue) == 0x000014, "Member 'WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel::CallFunc_MakeLiteralFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel, CallFunc_PlayAnimation_ReturnValue) == 0x000018, "Member 'WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel, CallFunc_PlayAnimation_ReturnValue_1) == 0x000020, "Member 'WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel::CallFunc_PlayAnimation_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel, K2Node_Event_IsDesignTime) == 0x000028, "Member 'WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel, K2Node_Event_InFocusEvent_1) == 0x00002C, "Member 'WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel::K2Node_Event_InFocusEvent_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel, K2Node_Event_InFocusEvent) == 0x000034, "Member 'WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel::K2Node_Event_InFocusEvent' has a wrong offset!");
static_assert(offsetof(WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel, CallFunc_SetSelected_Changed) == 0x00003C, "Member 'WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel::CallFunc_SetSelected_Changed' has a wrong offset!");
static_assert(offsetof(WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel, CallFunc_Conv_ByteToInt_ReturnValue) == 0x000040, "Member 'WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel::CallFunc_Conv_ByteToInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel, CallFunc_Conv_IntToString_ReturnValue) == 0x000048, "Member 'WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel::CallFunc_Conv_IntToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel, CallFunc_Conv_StringToName_ReturnValue) == 0x000058, "Member 'WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel::CallFunc_Conv_StringToName_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel, CallFunc_GetDataTableRowFromName_OutRow) == 0x000060, "Member 'WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel::CallFunc_GetDataTableRowFromName_OutRow' has a wrong offset!");
static_assert(offsetof(WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel, CallFunc_GetDataTableRowFromName_ReturnValue) == 0x0000A0, "Member 'WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel::CallFunc_GetDataTableRowFromName_ReturnValue' has a wrong offset!");

// Function WDG_WeatherTypePanel.WDG_WeatherTypePanel_C.OnRemovedFromFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_WeatherTypePanel_C_OnRemovedFromFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_WeatherTypePanel_C_OnRemovedFromFocusPath) == 0x000004, "Wrong alignment on WDG_WeatherTypePanel_C_OnRemovedFromFocusPath");
static_assert(sizeof(WDG_WeatherTypePanel_C_OnRemovedFromFocusPath) == 0x000008, "Wrong size on WDG_WeatherTypePanel_C_OnRemovedFromFocusPath");
static_assert(offsetof(WDG_WeatherTypePanel_C_OnRemovedFromFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_WeatherTypePanel_C_OnRemovedFromFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_WeatherTypePanel.WDG_WeatherTypePanel_C.OnAddedToFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_WeatherTypePanel_C_OnAddedToFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_WeatherTypePanel_C_OnAddedToFocusPath) == 0x000004, "Wrong alignment on WDG_WeatherTypePanel_C_OnAddedToFocusPath");
static_assert(sizeof(WDG_WeatherTypePanel_C_OnAddedToFocusPath) == 0x000008, "Wrong size on WDG_WeatherTypePanel_C_OnAddedToFocusPath");
static_assert(offsetof(WDG_WeatherTypePanel_C_OnAddedToFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_WeatherTypePanel_C_OnAddedToFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_WeatherTypePanel.WDG_WeatherTypePanel_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_WeatherTypePanel_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_WeatherTypePanel_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_WeatherTypePanel_C_PreConstruct");
static_assert(sizeof(WDG_WeatherTypePanel_C_PreConstruct) == 0x000001, "Wrong size on WDG_WeatherTypePanel_C_PreConstruct");
static_assert(offsetof(WDG_WeatherTypePanel_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_WeatherTypePanel_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WDG_WeatherTypePanel.WDG_WeatherTypePanel_C.Set Colors Based on Selected
// 0x0058 (0x0058 - 0x0000)
struct WDG_WeatherTypePanel_C_Set_Colors_Based_on_Selected final
{
public:
	bool                                          is_selected;                                       // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateColor                            K2Node_MakeStruct_SlateColor;                      // 0x0008(0x0028)()
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_1;                    // 0x0030(0x0028)()
};
static_assert(alignof(WDG_WeatherTypePanel_C_Set_Colors_Based_on_Selected) == 0x000008, "Wrong alignment on WDG_WeatherTypePanel_C_Set_Colors_Based_on_Selected");
static_assert(sizeof(WDG_WeatherTypePanel_C_Set_Colors_Based_on_Selected) == 0x000058, "Wrong size on WDG_WeatherTypePanel_C_Set_Colors_Based_on_Selected");
static_assert(offsetof(WDG_WeatherTypePanel_C_Set_Colors_Based_on_Selected, is_selected) == 0x000000, "Member 'WDG_WeatherTypePanel_C_Set_Colors_Based_on_Selected::is_selected' has a wrong offset!");
static_assert(offsetof(WDG_WeatherTypePanel_C_Set_Colors_Based_on_Selected, K2Node_MakeStruct_SlateColor) == 0x000008, "Member 'WDG_WeatherTypePanel_C_Set_Colors_Based_on_Selected::K2Node_MakeStruct_SlateColor' has a wrong offset!");
static_assert(offsetof(WDG_WeatherTypePanel_C_Set_Colors_Based_on_Selected, K2Node_MakeStruct_SlateColor_1) == 0x000030, "Member 'WDG_WeatherTypePanel_C_Set_Colors_Based_on_Selected::K2Node_MakeStruct_SlateColor_1' has a wrong offset!");

// Function WDG_WeatherTypePanel.WDG_WeatherTypePanel_C.SetSelected
// 0x0003 (0x0003 - 0x0000)
struct WDG_WeatherTypePanel_C_SetSelected final
{
public:
	bool                                          IsSelected_0;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          Changed;                                           // 0x0001(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_NotEqual_BoolBool_ReturnValue;            // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_WeatherTypePanel_C_SetSelected) == 0x000001, "Wrong alignment on WDG_WeatherTypePanel_C_SetSelected");
static_assert(sizeof(WDG_WeatherTypePanel_C_SetSelected) == 0x000003, "Wrong size on WDG_WeatherTypePanel_C_SetSelected");
static_assert(offsetof(WDG_WeatherTypePanel_C_SetSelected, IsSelected_0) == 0x000000, "Member 'WDG_WeatherTypePanel_C_SetSelected::IsSelected_0' has a wrong offset!");
static_assert(offsetof(WDG_WeatherTypePanel_C_SetSelected, Changed) == 0x000001, "Member 'WDG_WeatherTypePanel_C_SetSelected::Changed' has a wrong offset!");
static_assert(offsetof(WDG_WeatherTypePanel_C_SetSelected, CallFunc_NotEqual_BoolBool_ReturnValue) == 0x000002, "Member 'WDG_WeatherTypePanel_C_SetSelected::CallFunc_NotEqual_BoolBool_ReturnValue' has a wrong offset!");

}

