﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ServerInfoSessionItem

#include "Basic.hpp"

#include "Engine_structs.hpp"


namespace SDK::Params
{

// Function WDG_ServerInfoSessionItem.WDG_ServerInfoSessionItem_C.ExecuteUbergraph_WDG_ServerInfoSessionItem
// 0x00E0 (0x00E0 - 0x0000)
struct WDG_ServerInfoSessionItem_C_ExecuteUbergraph_WDG_ServerInfoSessionItem final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_5[0x3];                                        // 0x0005(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData;              // 0x0008(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	TArray<struct FFormatArgumentData>            K2Node_MakeArray_Array;                            // 0x0048(0x0010)(ReferenceParm)
	class FText                                   CallFunc_Format_ReturnValue;                       // 0x0058(0x0018)()
	bool                                          CallFunc_InRange_IntInt_ReturnValue;               // 0x0070(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_71[0x7];                                       // 0x0071(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData_1;            // 0x0078(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	TArray<struct FFormatArgumentData>            K2Node_MakeArray_Array_1;                          // 0x00B8(0x0010)(ReferenceParm)
	class FText                                   CallFunc_Format_ReturnValue_1;                     // 0x00C8(0x0018)()
};
static_assert(alignof(WDG_ServerInfoSessionItem_C_ExecuteUbergraph_WDG_ServerInfoSessionItem) == 0x000008, "Wrong alignment on WDG_ServerInfoSessionItem_C_ExecuteUbergraph_WDG_ServerInfoSessionItem");
static_assert(sizeof(WDG_ServerInfoSessionItem_C_ExecuteUbergraph_WDG_ServerInfoSessionItem) == 0x0000E0, "Wrong size on WDG_ServerInfoSessionItem_C_ExecuteUbergraph_WDG_ServerInfoSessionItem");
static_assert(offsetof(WDG_ServerInfoSessionItem_C_ExecuteUbergraph_WDG_ServerInfoSessionItem, EntryPoint) == 0x000000, "Member 'WDG_ServerInfoSessionItem_C_ExecuteUbergraph_WDG_ServerInfoSessionItem::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoSessionItem_C_ExecuteUbergraph_WDG_ServerInfoSessionItem, K2Node_Event_IsDesignTime) == 0x000004, "Member 'WDG_ServerInfoSessionItem_C_ExecuteUbergraph_WDG_ServerInfoSessionItem::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoSessionItem_C_ExecuteUbergraph_WDG_ServerInfoSessionItem, K2Node_MakeStruct_FormatArgumentData) == 0x000008, "Member 'WDG_ServerInfoSessionItem_C_ExecuteUbergraph_WDG_ServerInfoSessionItem::K2Node_MakeStruct_FormatArgumentData' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoSessionItem_C_ExecuteUbergraph_WDG_ServerInfoSessionItem, K2Node_MakeArray_Array) == 0x000048, "Member 'WDG_ServerInfoSessionItem_C_ExecuteUbergraph_WDG_ServerInfoSessionItem::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoSessionItem_C_ExecuteUbergraph_WDG_ServerInfoSessionItem, CallFunc_Format_ReturnValue) == 0x000058, "Member 'WDG_ServerInfoSessionItem_C_ExecuteUbergraph_WDG_ServerInfoSessionItem::CallFunc_Format_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoSessionItem_C_ExecuteUbergraph_WDG_ServerInfoSessionItem, CallFunc_InRange_IntInt_ReturnValue) == 0x000070, "Member 'WDG_ServerInfoSessionItem_C_ExecuteUbergraph_WDG_ServerInfoSessionItem::CallFunc_InRange_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoSessionItem_C_ExecuteUbergraph_WDG_ServerInfoSessionItem, K2Node_MakeStruct_FormatArgumentData_1) == 0x000078, "Member 'WDG_ServerInfoSessionItem_C_ExecuteUbergraph_WDG_ServerInfoSessionItem::K2Node_MakeStruct_FormatArgumentData_1' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoSessionItem_C_ExecuteUbergraph_WDG_ServerInfoSessionItem, K2Node_MakeArray_Array_1) == 0x0000B8, "Member 'WDG_ServerInfoSessionItem_C_ExecuteUbergraph_WDG_ServerInfoSessionItem::K2Node_MakeArray_Array_1' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoSessionItem_C_ExecuteUbergraph_WDG_ServerInfoSessionItem, CallFunc_Format_ReturnValue_1) == 0x0000C8, "Member 'WDG_ServerInfoSessionItem_C_ExecuteUbergraph_WDG_ServerInfoSessionItem::CallFunc_Format_ReturnValue_1' has a wrong offset!");

// Function WDG_ServerInfoSessionItem.WDG_ServerInfoSessionItem_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_ServerInfoSessionItem_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ServerInfoSessionItem_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_ServerInfoSessionItem_C_PreConstruct");
static_assert(sizeof(WDG_ServerInfoSessionItem_C_PreConstruct) == 0x000001, "Wrong size on WDG_ServerInfoSessionItem_C_PreConstruct");
static_assert(offsetof(WDG_ServerInfoSessionItem_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_ServerInfoSessionItem_C_PreConstruct::IsDesignTime' has a wrong offset!");

}

