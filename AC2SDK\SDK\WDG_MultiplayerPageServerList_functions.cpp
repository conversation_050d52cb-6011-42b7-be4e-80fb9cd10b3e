﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_MultiplayerPageServerList

#include "Basic.hpp"

#include "WDG_MultiplayerPageServerList_classes.hpp"
#include "WDG_MultiplayerPageServerList_parameters.hpp"


namespace SDK
{

// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.ExecuteUbergraph_WDG_MultiplayerPageServerList
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_MultiplayerPageServerList_C::ExecuteUbergraph_WDG_MultiplayerPageServerList(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "ExecuteUbergraph_WDG_MultiplayerPageServerList");

	Params::WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.BndEvt__ServerList_K2Node_ComponentBoundEvent_3_OnUserScrolledEvent__DelegateSignature
// (BlueprintEvent)
// Parameters:
// float                                   CurrentOffset                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_MultiplayerPageServerList_C::BndEvt__ServerList_K2Node_ComponentBoundEvent_3_OnUserScrolledEvent__DelegateSignature(float CurrentOffset)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "BndEvt__ServerList_K2Node_ComponentBoundEvent_3_OnUserScrolledEvent__DelegateSignature");

	Params::WDG_MultiplayerPageServerList_C_BndEvt__ServerList_K2Node_ComponentBoundEvent_3_OnUserScrolledEvent__DelegateSignature Parms{};

	Parms.CurrentOffset = CurrentOffset;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.ServerPinged
// (Event, Protected, BlueprintEvent)
// Parameters:
// int32                                   number_of_servers_pings                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   total_servers                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_MultiplayerPageServerList_C::ServerPinged(int32 number_of_servers_pings, int32 total_servers)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "ServerPinged");

	Params::WDG_MultiplayerPageServerList_C_ServerPinged Parms{};

	Parms.number_of_servers_pings = number_of_servers_pings;
	Parms.total_servers = total_servers;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.ListReceived
// (Event, Protected, BlueprintEvent)

void UWDG_MultiplayerPageServerList_C::ListReceived()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "ListReceived");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.ListSorted
// (Event, Protected, BlueprintEvent)

void UWDG_MultiplayerPageServerList_C::ListSorted()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "ListSorted");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.ListTimedOut
// (Event, Protected, BlueprintEvent)

void UWDG_MultiplayerPageServerList_C::ListTimedOut()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "ListTimedOut");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.ListRequested
// (Event, Protected, BlueprintEvent)

void UWDG_MultiplayerPageServerList_C::ListRequested()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "ListRequested");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.BndEvt__btnJoinable_K2Node_ComponentBoundEvent_12_OnToggled__DelegateSignature
// (BlueprintEvent)
// Parameters:
// bool                                    isOn                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_MultiplayerPageServerList_C::BndEvt__btnJoinable_K2Node_ComponentBoundEvent_12_OnToggled__DelegateSignature(bool isOn)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "BndEvt__btnJoinable_K2Node_ComponentBoundEvent_12_OnToggled__DelegateSignature");

	Params::WDG_MultiplayerPageServerList_C_BndEvt__btnJoinable_K2Node_ComponentBoundEvent_12_OnToggled__DelegateSignature Parms{};

	Parms.isOn = isOn;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.BndEvt__sliderPingLimit_K2Node_ComponentBoundEvent_11_SelectorChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UGenericSelectorItem*             Source                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_index                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_value                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_MultiplayerPageServerList_C::BndEvt__sliderPingLimit_K2Node_ComponentBoundEvent_11_SelectorChanged__DelegateSignature(class UGenericSelectorItem* Source, int32 current_index, int32 current_value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "BndEvt__sliderPingLimit_K2Node_ComponentBoundEvent_11_SelectorChanged__DelegateSignature");

	Params::WDG_MultiplayerPageServerList_C_BndEvt__sliderPingLimit_K2Node_ComponentBoundEvent_11_SelectorChanged__DelegateSignature Parms{};

	Parms.Source = Source;
	Parms.current_index = current_index;
	Parms.current_value = current_value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.BndEvt__btnSortPing_K2Node_ComponentBoundEvent_10_OnToggled__DelegateSignature
// (BlueprintEvent)
// Parameters:
// bool                                    isOn                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_MultiplayerPageServerList_C::BndEvt__btnSortPing_K2Node_ComponentBoundEvent_10_OnToggled__DelegateSignature(bool isOn)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "BndEvt__btnSortPing_K2Node_ComponentBoundEvent_10_OnToggled__DelegateSignature");

	Params::WDG_MultiplayerPageServerList_C_BndEvt__btnSortPing_K2Node_ComponentBoundEvent_10_OnToggled__DelegateSignature Parms{};

	Parms.isOn = isOn;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.BndEvt__btnSortDrivers_K2Node_ComponentBoundEvent_9_OnToggled__DelegateSignature
// (BlueprintEvent)
// Parameters:
// bool                                    isOn                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_MultiplayerPageServerList_C::BndEvt__btnSortDrivers_K2Node_ComponentBoundEvent_9_OnToggled__DelegateSignature(bool isOn)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "BndEvt__btnSortDrivers_K2Node_ComponentBoundEvent_9_OnToggled__DelegateSignature");

	Params::WDG_MultiplayerPageServerList_C_BndEvt__btnSortDrivers_K2Node_ComponentBoundEvent_9_OnToggled__DelegateSignature Parms{};

	Parms.isOn = isOn;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.BndEvt__btnAdvanced_K2Node_ComponentBoundEvent_8_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_MultiplayerPageServerList_C::BndEvt__btnAdvanced_K2Node_ComponentBoundEvent_8_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "BndEvt__btnAdvanced_K2Node_ComponentBoundEvent_8_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.BndEvt__btnCarSelection_K2Node_ComponentBoundEvent_7_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_MultiplayerPageServerList_C::BndEvt__btnCarSelection_K2Node_ComponentBoundEvent_7_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "BndEvt__btnCarSelection_K2Node_ComponentBoundEvent_7_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.BndEvt__btnConnect_K2Node_ComponentBoundEvent_6_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_MultiplayerPageServerList_C::BndEvt__btnConnect_K2Node_ComponentBoundEvent_6_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "BndEvt__btnConnect_K2Node_ComponentBoundEvent_6_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.BndEvt__btnQuickjoin_K2Node_ComponentBoundEvent_5_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_MultiplayerPageServerList_C::BndEvt__btnQuickjoin_K2Node_ComponentBoundEvent_5_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "BndEvt__btnQuickjoin_K2Node_ComponentBoundEvent_5_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.BndEvt__btnRefreshLAN_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_MultiplayerPageServerList_C::BndEvt__btnRefreshLAN_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "BndEvt__btnRefreshLAN_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.BndEvt__btnRefresh_K2Node_ComponentBoundEvent_3_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_MultiplayerPageServerList_C::BndEvt__btnRefresh_K2Node_ComponentBoundEvent_3_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "BndEvt__btnRefresh_K2Node_ComponentBoundEvent_3_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.OnServerDisconnection
// (Event, Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// EDisconnectionReason                    reason                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_MultiplayerPageServerList_C::OnServerDisconnection(EDisconnectionReason reason)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "OnServerDisconnection");

	Params::WDG_MultiplayerPageServerList_C_OnServerDisconnection Parms{};

	Parms.reason = reason;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_2_OnCarGroupSelected__DelegateSignature
// (BlueprintEvent)
// Parameters:
// EMPCarGroup                             CarGroup                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_MultiplayerPageServerList_C::BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_2_OnCarGroupSelected__DelegateSignature(EMPCarGroup CarGroup)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_2_OnCarGroupSelected__DelegateSignature");

	Params::WDG_MultiplayerPageServerList_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_2_OnCarGroupSelected__DelegateSignature Parms{};

	Parms.CarGroup = CarGroup;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.BndEvt__popupDLC_K2Node_ComponentBoundEvent_1_OnYes__DelegateSignature
// (BlueprintEvent)

void UWDG_MultiplayerPageServerList_C::BndEvt__popupDLC_K2Node_ComponentBoundEvent_1_OnYes__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "BndEvt__popupDLC_K2Node_ComponentBoundEvent_1_OnYes__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.BndEvt__popupDLC_K2Node_ComponentBoundEvent_0_OnNo__DelegateSignature
// (BlueprintEvent)

void UWDG_MultiplayerPageServerList_C::BndEvt__popupDLC_K2Node_ComponentBoundEvent_0_OnNo__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "BndEvt__popupDLC_K2Node_ComponentBoundEvent_0_OnNo__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.OnSeasonUnavailable
// (Event, Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// EContentType                            content_type                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_MultiplayerPageServerList_C::OnSeasonUnavailable(EContentType content_type)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "OnSeasonUnavailable");

	Params::WDG_MultiplayerPageServerList_C_OnSeasonUnavailable Parms{};

	Parms.content_type = content_type;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.BP_StartPage
// (Event, Public, BlueprintEvent)

void UWDG_MultiplayerPageServerList_C::BP_StartPage()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "BP_StartPage");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.BP_JoinServerFailed
// (Event, Protected, BlueprintEvent)

void UWDG_MultiplayerPageServerList_C::BP_JoinServerFailed()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "BP_JoinServerFailed");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.FocusToConnect
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// EUINavigation                           Navigation_0                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWidget*                          ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

class UWidget* UWDG_MultiplayerPageServerList_C::FocusToConnect(EUINavigation Navigation_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "FocusToConnect");

	Params::WDG_MultiplayerPageServerList_C_FocusToConnect Parms{};

	Parms.Navigation_0 = Navigation_0;

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.FocusToRefresh
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// EUINavigation                           Navigation_0                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWidget*                          ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

class UWidget* UWDG_MultiplayerPageServerList_C::FocusToRefresh(EUINavigation Navigation_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "FocusToRefresh");

	Params::WDG_MultiplayerPageServerList_C_FocusToRefresh Parms{};

	Parms.Navigation_0 = Navigation_0;

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.PreConnectionCheck
// (Event, Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FCarInfo&                  Car                                                    (BlueprintVisible, BlueprintReadOnly, Parm)
// const struct FCircuitInfo&              circuit                                                (BlueprintVisible, BlueprintReadOnly, Parm)
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor)

bool UWDG_MultiplayerPageServerList_C::PreConnectionCheck(const struct FCarInfo& Car, const struct FCircuitInfo& circuit)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "PreConnectionCheck");

	Params::WDG_MultiplayerPageServerList_C_PreConnectionCheck Parms{};

	Parms.Car = std::move(Car);
	Parms.circuit = std::move(circuit);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.ShowErrorMessage
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const class FText&                      MessageText                                            (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_MultiplayerPageServerList_C::ShowErrorMessage(const class FText& MessageText)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "ShowErrorMessage");

	Params::WDG_MultiplayerPageServerList_C_ShowErrorMessage Parms{};

	Parms.MessageText = std::move(MessageText);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.HideErrorMessage
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_MultiplayerPageServerList_C::HideErrorMessage()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "HideErrorMessage");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.OnPreviewKeyDown
// (Event, Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FKeyEvent&                 InKeyEvent                                             (BlueprintVisible, BlueprintReadOnly, Parm)
// struct FEventReply                      ReturnValue                                            (Parm, OutParm, ReturnParm)

struct FEventReply UWDG_MultiplayerPageServerList_C::OnPreviewKeyDown(const struct FGeometry& MyGeometry, const struct FKeyEvent& InKeyEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPageServerList_C", "OnPreviewKeyDown");

	Params::WDG_MultiplayerPageServerList_C_OnPreviewKeyDown Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InKeyEvent = std::move(InKeyEvent);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}

}

