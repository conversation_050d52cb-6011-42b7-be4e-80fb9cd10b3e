﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_VideoOptionsPage

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_VideoOptionsPage.WDG_VideoOptionsPage_C
// 0x0078 (0x0798 - 0x0720)
class UWDG_VideoOptionsPage_C final : public UVideoOptionsPage
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0720(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       ShowroomFade;                                      // 0x0728(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       PageFade;                                          // 0x0730(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWDG_FooterButton_C*                    Back;                                              // 0x0738(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 bgImage;                                           // 0x0740(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnPresets;                                        // 0x0748(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_FooterButton_C*                    Confirm;                                           // 0x0750(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_FilePanel_C*                       filePanel;                                         // 0x0758(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Footer_C*                          Footer;                                            // 0x0760(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Header_C*                          Header;                                            // 0x0768(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomModal_C*                   modalOverlay;                                      // 0x0770(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Page_C*                            PageBase;                                          // 0x0778(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_HelpInMenu_C*                      WDG_HelpInMenu;                                    // 0x0780(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class FString                                 TargettedFile;                                     // 0x0788(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_VideoOptionsPage(int32 EntryPoint);
	void BndEvt__filePanel_K2Node_ComponentBoundEvent_10_OnDeleteUserPreset__DelegateSignature(const class FString& Filename);
	void OnPanelFocused(class UAcPanelBase* panelOnFocus);
	void OnPopulatePresets();
	void BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature(class FName Filename, const class FString& DisplayName);
	void BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnSaveUserPreset__DelegateSignature(const class FString& Filename, bool ExistingFile);
	void BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_3_OnHide__DelegateSignature(class UAcPanelBase* CallingPanel, bool Cancelled);
	void BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnShow__DelegateSignature(class UAcPanelBase* CallingPanel);
	void BP_OnMenuNavigation(const EControllerActionType Input, bool isReleased);
	void BndEvt__btnPresets_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature(class UWDG_GenericBarItem_C* Sender);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_VideoOptionsPage_C">();
	}
	static class UWDG_VideoOptionsPage_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_VideoOptionsPage_C>();
	}
};
static_assert(alignof(UWDG_VideoOptionsPage_C) == 0x000008, "Wrong alignment on UWDG_VideoOptionsPage_C");
static_assert(sizeof(UWDG_VideoOptionsPage_C) == 0x000798, "Wrong size on UWDG_VideoOptionsPage_C");
static_assert(offsetof(UWDG_VideoOptionsPage_C, UberGraphFrame) == 0x000720, "Member 'UWDG_VideoOptionsPage_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_VideoOptionsPage_C, ShowroomFade) == 0x000728, "Member 'UWDG_VideoOptionsPage_C::ShowroomFade' has a wrong offset!");
static_assert(offsetof(UWDG_VideoOptionsPage_C, PageFade) == 0x000730, "Member 'UWDG_VideoOptionsPage_C::PageFade' has a wrong offset!");
static_assert(offsetof(UWDG_VideoOptionsPage_C, Back) == 0x000738, "Member 'UWDG_VideoOptionsPage_C::Back' has a wrong offset!");
static_assert(offsetof(UWDG_VideoOptionsPage_C, bgImage) == 0x000740, "Member 'UWDG_VideoOptionsPage_C::bgImage' has a wrong offset!");
static_assert(offsetof(UWDG_VideoOptionsPage_C, btnPresets) == 0x000748, "Member 'UWDG_VideoOptionsPage_C::btnPresets' has a wrong offset!");
static_assert(offsetof(UWDG_VideoOptionsPage_C, Confirm) == 0x000750, "Member 'UWDG_VideoOptionsPage_C::Confirm' has a wrong offset!");
static_assert(offsetof(UWDG_VideoOptionsPage_C, filePanel) == 0x000758, "Member 'UWDG_VideoOptionsPage_C::filePanel' has a wrong offset!");
static_assert(offsetof(UWDG_VideoOptionsPage_C, Footer) == 0x000760, "Member 'UWDG_VideoOptionsPage_C::Footer' has a wrong offset!");
static_assert(offsetof(UWDG_VideoOptionsPage_C, Header) == 0x000768, "Member 'UWDG_VideoOptionsPage_C::Header' has a wrong offset!");
static_assert(offsetof(UWDG_VideoOptionsPage_C, modalOverlay) == 0x000770, "Member 'UWDG_VideoOptionsPage_C::modalOverlay' has a wrong offset!");
static_assert(offsetof(UWDG_VideoOptionsPage_C, PageBase) == 0x000778, "Member 'UWDG_VideoOptionsPage_C::PageBase' has a wrong offset!");
static_assert(offsetof(UWDG_VideoOptionsPage_C, WDG_HelpInMenu) == 0x000780, "Member 'UWDG_VideoOptionsPage_C::WDG_HelpInMenu' has a wrong offset!");
static_assert(offsetof(UWDG_VideoOptionsPage_C, TargettedFile) == 0x000788, "Member 'UWDG_VideoOptionsPage_C::TargettedFile' has a wrong offset!");

}

