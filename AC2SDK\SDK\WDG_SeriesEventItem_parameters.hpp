﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SeriesEventItem

#include "Basic.hpp"

#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_SeriesEventItem.WDG_SeriesEventItem_C.ExecuteUbergraph_WDG_SeriesEventItem
// 0x0030 (0x0030 - 0x0000)
struct WDG_SeriesEventItem_C_ExecuteUbergraph_WDG_SeriesEventItem final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_highlighted;                          // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_5[0x3];                                        // 0x0005(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FFocusEvent                            K2Node_Event_InFocusEvent_1;                       // 0x0008(0x0008)(NoDestructor)
	struct FFocusEvent                            K2Node_Event_InFocusEvent;                         // 0x0010(0x0008)(NoDestructor)
	class UWDG_GenericBarItem_C*                  K2Node_ComponentBoundEvent_Sender_2;               // 0x0018(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  K2Node_ComponentBoundEvent_Sender_1;               // 0x0020(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  K2Node_ComponentBoundEvent_Sender;                 // 0x0028(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SeriesEventItem_C_ExecuteUbergraph_WDG_SeriesEventItem) == 0x000008, "Wrong alignment on WDG_SeriesEventItem_C_ExecuteUbergraph_WDG_SeriesEventItem");
static_assert(sizeof(WDG_SeriesEventItem_C_ExecuteUbergraph_WDG_SeriesEventItem) == 0x000030, "Wrong size on WDG_SeriesEventItem_C_ExecuteUbergraph_WDG_SeriesEventItem");
static_assert(offsetof(WDG_SeriesEventItem_C_ExecuteUbergraph_WDG_SeriesEventItem, EntryPoint) == 0x000000, "Member 'WDG_SeriesEventItem_C_ExecuteUbergraph_WDG_SeriesEventItem::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_SeriesEventItem_C_ExecuteUbergraph_WDG_SeriesEventItem, K2Node_Event_highlighted) == 0x000004, "Member 'WDG_SeriesEventItem_C_ExecuteUbergraph_WDG_SeriesEventItem::K2Node_Event_highlighted' has a wrong offset!");
static_assert(offsetof(WDG_SeriesEventItem_C_ExecuteUbergraph_WDG_SeriesEventItem, K2Node_Event_InFocusEvent_1) == 0x000008, "Member 'WDG_SeriesEventItem_C_ExecuteUbergraph_WDG_SeriesEventItem::K2Node_Event_InFocusEvent_1' has a wrong offset!");
static_assert(offsetof(WDG_SeriesEventItem_C_ExecuteUbergraph_WDG_SeriesEventItem, K2Node_Event_InFocusEvent) == 0x000010, "Member 'WDG_SeriesEventItem_C_ExecuteUbergraph_WDG_SeriesEventItem::K2Node_Event_InFocusEvent' has a wrong offset!");
static_assert(offsetof(WDG_SeriesEventItem_C_ExecuteUbergraph_WDG_SeriesEventItem, K2Node_ComponentBoundEvent_Sender_2) == 0x000018, "Member 'WDG_SeriesEventItem_C_ExecuteUbergraph_WDG_SeriesEventItem::K2Node_ComponentBoundEvent_Sender_2' has a wrong offset!");
static_assert(offsetof(WDG_SeriesEventItem_C_ExecuteUbergraph_WDG_SeriesEventItem, K2Node_ComponentBoundEvent_Sender_1) == 0x000020, "Member 'WDG_SeriesEventItem_C_ExecuteUbergraph_WDG_SeriesEventItem::K2Node_ComponentBoundEvent_Sender_1' has a wrong offset!");
static_assert(offsetof(WDG_SeriesEventItem_C_ExecuteUbergraph_WDG_SeriesEventItem, K2Node_ComponentBoundEvent_Sender) == 0x000028, "Member 'WDG_SeriesEventItem_C_ExecuteUbergraph_WDG_SeriesEventItem::K2Node_ComponentBoundEvent_Sender' has a wrong offset!");

// Function WDG_SeriesEventItem.WDG_SeriesEventItem_C.BndEvt__WDG_SeriesEventItem_btnDown_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_SeriesEventItem_C_BndEvt__WDG_SeriesEventItem_btnDown_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature final
{
public:
	class UWDG_GenericBarItem_C*                  Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SeriesEventItem_C_BndEvt__WDG_SeriesEventItem_btnDown_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature) == 0x000008, "Wrong alignment on WDG_SeriesEventItem_C_BndEvt__WDG_SeriesEventItem_btnDown_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature");
static_assert(sizeof(WDG_SeriesEventItem_C_BndEvt__WDG_SeriesEventItem_btnDown_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature) == 0x000008, "Wrong size on WDG_SeriesEventItem_C_BndEvt__WDG_SeriesEventItem_btnDown_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature");
static_assert(offsetof(WDG_SeriesEventItem_C_BndEvt__WDG_SeriesEventItem_btnDown_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature, Sender) == 0x000000, "Member 'WDG_SeriesEventItem_C_BndEvt__WDG_SeriesEventItem_btnDown_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature::Sender' has a wrong offset!");

// Function WDG_SeriesEventItem.WDG_SeriesEventItem_C.BndEvt__WDG_SeriesEventItem_btnUp_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_SeriesEventItem_C_BndEvt__WDG_SeriesEventItem_btnUp_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature final
{
public:
	class UWDG_GenericBarItem_C*                  Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SeriesEventItem_C_BndEvt__WDG_SeriesEventItem_btnUp_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature) == 0x000008, "Wrong alignment on WDG_SeriesEventItem_C_BndEvt__WDG_SeriesEventItem_btnUp_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature");
static_assert(sizeof(WDG_SeriesEventItem_C_BndEvt__WDG_SeriesEventItem_btnUp_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature) == 0x000008, "Wrong size on WDG_SeriesEventItem_C_BndEvt__WDG_SeriesEventItem_btnUp_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature");
static_assert(offsetof(WDG_SeriesEventItem_C_BndEvt__WDG_SeriesEventItem_btnUp_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature, Sender) == 0x000000, "Member 'WDG_SeriesEventItem_C_BndEvt__WDG_SeriesEventItem_btnUp_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature::Sender' has a wrong offset!");

// Function WDG_SeriesEventItem.WDG_SeriesEventItem_C.BndEvt__WDG_SeriesEventItem_btnDel_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_SeriesEventItem_C_BndEvt__WDG_SeriesEventItem_btnDel_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature final
{
public:
	class UWDG_GenericBarItem_C*                  Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SeriesEventItem_C_BndEvt__WDG_SeriesEventItem_btnDel_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature) == 0x000008, "Wrong alignment on WDG_SeriesEventItem_C_BndEvt__WDG_SeriesEventItem_btnDel_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature");
static_assert(sizeof(WDG_SeriesEventItem_C_BndEvt__WDG_SeriesEventItem_btnDel_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature) == 0x000008, "Wrong size on WDG_SeriesEventItem_C_BndEvt__WDG_SeriesEventItem_btnDel_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature");
static_assert(offsetof(WDG_SeriesEventItem_C_BndEvt__WDG_SeriesEventItem_btnDel_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature, Sender) == 0x000000, "Member 'WDG_SeriesEventItem_C_BndEvt__WDG_SeriesEventItem_btnDel_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature::Sender' has a wrong offset!");

// Function WDG_SeriesEventItem.WDG_SeriesEventItem_C.OnRemovedFromFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_SeriesEventItem_C_OnRemovedFromFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_SeriesEventItem_C_OnRemovedFromFocusPath) == 0x000004, "Wrong alignment on WDG_SeriesEventItem_C_OnRemovedFromFocusPath");
static_assert(sizeof(WDG_SeriesEventItem_C_OnRemovedFromFocusPath) == 0x000008, "Wrong size on WDG_SeriesEventItem_C_OnRemovedFromFocusPath");
static_assert(offsetof(WDG_SeriesEventItem_C_OnRemovedFromFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_SeriesEventItem_C_OnRemovedFromFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_SeriesEventItem.WDG_SeriesEventItem_C.OnAddedToFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_SeriesEventItem_C_OnAddedToFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_SeriesEventItem_C_OnAddedToFocusPath) == 0x000004, "Wrong alignment on WDG_SeriesEventItem_C_OnAddedToFocusPath");
static_assert(sizeof(WDG_SeriesEventItem_C_OnAddedToFocusPath) == 0x000008, "Wrong size on WDG_SeriesEventItem_C_OnAddedToFocusPath");
static_assert(offsetof(WDG_SeriesEventItem_C_OnAddedToFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_SeriesEventItem_C_OnAddedToFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_SeriesEventItem.WDG_SeriesEventItem_C.BP_SetHighlight
// 0x0001 (0x0001 - 0x0000)
struct WDG_SeriesEventItem_C_BP_SetHighlight final
{
public:
	bool                                          highlighted;                                       // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SeriesEventItem_C_BP_SetHighlight) == 0x000001, "Wrong alignment on WDG_SeriesEventItem_C_BP_SetHighlight");
static_assert(sizeof(WDG_SeriesEventItem_C_BP_SetHighlight) == 0x000001, "Wrong size on WDG_SeriesEventItem_C_BP_SetHighlight");
static_assert(offsetof(WDG_SeriesEventItem_C_BP_SetHighlight, highlighted) == 0x000000, "Member 'WDG_SeriesEventItem_C_BP_SetHighlight::highlighted' has a wrong offset!");

}

