﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SinglePlayerPage

#include "Basic.hpp"

#include "WDG_SinglePlayerPage_classes.hpp"
#include "WDG_SinglePlayerPage_parameters.hpp"


namespace SDK
{

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.ExecuteUbergraph_WDG_SinglePlayerPage
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SinglePlayerPage_C::ExecuteUbergraph_WDG_SinglePlayerPage(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "ExecuteUbergraph_WDG_SinglePlayerPage");

	Params::WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_season2024_K2Node_ComponentBoundEvent_8_SeasonNotChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// ESeasonType                             Season                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SinglePlayerPage_C::BndEvt__WDG_SinglePlayerPage_season2024_K2Node_ComponentBoundEvent_8_SeasonNotChanged__DelegateSignature(ESeasonType Season)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "BndEvt__WDG_SinglePlayerPage_season2024_K2Node_ComponentBoundEvent_8_SeasonNotChanged__DelegateSignature");

	Params::WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2024_K2Node_ComponentBoundEvent_8_SeasonNotChanged__DelegateSignature Parms{};

	Parms.Season = Season;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_seasonGT2_K2Node_ComponentBoundEvent_4_SeasonNotChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// ESeasonType                             Season                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SinglePlayerPage_C::BndEvt__WDG_SinglePlayerPage_seasonGT2_K2Node_ComponentBoundEvent_4_SeasonNotChanged__DelegateSignature(ESeasonType Season)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "BndEvt__WDG_SinglePlayerPage_seasonGT2_K2Node_ComponentBoundEvent_4_SeasonNotChanged__DelegateSignature");

	Params::WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonGT2_K2Node_ComponentBoundEvent_4_SeasonNotChanged__DelegateSignature Parms{};

	Parms.Season = Season;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_season2023_K2Node_ComponentBoundEvent_3_SeasonNotChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// ESeasonType                             Season                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SinglePlayerPage_C::BndEvt__WDG_SinglePlayerPage_season2023_K2Node_ComponentBoundEvent_3_SeasonNotChanged__DelegateSignature(ESeasonType Season)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "BndEvt__WDG_SinglePlayerPage_season2023_K2Node_ComponentBoundEvent_3_SeasonNotChanged__DelegateSignature");

	Params::WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2023_K2Node_ComponentBoundEvent_3_SeasonNotChanged__DelegateSignature Parms{};

	Parms.Season = Season;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_season2022_K2Node_ComponentBoundEvent_1_SeasonNotChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// ESeasonType                             Season                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SinglePlayerPage_C::BndEvt__WDG_SinglePlayerPage_season2022_K2Node_ComponentBoundEvent_1_SeasonNotChanged__DelegateSignature(ESeasonType Season)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "BndEvt__WDG_SinglePlayerPage_season2022_K2Node_ComponentBoundEvent_1_SeasonNotChanged__DelegateSignature");

	Params::WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2022_K2Node_ComponentBoundEvent_1_SeasonNotChanged__DelegateSignature Parms{};

	Parms.Season = Season;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_seasonOpen_K2Node_ComponentBoundEvent_21_SeasonNotChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// ESeasonType                             Season                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SinglePlayerPage_C::BndEvt__WDG_SinglePlayerPage_seasonOpen_K2Node_ComponentBoundEvent_21_SeasonNotChanged__DelegateSignature(ESeasonType Season)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "BndEvt__WDG_SinglePlayerPage_seasonOpen_K2Node_ComponentBoundEvent_21_SeasonNotChanged__DelegateSignature");

	Params::WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonOpen_K2Node_ComponentBoundEvent_21_SeasonNotChanged__DelegateSignature Parms{};

	Parms.Season = Season;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_seasonIGTC_K2Node_ComponentBoundEvent_19_SeasonNotChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// ESeasonType                             Season                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SinglePlayerPage_C::BndEvt__WDG_SinglePlayerPage_seasonIGTC_K2Node_ComponentBoundEvent_19_SeasonNotChanged__DelegateSignature(ESeasonType Season)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "BndEvt__WDG_SinglePlayerPage_seasonIGTC_K2Node_ComponentBoundEvent_19_SeasonNotChanged__DelegateSignature");

	Params::WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonIGTC_K2Node_ComponentBoundEvent_19_SeasonNotChanged__DelegateSignature Parms{};

	Parms.Season = Season;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_seasonGT4_K2Node_ComponentBoundEvent_18_SeasonNotChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// ESeasonType                             Season                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SinglePlayerPage_C::BndEvt__WDG_SinglePlayerPage_seasonGT4_K2Node_ComponentBoundEvent_18_SeasonNotChanged__DelegateSignature(ESeasonType Season)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "BndEvt__WDG_SinglePlayerPage_seasonGT4_K2Node_ComponentBoundEvent_18_SeasonNotChanged__DelegateSignature");

	Params::WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonGT4_K2Node_ComponentBoundEvent_18_SeasonNotChanged__DelegateSignature Parms{};

	Parms.Season = Season;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_seasonBGT_K2Node_ComponentBoundEvent_17_SeasonNotChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// ESeasonType                             Season                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SinglePlayerPage_C::BndEvt__WDG_SinglePlayerPage_seasonBGT_K2Node_ComponentBoundEvent_17_SeasonNotChanged__DelegateSignature(ESeasonType Season)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "BndEvt__WDG_SinglePlayerPage_seasonBGT_K2Node_ComponentBoundEvent_17_SeasonNotChanged__DelegateSignature");

	Params::WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonBGT_K2Node_ComponentBoundEvent_17_SeasonNotChanged__DelegateSignature Parms{};

	Parms.Season = Season;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_season2021_K2Node_ComponentBoundEvent_12_SeasonNotChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// ESeasonType                             Season                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SinglePlayerPage_C::BndEvt__WDG_SinglePlayerPage_season2021_K2Node_ComponentBoundEvent_12_SeasonNotChanged__DelegateSignature(ESeasonType Season)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "BndEvt__WDG_SinglePlayerPage_season2021_K2Node_ComponentBoundEvent_12_SeasonNotChanged__DelegateSignature");

	Params::WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2021_K2Node_ComponentBoundEvent_12_SeasonNotChanged__DelegateSignature Parms{};

	Parms.Season = Season;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_season2020_K2Node_ComponentBoundEvent_6_SeasonNotChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// ESeasonType                             Season                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SinglePlayerPage_C::BndEvt__WDG_SinglePlayerPage_season2020_K2Node_ComponentBoundEvent_6_SeasonNotChanged__DelegateSignature(ESeasonType Season)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "BndEvt__WDG_SinglePlayerPage_season2020_K2Node_ComponentBoundEvent_6_SeasonNotChanged__DelegateSignature");

	Params::WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2020_K2Node_ComponentBoundEvent_6_SeasonNotChanged__DelegateSignature Parms{};

	Parms.Season = Season;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_season2019_K2Node_ComponentBoundEvent_2_SeasonNotChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// ESeasonType                             Season                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SinglePlayerPage_C::BndEvt__WDG_SinglePlayerPage_season2019_K2Node_ComponentBoundEvent_2_SeasonNotChanged__DelegateSignature(ESeasonType Season)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "BndEvt__WDG_SinglePlayerPage_season2019_K2Node_ComponentBoundEvent_2_SeasonNotChanged__DelegateSignature");

	Params::WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2019_K2Node_ComponentBoundEvent_2_SeasonNotChanged__DelegateSignature Parms{};

	Parms.Season = Season;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_season2018_K2Node_ComponentBoundEvent_0_SeasonNotChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// ESeasonType                             Season                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SinglePlayerPage_C::BndEvt__WDG_SinglePlayerPage_season2018_K2Node_ComponentBoundEvent_0_SeasonNotChanged__DelegateSignature(ESeasonType Season)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "BndEvt__WDG_SinglePlayerPage_season2018_K2Node_ComponentBoundEvent_0_SeasonNotChanged__DelegateSignature");

	Params::WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2018_K2Node_ComponentBoundEvent_0_SeasonNotChanged__DelegateSignature Parms{};

	Parms.Season = Season;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.OnSeasonUnavailable
// (Event, Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// EContentType                            content_type                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SinglePlayerPage_C::OnSeasonUnavailable(EContentType content_type)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "OnSeasonUnavailable");

	Params::WDG_SinglePlayerPage_C_OnSeasonUnavailable Parms{};

	Parms.content_type = content_type;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.OnSeasonChanged
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// ESeasonType                             new_season                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SinglePlayerPage_C::OnSeasonChanged(ESeasonType new_season)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "OnSeasonChanged");

	Params::WDG_SinglePlayerPage_C_OnSeasonChanged Parms{};

	Parms.new_season = new_season;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_BtnSeasonSelector_K2Node_ComponentBoundEvent_20_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_SinglePlayerPage_C::BndEvt__WDG_SinglePlayerPage_BtnSeasonSelector_K2Node_ComponentBoundEvent_20_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "BndEvt__WDG_SinglePlayerPage_BtnSeasonSelector_K2Node_ComponentBoundEvent_20_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BP_StartPage
// (Event, Public, BlueprintEvent)

void UWDG_SinglePlayerPage_C::BP_StartPage()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "BP_StartPage");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_SinglePlayerPage_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__popupDLC_K2Node_ComponentBoundEvent_1_OnNo__DelegateSignature
// (BlueprintEvent)

void UWDG_SinglePlayerPage_C::BndEvt__popupDLC_K2Node_ComponentBoundEvent_1_OnNo__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "BndEvt__popupDLC_K2Node_ComponentBoundEvent_1_OnNo__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__popupDLC_K2Node_ComponentBoundEvent_2_OnMissing__DelegateSignature
// (BlueprintEvent)
// Parameters:
// EContentType                            ContentId                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SinglePlayerPage_C::BndEvt__popupDLC_K2Node_ComponentBoundEvent_2_OnMissing__DelegateSignature(EContentType ContentId)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "BndEvt__popupDLC_K2Node_ComponentBoundEvent_2_OnMissing__DelegateSignature");

	Params::WDG_SinglePlayerPage_C_BndEvt__popupDLC_K2Node_ComponentBoundEvent_2_OnMissing__DelegateSignature Parms{};

	Parms.ContentId = ContentId;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__popupDLC_K2Node_ComponentBoundEvent_0_OnYes__DelegateSignature
// (BlueprintEvent)

void UWDG_SinglePlayerPage_C::BndEvt__popupDLC_K2Node_ComponentBoundEvent_0_OnYes__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "BndEvt__popupDLC_K2Node_ComponentBoundEvent_0_OnYes__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.SetCurrentSeasonType
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    Init                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SinglePlayerPage_C::SetCurrentSeasonType(bool Init)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "SetCurrentSeasonType");

	Params::WDG_SinglePlayerPage_C_SetCurrentSeasonType Parms{};

	Parms.Init = Init;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.OnPreviewMouseButtonDown
// (BlueprintCosmetic, Event, Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FPointerEvent&             MouseEvent                                             (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// struct FEventReply                      ReturnValue                                            (Parm, OutParm, ReturnParm)

struct FEventReply UWDG_SinglePlayerPage_C::OnPreviewMouseButtonDown(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "OnPreviewMouseButtonDown");

	Params::WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.MouseEvent = std::move(MouseEvent);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.ValidateSeason
// (Event, Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// ESeasonType                             season_type                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor)

bool UWDG_SinglePlayerPage_C::ValidateSeason(ESeasonType season_type)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "ValidateSeason");

	Params::WDG_SinglePlayerPage_C_ValidateSeason Parms{};

	Parms.season_type = season_type;

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.ToggleSeriesSelect
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UWDG_GenericBarItemSlanted_C*     SelectedButton                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SinglePlayerPage_C::ToggleSeriesSelect(class UWDG_GenericBarItemSlanted_C* SelectedButton)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "ToggleSeriesSelect");

	Params::WDG_SinglePlayerPage_C_ToggleSeriesSelect Parms{};

	Parms.SelectedButton = SelectedButton;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.OnPreviewKeyDown
// (Event, Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FKeyEvent&                 InKeyEvent                                             (BlueprintVisible, BlueprintReadOnly, Parm)
// struct FEventReply                      ReturnValue                                            (Parm, OutParm, ReturnParm)

struct FEventReply UWDG_SinglePlayerPage_C::OnPreviewKeyDown(const struct FGeometry& MyGeometry, const struct FKeyEvent& InKeyEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "OnPreviewKeyDown");

	Params::WDG_SinglePlayerPage_C_OnPreviewKeyDown Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InKeyEvent = std::move(InKeyEvent);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.GetSeasonButtonsArray
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// TArray<class UWDG_SeasonItem_C*>*       Array                                                  (Parm, OutParm, ContainsInstancedReference)

void UWDG_SinglePlayerPage_C::GetSeasonButtonsArray(TArray<class UWDG_SeasonItem_C*>* Array)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "GetSeasonButtonsArray");

	Params::WDG_SinglePlayerPage_C_GetSeasonButtonsArray Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Array != nullptr)
		*Array = std::move(Parms.Array);
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.NavToSelectedSeason
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// EUINavigation                           Navigation_0                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWidget*                          ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

class UWidget* UWDG_SinglePlayerPage_C::NavToSelectedSeason(EUINavigation Navigation_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "NavToSelectedSeason");

	Params::WDG_SinglePlayerPage_C_NavToSelectedSeason Parms{};

	Parms.Navigation_0 = Navigation_0;

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.ToggleSeasonSelection
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    ResetFocus                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SinglePlayerPage_C::ToggleSeasonSelection(bool ResetFocus)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPage_C", "ToggleSeasonSelection");

	Params::WDG_SinglePlayerPage_C_ToggleSeasonSelection Parms{};

	Parms.ResetFocus = ResetFocus;

	UObject::ProcessEvent(Func, &Parms);
}

}

