﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SetupPresetButton

#include "Basic.hpp"


namespace SDK::Params
{

// Function WDG_SetupPresetButton.WDG_SetupPresetButton_C.ExecuteUbergraph_WDG_SetupPresetButton
// 0x0018 (0x0018 - 0x0000)
struct WDG_SetupPresetButton_C_ExecuteUbergraph_WDG_SetupPresetButton final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_1;              // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SetupPresetButton_C_ExecuteUbergraph_WDG_SetupPresetButton) == 0x000008, "Wrong alignment on WDG_SetupPresetButton_C_ExecuteUbergraph_WDG_SetupPresetButton");
static_assert(sizeof(WDG_SetupPresetButton_C_ExecuteUbergraph_WDG_SetupPresetButton) == 0x000018, "Wrong size on WDG_SetupPresetButton_C_ExecuteUbergraph_WDG_SetupPresetButton");
static_assert(offsetof(WDG_SetupPresetButton_C_ExecuteUbergraph_WDG_SetupPresetButton, EntryPoint) == 0x000000, "Member 'WDG_SetupPresetButton_C_ExecuteUbergraph_WDG_SetupPresetButton::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_SetupPresetButton_C_ExecuteUbergraph_WDG_SetupPresetButton, CallFunc_PlayAnimation_ReturnValue) == 0x000008, "Member 'WDG_SetupPresetButton_C_ExecuteUbergraph_WDG_SetupPresetButton::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SetupPresetButton_C_ExecuteUbergraph_WDG_SetupPresetButton, CallFunc_PlayAnimation_ReturnValue_1) == 0x000010, "Member 'WDG_SetupPresetButton_C_ExecuteUbergraph_WDG_SetupPresetButton::CallFunc_PlayAnimation_ReturnValue_1' has a wrong offset!");

}

