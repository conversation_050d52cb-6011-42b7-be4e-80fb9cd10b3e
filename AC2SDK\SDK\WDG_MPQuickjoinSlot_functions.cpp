﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_MPQuickjoinSlot

#include "Basic.hpp"

#include "WDG_MPQuickjoinSlot_classes.hpp"
#include "WDG_MPQuickjoinSlot_parameters.hpp"


namespace SDK
{

// Function WDG_MPQuickjoinSlot.WDG_MPQuickjoinSlot_C.ExecuteUbergraph_WDG_MPQuickjoinSlot
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_MPQuickjoinSlot_C::ExecuteUbergraph_WDG_MPQuickjoinSlot(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MPQuickjoinSlot_C", "ExecuteUbergraph_WDG_MPQuickjoinSlot");

	Params::WDG_MPQuickjoinSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlot Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MPQuickjoinSlot.WDG_MPQuickjoinSlot_C.BP_MouseLeave
// (Event, Public, BlueprintEvent)

void UWDG_MPQuickjoinSlot_C::BP_MouseLeave()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MPQuickjoinSlot_C", "BP_MouseLeave");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MPQuickjoinSlot.WDG_MPQuickjoinSlot_C.BP_MouseOver
// (Event, Public, BlueprintEvent)

void UWDG_MPQuickjoinSlot_C::BP_MouseOver()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MPQuickjoinSlot_C", "BP_MouseOver");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MPQuickjoinSlot.WDG_MPQuickjoinSlot_C.Tick
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// float                                   InDeltaTime                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_MPQuickjoinSlot_C::Tick(const struct FGeometry& MyGeometry, float InDeltaTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MPQuickjoinSlot_C", "Tick");

	Params::WDG_MPQuickjoinSlot_C_Tick Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InDeltaTime = InDeltaTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MPQuickjoinSlot.WDG_MPQuickjoinSlot_C.SetServer
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FOnlineServicesMPServerInfo&ServerInfo                                             (BlueprintVisible, BlueprintReadOnly, Parm)
// const class FText&                      waitingIndicatorText                                   (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_MPQuickjoinSlot_C::SetServer(const struct FOnlineServicesMPServerInfo& ServerInfo, const class FText& waitingIndicatorText)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MPQuickjoinSlot_C", "SetServer");

	Params::WDG_MPQuickjoinSlot_C_SetServer Parms{};

	Parms.ServerInfo = std::move(ServerInfo);
	Parms.waitingIndicatorText = std::move(waitingIndicatorText);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MPQuickjoinSlot.WDG_MPQuickjoinSlot_C.GetMinutesRemainingText
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FDateTime&                 SessionEndUtc                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// class FText*                            SessionEndText                                         (Parm, OutParm)

void UWDG_MPQuickjoinSlot_C::GetMinutesRemainingText(const struct FDateTime& SessionEndUtc, class FText* SessionEndText)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MPQuickjoinSlot_C", "GetMinutesRemainingText");

	Params::WDG_MPQuickjoinSlot_C_GetMinutesRemainingText Parms{};

	Parms.SessionEndUtc = std::move(SessionEndUtc);

	UObject::ProcessEvent(Func, &Parms);

	if (SessionEndText != nullptr)
		*SessionEndText = std::move(Parms.SessionEndText);
}

}

