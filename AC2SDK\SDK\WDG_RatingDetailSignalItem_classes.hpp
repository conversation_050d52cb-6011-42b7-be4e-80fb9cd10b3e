﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RatingDetailSignalItem

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RatingDetailSignalItem.WDG_RatingDetailSignalItem_C
// 0x0010 (0x0280 - 0x0270)
class UWDG_RatingDetailSignalItem_C final : public URatingDetailSignalItem
{
public:
	class UImage*                                 Image_0;                                           // 0x0270(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_1;                                           // 0x0278(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoD<PERSON>ru<PERSON>, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RatingDetailSignalItem_C">();
	}
	static class UWDG_RatingDetailSignalItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RatingDetailSignalItem_C>();
	}
};
static_assert(alignof(UWDG_RatingDetailSignalItem_C) == 0x000008, "Wrong alignment on UWDG_RatingDetailSignalItem_C");
static_assert(sizeof(UWDG_RatingDetailSignalItem_C) == 0x000280, "Wrong size on UWDG_RatingDetailSignalItem_C");
static_assert(offsetof(UWDG_RatingDetailSignalItem_C, Image_0) == 0x000270, "Member 'UWDG_RatingDetailSignalItem_C::Image_0' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailSignalItem_C, Image_1) == 0x000278, "Member 'UWDG_RatingDetailSignalItem_C::Image_1' has a wrong offset!");

}

