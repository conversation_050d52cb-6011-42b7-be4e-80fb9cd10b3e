﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_UISessionTimer

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "SlateCore_structs.hpp"
#include "AC2_structs.hpp"
#include "CoreUObject_structs.hpp"


namespace SDK::Params
{

// Function WDG_UISessionTimer.WDG_UISessionTimer_C.ExecuteUbergraph_WDG_UISessionTimer
// 0x01E0 (0x01E0 - 0x0000)
struct WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x0004(0x0001)(ZeroConstructor, Is<PERSON>lainOldData, NoDestructor)
	uint8                                         Pad_5[0x3];                                        // 0x0005(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class AGameModeBase*                          CallFunc_GetGameMode_ReturnValue;                  // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AAcRaceGameMode*                        K2Node_DynamicCast_AsAc_Race_Game_Mode;            // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_19[0x3];                                       // 0x0019(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_GetCurrentSessionRemainingTime_ReturnValue; // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSlateColor                            K2Node_MakeStruct_SlateColor;                      // 0x0020(0x0028)()
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x0048(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_49[0x7];                                       // 0x0049(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_ConvertInt32ToFormattedTime_ReturnValue;  // 0x0050(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0060(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_61[0x7];                                       // 0x0061(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x0068(0x0018)()
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0080(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x0081(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_82[0x2];                                       // 0x0082(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_GetCurrentSessionRemainingTime_ReturnValue_1; // 0x0084(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ERaceSessionPhase                             CallFunc_GetCurrentSessionPhaseUI_ReturnValue;     // 0x0088(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0089(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_8A[0x6];                                       // 0x008A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UCanvasPanelSlot*                       K2Node_DynamicCast_AsCanvas_Panel_Slot;            // 0x0090(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x0098(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_99[0x3];                                       // 0x0099(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate;              // 0x009C(0x0010)(ZeroConstructor, NoDestructor)
	uint8                                         Pad_AC[0x4];                                       // 0x00AC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_GetCurrentSessionName_ReturnValue;        // 0x00B0(0x0018)()
	struct FTimerHandle                           CallFunc_K2_SetTimerDelegate_ReturnValue;          // 0x00C8(0x0008)(NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetPitWindowTimeLeft_ReturnValue;         // 0x00D0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D4[0x4];                                       // 0x00D4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_MakeLiteralText_ReturnValue;              // 0x00D8(0x0018)()
	class FString                                 CallFunc_ConvertInt32ToFormattedTime_ReturnValue_1; // 0x00F0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData;              // 0x0100(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_1;          // 0x0140(0x0018)()
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData_1;            // 0x0158(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	TArray<struct FFormatArgumentData>            K2Node_MakeArray_Array;                            // 0x0198(0x0010)(ReferenceParm)
	class FText                                   CallFunc_Format_ReturnValue;                       // 0x01A8(0x0018)()
	bool                                          CallFunc_isPitWindowOpen_ReturnValue;              // 0x01C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1C1[0x7];                                      // 0x01C1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue;              // 0x01C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance;             // 0x01D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_2;                     // 0x01D8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_isOnline_ReturnValue;                     // 0x01D9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x01DA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	ESessionStatus                                CallFunc_getSessionStatus_ReturnValue;             // 0x01DB(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x01DC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	ERaceSessionPhase                             CallFunc_GetCurrentSessionPhaseUI_ReturnValue_1;   // 0x01DD(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer) == 0x000008, "Wrong alignment on WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer");
static_assert(sizeof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer) == 0x0001E0, "Wrong size on WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, EntryPoint) == 0x000000, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, CallFunc_Greater_IntInt_ReturnValue) == 0x000004, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, CallFunc_GetGameMode_ReturnValue) == 0x000008, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::CallFunc_GetGameMode_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, K2Node_DynamicCast_AsAc_Race_Game_Mode) == 0x000010, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::K2Node_DynamicCast_AsAc_Race_Game_Mode' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, K2Node_DynamicCast_bSuccess) == 0x000018, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, CallFunc_GetCurrentSessionRemainingTime_ReturnValue) == 0x00001C, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::CallFunc_GetCurrentSessionRemainingTime_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, K2Node_MakeStruct_SlateColor) == 0x000020, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::K2Node_MakeStruct_SlateColor' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, CallFunc_LessEqual_IntInt_ReturnValue) == 0x000048, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, CallFunc_ConvertInt32ToFormattedTime_ReturnValue) == 0x000050, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::CallFunc_ConvertInt32ToFormattedTime_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, K2Node_Event_IsDesignTime) == 0x000060, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, CallFunc_Conv_StringToText_ReturnValue) == 0x000068, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000080, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, CallFunc_BooleanOR_ReturnValue) == 0x000081, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, CallFunc_GetCurrentSessionRemainingTime_ReturnValue_1) == 0x000084, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::CallFunc_GetCurrentSessionRemainingTime_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, CallFunc_GetCurrentSessionPhaseUI_ReturnValue) == 0x000088, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::CallFunc_GetCurrentSessionPhaseUI_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, CallFunc_IsValid_ReturnValue) == 0x000089, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, K2Node_DynamicCast_AsCanvas_Panel_Slot) == 0x000090, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::K2Node_DynamicCast_AsCanvas_Panel_Slot' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, K2Node_DynamicCast_bSuccess_1) == 0x000098, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, K2Node_CreateDelegate_OutputDelegate) == 0x00009C, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, CallFunc_GetCurrentSessionName_ReturnValue) == 0x0000B0, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::CallFunc_GetCurrentSessionName_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, CallFunc_K2_SetTimerDelegate_ReturnValue) == 0x0000C8, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::CallFunc_K2_SetTimerDelegate_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, CallFunc_GetPitWindowTimeLeft_ReturnValue) == 0x0000D0, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::CallFunc_GetPitWindowTimeLeft_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, CallFunc_MakeLiteralText_ReturnValue) == 0x0000D8, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::CallFunc_MakeLiteralText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, CallFunc_ConvertInt32ToFormattedTime_ReturnValue_1) == 0x0000F0, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::CallFunc_ConvertInt32ToFormattedTime_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, K2Node_MakeStruct_FormatArgumentData) == 0x000100, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::K2Node_MakeStruct_FormatArgumentData' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, CallFunc_Conv_StringToText_ReturnValue_1) == 0x000140, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::CallFunc_Conv_StringToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, K2Node_MakeStruct_FormatArgumentData_1) == 0x000158, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::K2Node_MakeStruct_FormatArgumentData_1' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, K2Node_MakeArray_Array) == 0x000198, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, CallFunc_Format_ReturnValue) == 0x0001A8, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::CallFunc_Format_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, CallFunc_isPitWindowOpen_ReturnValue) == 0x0001C0, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::CallFunc_isPitWindowOpen_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, CallFunc_GetGameInstance_ReturnValue) == 0x0001C8, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::CallFunc_GetGameInstance_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, K2Node_DynamicCast_AsAc_Game_Instance) == 0x0001D0, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::K2Node_DynamicCast_AsAc_Game_Instance' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, K2Node_DynamicCast_bSuccess_2) == 0x0001D8, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::K2Node_DynamicCast_bSuccess_2' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, CallFunc_isOnline_ReturnValue) == 0x0001D9, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::CallFunc_isOnline_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, CallFunc_Not_PreBool_ReturnValue) == 0x0001DA, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, CallFunc_getSessionStatus_ReturnValue) == 0x0001DB, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::CallFunc_getSessionStatus_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, K2Node_SwitchEnum_CmpSuccess) == 0x0001DC, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer, CallFunc_GetCurrentSessionPhaseUI_ReturnValue_1) == 0x0001DD, "Member 'WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer::CallFunc_GetCurrentSessionPhaseUI_ReturnValue_1' has a wrong offset!");

// Function WDG_UISessionTimer.WDG_UISessionTimer_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_UISessionTimer_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_UISessionTimer_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_UISessionTimer_C_PreConstruct");
static_assert(sizeof(WDG_UISessionTimer_C_PreConstruct) == 0x000001, "Wrong size on WDG_UISessionTimer_C_PreConstruct");
static_assert(offsetof(WDG_UISessionTimer_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_UISessionTimer_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WDG_UISessionTimer.WDG_UISessionTimer_C.IsTimeRemainingVisible
// 0x0002 (0x0002 - 0x0000)
struct WDG_UISessionTimer_C_IsTimeRemainingVisible final
{
public:
	bool                                          IsVisible_0;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsVisible_ReturnValue;                    // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_UISessionTimer_C_IsTimeRemainingVisible) == 0x000001, "Wrong alignment on WDG_UISessionTimer_C_IsTimeRemainingVisible");
static_assert(sizeof(WDG_UISessionTimer_C_IsTimeRemainingVisible) == 0x000002, "Wrong size on WDG_UISessionTimer_C_IsTimeRemainingVisible");
static_assert(offsetof(WDG_UISessionTimer_C_IsTimeRemainingVisible, IsVisible_0) == 0x000000, "Member 'WDG_UISessionTimer_C_IsTimeRemainingVisible::IsVisible_0' has a wrong offset!");
static_assert(offsetof(WDG_UISessionTimer_C_IsTimeRemainingVisible, CallFunc_IsVisible_ReturnValue) == 0x000001, "Member 'WDG_UISessionTimer_C_IsTimeRemainingVisible::CallFunc_IsVisible_ReturnValue' has a wrong offset!");

}

