---
type: "always_apply"
---

# Compilation & Injection
- Use that command for compiling the project: 
Z:\Logiciels\DllInjector\Injector.x64.exe --process-name AC2-Win64-Shipping.exe --module-name Z:\Work\UpWork\AC2\AC2SDK\x64\Debug\AC2SDK.dll --eject; & "Z:\\Logiciels\\VisualStudio\\Setup\\MSBuild\\Current\\Bin\\MSBuild.exe" AC2SDK.sln /m:16 /p:MultiProcMaxCount=16 /p:UseMultiToolTask=true /p:EnforceProcessCountAcrossBuilds=true /t:Build

- That one for injecting the process:
Z:\Logiciels\DllInjector\Injector.x64.exe --process-name AC2-Win64-Shipping.exe --module-name Z:\Work\UpWork\AC2\AC2SDK\x64\Debug\AC2SDK.dll --inject

# Source Code Location
- If you look for sources, they're here: Z:/Work/UpWork/AC2/AC2SDK/AC2SDK/SDK
- Instead of findstr, please do a "grep" (it is accessible even in Powershell) on ALL hpp+cpp files in Z:/Work/UpWork/AC2/AC2SDK/AC2SDK/SDK.

# Data Access & Safety
- Avoid hardcoded memory offsets and direct memory manipulation - use only SDK methods to prevent crashes when accessing game data.

# Implementation Priorities
- User prefers WORKING solutions with current SDK over theoretical approaches, willing to use any method as long as it's fully implemented and functional.