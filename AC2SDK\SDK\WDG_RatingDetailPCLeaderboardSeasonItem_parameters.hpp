﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RatingDetailPCLeaderboardSeasonItem

#include "Basic.hpp"

#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_RatingDetailPCLeaderboardSeasonItem.WDG_RatingDetailPCLeaderboardSeasonItem_C.SetSeasonNo
// 0x0040 (0x0040 - 0x0000)
struct WDG_RatingDetailPCLeaderboardSeasonItem_C_SetSeasonNo final
{
public:
	int32                                         Season;                                            // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Conv_IntToString_ReturnValue;             // 0x0008(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue;                // 0x0018(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x0028(0x0018)()
};
static_assert(alignof(WDG_RatingDetailPCLeaderboardSeasonItem_C_SetSeasonNo) == 0x000008, "Wrong alignment on WDG_RatingDetailPCLeaderboardSeasonItem_C_SetSeasonNo");
static_assert(sizeof(WDG_RatingDetailPCLeaderboardSeasonItem_C_SetSeasonNo) == 0x000040, "Wrong size on WDG_RatingDetailPCLeaderboardSeasonItem_C_SetSeasonNo");
static_assert(offsetof(WDG_RatingDetailPCLeaderboardSeasonItem_C_SetSeasonNo, Season) == 0x000000, "Member 'WDG_RatingDetailPCLeaderboardSeasonItem_C_SetSeasonNo::Season' has a wrong offset!");
static_assert(offsetof(WDG_RatingDetailPCLeaderboardSeasonItem_C_SetSeasonNo, CallFunc_Conv_IntToString_ReturnValue) == 0x000008, "Member 'WDG_RatingDetailPCLeaderboardSeasonItem_C_SetSeasonNo::CallFunc_Conv_IntToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RatingDetailPCLeaderboardSeasonItem_C_SetSeasonNo, CallFunc_Concat_StrStr_ReturnValue) == 0x000018, "Member 'WDG_RatingDetailPCLeaderboardSeasonItem_C_SetSeasonNo::CallFunc_Concat_StrStr_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RatingDetailPCLeaderboardSeasonItem_C_SetSeasonNo, CallFunc_Conv_StringToText_ReturnValue) == 0x000028, "Member 'WDG_RatingDetailPCLeaderboardSeasonItem_C_SetSeasonNo::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");

// Function WDG_RatingDetailPCLeaderboardSeasonItem.WDG_RatingDetailPCLeaderboardSeasonItem_C.AddLeaderboard
// 0x0028 (0x0028 - 0x0000)
struct WDG_RatingDetailPCLeaderboardSeasonItem_C_AddLeaderboard final
{
public:
	struct FOnlineServicesLeaderboardRank         Rank;                                              // 0x0000(0x0014)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
	uint8                                         Pad_14[0x4];                                       // 0x0014(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_RatingDetailPCLeaderboardItem_C*   CallFunc_Create_ReturnValue;                       // 0x0018(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UVerticalBoxSlot*                       CallFunc_AddChildToVerticalBox_ReturnValue;        // 0x0020(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_RatingDetailPCLeaderboardSeasonItem_C_AddLeaderboard) == 0x000008, "Wrong alignment on WDG_RatingDetailPCLeaderboardSeasonItem_C_AddLeaderboard");
static_assert(sizeof(WDG_RatingDetailPCLeaderboardSeasonItem_C_AddLeaderboard) == 0x000028, "Wrong size on WDG_RatingDetailPCLeaderboardSeasonItem_C_AddLeaderboard");
static_assert(offsetof(WDG_RatingDetailPCLeaderboardSeasonItem_C_AddLeaderboard, Rank) == 0x000000, "Member 'WDG_RatingDetailPCLeaderboardSeasonItem_C_AddLeaderboard::Rank' has a wrong offset!");
static_assert(offsetof(WDG_RatingDetailPCLeaderboardSeasonItem_C_AddLeaderboard, CallFunc_Create_ReturnValue) == 0x000018, "Member 'WDG_RatingDetailPCLeaderboardSeasonItem_C_AddLeaderboard::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RatingDetailPCLeaderboardSeasonItem_C_AddLeaderboard, CallFunc_AddChildToVerticalBox_ReturnValue) == 0x000020, "Member 'WDG_RatingDetailPCLeaderboardSeasonItem_C_AddLeaderboard::CallFunc_AddChildToVerticalBox_ReturnValue' has a wrong offset!");

}

