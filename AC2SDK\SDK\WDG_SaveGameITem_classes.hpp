﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SaveGameITem

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "AC2_classes.hpp"
#include "Engine_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SaveGameITem.WDG_SaveGameItem_C
// 0x02F0 (0x08D0 - 0x05E0)
class UWDG_SaveGameItem_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UBorder*                                background;                                        // 0x05E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                borderType;                                        // 0x05F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnDel;                                            // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnPlay;                                           // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnSave;                                           // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         controls;                                          // 0x0610(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_1;                                       // 0x0618(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtCar;                                            // 0x0620(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtDate;                                           // 0x0628(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtGameMode;                                       // 0x0630(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtSessionRound;                                   // 0x0638(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtSessionType;                                    // 0x0640(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtTimeLeft;                                       // 0x0648(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtTrack;                                          // 0x0650(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	struct FAcSaveGameHeader                      SaveGame;                                          // 0x0658(0x0158)(Edit, BlueprintVisible, ExposeOnSpawn)
	EReplayStore                                  replayStore;                                       // 0x07B0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EReplayStore                                  DefaultNavigation;                                 // 0x07B1(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_7B2[0x6];                                      // 0x07B2(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                FocusedButton;                                     // 0x07B8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TMulticastInlineDelegate<void(const struct FAcSaveGameHeader& SaveGame, class UWDG_SaveGameItem_C* Sender)> OnLoad; // 0x07C0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void(const struct FAcSaveGameHeader& SaveGame, class UWDG_SaveGameItem_C* Sender)> OnDel; // 0x07D0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void(const struct FAcSaveGameHeader& SaveGame)> OnSave;                 // 0x07E0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	class AAcMenuGameMode*                        GameMode;                                          // 0x07F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   DateText;                                          // 0x07F8(0x0018)(Edit, BlueprintVisible, ExposeOnSpawn)
	class FText                                   SessionTypeText;                                   // 0x0810(0x0018)(Edit, BlueprintVisible, ExposeOnSpawn)
	class FText                                   CarText;                                           // 0x0828(0x0018)(Edit, BlueprintVisible, ExposeOnSpawn)
	class FText                                   TrackText;                                         // 0x0840(0x0018)(Edit, BlueprintVisible, ExposeOnSpawn)
	class FText                                   TimeLeftText;                                      // 0x0858(0x0018)(Edit, BlueprintVisible, ExposeOnSpawn)
	class FText                                   PositionText;                                      // 0x0870(0x0018)(Edit, BlueprintVisible, ExposeOnSpawn)
	class FText                                   GameModeText;                                      // 0x0888(0x0018)(Edit, BlueprintVisible, ExposeOnSpawn)
	class FString                                 Filename;                                          // 0x08A0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, ExposeOnSpawn, HasGetValueTypeHash)
	class FText                                   SessionRoundText;                                  // 0x08B0(0x0018)(Edit, BlueprintVisible, ExposeOnSpawn)
	bool                                          IsMultiRound;                                      // 0x08C8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn)
	EGuiGameModes                                 GameModeType;                                      // 0x08C9(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_SaveGameItem(int32 EntryPoint);
	void BndEvt__btnSave_K2Node_ComponentBoundEvent_3_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__btnDel_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__btnPlay_K2Node_ComponentBoundEvent_5_OnAcPanelForwardEvent__DelegateSignature();
	void BP_SetHighlight(bool highlighted);
	void BP_MouseOver();
	void BP_MouseLeave();
	void OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent);
	void OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent);
	void Construct();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SaveGameItem_C">();
	}
	static class UWDG_SaveGameItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SaveGameItem_C>();
	}
};
static_assert(alignof(UWDG_SaveGameItem_C) == 0x000008, "Wrong alignment on UWDG_SaveGameItem_C");
static_assert(sizeof(UWDG_SaveGameItem_C) == 0x0008D0, "Wrong size on UWDG_SaveGameItem_C");
static_assert(offsetof(UWDG_SaveGameItem_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_SaveGameItem_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, background) == 0x0005E8, "Member 'UWDG_SaveGameItem_C::background' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, borderType) == 0x0005F0, "Member 'UWDG_SaveGameItem_C::borderType' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, btnDel) == 0x0005F8, "Member 'UWDG_SaveGameItem_C::btnDel' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, btnPlay) == 0x000600, "Member 'UWDG_SaveGameItem_C::btnPlay' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, btnSave) == 0x000608, "Member 'UWDG_SaveGameItem_C::btnSave' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, controls) == 0x000610, "Member 'UWDG_SaveGameItem_C::controls' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, TextBlock_1) == 0x000618, "Member 'UWDG_SaveGameItem_C::TextBlock_1' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, txtCar) == 0x000620, "Member 'UWDG_SaveGameItem_C::txtCar' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, txtDate) == 0x000628, "Member 'UWDG_SaveGameItem_C::txtDate' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, txtGameMode) == 0x000630, "Member 'UWDG_SaveGameItem_C::txtGameMode' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, txtSessionRound) == 0x000638, "Member 'UWDG_SaveGameItem_C::txtSessionRound' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, txtSessionType) == 0x000640, "Member 'UWDG_SaveGameItem_C::txtSessionType' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, txtTimeLeft) == 0x000648, "Member 'UWDG_SaveGameItem_C::txtTimeLeft' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, txtTrack) == 0x000650, "Member 'UWDG_SaveGameItem_C::txtTrack' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, SaveGame) == 0x000658, "Member 'UWDG_SaveGameItem_C::SaveGame' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, replayStore) == 0x0007B0, "Member 'UWDG_SaveGameItem_C::replayStore' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, DefaultNavigation) == 0x0007B1, "Member 'UWDG_SaveGameItem_C::DefaultNavigation' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, FocusedButton) == 0x0007B8, "Member 'UWDG_SaveGameItem_C::FocusedButton' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, OnLoad) == 0x0007C0, "Member 'UWDG_SaveGameItem_C::OnLoad' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, OnDel) == 0x0007D0, "Member 'UWDG_SaveGameItem_C::OnDel' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, OnSave) == 0x0007E0, "Member 'UWDG_SaveGameItem_C::OnSave' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, GameMode) == 0x0007F0, "Member 'UWDG_SaveGameItem_C::GameMode' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, DateText) == 0x0007F8, "Member 'UWDG_SaveGameItem_C::DateText' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, SessionTypeText) == 0x000810, "Member 'UWDG_SaveGameItem_C::SessionTypeText' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, CarText) == 0x000828, "Member 'UWDG_SaveGameItem_C::CarText' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, TrackText) == 0x000840, "Member 'UWDG_SaveGameItem_C::TrackText' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, TimeLeftText) == 0x000858, "Member 'UWDG_SaveGameItem_C::TimeLeftText' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, PositionText) == 0x000870, "Member 'UWDG_SaveGameItem_C::PositionText' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, GameModeText) == 0x000888, "Member 'UWDG_SaveGameItem_C::GameModeText' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, Filename) == 0x0008A0, "Member 'UWDG_SaveGameItem_C::Filename' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, SessionRoundText) == 0x0008B0, "Member 'UWDG_SaveGameItem_C::SessionRoundText' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, IsMultiRound) == 0x0008C8, "Member 'UWDG_SaveGameItem_C::IsMultiRound' has a wrong offset!");
static_assert(offsetof(UWDG_SaveGameItem_C, GameModeType) == 0x0008C9, "Member 'UWDG_SaveGameItem_C::GameModeType' has a wrong offset!");

}

