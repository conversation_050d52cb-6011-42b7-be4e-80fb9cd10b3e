﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_YesNoPopup

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_YesNoPopup.WDG_YesNoPopup_C
// 0x0030 (0x0588 - 0x0558)
class UWDG_YesNoPopup_C final : public UYesNoPopup
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0558(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UBackgroundBlur*                        BackgroundBlur_22;                                 // 0x0560(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 BackgroundTransparency;                            // 0x0568(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           CanvasPosition;                                    // 0x0570(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	TMulticastInlineDelegate<void(const struct FVector2D& NewParam)> WDG_PopUp_UpdatePosition;       // 0x0578(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)

public:
	void ExecuteUbergraph_WDG_YesNoPopup(int32 EntryPoint);
	void Construct();
	void OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_YesNoPopup_C">();
	}
	static class UWDG_YesNoPopup_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_YesNoPopup_C>();
	}
};
static_assert(alignof(UWDG_YesNoPopup_C) == 0x000008, "Wrong alignment on UWDG_YesNoPopup_C");
static_assert(sizeof(UWDG_YesNoPopup_C) == 0x000588, "Wrong size on UWDG_YesNoPopup_C");
static_assert(offsetof(UWDG_YesNoPopup_C, UberGraphFrame) == 0x000558, "Member 'UWDG_YesNoPopup_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_YesNoPopup_C, BackgroundBlur_22) == 0x000560, "Member 'UWDG_YesNoPopup_C::BackgroundBlur_22' has a wrong offset!");
static_assert(offsetof(UWDG_YesNoPopup_C, BackgroundTransparency) == 0x000568, "Member 'UWDG_YesNoPopup_C::BackgroundTransparency' has a wrong offset!");
static_assert(offsetof(UWDG_YesNoPopup_C, CanvasPosition) == 0x000570, "Member 'UWDG_YesNoPopup_C::CanvasPosition' has a wrong offset!");
static_assert(offsetof(UWDG_YesNoPopup_C, WDG_PopUp_UpdatePosition) == 0x000578, "Member 'UWDG_YesNoPopup_C::WDG_PopUp_UpdatePosition' has a wrong offset!");

}

