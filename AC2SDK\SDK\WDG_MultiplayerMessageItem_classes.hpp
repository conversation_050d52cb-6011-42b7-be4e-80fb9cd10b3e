﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_MultiplayerMessageItem

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_MultiplayerMessageItem.WDG_MultiplayerMessageItem_C
// 0x0000 (0x0280 - 0x0280)
class UWDG_MultiplayerMessageItem_C final : public UMultiplayerMessageItem
{
public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_MultiplayerMessageItem_C">();
	}
	static class UWDG_MultiplayerMessageItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_MultiplayerMessageItem_C>();
	}
};
static_assert(alignof(UWDG_MultiplayerMessageItem_C) == 0x000008, "Wrong alignment on UWDG_MultiplayerMessageItem_C");
static_assert(sizeof(UWDG_MultiplayerMessageItem_C) == 0x000280, "Wrong size on UWDG_MultiplayerMessageItem_C");

}

