﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RatingDetailPCLeaderboardItem

#include "Basic.hpp"

#include "WDG_RatingDetailPCLeaderboardItem_classes.hpp"
#include "WDG_RatingDetailPCLeaderboardItem_parameters.hpp"


namespace SDK
{

// Function WDG_RatingDetailPCLeaderboardItem.WDG_RatingDetailPCLeaderboardItem_C.SetData
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FOnlineServicesLeaderboardRank&Rank                                                   (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_RatingDetailPCLeaderboardItem_C::SetData(const struct FOnlineServicesLeaderboardRank& Rank)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_RatingDetailPCLeaderboardItem_C", "SetData");

	Params::WDG_RatingDetailPCLeaderboardItem_C_SetData Parms{};

	Parms.Rank = std::move(Rank);

	UObject::ProcessEvent(Func, &Parms);
}

}

