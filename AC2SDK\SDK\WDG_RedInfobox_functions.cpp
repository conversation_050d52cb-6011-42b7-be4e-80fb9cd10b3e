﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RedInfobox

#include "Basic.hpp"

#include "WDG_RedInfobox_classes.hpp"
#include "WDG_RedInfobox_parameters.hpp"


namespace SDK
{

// Function WDG_RedInfobox.WDG_RedInfobox_C.ExecuteUbergraph_WDG_RedInfobox
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_RedInfobox_C::ExecuteUbergraph_WDG_RedInfobox(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_RedInfobox_C", "ExecuteUbergraph_WDG_RedInfobox");

	Params::WDG_RedInfobox_C_ExecuteUbergraph_WDG_RedInfobox Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_RedInfobox.WDG_RedInfobox_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_RedInfobox_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_RedInfobox_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_RedInfobox.WDG_RedInfobox_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_RedInfobox_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_RedInfobox_C", "PreConstruct");

	Params::WDG_RedInfobox_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}

}

