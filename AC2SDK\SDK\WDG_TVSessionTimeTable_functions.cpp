﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TVSessionTimeTable

#include "Basic.hpp"

#include "WDG_TVSessionTimeTable_classes.hpp"
#include "WDG_TVSessionTimeTable_parameters.hpp"


namespace SDK
{

// Function WDG_TVSessionTimeTable.WDG_TVSessionTimeTable_C.ExecuteUbergraph_WDG_TVSessionTimeTable
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_TVSessionTimeTable_C::ExecuteUbergraph_WDG_TVSessionTimeTable(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TVSessionTimeTable_C", "ExecuteUbergraph_WDG_TVSessionTimeTable");

	Params::WDG_TVSessionTimeTable_C_ExecuteUbergraph_WDG_TVSessionTimeTable Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_TVSessionTimeTable.WDG_TVSessionTimeTable_C.OnHudVisibility
// (Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsWidgetVisible                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_TVSessionTimeTable_C::OnHudVisibility(bool IsWidgetVisible)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TVSessionTimeTable_C", "OnHudVisibility");

	Params::WDG_TVSessionTimeTable_C_OnHudVisibility Parms{};

	Parms.IsWidgetVisible = IsWidgetVisible;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_TVSessionTimeTable.WDG_TVSessionTimeTable_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_TVSessionTimeTable_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TVSessionTimeTable_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_TVSessionTimeTable.WDG_TVSessionTimeTable_C.OnMouseWheel
// (BlueprintCosmetic, Event, Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FPointerEvent&             MouseEvent                                             (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// struct FEventReply                      ReturnValue                                            (Parm, OutParm, ReturnParm)

struct FEventReply UWDG_TVSessionTimeTable_C::OnMouseWheel(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TVSessionTimeTable_C", "OnMouseWheel");

	Params::WDG_TVSessionTimeTable_C_OnMouseWheel Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.MouseEvent = std::move(MouseEvent);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}

}

