﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_NewsItem

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"
#include "NewsItem_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_NewsItem.WDG_NewsItem_C
// 0x00E0 (0x06C0 - 0x05E0)
class UWDG_NewsItem_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWDG_GenericBarItem_C*                  btnReadMore;                                       // 0x05E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             textBody;                                          // 0x05F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtReadMore;                                       // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	struct FNewsItem                              News;                                              // 0x0600(0x00B0)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	class FString                                 TargetURL;                                         // 0x06B0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_NewsItem(int32 EntryPoint);
	void PreConstruct(bool IsDesignTime);
	void BndEvt__btnReadMore_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature(class UWDG_GenericBarItem_C* Sender);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_NewsItem_C">();
	}
	static class UWDG_NewsItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_NewsItem_C>();
	}
};
static_assert(alignof(UWDG_NewsItem_C) == 0x000008, "Wrong alignment on UWDG_NewsItem_C");
static_assert(sizeof(UWDG_NewsItem_C) == 0x0006C0, "Wrong size on UWDG_NewsItem_C");
static_assert(offsetof(UWDG_NewsItem_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_NewsItem_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_NewsItem_C, btnReadMore) == 0x0005E8, "Member 'UWDG_NewsItem_C::btnReadMore' has a wrong offset!");
static_assert(offsetof(UWDG_NewsItem_C, textBody) == 0x0005F0, "Member 'UWDG_NewsItem_C::textBody' has a wrong offset!");
static_assert(offsetof(UWDG_NewsItem_C, txtReadMore) == 0x0005F8, "Member 'UWDG_NewsItem_C::txtReadMore' has a wrong offset!");
static_assert(offsetof(UWDG_NewsItem_C, News) == 0x000600, "Member 'UWDG_NewsItem_C::News' has a wrong offset!");
static_assert(offsetof(UWDG_NewsItem_C, TargetURL) == 0x0006B0, "Member 'UWDG_NewsItem_C::TargetURL' has a wrong offset!");

}

