﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_MPQuickjoinPublicMPSlot

#include "Basic.hpp"

#include "WDG_MPQuickjoinPublicMPSlot_classes.hpp"
#include "WDG_MPQuickjoinPublicMPSlot_parameters.hpp"


namespace SDK
{

// Function WDG_MPQuickjoinPublicMPSlot.WDG_MPQuickjoinPublicMPSlot_C.ExecuteUbergraph_WDG_MPQuickjoinPublicMPSlot
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_MPQuickjoinPublicMPSlot_C::ExecuteUbergraph_WDG_MPQuickjoinPublicMPSlot(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MPQuickjoinPublicMPSlot_C", "ExecuteUbergraph_WDG_MPQuickjoinPublicMPSlot");

	Params::WDG_MPQuickjoinPublicMPSlot_C_ExecuteUbergraph_WDG_MPQuickjoinPublicMPSlot Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MPQuickjoinPublicMPSlot.WDG_MPQuickjoinPublicMPSlot_C.BP_MouseLeave
// (Event, Public, BlueprintEvent)

void UWDG_MPQuickjoinPublicMPSlot_C::BP_MouseLeave()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MPQuickjoinPublicMPSlot_C", "BP_MouseLeave");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MPQuickjoinPublicMPSlot.WDG_MPQuickjoinPublicMPSlot_C.BP_MouseOver
// (Event, Public, BlueprintEvent)

void UWDG_MPQuickjoinPublicMPSlot_C::BP_MouseOver()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MPQuickjoinPublicMPSlot_C", "BP_MouseOver");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MPQuickjoinPublicMPSlot.WDG_MPQuickjoinPublicMPSlot_C.SetQuickjoinInfo
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FOnlineServicesMPQuickjoinPanelInfo&quickjoinInfo                                          (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
// const class FText&                      waitingIndicatorText                                   (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_MPQuickjoinPublicMPSlot_C::SetQuickjoinInfo(const struct FOnlineServicesMPQuickjoinPanelInfo& quickjoinInfo, const class FText& waitingIndicatorText)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MPQuickjoinPublicMPSlot_C", "SetQuickjoinInfo");

	Params::WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo Parms{};

	Parms.quickjoinInfo = std::move(quickjoinInfo);
	Parms.waitingIndicatorText = std::move(waitingIndicatorText);

	UObject::ProcessEvent(Func, &Parms);
}

}

