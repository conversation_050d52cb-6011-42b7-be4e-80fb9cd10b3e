﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SpecialEventItem

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "AC2_classes.hpp"
#include "Engine_structs.hpp"
#include "CoreUObject_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SpecialEventItem.WDG_SpecialEventItem_C
// 0x0B08 (0x10E8 - 0x05E0)
class UWDG_SpecialEventItem_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       Scale;                                             // 0x05E8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       Intro;                                             // 0x05F0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UScaleBox*                              BackgroundImage;                                   // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                bdrMain;                                           // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                borderTitle;                                       // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         boxDuration;                                       // 0x0610(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         boxLockedSetup;                                    // 0x0618(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                brdAltGradient;                                    // 0x0620(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                brdEventId;                                        // 0x0628(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                brdFixedSetupLabel;                                // 0x0630(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                brdFooter;                                         // 0x0638(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                brdGradient;                                       // 0x0640(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                brdHeader;                                         // 0x0648(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                brdLockedSetup;                                    // 0x0650(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                brdSeason;                                         // 0x0658(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                brdStaticGradient;                                 // 0x0660(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_89;                                          // 0x0668(0x0008)(ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_92;                                          // 0x0670(0x0008)(ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgBackground;                                     // 0x0678(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgLocked;                                         // 0x0680(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgTeamLogo;                                       // 0x0688(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             lblSeason;                                         // 0x0690(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             minutes;                                           // 0x0698(0x0008)(ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class USizeBox*                               sizeWrapper;                                       // 0x06A0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtDisplayName;                                    // 0x06A8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtDuration;                                       // 0x06B0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtGameMode;                                       // 0x06B8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtId;                                             // 0x06C0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtModelName;                                      // 0x06C8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtSeason;                                         // 0x06D0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtTimeOfDay;                                      // 0x06D8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtTrackConditions;                                // 0x06E0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtTrackName;                                      // 0x06E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtWeatherConditions;                              // 0x06F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         wrapperSeason;                                     // 0x06F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	struct FSpecialEventPreset                    SpecialEvent;                                      // 0x0700(0x0240)(Edit, BlueprintVisible, ExposeOnSpawn)
	TMulticastInlineDelegate<void(class UAcPanelBase* Sender)> OnControllerFocus;                    // 0x0940(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	int32                                         Index_0;                                           // 0x0950(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	uint8                                         Pad_954[0x4];                                      // 0x0954(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TMap<EGuiGameModes, struct FLinearColor>      EventBgrColorCodes;                                // 0x0958(0x0050)(Edit, BlueprintVisible, DisableEditOnInstance)
	struct FLinearColor                           EventBgrColor;                                     // 0x09A8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           EventTextColor;                                    // 0x09B8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           TitleBgrColor;                                     // 0x09C8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           TitleTextColor;                                    // 0x09D8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TMap<EGuiGameModes, struct FLinearColor>      EventTextColorCodes;                               // 0x09E8(0x0050)(Edit, BlueprintVisible, DisableEditOnInstance)
	TMulticastInlineDelegate<void(class UWDG_SpecialEventItem_C* Sender, bool ByMouse)> OnItemForward; // 0x0A38(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	struct FLinearColor                           BodyTextColor;                                     // 0x0A48(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           BorderColor;                                       // 0x0A58(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          IsSelected;                                        // 0x0A68(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	uint8                                         Pad_A69[0x3];                                      // 0x0A69(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           GradientColor;                                     // 0x0A6C(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_A7C[0x4];                                      // 0x0A7C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSpecialEventUIData                    SpecialEventUIData;                                // 0x0A80(0x0630)(Edit, BlueprintVisible)
	bool                                          IsNotInList;                                       // 0x10B0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_10B1[0x3];                                     // 0x10B1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              Size;                                              // 0x10B4(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          FixedSize;                                         // 0x10BC(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_10BD[0x3];                                     // 0x10BD(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           SelectorColor;                                     // 0x10C0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           StaticGradientColor;                               // 0x10D0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          ZoomedImage;                                       // 0x10E0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)

public:
	void ExecuteUbergraph_WDG_SpecialEventItem(int32 EntryPoint);
	void PreConstruct(bool IsDesignTime);
	void BP_MouseFocus(bool mouse_enter);
	void ForwardM(class UAcPanelBase* panel, bool mouse_over);
	void Construct();
	void OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent);
	void OnAfterConstruct();
	void OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent);
	void BP_MouseLeave();
	void BP_MouseOver();
	void GetPresetData(class FText* Car_Model_Name, EBrandType* Brand, class FText* Track_Name, class FText* GameMode, TSoftObjectPtr<class UTexture2D>* teamLogo);
	void SetColors();
	void GetMainColor(struct FLinearColor* Value);
	void SetBackgroundImage();
	void SetSelected(bool IsSelected_0);
	void UpdateFromPreset(const struct FSpecialEventPreset& SpecialEvent_0);
	void ResizeTextFont(class UTextBlock* text, int32 Size_0);
	void imgZoomedOut(class UImage* Target);
	void imgZoomedIn(class UImage* Target);
	void SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_0(class UImage* Target);
	void SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_1(class UImage* Target);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SpecialEventItem_C">();
	}
	static class UWDG_SpecialEventItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SpecialEventItem_C>();
	}
};
static_assert(alignof(UWDG_SpecialEventItem_C) == 0x000008, "Wrong alignment on UWDG_SpecialEventItem_C");
static_assert(sizeof(UWDG_SpecialEventItem_C) == 0x0010E8, "Wrong size on UWDG_SpecialEventItem_C");
static_assert(offsetof(UWDG_SpecialEventItem_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_SpecialEventItem_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, Scale) == 0x0005E8, "Member 'UWDG_SpecialEventItem_C::Scale' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, Intro) == 0x0005F0, "Member 'UWDG_SpecialEventItem_C::Intro' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, BackgroundImage) == 0x0005F8, "Member 'UWDG_SpecialEventItem_C::BackgroundImage' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, bdrMain) == 0x000600, "Member 'UWDG_SpecialEventItem_C::bdrMain' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, borderTitle) == 0x000608, "Member 'UWDG_SpecialEventItem_C::borderTitle' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, boxDuration) == 0x000610, "Member 'UWDG_SpecialEventItem_C::boxDuration' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, boxLockedSetup) == 0x000618, "Member 'UWDG_SpecialEventItem_C::boxLockedSetup' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, brdAltGradient) == 0x000620, "Member 'UWDG_SpecialEventItem_C::brdAltGradient' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, brdEventId) == 0x000628, "Member 'UWDG_SpecialEventItem_C::brdEventId' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, brdFixedSetupLabel) == 0x000630, "Member 'UWDG_SpecialEventItem_C::brdFixedSetupLabel' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, brdFooter) == 0x000638, "Member 'UWDG_SpecialEventItem_C::brdFooter' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, brdGradient) == 0x000640, "Member 'UWDG_SpecialEventItem_C::brdGradient' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, brdHeader) == 0x000648, "Member 'UWDG_SpecialEventItem_C::brdHeader' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, brdLockedSetup) == 0x000650, "Member 'UWDG_SpecialEventItem_C::brdLockedSetup' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, brdSeason) == 0x000658, "Member 'UWDG_SpecialEventItem_C::brdSeason' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, brdStaticGradient) == 0x000660, "Member 'UWDG_SpecialEventItem_C::brdStaticGradient' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, Image_89) == 0x000668, "Member 'UWDG_SpecialEventItem_C::Image_89' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, Image_92) == 0x000670, "Member 'UWDG_SpecialEventItem_C::Image_92' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, imgBackground) == 0x000678, "Member 'UWDG_SpecialEventItem_C::imgBackground' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, imgLocked) == 0x000680, "Member 'UWDG_SpecialEventItem_C::imgLocked' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, imgTeamLogo) == 0x000688, "Member 'UWDG_SpecialEventItem_C::imgTeamLogo' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, lblSeason) == 0x000690, "Member 'UWDG_SpecialEventItem_C::lblSeason' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, minutes) == 0x000698, "Member 'UWDG_SpecialEventItem_C::minutes' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, sizeWrapper) == 0x0006A0, "Member 'UWDG_SpecialEventItem_C::sizeWrapper' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, txtDisplayName) == 0x0006A8, "Member 'UWDG_SpecialEventItem_C::txtDisplayName' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, txtDuration) == 0x0006B0, "Member 'UWDG_SpecialEventItem_C::txtDuration' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, txtGameMode) == 0x0006B8, "Member 'UWDG_SpecialEventItem_C::txtGameMode' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, txtId) == 0x0006C0, "Member 'UWDG_SpecialEventItem_C::txtId' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, txtModelName) == 0x0006C8, "Member 'UWDG_SpecialEventItem_C::txtModelName' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, txtSeason) == 0x0006D0, "Member 'UWDG_SpecialEventItem_C::txtSeason' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, txtTimeOfDay) == 0x0006D8, "Member 'UWDG_SpecialEventItem_C::txtTimeOfDay' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, txtTrackConditions) == 0x0006E0, "Member 'UWDG_SpecialEventItem_C::txtTrackConditions' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, txtTrackName) == 0x0006E8, "Member 'UWDG_SpecialEventItem_C::txtTrackName' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, txtWeatherConditions) == 0x0006F0, "Member 'UWDG_SpecialEventItem_C::txtWeatherConditions' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, wrapperSeason) == 0x0006F8, "Member 'UWDG_SpecialEventItem_C::wrapperSeason' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, SpecialEvent) == 0x000700, "Member 'UWDG_SpecialEventItem_C::SpecialEvent' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, OnControllerFocus) == 0x000940, "Member 'UWDG_SpecialEventItem_C::OnControllerFocus' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, Index_0) == 0x000950, "Member 'UWDG_SpecialEventItem_C::Index_0' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, EventBgrColorCodes) == 0x000958, "Member 'UWDG_SpecialEventItem_C::EventBgrColorCodes' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, EventBgrColor) == 0x0009A8, "Member 'UWDG_SpecialEventItem_C::EventBgrColor' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, EventTextColor) == 0x0009B8, "Member 'UWDG_SpecialEventItem_C::EventTextColor' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, TitleBgrColor) == 0x0009C8, "Member 'UWDG_SpecialEventItem_C::TitleBgrColor' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, TitleTextColor) == 0x0009D8, "Member 'UWDG_SpecialEventItem_C::TitleTextColor' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, EventTextColorCodes) == 0x0009E8, "Member 'UWDG_SpecialEventItem_C::EventTextColorCodes' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, OnItemForward) == 0x000A38, "Member 'UWDG_SpecialEventItem_C::OnItemForward' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, BodyTextColor) == 0x000A48, "Member 'UWDG_SpecialEventItem_C::BodyTextColor' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, BorderColor) == 0x000A58, "Member 'UWDG_SpecialEventItem_C::BorderColor' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, IsSelected) == 0x000A68, "Member 'UWDG_SpecialEventItem_C::IsSelected' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, GradientColor) == 0x000A6C, "Member 'UWDG_SpecialEventItem_C::GradientColor' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, SpecialEventUIData) == 0x000A80, "Member 'UWDG_SpecialEventItem_C::SpecialEventUIData' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, IsNotInList) == 0x0010B0, "Member 'UWDG_SpecialEventItem_C::IsNotInList' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, Size) == 0x0010B4, "Member 'UWDG_SpecialEventItem_C::Size' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, FixedSize) == 0x0010BC, "Member 'UWDG_SpecialEventItem_C::FixedSize' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, SelectorColor) == 0x0010C0, "Member 'UWDG_SpecialEventItem_C::SelectorColor' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, StaticGradientColor) == 0x0010D0, "Member 'UWDG_SpecialEventItem_C::StaticGradientColor' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventItem_C, ZoomedImage) == 0x0010E0, "Member 'UWDG_SpecialEventItem_C::ZoomedImage' has a wrong offset!");

}

