﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomModal

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "InputCore_structs.hpp"
#include "UMG_structs.hpp"


namespace SDK::Params
{

// Function WDG_ShowroomModal.WDG_ShowroomModal_C.ExecuteUbergraph_WDG_ShowroomModal
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomModal_C_ExecuteUbergraph_WDG_ShowroomModal final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0004(0x0001)(ZeroConstructor, Is<PERSON>lainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomModal_C_ExecuteUbergraph_WDG_ShowroomModal) == 0x000004, "Wrong alignment on WDG_ShowroomModal_C_ExecuteUbergraph_WDG_ShowroomModal");
static_assert(sizeof(WDG_ShowroomModal_C_ExecuteUbergraph_WDG_ShowroomModal) == 0x000008, "Wrong size on WDG_ShowroomModal_C_ExecuteUbergraph_WDG_ShowroomModal");
static_assert(offsetof(WDG_ShowroomModal_C_ExecuteUbergraph_WDG_ShowroomModal, EntryPoint) == 0x000000, "Member 'WDG_ShowroomModal_C_ExecuteUbergraph_WDG_ShowroomModal::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomModal_C_ExecuteUbergraph_WDG_ShowroomModal, K2Node_Event_IsDesignTime) == 0x000004, "Member 'WDG_ShowroomModal_C_ExecuteUbergraph_WDG_ShowroomModal::K2Node_Event_IsDesignTime' has a wrong offset!");

// Function WDG_ShowroomModal.WDG_ShowroomModal_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_ShowroomModal_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomModal_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_ShowroomModal_C_PreConstruct");
static_assert(sizeof(WDG_ShowroomModal_C_PreConstruct) == 0x000001, "Wrong size on WDG_ShowroomModal_C_PreConstruct");
static_assert(offsetof(WDG_ShowroomModal_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_ShowroomModal_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WDG_ShowroomModal.WDG_ShowroomModal_C.Hide
// 0x0001 (0x0001 - 0x0000)
struct WDG_ShowroomModal_C_Hide final
{
public:
	bool                                          Cancelled;                                         // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomModal_C_Hide) == 0x000001, "Wrong alignment on WDG_ShowroomModal_C_Hide");
static_assert(sizeof(WDG_ShowroomModal_C_Hide) == 0x000001, "Wrong size on WDG_ShowroomModal_C_Hide");
static_assert(offsetof(WDG_ShowroomModal_C_Hide, Cancelled) == 0x000000, "Member 'WDG_ShowroomModal_C_Hide::Cancelled' has a wrong offset!");

// Function WDG_ShowroomModal.WDG_ShowroomModal_C.OnMouseButtonDoubleClick
// 0x0218 (0x0218 - 0x0000)
struct WDG_ShowroomModal_C_OnMouseButtonDoubleClick final
{
public:
	struct FGeometry                              InMyGeometry;                                      // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FPointerEvent                          InMouseEvent;                                      // 0x0038(0x0070)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	struct FEventReply                            ReturnValue;                                       // 0x00A8(0x00B8)(Parm, OutParm, ReturnParm)
	struct FEventReply                            CallFunc_Handled_ReturnValue;                      // 0x0160(0x00B8)()
};
static_assert(alignof(WDG_ShowroomModal_C_OnMouseButtonDoubleClick) == 0x000008, "Wrong alignment on WDG_ShowroomModal_C_OnMouseButtonDoubleClick");
static_assert(sizeof(WDG_ShowroomModal_C_OnMouseButtonDoubleClick) == 0x000218, "Wrong size on WDG_ShowroomModal_C_OnMouseButtonDoubleClick");
static_assert(offsetof(WDG_ShowroomModal_C_OnMouseButtonDoubleClick, InMyGeometry) == 0x000000, "Member 'WDG_ShowroomModal_C_OnMouseButtonDoubleClick::InMyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomModal_C_OnMouseButtonDoubleClick, InMouseEvent) == 0x000038, "Member 'WDG_ShowroomModal_C_OnMouseButtonDoubleClick::InMouseEvent' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomModal_C_OnMouseButtonDoubleClick, ReturnValue) == 0x0000A8, "Member 'WDG_ShowroomModal_C_OnMouseButtonDoubleClick::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomModal_C_OnMouseButtonDoubleClick, CallFunc_Handled_ReturnValue) == 0x000160, "Member 'WDG_ShowroomModal_C_OnMouseButtonDoubleClick::CallFunc_Handled_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomModal.WDG_ShowroomModal_C.OnMouseButtonDown
// 0x0218 (0x0218 - 0x0000)
struct WDG_ShowroomModal_C_OnMouseButtonDown final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FPointerEvent                          MouseEvent;                                        // 0x0038(0x0070)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	struct FEventReply                            ReturnValue;                                       // 0x00A8(0x00B8)(Parm, OutParm, ReturnParm)
	struct FEventReply                            CallFunc_Handled_ReturnValue;                      // 0x0160(0x00B8)()
};
static_assert(alignof(WDG_ShowroomModal_C_OnMouseButtonDown) == 0x000008, "Wrong alignment on WDG_ShowroomModal_C_OnMouseButtonDown");
static_assert(sizeof(WDG_ShowroomModal_C_OnMouseButtonDown) == 0x000218, "Wrong size on WDG_ShowroomModal_C_OnMouseButtonDown");
static_assert(offsetof(WDG_ShowroomModal_C_OnMouseButtonDown, MyGeometry) == 0x000000, "Member 'WDG_ShowroomModal_C_OnMouseButtonDown::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomModal_C_OnMouseButtonDown, MouseEvent) == 0x000038, "Member 'WDG_ShowroomModal_C_OnMouseButtonDown::MouseEvent' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomModal_C_OnMouseButtonDown, ReturnValue) == 0x0000A8, "Member 'WDG_ShowroomModal_C_OnMouseButtonDown::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomModal_C_OnMouseButtonDown, CallFunc_Handled_ReturnValue) == 0x000160, "Member 'WDG_ShowroomModal_C_OnMouseButtonDown::CallFunc_Handled_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomModal.WDG_ShowroomModal_C.ShowByPanel
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomModal_C_ShowByPanel final
{
public:
	class UAcPanelBase*                           CallingPanel_0;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomModal_C_ShowByPanel) == 0x000008, "Wrong alignment on WDG_ShowroomModal_C_ShowByPanel");
static_assert(sizeof(WDG_ShowroomModal_C_ShowByPanel) == 0x000008, "Wrong size on WDG_ShowroomModal_C_ShowByPanel");
static_assert(offsetof(WDG_ShowroomModal_C_ShowByPanel, CallingPanel_0) == 0x000000, "Member 'WDG_ShowroomModal_C_ShowByPanel::CallingPanel_0' has a wrong offset!");

// Function WDG_ShowroomModal.WDG_ShowroomModal_C.OnPreviewKeyDown
// 0x0370 (0x0370 - 0x0000)
struct WDG_ShowroomModal_C_OnPreviewKeyDown final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FKeyEvent                              InKeyEvent;                                        // 0x0038(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm)
	struct FEventReply                            ReturnValue;                                       // 0x0070(0x00B8)(Parm, OutParm, ReturnParm)
	struct FEventReply                            CallFunc_Handled_ReturnValue;                      // 0x0128(0x00B8)()
	struct FKey                                   CallFunc_GetKey_ReturnValue;                       // 0x01E0(0x0018)(HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue;            // 0x01F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue_1;          // 0x01F9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue_2;          // 0x01FA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x01FB(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1FC[0x4];                                      // 0x01FC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_Handled_ReturnValue_1;                    // 0x0200(0x00B8)()
	struct FEventReply                            CallFunc_Unhandled_ReturnValue;                    // 0x02B8(0x00B8)()
};
static_assert(alignof(WDG_ShowroomModal_C_OnPreviewKeyDown) == 0x000008, "Wrong alignment on WDG_ShowroomModal_C_OnPreviewKeyDown");
static_assert(sizeof(WDG_ShowroomModal_C_OnPreviewKeyDown) == 0x000370, "Wrong size on WDG_ShowroomModal_C_OnPreviewKeyDown");
static_assert(offsetof(WDG_ShowroomModal_C_OnPreviewKeyDown, MyGeometry) == 0x000000, "Member 'WDG_ShowroomModal_C_OnPreviewKeyDown::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomModal_C_OnPreviewKeyDown, InKeyEvent) == 0x000038, "Member 'WDG_ShowroomModal_C_OnPreviewKeyDown::InKeyEvent' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomModal_C_OnPreviewKeyDown, ReturnValue) == 0x000070, "Member 'WDG_ShowroomModal_C_OnPreviewKeyDown::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomModal_C_OnPreviewKeyDown, CallFunc_Handled_ReturnValue) == 0x000128, "Member 'WDG_ShowroomModal_C_OnPreviewKeyDown::CallFunc_Handled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomModal_C_OnPreviewKeyDown, CallFunc_GetKey_ReturnValue) == 0x0001E0, "Member 'WDG_ShowroomModal_C_OnPreviewKeyDown::CallFunc_GetKey_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomModal_C_OnPreviewKeyDown, CallFunc_EqualEqual_KeyKey_ReturnValue) == 0x0001F8, "Member 'WDG_ShowroomModal_C_OnPreviewKeyDown::CallFunc_EqualEqual_KeyKey_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomModal_C_OnPreviewKeyDown, CallFunc_EqualEqual_KeyKey_ReturnValue_1) == 0x0001F9, "Member 'WDG_ShowroomModal_C_OnPreviewKeyDown::CallFunc_EqualEqual_KeyKey_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomModal_C_OnPreviewKeyDown, CallFunc_EqualEqual_KeyKey_ReturnValue_2) == 0x0001FA, "Member 'WDG_ShowroomModal_C_OnPreviewKeyDown::CallFunc_EqualEqual_KeyKey_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomModal_C_OnPreviewKeyDown, CallFunc_BooleanOR_ReturnValue) == 0x0001FB, "Member 'WDG_ShowroomModal_C_OnPreviewKeyDown::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomModal_C_OnPreviewKeyDown, CallFunc_Handled_ReturnValue_1) == 0x000200, "Member 'WDG_ShowroomModal_C_OnPreviewKeyDown::CallFunc_Handled_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomModal_C_OnPreviewKeyDown, CallFunc_Unhandled_ReturnValue) == 0x0002B8, "Member 'WDG_ShowroomModal_C_OnPreviewKeyDown::CallFunc_Unhandled_ReturnValue' has a wrong offset!");

}

