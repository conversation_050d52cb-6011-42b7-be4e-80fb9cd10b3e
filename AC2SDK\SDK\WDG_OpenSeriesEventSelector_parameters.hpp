﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_OpenSeriesEventSelector

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_OpenSeriesEventSelector.WDG_OpenSeriesEventSelector_C.ExecuteUbergraph_WDG_OpenSeriesEventSelector
// 0x0588 (0x0588 - 0x0000)
struct WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue;              // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<class UWidget*>                        CallFunc_GetAllChildren_ReturnValue;               // 0x0010(0x0010)(ReferenceParm, ContainsInstancedReference)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance;             // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0028(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_29[0x3];                                       // 0x0029(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x002C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0034(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0038(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_1;                  // 0x003C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x0040(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_2;                  // 0x0044(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0048(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_49[0x3];                                       // 0x0049(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_2;                 // 0x004C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_1;                   // 0x0050(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_54[0x4];                                       // 0x0054(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                CallFunc_Array_Get_Item;                           // 0x0058(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  K2Node_DynamicCast_AsWDG_Generic_Bar_Item;         // 0x0060(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x0068(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_69[0x7];                                       // 0x0069(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class FName>                           CallFunc_GetDataTableRowNames_OutRowNames;         // 0x0070(0x0010)(ReferenceParm)
	int32                                         Temp_int_Loop_Counter_Variable_3;                  // 0x0080(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x0084(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_3;                 // 0x0088(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_1;                // 0x008C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_8D[0x3];                                       // 0x008D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_GenericBarItem_C*                  CallFunc_Create_ReturnValue;                       // 0x0090(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue;                     // 0x0098(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FMargin                                K2Node_MakeStruct_Margin;                          // 0x00A0(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	TMap<class FName, class FText>                CallFunc_SortNamedTexts_ReturnValue;               // 0x00B0(0x0050)()
	TArray<class FName>                           CallFunc_Map_Keys_Keys;                            // 0x0100(0x0010)(ReferenceParm)
	struct FMargin                                K2Node_MakeStruct_Margin_1;                        // 0x0110(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	int32                                         CallFunc_Array_Length_ReturnValue_2;               // 0x0120(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_2;                // 0x0124(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_125[0x3];                                      // 0x0125(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void(class UWDG_GenericBarItem_C* Sender)> K2Node_CreateDelegate_OutputDelegate;       // 0x0128(0x0010)(ZeroConstructor, NoDestructor)
	int32                                         Temp_int_Array_Index_Variable_2;                   // 0x0138(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   CallFunc_Array_Get_Item_1;                         // 0x013C(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_144[0x4];                                      // 0x0144(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FChampEventPreset                      CallFunc_GetDataTableRowFromName_OutRow;           // 0x0148(0x01D8)()
	bool                                          CallFunc_GetDataTableRowFromName_ReturnValue;      // 0x0320(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_321[0x3];                                      // 0x0321(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class FName                                   CallFunc_Array_Get_Item_2;                         // 0x0324(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_32C[0x4];                                      // 0x032C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FCircuitInfo                           CallFunc_GetDataTableRowFromName_OutRow_1;         // 0x0330(0x01F0)()
	bool                                          CallFunc_GetDataTableRowFromName_ReturnValue_1;    // 0x0520(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_NameName_ReturnValue;          // 0x0521(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_522[0x2];                                      // 0x0522(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue_3;               // 0x0524(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_HasAccessToContent_ReturnValue;           // 0x0528(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Less_IntInt_ReturnValue_3;                // 0x0529(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_52A[0x6];                                      // 0x052A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x0530(0x0018)()
	int32                                         Temp_int_Array_Index_Variable_3;                   // 0x0548(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   CallFunc_Array_Get_Item_3;                         // 0x054C(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_554[0x4];                                      // 0x0554(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Conv_NameToString_ReturnValue;            // 0x0558(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Map_Find_Value;                           // 0x0568(0x0018)()
	bool                                          CallFunc_Map_Find_ReturnValue;                     // 0x0580(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_581[0x3];                                      // 0x0581(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Conv_StringToInt_ReturnValue;             // 0x0584(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector) == 0x000008, "Wrong alignment on WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector");
static_assert(sizeof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector) == 0x000588, "Wrong size on WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, EntryPoint) == 0x000000, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_GetGameInstance_ReturnValue) == 0x000008, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_GetGameInstance_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_GetAllChildren_ReturnValue) == 0x000010, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_GetAllChildren_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, K2Node_DynamicCast_AsAc_Game_Instance) == 0x000020, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::K2Node_DynamicCast_AsAc_Game_Instance' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, K2Node_DynamicCast_bSuccess) == 0x000028, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_Array_Length_ReturnValue) == 0x00002C, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, Temp_int_Loop_Counter_Variable) == 0x000030, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_Add_IntInt_ReturnValue) == 0x000034, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, Temp_int_Array_Index_Variable) == 0x000038, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, Temp_int_Loop_Counter_Variable_1) == 0x00003C, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::Temp_int_Loop_Counter_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_Add_IntInt_ReturnValue_1) == 0x000040, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, Temp_int_Loop_Counter_Variable_2) == 0x000044, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::Temp_int_Loop_Counter_Variable_2' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_Less_IntInt_ReturnValue) == 0x000048, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_Add_IntInt_ReturnValue_2) == 0x00004C, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_Add_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, Temp_int_Array_Index_Variable_1) == 0x000050, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::Temp_int_Array_Index_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_Array_Get_Item) == 0x000058, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, K2Node_DynamicCast_AsWDG_Generic_Bar_Item) == 0x000060, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::K2Node_DynamicCast_AsWDG_Generic_Bar_Item' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, K2Node_DynamicCast_bSuccess_1) == 0x000068, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_GetDataTableRowNames_OutRowNames) == 0x000070, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_GetDataTableRowNames_OutRowNames' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, Temp_int_Loop_Counter_Variable_3) == 0x000080, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::Temp_int_Loop_Counter_Variable_3' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_Array_Length_ReturnValue_1) == 0x000084, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_Add_IntInt_ReturnValue_3) == 0x000088, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_Add_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_Less_IntInt_ReturnValue_1) == 0x00008C, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_Less_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_Create_ReturnValue) == 0x000090, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_AddChild_ReturnValue) == 0x000098, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_AddChild_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, K2Node_MakeStruct_Margin) == 0x0000A0, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::K2Node_MakeStruct_Margin' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_SortNamedTexts_ReturnValue) == 0x0000B0, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_SortNamedTexts_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_Map_Keys_Keys) == 0x000100, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_Map_Keys_Keys' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, K2Node_MakeStruct_Margin_1) == 0x000110, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::K2Node_MakeStruct_Margin_1' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_Array_Length_ReturnValue_2) == 0x000120, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_Array_Length_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_Less_IntInt_ReturnValue_2) == 0x000124, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_Less_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, K2Node_CreateDelegate_OutputDelegate) == 0x000128, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, Temp_int_Array_Index_Variable_2) == 0x000138, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::Temp_int_Array_Index_Variable_2' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_Array_Get_Item_1) == 0x00013C, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_GetDataTableRowFromName_OutRow) == 0x000148, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_GetDataTableRowFromName_OutRow' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_GetDataTableRowFromName_ReturnValue) == 0x000320, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_GetDataTableRowFromName_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_Array_Get_Item_2) == 0x000324, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_Array_Get_Item_2' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_GetDataTableRowFromName_OutRow_1) == 0x000330, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_GetDataTableRowFromName_OutRow_1' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_GetDataTableRowFromName_ReturnValue_1) == 0x000520, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_GetDataTableRowFromName_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_EqualEqual_NameName_ReturnValue) == 0x000521, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_EqualEqual_NameName_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_Array_Length_ReturnValue_3) == 0x000524, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_Array_Length_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_HasAccessToContent_ReturnValue) == 0x000528, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_HasAccessToContent_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_Less_IntInt_ReturnValue_3) == 0x000529, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_Less_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_Conv_StringToText_ReturnValue) == 0x000530, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, Temp_int_Array_Index_Variable_3) == 0x000548, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::Temp_int_Array_Index_Variable_3' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_Array_Get_Item_3) == 0x00054C, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_Array_Get_Item_3' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_Conv_NameToString_ReturnValue) == 0x000558, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_Conv_NameToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_Map_Find_Value) == 0x000568, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_Map_Find_Value' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_Map_Find_ReturnValue) == 0x000580, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_Map_Find_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector, CallFunc_Conv_StringToInt_ReturnValue) == 0x000584, "Member 'WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector::CallFunc_Conv_StringToInt_ReturnValue' has a wrong offset!");

// Function WDG_OpenSeriesEventSelector.WDG_OpenSeriesEventSelector_C.AddCircuit
// 0x0038 (0x0038 - 0x0000)
struct WDG_OpenSeriesEventSelector_C_AddCircuit final
{
public:
	class UWDG_GenericBarItem_C*                  Item;                                              // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_SeriesEventItem_C*                 CallFunc_Create_ReturnValue;                       // 0x0008(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void(class UWDG_SeriesEventItem_C* Sender, EUINavigation Direction)> K2Node_CreateDelegate_OutputDelegate; // 0x0010(0x0010)(ZeroConstructor, NoDestructor)
	TDelegate<void(class UWDG_SeriesEventItem_C* Sender)> K2Node_CreateDelegate_OutputDelegate_1;    // 0x0020(0x0010)(ZeroConstructor, NoDestructor)
	int32                                         CallFunc_Array_Add_ReturnValue;                    // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_OpenSeriesEventSelector_C_AddCircuit) == 0x000008, "Wrong alignment on WDG_OpenSeriesEventSelector_C_AddCircuit");
static_assert(sizeof(WDG_OpenSeriesEventSelector_C_AddCircuit) == 0x000038, "Wrong size on WDG_OpenSeriesEventSelector_C_AddCircuit");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_AddCircuit, Item) == 0x000000, "Member 'WDG_OpenSeriesEventSelector_C_AddCircuit::Item' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_AddCircuit, CallFunc_Create_ReturnValue) == 0x000008, "Member 'WDG_OpenSeriesEventSelector_C_AddCircuit::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_AddCircuit, K2Node_CreateDelegate_OutputDelegate) == 0x000010, "Member 'WDG_OpenSeriesEventSelector_C_AddCircuit::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_AddCircuit, K2Node_CreateDelegate_OutputDelegate_1) == 0x000020, "Member 'WDG_OpenSeriesEventSelector_C_AddCircuit::K2Node_CreateDelegate_OutputDelegate_1' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_AddCircuit, CallFunc_Array_Add_ReturnValue) == 0x000030, "Member 'WDG_OpenSeriesEventSelector_C_AddCircuit::CallFunc_Array_Add_ReturnValue' has a wrong offset!");

// Function WDG_OpenSeriesEventSelector.WDG_OpenSeriesEventSelector_C.DelCircuit
// 0x0078 (0x0078 - 0x0000)
struct WDG_OpenSeriesEventSelector_C_DelCircuit final
{
public:
	class UWDG_SeriesEventItem_C*                 Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcPanelBase*                           WidgetToFocus;                                     // 0x0008(0x0008)(Edit, BlueprintVisible, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x001C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1D[0x3];                                       // 0x001D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_SeriesEventItem_C*                 CallFunc_Array_Get_Item;                           // 0x0020(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x002C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2D[0x3];                                       // 0x002D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                CallFunc_GetChildAt_ReturnValue;                   // 0x0030(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UGenericBarItem*                        K2Node_DynamicCast_AsGeneric_Bar_Item;             // 0x0038(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_41[0x7];                                       // 0x0041(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UWidget*>                        CallFunc_GetAllChildren_ReturnValue;               // 0x0048(0x0010)(ReferenceParm, ContainsInstancedReference)
	class UWidget*                                CallFunc_Array_Get_Item_1;                         // 0x0058(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x0060(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_64[0x4];                                       // 0x0064(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_GenericBarItem_C*                  K2Node_DynamicCast_AsWDG_Generic_Bar_Item;         // 0x0068(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x0070(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0071(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x0072(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Array_Contains_ReturnValue;               // 0x0073(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Array_RemoveItem_ReturnValue;             // 0x0074(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_OpenSeriesEventSelector_C_DelCircuit) == 0x000008, "Wrong alignment on WDG_OpenSeriesEventSelector_C_DelCircuit");
static_assert(sizeof(WDG_OpenSeriesEventSelector_C_DelCircuit) == 0x000078, "Wrong size on WDG_OpenSeriesEventSelector_C_DelCircuit");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DelCircuit, Sender) == 0x000000, "Member 'WDG_OpenSeriesEventSelector_C_DelCircuit::Sender' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DelCircuit, WidgetToFocus) == 0x000008, "Member 'WDG_OpenSeriesEventSelector_C_DelCircuit::WidgetToFocus' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DelCircuit, Temp_int_Array_Index_Variable) == 0x000010, "Member 'WDG_OpenSeriesEventSelector_C_DelCircuit::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DelCircuit, Temp_int_Loop_Counter_Variable) == 0x000014, "Member 'WDG_OpenSeriesEventSelector_C_DelCircuit::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DelCircuit, CallFunc_Add_IntInt_ReturnValue) == 0x000018, "Member 'WDG_OpenSeriesEventSelector_C_DelCircuit::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DelCircuit, CallFunc_IsValid_ReturnValue) == 0x00001C, "Member 'WDG_OpenSeriesEventSelector_C_DelCircuit::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DelCircuit, CallFunc_Array_Get_Item) == 0x000020, "Member 'WDG_OpenSeriesEventSelector_C_DelCircuit::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DelCircuit, CallFunc_Array_Length_ReturnValue) == 0x000028, "Member 'WDG_OpenSeriesEventSelector_C_DelCircuit::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DelCircuit, CallFunc_Greater_IntInt_ReturnValue) == 0x00002C, "Member 'WDG_OpenSeriesEventSelector_C_DelCircuit::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DelCircuit, CallFunc_GetChildAt_ReturnValue) == 0x000030, "Member 'WDG_OpenSeriesEventSelector_C_DelCircuit::CallFunc_GetChildAt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DelCircuit, K2Node_DynamicCast_AsGeneric_Bar_Item) == 0x000038, "Member 'WDG_OpenSeriesEventSelector_C_DelCircuit::K2Node_DynamicCast_AsGeneric_Bar_Item' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DelCircuit, K2Node_DynamicCast_bSuccess) == 0x000040, "Member 'WDG_OpenSeriesEventSelector_C_DelCircuit::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DelCircuit, CallFunc_GetAllChildren_ReturnValue) == 0x000048, "Member 'WDG_OpenSeriesEventSelector_C_DelCircuit::CallFunc_GetAllChildren_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DelCircuit, CallFunc_Array_Get_Item_1) == 0x000058, "Member 'WDG_OpenSeriesEventSelector_C_DelCircuit::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DelCircuit, CallFunc_Array_Length_ReturnValue_1) == 0x000060, "Member 'WDG_OpenSeriesEventSelector_C_DelCircuit::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DelCircuit, K2Node_DynamicCast_AsWDG_Generic_Bar_Item) == 0x000068, "Member 'WDG_OpenSeriesEventSelector_C_DelCircuit::K2Node_DynamicCast_AsWDG_Generic_Bar_Item' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DelCircuit, K2Node_DynamicCast_bSuccess_1) == 0x000070, "Member 'WDG_OpenSeriesEventSelector_C_DelCircuit::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DelCircuit, CallFunc_Less_IntInt_ReturnValue) == 0x000071, "Member 'WDG_OpenSeriesEventSelector_C_DelCircuit::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DelCircuit, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x000072, "Member 'WDG_OpenSeriesEventSelector_C_DelCircuit::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DelCircuit, CallFunc_Array_Contains_ReturnValue) == 0x000073, "Member 'WDG_OpenSeriesEventSelector_C_DelCircuit::CallFunc_Array_Contains_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DelCircuit, CallFunc_Array_RemoveItem_ReturnValue) == 0x000074, "Member 'WDG_OpenSeriesEventSelector_C_DelCircuit::CallFunc_Array_RemoveItem_ReturnValue' has a wrong offset!");

// Function WDG_OpenSeriesEventSelector.WDG_OpenSeriesEventSelector_C.ReOrderCircuit
// 0x0028 (0x0028 - 0x0000)
struct WDG_OpenSeriesEventSelector_C_ReOrderCircuit final
{
public:
	class UWDG_SeriesEventItem_C*                 Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EUINavigation                                 Direction;                                         // 0x0008(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0009(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_A[0x2];                                        // 0x000A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Find_ReturnValue;                   // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_LastIndex_ReturnValue;              // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_19[0x3];                                       // 0x0019(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_1;             // 0x0021(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_OpenSeriesEventSelector_C_ReOrderCircuit) == 0x000008, "Wrong alignment on WDG_OpenSeriesEventSelector_C_ReOrderCircuit");
static_assert(sizeof(WDG_OpenSeriesEventSelector_C_ReOrderCircuit) == 0x000028, "Wrong size on WDG_OpenSeriesEventSelector_C_ReOrderCircuit");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ReOrderCircuit, Sender) == 0x000000, "Member 'WDG_OpenSeriesEventSelector_C_ReOrderCircuit::Sender' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ReOrderCircuit, Direction) == 0x000008, "Member 'WDG_OpenSeriesEventSelector_C_ReOrderCircuit::Direction' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ReOrderCircuit, K2Node_SwitchEnum_CmpSuccess) == 0x000009, "Member 'WDG_OpenSeriesEventSelector_C_ReOrderCircuit::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ReOrderCircuit, CallFunc_Array_Find_ReturnValue) == 0x00000C, "Member 'WDG_OpenSeriesEventSelector_C_ReOrderCircuit::CallFunc_Array_Find_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ReOrderCircuit, CallFunc_Array_LastIndex_ReturnValue) == 0x000010, "Member 'WDG_OpenSeriesEventSelector_C_ReOrderCircuit::CallFunc_Array_LastIndex_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ReOrderCircuit, CallFunc_Add_IntInt_ReturnValue) == 0x000014, "Member 'WDG_OpenSeriesEventSelector_C_ReOrderCircuit::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ReOrderCircuit, CallFunc_Less_IntInt_ReturnValue) == 0x000018, "Member 'WDG_OpenSeriesEventSelector_C_ReOrderCircuit::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ReOrderCircuit, CallFunc_Subtract_IntInt_ReturnValue) == 0x00001C, "Member 'WDG_OpenSeriesEventSelector_C_ReOrderCircuit::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ReOrderCircuit, CallFunc_Greater_IntInt_ReturnValue) == 0x000020, "Member 'WDG_OpenSeriesEventSelector_C_ReOrderCircuit::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_ReOrderCircuit, CallFunc_Greater_IntInt_ReturnValue_1) == 0x000021, "Member 'WDG_OpenSeriesEventSelector_C_ReOrderCircuit::CallFunc_Greater_IntInt_ReturnValue_1' has a wrong offset!");

// Function WDG_OpenSeriesEventSelector.WDG_OpenSeriesEventSelector_C.PopulateSelectedCircuits
// 0x0050 (0x0050 - 0x0000)
struct WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits final
{
public:
	TArray<class FName>                           circuits;                                          // 0x0000(0x0010)(Edit, BlueprintVisible)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_21[0x7];                                       // 0x0021(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_SeriesEventItem_C*                 CallFunc_Array_Get_Item;                           // 0x0030(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x0038(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x003C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_3D[0x3];                                       // 0x003D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Add_ReturnValue;                    // 0x0040(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue_1;                  // 0x0044(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue;                     // 0x0048(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits) == 0x000008, "Wrong alignment on WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits");
static_assert(sizeof(WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits) == 0x000050, "Wrong size on WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits, circuits) == 0x000000, "Member 'WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits::circuits' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits, Temp_int_Array_Index_Variable) == 0x000010, "Member 'WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits, Temp_int_Loop_Counter_Variable) == 0x000014, "Member 'WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits, CallFunc_Add_IntInt_ReturnValue) == 0x000018, "Member 'WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits, CallFunc_Array_Length_ReturnValue) == 0x00001C, "Member 'WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits, CallFunc_Greater_IntInt_ReturnValue) == 0x000020, "Member 'WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits, CallFunc_PlayAnimation_ReturnValue) == 0x000028, "Member 'WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits, CallFunc_Array_Get_Item) == 0x000030, "Member 'WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits, CallFunc_Array_Length_ReturnValue_1) == 0x000038, "Member 'WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits, CallFunc_Less_IntInt_ReturnValue) == 0x00003C, "Member 'WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits, CallFunc_Array_Add_ReturnValue) == 0x000040, "Member 'WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits::CallFunc_Array_Add_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits, CallFunc_Array_Add_ReturnValue_1) == 0x000044, "Member 'WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits::CallFunc_Array_Add_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits, CallFunc_AddChild_ReturnValue) == 0x000048, "Member 'WDG_OpenSeriesEventSelector_C_PopulateSelectedCircuits::CallFunc_AddChild_ReturnValue' has a wrong offset!");

// Function WDG_OpenSeriesEventSelector.WDG_OpenSeriesEventSelector_C.DoCustomNavigation_0
// 0x0020 (0x0020 - 0x0000)
struct WDG_OpenSeriesEventSelector_C_DoCustomNavigation_0 final
{
public:
	EUINavigation                                 Navigation_0;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                ReturnValue;                                       // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWidget*                                CallFunc_GetChildAt_ReturnValue;                   // 0x0010(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_OpenSeriesEventSelector_C_DoCustomNavigation_0) == 0x000008, "Wrong alignment on WDG_OpenSeriesEventSelector_C_DoCustomNavigation_0");
static_assert(sizeof(WDG_OpenSeriesEventSelector_C_DoCustomNavigation_0) == 0x000020, "Wrong size on WDG_OpenSeriesEventSelector_C_DoCustomNavigation_0");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DoCustomNavigation_0, Navigation_0) == 0x000000, "Member 'WDG_OpenSeriesEventSelector_C_DoCustomNavigation_0::Navigation_0' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DoCustomNavigation_0, ReturnValue) == 0x000008, "Member 'WDG_OpenSeriesEventSelector_C_DoCustomNavigation_0::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DoCustomNavigation_0, CallFunc_GetChildAt_ReturnValue) == 0x000010, "Member 'WDG_OpenSeriesEventSelector_C_DoCustomNavigation_0::CallFunc_GetChildAt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_OpenSeriesEventSelector_C_DoCustomNavigation_0, CallFunc_IsValid_ReturnValue) == 0x000018, "Member 'WDG_OpenSeriesEventSelector_C_DoCustomNavigation_0::CallFunc_IsValid_ReturnValue' has a wrong offset!");

}

