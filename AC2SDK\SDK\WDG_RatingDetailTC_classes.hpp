﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RatingDetailTC

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RatingDetailTC.WDG_RatingDetailTC_C
// 0x0010 (0x06C8 - 0x06B8)
class UWDG_RatingDetailTC_C final : public URatingTCDetail
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x06B8(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UImage*                                 imgBackground;                                     // 0x06C0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_RatingDetailTC(int32 EntryPoint);
	void Construct();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RatingDetailTC_C">();
	}
	static class UWDG_RatingDetailTC_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RatingDetailTC_C>();
	}
};
static_assert(alignof(UWDG_RatingDetailTC_C) == 0x000008, "Wrong alignment on UWDG_RatingDetailTC_C");
static_assert(sizeof(UWDG_RatingDetailTC_C) == 0x0006C8, "Wrong size on UWDG_RatingDetailTC_C");
static_assert(offsetof(UWDG_RatingDetailTC_C, UberGraphFrame) == 0x0006B8, "Member 'UWDG_RatingDetailTC_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailTC_C, imgBackground) == 0x0006C0, "Member 'UWDG_RatingDetailTC_C::imgBackground' has a wrong offset!");

}

