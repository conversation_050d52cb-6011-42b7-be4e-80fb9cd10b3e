﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_StartUpLoadingScreen

#include "Basic.hpp"


namespace SDK::Params
{

// Function WDG_StartUpLoadingScreen.WDG_StartUpLoadingScreen_C.ExecuteUbergraph_WDG_StartUpLoadingScreen
// 0x00A8 (0x00A8 - 0x0000)
struct WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0008(0x0008)(ZeroConstructor, Is<PERSON>lainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_TextToString_ReturnValue;            // 0x0010(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue;              // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue;                // 0x0028(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance;             // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_41[0x7];                                       // 0x0041(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_GetCurrentGameVersion_ReturnValue;        // 0x0048(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x0058(0x0018)()
	class FString                                 CallFunc_Conv_TextToString_ReturnValue_1;          // 0x0070(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_1;              // 0x0080(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_1;          // 0x0090(0x0018)()
};
static_assert(alignof(WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen) == 0x000008, "Wrong alignment on WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen");
static_assert(sizeof(WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen) == 0x0000A8, "Wrong size on WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen");
static_assert(offsetof(WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen, EntryPoint) == 0x000000, "Member 'WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen, CallFunc_PlayAnimation_ReturnValue) == 0x000008, "Member 'WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen, CallFunc_Conv_TextToString_ReturnValue) == 0x000010, "Member 'WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen::CallFunc_Conv_TextToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen, CallFunc_GetGameInstance_ReturnValue) == 0x000020, "Member 'WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen::CallFunc_GetGameInstance_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen, CallFunc_Concat_StrStr_ReturnValue) == 0x000028, "Member 'WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen::CallFunc_Concat_StrStr_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen, K2Node_DynamicCast_AsAc_Game_Instance) == 0x000038, "Member 'WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen::K2Node_DynamicCast_AsAc_Game_Instance' has a wrong offset!");
static_assert(offsetof(WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen, K2Node_DynamicCast_bSuccess) == 0x000040, "Member 'WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen, CallFunc_GetCurrentGameVersion_ReturnValue) == 0x000048, "Member 'WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen::CallFunc_GetCurrentGameVersion_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen, CallFunc_Conv_StringToText_ReturnValue) == 0x000058, "Member 'WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen, CallFunc_Conv_TextToString_ReturnValue_1) == 0x000070, "Member 'WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen::CallFunc_Conv_TextToString_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen, CallFunc_Concat_StrStr_ReturnValue_1) == 0x000080, "Member 'WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen::CallFunc_Concat_StrStr_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen, CallFunc_Conv_StringToText_ReturnValue_1) == 0x000090, "Member 'WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen::CallFunc_Conv_StringToText_ReturnValue_1' has a wrong offset!");

// Function WDG_StartUpLoadingScreen.WDG_StartUpLoadingScreen_C.Get_Loading_lbl_Text_0
// 0x00B0 (0x00B0 - 0x0000)
struct WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0 final
{
public:
	class FText                                   ReturnValue;                                       // 0x0000(0x0018)(Parm, OutParm, ReturnParm)
	class FText                                   Temp_text_Variable;                                // 0x0018(0x0018)()
	class FText                                   Temp_text_Variable_1;                              // 0x0030(0x0018)()
	int32                                         Temp_int_Variable;                                 // 0x0048(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_FloatFloat_ReturnValue;           // 0x004C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_4D[0x3];                                       // 0x004D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Percent_IntInt_ReturnValue;               // 0x0050(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_54[0x4];                                       // 0x0054(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   Temp_text_Variable_2;                              // 0x0058(0x0018)()
	class FText                                   Temp_text_Variable_3;                              // 0x0070(0x0018)()
	int32                                         Temp_int_Variable_1;                               // 0x0088(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_8C[0x4];                                       // 0x008C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   K2Node_Select_Default;                             // 0x0090(0x0018)()
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x00A8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Add_FloatFloat_ReturnValue;               // 0x00AC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0) == 0x000008, "Wrong alignment on WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0");
static_assert(sizeof(WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0) == 0x0000B0, "Wrong size on WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0");
static_assert(offsetof(WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0, ReturnValue) == 0x000000, "Member 'WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0, Temp_text_Variable) == 0x000018, "Member 'WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0::Temp_text_Variable' has a wrong offset!");
static_assert(offsetof(WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0, Temp_text_Variable_1) == 0x000030, "Member 'WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0::Temp_text_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0, Temp_int_Variable) == 0x000048, "Member 'WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0, CallFunc_Greater_FloatFloat_ReturnValue) == 0x00004C, "Member 'WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0::CallFunc_Greater_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0, CallFunc_Percent_IntInt_ReturnValue) == 0x000050, "Member 'WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0::CallFunc_Percent_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0, Temp_text_Variable_2) == 0x000058, "Member 'WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0::Temp_text_Variable_2' has a wrong offset!");
static_assert(offsetof(WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0, Temp_text_Variable_3) == 0x000070, "Member 'WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0::Temp_text_Variable_3' has a wrong offset!");
static_assert(offsetof(WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0, Temp_int_Variable_1) == 0x000088, "Member 'WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0, K2Node_Select_Default) == 0x000090, "Member 'WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0, CallFunc_Add_IntInt_ReturnValue) == 0x0000A8, "Member 'WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0, CallFunc_Add_FloatFloat_ReturnValue) == 0x0000AC, "Member 'WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0::CallFunc_Add_FloatFloat_ReturnValue' has a wrong offset!");

}

