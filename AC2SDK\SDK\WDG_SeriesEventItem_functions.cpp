﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SeriesEventItem

#include "Basic.hpp"

#include "WDG_SeriesEventItem_classes.hpp"
#include "WDG_SeriesEventItem_parameters.hpp"


namespace SDK
{

// Function WDG_SeriesEventItem.WDG_SeriesEventItem_C.ExecuteUbergraph_WDG_SeriesEventItem
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SeriesEventItem_C::ExecuteUbergraph_WDG_SeriesEventItem(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeriesEventItem_C", "ExecuteUbergraph_WDG_SeriesEventItem");

	Params::WDG_SeriesEventItem_C_ExecuteUbergraph_WDG_SeriesEventItem Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SeriesEventItem.WDG_SeriesEventItem_C.BndEvt__WDG_SeriesEventItem_btnDown_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UWDG_GenericBarItem_C*            Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SeriesEventItem_C::BndEvt__WDG_SeriesEventItem_btnDown_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature(class UWDG_GenericBarItem_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeriesEventItem_C", "BndEvt__WDG_SeriesEventItem_btnDown_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature");

	Params::WDG_SeriesEventItem_C_BndEvt__WDG_SeriesEventItem_btnDown_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature Parms{};

	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SeriesEventItem.WDG_SeriesEventItem_C.BndEvt__WDG_SeriesEventItem_btnUp_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UWDG_GenericBarItem_C*            Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SeriesEventItem_C::BndEvt__WDG_SeriesEventItem_btnUp_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature(class UWDG_GenericBarItem_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeriesEventItem_C", "BndEvt__WDG_SeriesEventItem_btnUp_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature");

	Params::WDG_SeriesEventItem_C_BndEvt__WDG_SeriesEventItem_btnUp_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature Parms{};

	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SeriesEventItem.WDG_SeriesEventItem_C.BndEvt__WDG_SeriesEventItem_btnDel_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UWDG_GenericBarItem_C*            Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SeriesEventItem_C::BndEvt__WDG_SeriesEventItem_btnDel_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature(class UWDG_GenericBarItem_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeriesEventItem_C", "BndEvt__WDG_SeriesEventItem_btnDel_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature");

	Params::WDG_SeriesEventItem_C_BndEvt__WDG_SeriesEventItem_btnDel_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature Parms{};

	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SeriesEventItem.WDG_SeriesEventItem_C.OnRemovedFromFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_SeriesEventItem_C::OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeriesEventItem_C", "OnRemovedFromFocusPath");

	Params::WDG_SeriesEventItem_C_OnRemovedFromFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SeriesEventItem.WDG_SeriesEventItem_C.OnAddedToFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_SeriesEventItem_C::OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeriesEventItem_C", "OnAddedToFocusPath");

	Params::WDG_SeriesEventItem_C_OnAddedToFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SeriesEventItem.WDG_SeriesEventItem_C.BP_MouseLeave
// (Event, Public, BlueprintEvent)

void UWDG_SeriesEventItem_C::BP_MouseLeave()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeriesEventItem_C", "BP_MouseLeave");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SeriesEventItem.WDG_SeriesEventItem_C.BP_MouseOver
// (Event, Public, BlueprintEvent)

void UWDG_SeriesEventItem_C::BP_MouseOver()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeriesEventItem_C", "BP_MouseOver");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SeriesEventItem.WDG_SeriesEventItem_C.BP_SetHighlight
// (Event, Public, BlueprintEvent)
// Parameters:
// bool                                    highlighted                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SeriesEventItem_C::BP_SetHighlight(bool highlighted)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeriesEventItem_C", "BP_SetHighlight");

	Params::WDG_SeriesEventItem_C_BP_SetHighlight Parms{};

	Parms.highlighted = highlighted;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SeriesEventItem.WDG_SeriesEventItem_C.OnAfterConstruct
// (Event, Public, BlueprintEvent)

void UWDG_SeriesEventItem_C::OnAfterConstruct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeriesEventItem_C", "OnAfterConstruct");

	UObject::ProcessEvent(Func, nullptr);
}

}

