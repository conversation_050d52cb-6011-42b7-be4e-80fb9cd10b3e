﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ServerInfoItem

#include "Basic.hpp"

#include "WDG_ServerInfoItem_classes.hpp"
#include "WDG_ServerInfoItem_parameters.hpp"


namespace SDK
{

// Function WDG_ServerInfoItem.WDG_ServerInfoItem_C.ExecuteUbergraph_WDG_ServerInfoItem
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ServerInfoItem_C::ExecuteUbergraph_WDG_ServerInfoItem(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ServerInfoItem_C", "ExecuteUbergraph_WDG_ServerInfoItem");

	Params::WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ServerInfoItem.WDG_ServerInfoItem_C.Tick
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// float                                   InDeltaTime                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ServerInfoItem_C::Tick(const struct FGeometry& MyGeometry, float InDeltaTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ServerInfoItem_C", "Tick");

	Params::WDG_ServerInfoItem_C_Tick Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InDeltaTime = InDeltaTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ServerInfoItem.WDG_ServerInfoItem_C.BP_MouseFocus
// (Event, Public, BlueprintEvent)
// Parameters:
// bool                                    mouse_enter                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ServerInfoItem_C::BP_MouseFocus(bool mouse_enter)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ServerInfoItem_C", "BP_MouseFocus");

	Params::WDG_ServerInfoItem_C_BP_MouseFocus Parms{};

	Parms.mouse_enter = mouse_enter;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ServerInfoItem.WDG_ServerInfoItem_C.OnSetServerInfoComplete
// (Event, Public, BlueprintEvent)
// Parameters:
// const struct FOnlineServicesMPServerInfo&receivedServerInfo                                     (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_ServerInfoItem_C::OnSetServerInfoComplete(const struct FOnlineServicesMPServerInfo& receivedServerInfo)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ServerInfoItem_C", "OnSetServerInfoComplete");

	Params::WDG_ServerInfoItem_C_OnSetServerInfoComplete Parms{};

	Parms.receivedServerInfo = std::move(receivedServerInfo);

	UObject::ProcessEvent(Func, &Parms);
}

}

