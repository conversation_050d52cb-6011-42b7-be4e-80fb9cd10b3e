﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PublicHelpWidget

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_PublicHelpWidget.WDG_PublicHelpWidget_C
// 0x0000 (0x0658 - 0x0658)
class UWDG_PublicHelpWidget_C final : public UAcRaceWidgetBase
{
public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_PublicHelpWidget_C">();
	}
	static class UWDG_PublicHelpWidget_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_PublicHelpWidget_C>();
	}
};
static_assert(alignof(UWDG_PublicHelpWidget_C) == 0x000008, "Wrong alignment on UWDG_PublicHelpWidget_C");
static_assert(sizeof(UWDG_PublicHelpWidget_C) == 0x000658, "Wrong size on UWDG_PublicHelpWidget_C");

}

