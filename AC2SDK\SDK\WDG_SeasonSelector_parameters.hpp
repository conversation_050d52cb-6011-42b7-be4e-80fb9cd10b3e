﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SeasonSelector

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_SeasonSelector.WDG_SeasonSelector_C.ExecuteUbergraph_WDG_SeasonSelector
// 0x0110 (0x0110 - 0x0000)
struct WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void(ESeasonType new_season)>       K2Node_CreateDelegate_OutputDelegate;              // 0x0004(0x0010)(ZeroConstructor, NoDestructor)
	bool                                          K2Node_Event_active;                               // 0x0014(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0015(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_16[0x2];                                       // 0x0016(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateFontInfo                         K2Node_MakeStruct_SlateFontInfo;                   // 0x0018(0x0058)(UObjectWrapper, HasGetValueTypeHash)
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate_1;            // 0x0070(0x0010)(ZeroConstructor, NoDestructor)
	class UBorderSlot*                            CallFunc_SlotAsBorderSlot_ReturnValue;             // 0x0080(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0088(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_1;              // 0x0090(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSlateColor                            K2Node_MakeStruct_SlateColor;                      // 0x0098(0x0028)()
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_1;                    // 0x00C0(0x0028)(UObjectWrapper)
	struct FFocusEvent                            K2Node_Event_InFocusEvent;                         // 0x00E8(0x0008)(NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x00F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x00F1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_F2[0x6];                                       // 0x00F2(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class AGameModeBase*                          CallFunc_GetGameMode_ReturnValue;                  // 0x00F8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AAcMenuGameMode*                        K2Node_DynamicCast_AsAc_Menu_Game_Mode;            // 0x0100(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0108(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	ESeasonType                                   K2Node_CustomEvent_new_season;                     // 0x0109(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector) == 0x000008, "Wrong alignment on WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector");
static_assert(sizeof(WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector) == 0x000110, "Wrong size on WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector");
static_assert(offsetof(WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector, EntryPoint) == 0x000000, "Member 'WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector, K2Node_CreateDelegate_OutputDelegate) == 0x000004, "Member 'WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector, K2Node_Event_active) == 0x000014, "Member 'WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector::K2Node_Event_active' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector, K2Node_Event_IsDesignTime) == 0x000015, "Member 'WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector, K2Node_MakeStruct_SlateFontInfo) == 0x000018, "Member 'WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector::K2Node_MakeStruct_SlateFontInfo' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector, K2Node_CreateDelegate_OutputDelegate_1) == 0x000070, "Member 'WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector::K2Node_CreateDelegate_OutputDelegate_1' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector, CallFunc_SlotAsBorderSlot_ReturnValue) == 0x000080, "Member 'WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector::CallFunc_SlotAsBorderSlot_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector, CallFunc_PlayAnimation_ReturnValue) == 0x000088, "Member 'WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector, CallFunc_PlayAnimation_ReturnValue_1) == 0x000090, "Member 'WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector::CallFunc_PlayAnimation_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector, K2Node_MakeStruct_SlateColor) == 0x000098, "Member 'WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector::K2Node_MakeStruct_SlateColor' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector, K2Node_MakeStruct_SlateColor_1) == 0x0000C0, "Member 'WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector::K2Node_MakeStruct_SlateColor_1' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector, K2Node_Event_InFocusEvent) == 0x0000E8, "Member 'WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector::K2Node_Event_InFocusEvent' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector, CallFunc_Not_PreBool_ReturnValue) == 0x0000F0, "Member 'WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector, CallFunc_BooleanAND_ReturnValue) == 0x0000F1, "Member 'WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector, CallFunc_GetGameMode_ReturnValue) == 0x0000F8, "Member 'WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector::CallFunc_GetGameMode_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector, K2Node_DynamicCast_AsAc_Menu_Game_Mode) == 0x000100, "Member 'WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector::K2Node_DynamicCast_AsAc_Menu_Game_Mode' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector, K2Node_DynamicCast_bSuccess) == 0x000108, "Member 'WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector, K2Node_CustomEvent_new_season) == 0x000109, "Member 'WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector::K2Node_CustomEvent_new_season' has a wrong offset!");

// Function WDG_SeasonSelector.WDG_SeasonSelector_C.OnSeasonChanged_Event_0
// 0x0001 (0x0001 - 0x0000)
struct WDG_SeasonSelector_C_OnSeasonChanged_Event_0 final
{
public:
	ESeasonType                                   new_season;                                        // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SeasonSelector_C_OnSeasonChanged_Event_0) == 0x000001, "Wrong alignment on WDG_SeasonSelector_C_OnSeasonChanged_Event_0");
static_assert(sizeof(WDG_SeasonSelector_C_OnSeasonChanged_Event_0) == 0x000001, "Wrong size on WDG_SeasonSelector_C_OnSeasonChanged_Event_0");
static_assert(offsetof(WDG_SeasonSelector_C_OnSeasonChanged_Event_0, new_season) == 0x000000, "Member 'WDG_SeasonSelector_C_OnSeasonChanged_Event_0::new_season' has a wrong offset!");

// Function WDG_SeasonSelector.WDG_SeasonSelector_C.OnRemovedFromFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_SeasonSelector_C_OnRemovedFromFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_SeasonSelector_C_OnRemovedFromFocusPath) == 0x000004, "Wrong alignment on WDG_SeasonSelector_C_OnRemovedFromFocusPath");
static_assert(sizeof(WDG_SeasonSelector_C_OnRemovedFromFocusPath) == 0x000008, "Wrong size on WDG_SeasonSelector_C_OnRemovedFromFocusPath");
static_assert(offsetof(WDG_SeasonSelector_C_OnRemovedFromFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_SeasonSelector_C_OnRemovedFromFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_SeasonSelector.WDG_SeasonSelector_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_SeasonSelector_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SeasonSelector_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_SeasonSelector_C_PreConstruct");
static_assert(sizeof(WDG_SeasonSelector_C_PreConstruct) == 0x000001, "Wrong size on WDG_SeasonSelector_C_PreConstruct");
static_assert(offsetof(WDG_SeasonSelector_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_SeasonSelector_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WDG_SeasonSelector.WDG_SeasonSelector_C.BP_UpdateActivity
// 0x0001 (0x0001 - 0x0000)
struct WDG_SeasonSelector_C_BP_UpdateActivity final
{
public:
	bool                                          Active;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SeasonSelector_C_BP_UpdateActivity) == 0x000001, "Wrong alignment on WDG_SeasonSelector_C_BP_UpdateActivity");
static_assert(sizeof(WDG_SeasonSelector_C_BP_UpdateActivity) == 0x000001, "Wrong size on WDG_SeasonSelector_C_BP_UpdateActivity");
static_assert(offsetof(WDG_SeasonSelector_C_BP_UpdateActivity, Active) == 0x000000, "Member 'WDG_SeasonSelector_C_BP_UpdateActivity::Active' has a wrong offset!");

// Function WDG_SeasonSelector.WDG_SeasonSelector_C.SetTitleText
// 0x0018 (0x0018 - 0x0000)
struct WDG_SeasonSelector_C_SetTitleText final
{
public:
	class FText                                   text;                                              // 0x0000(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_SeasonSelector_C_SetTitleText) == 0x000008, "Wrong alignment on WDG_SeasonSelector_C_SetTitleText");
static_assert(sizeof(WDG_SeasonSelector_C_SetTitleText) == 0x000018, "Wrong size on WDG_SeasonSelector_C_SetTitleText");
static_assert(offsetof(WDG_SeasonSelector_C_SetTitleText, text) == 0x000000, "Member 'WDG_SeasonSelector_C_SetTitleText::text' has a wrong offset!");

// Function WDG_SeasonSelector.WDG_SeasonSelector_C.SetToggled
// 0x0058 (0x0058 - 0x0000)
struct WDG_SeasonSelector_C_SetToggled final
{
public:
	bool                                          IsToggledOn;                                       // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          ForceTextColor;                                    // 0x0001(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          TriggerEvent;                                      // 0x0002(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          NewLocalVar_0;                                     // 0x0003(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateColor                            K2Node_MakeStruct_SlateColor;                      // 0x0008(0x0028)(UObjectWrapper)
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_1;                    // 0x0030(0x0028)(UObjectWrapper)
};
static_assert(alignof(WDG_SeasonSelector_C_SetToggled) == 0x000008, "Wrong alignment on WDG_SeasonSelector_C_SetToggled");
static_assert(sizeof(WDG_SeasonSelector_C_SetToggled) == 0x000058, "Wrong size on WDG_SeasonSelector_C_SetToggled");
static_assert(offsetof(WDG_SeasonSelector_C_SetToggled, IsToggledOn) == 0x000000, "Member 'WDG_SeasonSelector_C_SetToggled::IsToggledOn' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_SetToggled, ForceTextColor) == 0x000001, "Member 'WDG_SeasonSelector_C_SetToggled::ForceTextColor' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_SetToggled, TriggerEvent) == 0x000002, "Member 'WDG_SeasonSelector_C_SetToggled::TriggerEvent' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_SetToggled, NewLocalVar_0) == 0x000003, "Member 'WDG_SeasonSelector_C_SetToggled::NewLocalVar_0' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_SetToggled, K2Node_MakeStruct_SlateColor) == 0x000008, "Member 'WDG_SeasonSelector_C_SetToggled::K2Node_MakeStruct_SlateColor' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_SetToggled, K2Node_MakeStruct_SlateColor_1) == 0x000030, "Member 'WDG_SeasonSelector_C_SetToggled::K2Node_MakeStruct_SlateColor_1' has a wrong offset!");

// Function WDG_SeasonSelector.WDG_SeasonSelector_C.SetNormalColor
// 0x0001 (0x0001 - 0x0000)
struct WDG_SeasonSelector_C_SetNormalColor final
{
public:
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0000(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SeasonSelector_C_SetNormalColor) == 0x000001, "Wrong alignment on WDG_SeasonSelector_C_SetNormalColor");
static_assert(sizeof(WDG_SeasonSelector_C_SetNormalColor) == 0x000001, "Wrong size on WDG_SeasonSelector_C_SetNormalColor");
static_assert(offsetof(WDG_SeasonSelector_C_SetNormalColor, CallFunc_IsValid_ReturnValue) == 0x000000, "Member 'WDG_SeasonSelector_C_SetNormalColor::CallFunc_IsValid_ReturnValue' has a wrong offset!");

// Function WDG_SeasonSelector.WDG_SeasonSelector_C.PulseAnimation
// 0x0008 (0x0008 - 0x0000)
struct WDG_SeasonSelector_C_PulseAnimation final
{
public:
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0000(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SeasonSelector_C_PulseAnimation) == 0x000008, "Wrong alignment on WDG_SeasonSelector_C_PulseAnimation");
static_assert(sizeof(WDG_SeasonSelector_C_PulseAnimation) == 0x000008, "Wrong size on WDG_SeasonSelector_C_PulseAnimation");
static_assert(offsetof(WDG_SeasonSelector_C_PulseAnimation, CallFunc_PlayAnimation_ReturnValue) == 0x000000, "Member 'WDG_SeasonSelector_C_PulseAnimation::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");

// Function WDG_SeasonSelector.WDG_SeasonSelector_C.SetSlantImageWidth
// 0x0018 (0x0018 - 0x0000)
struct WDG_SeasonSelector_C_SetSlantImageWidth final
{
public:
	class UImage*                                 SlantImage;                                        // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakVector2D_X;                          // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakVector2D_Y;                          // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue;                 // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SeasonSelector_C_SetSlantImageWidth) == 0x000008, "Wrong alignment on WDG_SeasonSelector_C_SetSlantImageWidth");
static_assert(sizeof(WDG_SeasonSelector_C_SetSlantImageWidth) == 0x000018, "Wrong size on WDG_SeasonSelector_C_SetSlantImageWidth");
static_assert(offsetof(WDG_SeasonSelector_C_SetSlantImageWidth, SlantImage) == 0x000000, "Member 'WDG_SeasonSelector_C_SetSlantImageWidth::SlantImage' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_SetSlantImageWidth, CallFunc_BreakVector2D_X) == 0x000008, "Member 'WDG_SeasonSelector_C_SetSlantImageWidth::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_SetSlantImageWidth, CallFunc_BreakVector2D_Y) == 0x00000C, "Member 'WDG_SeasonSelector_C_SetSlantImageWidth::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_SetSlantImageWidth, CallFunc_MakeVector2D_ReturnValue) == 0x000010, "Member 'WDG_SeasonSelector_C_SetSlantImageWidth::CallFunc_MakeVector2D_ReturnValue' has a wrong offset!");

// Function WDG_SeasonSelector.WDG_SeasonSelector_C.ResetAnimation
// 0x0010 (0x0010 - 0x0000)
struct WDG_SeasonSelector_C_ResetAnimation final
{
public:
	bool                                          CallFunc_IsAnimationPlaying_ReturnValue;           // 0x0000(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationTimeRange_ReturnValue;       // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SeasonSelector_C_ResetAnimation) == 0x000008, "Wrong alignment on WDG_SeasonSelector_C_ResetAnimation");
static_assert(sizeof(WDG_SeasonSelector_C_ResetAnimation) == 0x000010, "Wrong size on WDG_SeasonSelector_C_ResetAnimation");
static_assert(offsetof(WDG_SeasonSelector_C_ResetAnimation, CallFunc_IsAnimationPlaying_ReturnValue) == 0x000000, "Member 'WDG_SeasonSelector_C_ResetAnimation::CallFunc_IsAnimationPlaying_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_ResetAnimation, CallFunc_PlayAnimationTimeRange_ReturnValue) == 0x000008, "Member 'WDG_SeasonSelector_C_ResetAnimation::CallFunc_PlayAnimationTimeRange_ReturnValue' has a wrong offset!");

// Function WDG_SeasonSelector.WDG_SeasonSelector_C.SetSeason
// 0x0030 (0x0030 - 0x0000)
struct WDG_SeasonSelector_C_SetSeason final
{
public:
	ESeasonType                                   Season;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_GetSeasonName_Output;                     // 0x0008(0x0018)()
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_21[0x7];                                       // 0x0021(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationForward_ReturnValue;         // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SeasonSelector_C_SetSeason) == 0x000008, "Wrong alignment on WDG_SeasonSelector_C_SetSeason");
static_assert(sizeof(WDG_SeasonSelector_C_SetSeason) == 0x000030, "Wrong size on WDG_SeasonSelector_C_SetSeason");
static_assert(offsetof(WDG_SeasonSelector_C_SetSeason, Season) == 0x000000, "Member 'WDG_SeasonSelector_C_SetSeason::Season' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_SetSeason, CallFunc_GetSeasonName_Output) == 0x000008, "Member 'WDG_SeasonSelector_C_SetSeason::CallFunc_GetSeasonName_Output' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_SetSeason, K2Node_SwitchEnum_CmpSuccess) == 0x000020, "Member 'WDG_SeasonSelector_C_SetSeason::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_SeasonSelector_C_SetSeason, CallFunc_PlayAnimationForward_ReturnValue) == 0x000028, "Member 'WDG_SeasonSelector_C_SetSeason::CallFunc_PlayAnimationForward_ReturnValue' has a wrong offset!");

}

