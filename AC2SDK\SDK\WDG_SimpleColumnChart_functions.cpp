﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SimpleColumnChart

#include "Basic.hpp"

#include "WDG_SimpleColumnChart_classes.hpp"
#include "WDG_SimpleColumnChart_parameters.hpp"


namespace SDK
{

// Function WDG_SimpleColumnChart.WDG_SimpleColumnChart_C.ExecuteUbergraph_WDG_SimpleColumnChart
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SimpleColumnChart_C::ExecuteUbergraph_WDG_SimpleColumnChart(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SimpleColumnChart_C", "ExecuteUbergraph_WDG_SimpleColumnChart");

	Params::WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SimpleColumnChart.WDG_SimpleColumnChart_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SimpleColumnChart_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SimpleColumnChart_C", "PreConstruct");

	Params::WDG_SimpleColumnChart_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SimpleColumnChart.WDG_SimpleColumnChart_C.AddColumnWithColor
// (Event, Public, HasOutParams, BlueprintEvent)
// Parameters:
// int32                                   ID                                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// float                                   Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FLinearColor&              Color                                                  (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ZeroConstructor, ReferenceParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    isColumnNavigable                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SimpleColumnChart_C::AddColumnWithColor(int32 ID, float Value, const struct FLinearColor& Color, bool isColumnNavigable)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SimpleColumnChart_C", "AddColumnWithColor");

	Params::WDG_SimpleColumnChart_C_AddColumnWithColor Parms{};

	Parms.ID = ID;
	Parms.Value = Value;
	Parms.Color = std::move(Color);
	Parms.isColumnNavigable = isColumnNavigable;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SimpleColumnChart.WDG_SimpleColumnChart_C.ClearColumns
// (Event, Public, BlueprintEvent)

void UWDG_SimpleColumnChart_C::ClearColumns()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SimpleColumnChart_C", "ClearColumns");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SimpleColumnChart.WDG_SimpleColumnChart_C.AddColumn
// (Event, Public, BlueprintEvent)
// Parameters:
// int32                                   ID                                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// float                                   Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    isColumnNavigable                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SimpleColumnChart_C::AddColumn(int32 ID, float Value, bool isColumnNavigable)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SimpleColumnChart_C", "AddColumn");

	Params::WDG_SimpleColumnChart_C_AddColumn Parms{};

	Parms.ID = ID;
	Parms.Value = Value;
	Parms.isColumnNavigable = isColumnNavigable;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SimpleColumnChart.WDG_SimpleColumnChart_C.WDG_AddSpacer
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void UWDG_SimpleColumnChart_C::WDG_AddSpacer()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SimpleColumnChart_C", "WDG_AddSpacer");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SimpleColumnChart.WDG_SimpleColumnChart_C.WDG_AddColumn
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UWDG_SimpleColumnChartColumn_C*   col                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SimpleColumnChart_C::WDG_AddColumn(class UWDG_SimpleColumnChartColumn_C* col)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SimpleColumnChart_C", "WDG_AddColumn");

	Params::WDG_SimpleColumnChart_C_WDG_AddColumn Parms{};

	Parms.col = col;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SimpleColumnChart.WDG_SimpleColumnChart_C.OnColumnSelectedCallback
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UWDG_SimpleColumnChartColumn_C*   selectedColumn                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SimpleColumnChart_C::OnColumnSelectedCallback(class UWDG_SimpleColumnChartColumn_C* selectedColumn)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SimpleColumnChart_C", "OnColumnSelectedCallback");

	Params::WDG_SimpleColumnChart_C_OnColumnSelectedCallback Parms{};

	Parms.selectedColumn = selectedColumn;

	UObject::ProcessEvent(Func, &Parms);
}

}

