﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_Subtitles

#include "Basic.hpp"

#include "WDG_Subtitles_classes.hpp"
#include "WDG_Subtitles_parameters.hpp"


namespace SDK
{

// Function WDG_Subtitles.WDG_Subtitles_C.ExecuteUbergraph_WDG_Subtitles
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_Subtitles_C::ExecuteUbergraph_WDG_Subtitles(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_Subtitles_C", "ExecuteUbergraph_WDG_Subtitles");

	Params::WDG_Subtitles_C_ExecuteUbergraph_WDG_Subtitles Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_Subtitles.WDG_Subtitles_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_Subtitles_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_Subtitles_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_Subtitles.WDG_Subtitles_C.UpdateSubText
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   playTimeMS                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_Subtitles_C::UpdateSubText(int32 playTimeMS)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_Subtitles_C", "UpdateSubText");

	Params::WDG_Subtitles_C_UpdateSubText Parms{};

	Parms.playTimeMS = playTimeMS;

	UObject::ProcessEvent(Func, &Parms);
}

}

