﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SeasonItem

#include "Basic.hpp"

#include "WDG_SeasonItem_classes.hpp"
#include "WDG_SeasonItem_parameters.hpp"


namespace SDK
{

// Function WDG_SeasonItem.WDG_SeasonItem_C.ExecuteUbergraph_WDG_SeasonItem
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SeasonItem_C::ExecuteUbergraph_WDG_SeasonItem(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonItem_C", "ExecuteUbergraph_WDG_SeasonItem");

	Params::WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SeasonItem.WDG_SeasonItem_C.OnAfterConstruct
// (Event, Public, BlueprintEvent)

void UWDG_SeasonItem_C::OnAfterConstruct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonItem_C", "OnAfterConstruct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SeasonItem.WDG_SeasonItem_C.OnSeasonChanged
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// ESeasonType                             new_season                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SeasonItem_C::OnSeasonChanged(ESeasonType new_season)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonItem_C", "OnSeasonChanged");

	Params::WDG_SeasonItem_C_OnSeasonChanged Parms{};

	Parms.new_season = new_season;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SeasonItem.WDG_SeasonItem_C.OnForwardPressed
// (BlueprintCallable, BlueprintEvent)

void UWDG_SeasonItem_C::OnForwardPressed()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonItem_C", "OnForwardPressed");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SeasonItem.WDG_SeasonItem_C.BP_MouseLeave
// (Event, Public, BlueprintEvent)

void UWDG_SeasonItem_C::BP_MouseLeave()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonItem_C", "BP_MouseLeave");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SeasonItem.WDG_SeasonItem_C.BP_MouseOver
// (Event, Public, BlueprintEvent)

void UWDG_SeasonItem_C::BP_MouseOver()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonItem_C", "BP_MouseOver");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SeasonItem.WDG_SeasonItem_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SeasonItem_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonItem_C", "PreConstruct");

	Params::WDG_SeasonItem_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SeasonItem.WDG_SeasonItem_C.SetSelected
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    Selected                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SeasonItem_C::SetSelected(bool Selected)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonItem_C", "SetSelected");

	Params::WDG_SeasonItem_C_SetSelected Parms{};

	Parms.Selected = Selected;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SeasonItem.WDG_SeasonItem_C.PlayHoverAnimation
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// EUMGSequencePlayMode                    PlayMode                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SeasonItem_C::PlayHoverAnimation(EUMGSequencePlayMode PlayMode)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonItem_C", "PlayHoverAnimation");

	Params::WDG_SeasonItem_C_PlayHoverAnimation Parms{};

	Parms.PlayMode = PlayMode;

	UObject::ProcessEvent(Func, &Parms);
}

}

