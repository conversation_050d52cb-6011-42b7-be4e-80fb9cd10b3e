﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PitLimiterWarning

#include "Basic.hpp"

#include "WDG_PitLimiterWarning_classes.hpp"
#include "WDG_PitLimiterWarning_parameters.hpp"


namespace SDK
{

// Function WDG_PitLimiterWarning.WDG_PitLimiterWarning_C.ExecuteUbergraph_WDG_PitLimiterWarning
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_PitLimiterWarning_C::ExecuteUbergraph_WDG_PitLimiterWarning(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PitLimiterWarning_C", "ExecuteUbergraph_WDG_PitLimiterWarning");

	Params::WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PitLimiterWarning.WDG_PitLimiterWarning_C.OnStartWidget
// (Event, Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UAcGameInstance*                  GameInstance                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class AAcRaceGameMode*                  raceGameMode                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class ACarAvatar*                       CarAvatar                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FHUDOptions&               HUDOptions                                             (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)

void UWDG_PitLimiterWarning_C::OnStartWidget(class UAcGameInstance* GameInstance, class AAcRaceGameMode* raceGameMode, class ACarAvatar* CarAvatar, const struct FHUDOptions& HUDOptions)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PitLimiterWarning_C", "OnStartWidget");

	Params::WDG_PitLimiterWarning_C_OnStartWidget Parms{};

	Parms.GameInstance = GameInstance;
	Parms.raceGameMode = raceGameMode;
	Parms.CarAvatar = CarAvatar;
	Parms.HUDOptions = std::move(HUDOptions);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PitLimiterWarning.WDG_PitLimiterWarning_C.OnHudTick
// (Event, Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FRaceHUDState&             State                                                  (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_PitLimiterWarning_C::OnHudTick(const struct FRaceHUDState& State)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PitLimiterWarning_C", "OnHudTick");

	Params::WDG_PitLimiterWarning_C_OnHudTick Parms{};

	Parms.State = std::move(State);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PitLimiterWarning.WDG_PitLimiterWarning_C.Destruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_PitLimiterWarning_C::Destruct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PitLimiterWarning_C", "Destruct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_PitLimiterWarning.WDG_PitLimiterWarning_C.warningTimer
// (BlueprintCallable, BlueprintEvent)

void UWDG_PitLimiterWarning_C::warningTimer()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PitLimiterWarning_C", "warningTimer");

	UObject::ProcessEvent(Func, nullptr);
}

}

