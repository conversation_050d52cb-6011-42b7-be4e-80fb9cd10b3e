﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ScalableButtonSelection

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ScalableButtonSelection.WDG_ScalableButtonSelection_C
// 0x0018 (0x05F8 - 0x05E0)
class UWDG_ScalableButtonSelection_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       OpacityAnim;                                       // 0x05E8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoD<PERSON>ructor, HasGetValueTypeHash)
	class UWDG_ScalableButton_C*                  SetupElement_0;                                    // 0x05F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_ScalableButtonSelection(int32 EntryPoint);
	void BP_SetupElementResize();
	void BP_MouseLeave();
	void BP_MouseOver();
	void Construct();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ScalableButtonSelection_C">();
	}
	static class UWDG_ScalableButtonSelection_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ScalableButtonSelection_C>();
	}
};
static_assert(alignof(UWDG_ScalableButtonSelection_C) == 0x000008, "Wrong alignment on UWDG_ScalableButtonSelection_C");
static_assert(sizeof(UWDG_ScalableButtonSelection_C) == 0x0005F8, "Wrong size on UWDG_ScalableButtonSelection_C");
static_assert(offsetof(UWDG_ScalableButtonSelection_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_ScalableButtonSelection_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_ScalableButtonSelection_C, OpacityAnim) == 0x0005E8, "Member 'UWDG_ScalableButtonSelection_C::OpacityAnim' has a wrong offset!");
static_assert(offsetof(UWDG_ScalableButtonSelection_C, SetupElement_0) == 0x0005F0, "Member 'UWDG_ScalableButtonSelection_C::SetupElement_0' has a wrong offset!");

}

