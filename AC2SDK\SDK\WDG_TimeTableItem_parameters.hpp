﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TimeTableItem

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "SlateCore_structs.hpp"
#include "Engine_structs.hpp"


namespace SDK::Params
{

// Function WDG_TimeTableItem.WDG_TimeTableItem_C.ExecuteUbergraph_WDG_TimeTableItem
// 0x02A8 (0x02A8 - 0x0000)
struct WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0004(0x0001)(ZeroConstructor, <PERSON><PERSON><PERSON>OldData, NoD<PERSON>ru<PERSON>, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_2;            // 0x0006(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_3;            // 0x0007(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_4;            // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_5;            // 0x0009(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_6;            // 0x000A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_7;            // 0x000B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_8;            // 0x000C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D[0x3];                                        // 0x000D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTimeTableEntry                        K2Node_Event_entry;                                // 0x0010(0x0110)()
	bool                                          CallFunc_NotEqual_ByteByte_ReturnValue;            // 0x0120(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0121(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_122[0x2];                                      // 0x0122(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           CallFunc_GetCarGroupColor_Color;                   // 0x0124(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_FloatFloat_ReturnValue;           // 0x0134(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_135[0x3];                                      // 0x0135(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_AsPercent_Float_ReturnValue;              // 0x0138(0x0018)()
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData;              // 0x0150(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData_1;            // 0x0190(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	TArray<struct FFormatArgumentData>            K2Node_MakeArray_Array;                            // 0x01D0(0x0010)(ReferenceParm)
	TArray<struct FFormatArgumentData>            K2Node_MakeArray_Array_1;                          // 0x01E0(0x0010)(ReferenceParm)
	class FText                                   CallFunc_Format_ReturnValue;                       // 0x01F0(0x0018)()
	class FText                                   CallFunc_Format_ReturnValue_1;                     // 0x0208(0x0018)()
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0220(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_NotEqual_IntInt_ReturnValue;              // 0x0221(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_222[0x2];                                      // 0x0222(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Conv_IntToFloat_ReturnValue;              // 0x0224(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Add_FloatFloat_ReturnValue;               // 0x0228(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NotEqual_FloatFloat_ReturnValue;          // 0x022C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x022D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_GreaterEqual_IntInt_ReturnValue;          // 0x022E(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_22F[0x1];                                      // 0x022F(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_IntToText_ReturnValue;               // 0x0230(0x0018)()
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_9;            // 0x0248(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_10;           // 0x0249(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_11;           // 0x024A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_12;           // 0x024B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_24C[0x4];                                      // 0x024C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateColor                            K2Node_MakeStruct_SlateColor;                      // 0x0250(0x0028)(UObjectWrapper)
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_1;                    // 0x0278(0x0028)()
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_13;           // 0x02A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem) == 0x000008, "Wrong alignment on WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem");
static_assert(sizeof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem) == 0x0002A8, "Wrong size on WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, EntryPoint) == 0x000000, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_MakeLiteralByte_ReturnValue) == 0x000004, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000005, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_MakeLiteralByte_ReturnValue_2) == 0x000006, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_MakeLiteralByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_MakeLiteralByte_ReturnValue_3) == 0x000007, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_MakeLiteralByte_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_MakeLiteralByte_ReturnValue_4) == 0x000008, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_MakeLiteralByte_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_MakeLiteralByte_ReturnValue_5) == 0x000009, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_MakeLiteralByte_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_MakeLiteralByte_ReturnValue_6) == 0x00000A, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_MakeLiteralByte_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_MakeLiteralByte_ReturnValue_7) == 0x00000B, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_MakeLiteralByte_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_MakeLiteralByte_ReturnValue_8) == 0x00000C, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_MakeLiteralByte_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, K2Node_Event_entry) == 0x000010, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::K2Node_Event_entry' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_NotEqual_ByteByte_ReturnValue) == 0x000120, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_NotEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000121, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_GetCarGroupColor_Color) == 0x000124, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_GetCarGroupColor_Color' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_Greater_FloatFloat_ReturnValue) == 0x000134, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_Greater_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_AsPercent_Float_ReturnValue) == 0x000138, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_AsPercent_Float_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, K2Node_MakeStruct_FormatArgumentData) == 0x000150, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::K2Node_MakeStruct_FormatArgumentData' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, K2Node_MakeStruct_FormatArgumentData_1) == 0x000190, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::K2Node_MakeStruct_FormatArgumentData_1' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, K2Node_MakeArray_Array) == 0x0001D0, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, K2Node_MakeArray_Array_1) == 0x0001E0, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::K2Node_MakeArray_Array_1' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_Format_ReturnValue) == 0x0001F0, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_Format_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_Format_ReturnValue_1) == 0x000208, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_Format_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_Less_IntInt_ReturnValue) == 0x000220, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_NotEqual_IntInt_ReturnValue) == 0x000221, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_NotEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_Conv_IntToFloat_ReturnValue) == 0x000224, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_Conv_IntToFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_Add_FloatFloat_ReturnValue) == 0x000228, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_Add_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_NotEqual_FloatFloat_ReturnValue) == 0x00022C, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_NotEqual_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_Greater_IntInt_ReturnValue) == 0x00022D, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_GreaterEqual_IntInt_ReturnValue) == 0x00022E, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_GreaterEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_Conv_IntToText_ReturnValue) == 0x000230, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_Conv_IntToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_MakeLiteralByte_ReturnValue_9) == 0x000248, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_MakeLiteralByte_ReturnValue_9' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_MakeLiteralByte_ReturnValue_10) == 0x000249, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_MakeLiteralByte_ReturnValue_10' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_MakeLiteralByte_ReturnValue_11) == 0x00024A, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_MakeLiteralByte_ReturnValue_11' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_MakeLiteralByte_ReturnValue_12) == 0x00024B, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_MakeLiteralByte_ReturnValue_12' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, K2Node_MakeStruct_SlateColor) == 0x000250, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::K2Node_MakeStruct_SlateColor' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, K2Node_MakeStruct_SlateColor_1) == 0x000278, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::K2Node_MakeStruct_SlateColor_1' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem, CallFunc_MakeLiteralByte_ReturnValue_13) == 0x0002A0, "Member 'WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem::CallFunc_MakeLiteralByte_ReturnValue_13' has a wrong offset!");

// Function WDG_TimeTableItem.WDG_TimeTableItem_C.OnTimeTableItemUpdate
// 0x0110 (0x0110 - 0x0000)
struct WDG_TimeTableItem_C_OnTimeTableItemUpdate final
{
public:
	struct FTimeTableEntry                        Entry;                                             // 0x0000(0x0110)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_TimeTableItem_C_OnTimeTableItemUpdate) == 0x000008, "Wrong alignment on WDG_TimeTableItem_C_OnTimeTableItemUpdate");
static_assert(sizeof(WDG_TimeTableItem_C_OnTimeTableItemUpdate) == 0x000110, "Wrong size on WDG_TimeTableItem_C_OnTimeTableItemUpdate");
static_assert(offsetof(WDG_TimeTableItem_C_OnTimeTableItemUpdate, Entry) == 0x000000, "Member 'WDG_TimeTableItem_C_OnTimeTableItemUpdate::Entry' has a wrong offset!");

}

