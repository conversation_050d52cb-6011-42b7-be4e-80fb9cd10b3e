﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_WeatherForecast

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "Slate_structs.hpp"
#include "Engine_structs.hpp"
#include "CoreUObject_structs.hpp"


namespace SDK::Params
{

// Function WDG_WeatherForecast.WDG_WeatherForecast_C.ExecuteUbergraph_WDG_WeatherForecast
// 0x08A0 (0x08A0 - 0x0000)
struct WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0004(0x0001)(ZeroConstructor, IsPlainOld<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	uint8                                         Pad_5[0x3];                                        // 0x0005(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTrackStatus                           CallFunc_getTrackStatusForUI_ReturnValue;          // 0x0008(0x001C)(ConstParm, NoDestructor)
	float                                         CallFunc_getRelativeWindDir_ReturnValue;           // 0x0024(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Add_FloatFloat_ReturnValue;               // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FWeatherStatus                         CallFunc_getWeatherStatusForUI_ReturnValue;        // 0x002C(0x0020)(ConstParm, NoDestructor)
	bool                                          CallFunc_LessEqual_FloatFloat_ReturnValue;         // 0x004C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue;           // 0x004D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_1;         // 0x004E(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_2;         // 0x004F(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_3;         // 0x0050(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_4;         // 0x0051(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_52[0x2];                                       // 0x0052(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Subtract_FloatFloat_ReturnValue;          // 0x0054(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_FloatFloat_ReturnValue_1;       // 0x0058(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_59[0x3];                                       // 0x0059(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Abs_ReturnValue;                          // 0x005C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GreaterEqual_FloatFloat_ReturnValue;      // 0x0060(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_5;         // 0x0061(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_6;         // 0x0062(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_7;         // 0x0063(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_8;         // 0x0064(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_9;         // 0x0065(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_66[0x2];                                       // 0x0066(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Subtract_FloatFloat_ReturnValue_1;        // 0x0068(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Abs_ReturnValue_1;                        // 0x006C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_10;        // 0x0070(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_71[0x3];                                       // 0x0071(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Add_FloatFloat_ReturnValue_1;             // 0x0074(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Subtract_FloatFloat_ReturnValue_2;        // 0x0078(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GreaterEqual_FloatFloat_ReturnValue_1;    // 0x007C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_7D[0x3];                                       // 0x007D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Abs_ReturnValue_2;                        // 0x0080(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x0084(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_GreaterEqual_FloatFloat_ReturnValue_2;    // 0x0085(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_11;        // 0x0086(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanOR_ReturnValue_1;                  // 0x0087(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_12;        // 0x0088(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_13;        // 0x0089(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_14;        // 0x008A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_15;        // 0x008B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_16;        // 0x008C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_17;        // 0x008D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_18;        // 0x008E(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_19;        // 0x008F(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_20;        // 0x0090(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_21;        // 0x0091(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_LessEqual_FloatFloat_ReturnValue_2;       // 0x0092(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_22;        // 0x0093(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_23;        // 0x0094(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_24;        // 0x0095(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_25;        // 0x0096(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_26;        // 0x0097(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	float                                         CallFunc_Abs_ReturnValue_3;                        // 0x0098(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_9C[0x4];                                       // 0x009C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_FloatToText_ReturnValue;             // 0x00A0(0x0018)()
	class FText                                   CallFunc_Conv_FloatToText_ReturnValue_1;           // 0x00B8(0x0018)()
	class FText                                   CallFunc_Conv_FloatToText_ReturnValue_2;           // 0x00D0(0x0018)()
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x00E8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_E9[0x3];                                       // 0x00E9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate;              // 0x00EC(0x0010)(ZeroConstructor, NoDestructor)
	uint8                                         Pad_FC[0x4];                                       // 0x00FC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTimerHandle                           CallFunc_K2_SetTimerDelegate_ReturnValue;          // 0x0100(0x0008)(NoDestructor, HasGetValueTypeHash)
	struct FTrackStatus                           CallFunc_getTrackStatusForUI_ReturnValue_1;        // 0x0108(0x001C)(ConstParm, NoDestructor)
	float                                         CallFunc_getRelativeWindDir_ReturnValue_1;         // 0x0124(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FWeatherStatus                         CallFunc_getWeatherStatusForUI_ReturnValue_1;      // 0x0128(0x0020)(ConstParm, NoDestructor)
	float                                         CallFunc_Add_FloatFloat_ReturnValue_2;             // 0x0148(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_FloatFloat_ReturnValue_3;       // 0x014C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_27;        // 0x014D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_28;        // 0x014E(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_29;        // 0x014F(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_30;        // 0x0150(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_31;        // 0x0151(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_152[0x2];                                      // 0x0152(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Subtract_FloatFloat_ReturnValue_3;        // 0x0154(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_FloatFloat_ReturnValue_4;       // 0x0158(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_159[0x3];                                      // 0x0159(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Abs_ReturnValue_4;                        // 0x015C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GreaterEqual_FloatFloat_ReturnValue_3;    // 0x0160(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_32;        // 0x0161(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_33;        // 0x0162(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_34;        // 0x0163(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_35;        // 0x0164(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_36;        // 0x0165(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_166[0x2];                                      // 0x0166(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Subtract_FloatFloat_ReturnValue_4;        // 0x0168(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Abs_ReturnValue_5;                        // 0x016C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_37;        // 0x0170(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_171[0x3];                                      // 0x0171(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Add_FloatFloat_ReturnValue_3;             // 0x0174(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Subtract_FloatFloat_ReturnValue_5;        // 0x0178(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GreaterEqual_FloatFloat_ReturnValue_4;    // 0x017C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_17D[0x3];                                      // 0x017D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Abs_ReturnValue_6;                        // 0x0180(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue_2;                  // 0x0184(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_GreaterEqual_FloatFloat_ReturnValue_5;    // 0x0185(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_38;        // 0x0186(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanOR_ReturnValue_3;                  // 0x0187(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_39;        // 0x0188(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_40;        // 0x0189(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_41;        // 0x018A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_42;        // 0x018B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_43;        // 0x018C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_44;        // 0x018D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_45;        // 0x018E(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_46;        // 0x018F(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_47;        // 0x0190(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_48;        // 0x0191(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_LessEqual_FloatFloat_ReturnValue_5;       // 0x0192(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_49;        // 0x0193(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_50;        // 0x0194(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_51;        // 0x0195(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_52;        // 0x0196(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_InRange_FloatFloat_ReturnValue_53;        // 0x0197(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	float                                         CallFunc_Abs_ReturnValue_7;                        // 0x0198(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_19C[0x4];                                      // 0x019C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_FloatToText_ReturnValue_3;           // 0x01A0(0x0018)()
	class FText                                   CallFunc_Conv_FloatToText_ReturnValue_4;           // 0x01B8(0x0018)()
	class FText                                   CallFunc_Conv_FloatToText_ReturnValue_5;           // 0x01D0(0x0018)()
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x01E8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x01E9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_2;            // 0x01EA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1EB[0x5];                                      // 0x01EB(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_getCurrentTrackConditionText_ReturnValue; // 0x01F0(0x0018)()
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_3;            // 0x0208(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_209[0x7];                                      // 0x0209(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_getCurrentTrackConditionText_ReturnValue_1; // 0x0210(0x0018)()
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_4;            // 0x0228(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_229[0x3];                                      // 0x0229(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_getCurrentTimeMultiplier_ReturnValue;     // 0x022C(0x0004)(ConstParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_FMod_Remainder;                           // 0x0230(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FMod_ReturnValue;                         // 0x0234(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_FMod_Remainder_1;                         // 0x0238(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FMod_ReturnValue_1;                       // 0x023C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Conv_IntToFloat_ReturnValue;              // 0x0240(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Conv_IntToFloat_ReturnValue_1;            // 0x0244(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_FClamp_ReturnValue;                       // 0x0248(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_FClamp_ReturnValue_1;                     // 0x024C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_FloatToText_ReturnValue_6;           // 0x0250(0x0018)()
	class FText                                   CallFunc_Conv_FloatToText_ReturnValue_7;           // 0x0268(0x0018)()
	class FString                                 CallFunc_Conv_TextToString_ReturnValue;            // 0x0280(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_TextToString_ReturnValue_1;          // 0x0290(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue;                // 0x02A0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_1;              // 0x02B0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x02C0(0x0018)()
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_1;          // 0x02D8(0x0018)()
	bool                                          K2Node_Event_IsDesignTime;                         // 0x02F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2F1[0x7];                                      // 0x02F1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class AGameModeBase*                          CallFunc_GetGameMode_ReturnValue;                  // 0x02F8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AAcRaceGameMode*                        K2Node_DynamicCast_AsAc_Race_Game_Mode;            // 0x0300(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0308(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_5;            // 0x0309(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_30A[0x6];                                      // 0x030A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcGameInstance*                        K2Node_Event_gameInstance;                         // 0x0310(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AAcRaceGameMode*                        K2Node_Event_raceGameMode;                         // 0x0318(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class ACarAvatar*                             K2Node_Event_carAvatar;                            // 0x0320(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FHUDOptions                            K2Node_Event_hudOptions;                           // 0x0328(0x00C0)(ConstParm, NoDestructor)
	struct FRaceHUDState                          K2Node_Event_state;                                // 0x03E8(0x03E0)(ConstParm)
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue;          // 0x07C8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue_1;        // 0x07CC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x07D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x07D1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_1;          // 0x07D2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_isOnline_ReturnValue;                     // 0x07D3(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanOR_ReturnValue_4;                  // 0x07D4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_6;            // 0x07D5(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_7D6[0x2];                                      // 0x07D6(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FAnchors                               K2Node_MakeStruct_Anchors;                         // 0x07D8(0x0010)(NoDestructor)
	class UPanelWidget*                           CallFunc_GetParent_ReturnValue;                    // 0x07E8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_7;            // 0x07F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_7F1[0x7];                                      // 0x07F1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue;             // 0x07F8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0800(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_801[0x3];                                      // 0x0801(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              CallFunc_GetPosition_ReturnValue;                  // 0x0804(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakVector2D_X;                          // 0x080C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakVector2D_Y;                          // 0x0810(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue;                 // 0x0814(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_81C[0x4];                                      // 0x081C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UScaleBox*                              K2Node_DynamicCast_AsScale_Box;                    // 0x0820(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x0828(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_829[0x7];                                      // 0x0829(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class APlayerController*                      CallFunc_GetPlayerController_ReturnValue;          // 0x0830(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FAnchors                               K2Node_MakeStruct_Anchors_1;                       // 0x0838(0x0010)(NoDestructor)
	class APlayerCarController*                   K2Node_DynamicCast_AsPlayer_Car_Controller;        // 0x0848(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_2;                     // 0x0850(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_851[0x7];                                      // 0x0851(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UPanelWidget*                           CallFunc_GetParent_ReturnValue_1;                  // 0x0858(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetCurrentHudOverlayId_ReturnValue;       // 0x0860(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_864[0x4];                                      // 0x0864(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue_1;           // 0x0868(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_2;          // 0x0870(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x0871(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_872[0x2];                                      // 0x0872(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              CallFunc_GetPosition_ReturnValue_1;                // 0x0874(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakVector2D_X_1;                        // 0x087C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakVector2D_Y_1;                        // 0x0880(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue_1;               // 0x0884(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_88C[0x4];                                      // 0x088C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UScaleBox*                              K2Node_DynamicCast_AsScale_Box_1;                  // 0x0890(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_3;                     // 0x0898(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
};
static_assert(alignof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast) == 0x000008, "Wrong alignment on WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast");
static_assert(sizeof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast) == 0x0008A0, "Wrong size on WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, EntryPoint) == 0x000000, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_MakeLiteralByte_ReturnValue) == 0x000004, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_getTrackStatusForUI_ReturnValue) == 0x000008, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_getTrackStatusForUI_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_getRelativeWindDir_ReturnValue) == 0x000024, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_getRelativeWindDir_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Add_FloatFloat_ReturnValue) == 0x000028, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Add_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_getWeatherStatusForUI_ReturnValue) == 0x00002C, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_getWeatherStatusForUI_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_LessEqual_FloatFloat_ReturnValue) == 0x00004C, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_LessEqual_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue) == 0x00004D, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_1) == 0x00004E, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_2) == 0x00004F, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_3) == 0x000050, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_4) == 0x000051, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Subtract_FloatFloat_ReturnValue) == 0x000054, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Subtract_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_LessEqual_FloatFloat_ReturnValue_1) == 0x000058, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_LessEqual_FloatFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Abs_ReturnValue) == 0x00005C, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Abs_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_GreaterEqual_FloatFloat_ReturnValue) == 0x000060, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_GreaterEqual_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_5) == 0x000061, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_6) == 0x000062, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_7) == 0x000063, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_8) == 0x000064, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_9) == 0x000065, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_9' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Subtract_FloatFloat_ReturnValue_1) == 0x000068, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Subtract_FloatFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Abs_ReturnValue_1) == 0x00006C, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Abs_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_10) == 0x000070, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_10' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Add_FloatFloat_ReturnValue_1) == 0x000074, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Add_FloatFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Subtract_FloatFloat_ReturnValue_2) == 0x000078, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Subtract_FloatFloat_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_GreaterEqual_FloatFloat_ReturnValue_1) == 0x00007C, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_GreaterEqual_FloatFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Abs_ReturnValue_2) == 0x000080, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Abs_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_BooleanOR_ReturnValue) == 0x000084, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_GreaterEqual_FloatFloat_ReturnValue_2) == 0x000085, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_GreaterEqual_FloatFloat_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_11) == 0x000086, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_11' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_BooleanOR_ReturnValue_1) == 0x000087, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_BooleanOR_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_12) == 0x000088, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_12' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_13) == 0x000089, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_13' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_14) == 0x00008A, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_14' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_15) == 0x00008B, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_15' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_16) == 0x00008C, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_16' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_17) == 0x00008D, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_17' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_18) == 0x00008E, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_18' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_19) == 0x00008F, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_19' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_20) == 0x000090, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_20' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_21) == 0x000091, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_21' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_LessEqual_FloatFloat_ReturnValue_2) == 0x000092, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_LessEqual_FloatFloat_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_22) == 0x000093, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_22' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_23) == 0x000094, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_23' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_24) == 0x000095, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_24' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_25) == 0x000096, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_25' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_26) == 0x000097, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_26' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Abs_ReturnValue_3) == 0x000098, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Abs_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Conv_FloatToText_ReturnValue) == 0x0000A0, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Conv_FloatToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Conv_FloatToText_ReturnValue_1) == 0x0000B8, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Conv_FloatToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Conv_FloatToText_ReturnValue_2) == 0x0000D0, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Conv_FloatToText_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_BooleanAND_ReturnValue) == 0x0000E8, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, K2Node_CreateDelegate_OutputDelegate) == 0x0000EC, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_K2_SetTimerDelegate_ReturnValue) == 0x000100, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_K2_SetTimerDelegate_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_getTrackStatusForUI_ReturnValue_1) == 0x000108, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_getTrackStatusForUI_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_getRelativeWindDir_ReturnValue_1) == 0x000124, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_getRelativeWindDir_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_getWeatherStatusForUI_ReturnValue_1) == 0x000128, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_getWeatherStatusForUI_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Add_FloatFloat_ReturnValue_2) == 0x000148, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Add_FloatFloat_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_LessEqual_FloatFloat_ReturnValue_3) == 0x00014C, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_LessEqual_FloatFloat_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_27) == 0x00014D, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_27' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_28) == 0x00014E, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_28' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_29) == 0x00014F, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_29' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_30) == 0x000150, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_30' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_31) == 0x000151, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_31' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Subtract_FloatFloat_ReturnValue_3) == 0x000154, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Subtract_FloatFloat_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_LessEqual_FloatFloat_ReturnValue_4) == 0x000158, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_LessEqual_FloatFloat_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Abs_ReturnValue_4) == 0x00015C, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Abs_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_GreaterEqual_FloatFloat_ReturnValue_3) == 0x000160, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_GreaterEqual_FloatFloat_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_32) == 0x000161, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_32' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_33) == 0x000162, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_33' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_34) == 0x000163, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_34' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_35) == 0x000164, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_35' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_36) == 0x000165, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_36' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Subtract_FloatFloat_ReturnValue_4) == 0x000168, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Subtract_FloatFloat_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Abs_ReturnValue_5) == 0x00016C, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Abs_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_37) == 0x000170, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_37' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Add_FloatFloat_ReturnValue_3) == 0x000174, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Add_FloatFloat_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Subtract_FloatFloat_ReturnValue_5) == 0x000178, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Subtract_FloatFloat_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_GreaterEqual_FloatFloat_ReturnValue_4) == 0x00017C, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_GreaterEqual_FloatFloat_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Abs_ReturnValue_6) == 0x000180, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Abs_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_BooleanOR_ReturnValue_2) == 0x000184, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_BooleanOR_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_GreaterEqual_FloatFloat_ReturnValue_5) == 0x000185, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_GreaterEqual_FloatFloat_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_38) == 0x000186, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_38' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_BooleanOR_ReturnValue_3) == 0x000187, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_BooleanOR_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_39) == 0x000188, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_39' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_40) == 0x000189, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_40' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_41) == 0x00018A, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_41' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_42) == 0x00018B, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_42' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_43) == 0x00018C, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_43' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_44) == 0x00018D, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_44' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_45) == 0x00018E, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_45' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_46) == 0x00018F, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_46' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_47) == 0x000190, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_47' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_48) == 0x000191, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_48' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_LessEqual_FloatFloat_ReturnValue_5) == 0x000192, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_LessEqual_FloatFloat_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_49) == 0x000193, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_49' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_50) == 0x000194, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_50' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_51) == 0x000195, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_51' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_52) == 0x000196, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_52' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_InRange_FloatFloat_ReturnValue_53) == 0x000197, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_InRange_FloatFloat_ReturnValue_53' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Abs_ReturnValue_7) == 0x000198, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Abs_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Conv_FloatToText_ReturnValue_3) == 0x0001A0, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Conv_FloatToText_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Conv_FloatToText_ReturnValue_4) == 0x0001B8, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Conv_FloatToText_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Conv_FloatToText_ReturnValue_5) == 0x0001D0, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Conv_FloatToText_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x0001E8, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_BooleanAND_ReturnValue_1) == 0x0001E9, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_MakeLiteralByte_ReturnValue_2) == 0x0001EA, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_MakeLiteralByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_getCurrentTrackConditionText_ReturnValue) == 0x0001F0, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_getCurrentTrackConditionText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_MakeLiteralByte_ReturnValue_3) == 0x000208, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_MakeLiteralByte_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_getCurrentTrackConditionText_ReturnValue_1) == 0x000210, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_getCurrentTrackConditionText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_MakeLiteralByte_ReturnValue_4) == 0x000228, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_MakeLiteralByte_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_getCurrentTimeMultiplier_ReturnValue) == 0x00022C, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_getCurrentTimeMultiplier_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_FMod_Remainder) == 0x000230, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_FMod_Remainder' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_FMod_ReturnValue) == 0x000234, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_FMod_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_FMod_Remainder_1) == 0x000238, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_FMod_Remainder_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_FMod_ReturnValue_1) == 0x00023C, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_FMod_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Conv_IntToFloat_ReturnValue) == 0x000240, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Conv_IntToFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Conv_IntToFloat_ReturnValue_1) == 0x000244, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Conv_IntToFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_FClamp_ReturnValue) == 0x000248, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_FClamp_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_FClamp_ReturnValue_1) == 0x00024C, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_FClamp_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Conv_FloatToText_ReturnValue_6) == 0x000250, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Conv_FloatToText_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Conv_FloatToText_ReturnValue_7) == 0x000268, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Conv_FloatToText_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Conv_TextToString_ReturnValue) == 0x000280, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Conv_TextToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Conv_TextToString_ReturnValue_1) == 0x000290, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Conv_TextToString_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Concat_StrStr_ReturnValue) == 0x0002A0, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Concat_StrStr_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Concat_StrStr_ReturnValue_1) == 0x0002B0, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Concat_StrStr_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Conv_StringToText_ReturnValue) == 0x0002C0, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Conv_StringToText_ReturnValue_1) == 0x0002D8, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Conv_StringToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, K2Node_Event_IsDesignTime) == 0x0002F0, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_GetGameMode_ReturnValue) == 0x0002F8, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_GetGameMode_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, K2Node_DynamicCast_AsAc_Race_Game_Mode) == 0x000300, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::K2Node_DynamicCast_AsAc_Race_Game_Mode' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, K2Node_DynamicCast_bSuccess) == 0x000308, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_MakeLiteralByte_ReturnValue_5) == 0x000309, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_MakeLiteralByte_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, K2Node_Event_gameInstance) == 0x000310, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::K2Node_Event_gameInstance' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, K2Node_Event_raceGameMode) == 0x000318, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::K2Node_Event_raceGameMode' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, K2Node_Event_carAvatar) == 0x000320, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::K2Node_Event_carAvatar' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, K2Node_Event_hudOptions) == 0x000328, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::K2Node_Event_hudOptions' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, K2Node_Event_state) == 0x0003E8, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::K2Node_Event_state' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Multiply_FloatFloat_ReturnValue) == 0x0007C8, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Multiply_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Multiply_FloatFloat_ReturnValue_1) == 0x0007CC, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Multiply_FloatFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x0007D0, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_Greater_IntInt_ReturnValue) == 0x0007D1, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_EqualEqual_IntInt_ReturnValue_1) == 0x0007D2, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_EqualEqual_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_isOnline_ReturnValue) == 0x0007D3, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_isOnline_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_BooleanOR_ReturnValue_4) == 0x0007D4, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_BooleanOR_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_MakeLiteralByte_ReturnValue_6) == 0x0007D5, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_MakeLiteralByte_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, K2Node_MakeStruct_Anchors) == 0x0007D8, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::K2Node_MakeStruct_Anchors' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_GetParent_ReturnValue) == 0x0007E8, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_GetParent_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_MakeLiteralByte_ReturnValue_7) == 0x0007F0, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_MakeLiteralByte_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_SlotAsCanvasSlot_ReturnValue) == 0x0007F8, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_SlotAsCanvasSlot_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_IsValid_ReturnValue) == 0x000800, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_GetPosition_ReturnValue) == 0x000804, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_GetPosition_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_BreakVector2D_X) == 0x00080C, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_BreakVector2D_Y) == 0x000810, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_MakeVector2D_ReturnValue) == 0x000814, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_MakeVector2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, K2Node_DynamicCast_AsScale_Box) == 0x000820, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::K2Node_DynamicCast_AsScale_Box' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, K2Node_DynamicCast_bSuccess_1) == 0x000828, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_GetPlayerController_ReturnValue) == 0x000830, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_GetPlayerController_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, K2Node_MakeStruct_Anchors_1) == 0x000838, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::K2Node_MakeStruct_Anchors_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, K2Node_DynamicCast_AsPlayer_Car_Controller) == 0x000848, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::K2Node_DynamicCast_AsPlayer_Car_Controller' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, K2Node_DynamicCast_bSuccess_2) == 0x000850, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::K2Node_DynamicCast_bSuccess_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_GetParent_ReturnValue_1) == 0x000858, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_GetParent_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_GetCurrentHudOverlayId_ReturnValue) == 0x000860, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_GetCurrentHudOverlayId_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_SlotAsCanvasSlot_ReturnValue_1) == 0x000868, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_SlotAsCanvasSlot_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_EqualEqual_IntInt_ReturnValue_2) == 0x000870, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_EqualEqual_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_IsValid_ReturnValue_1) == 0x000871, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_GetPosition_ReturnValue_1) == 0x000874, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_GetPosition_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_BreakVector2D_X_1) == 0x00087C, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_BreakVector2D_X_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_BreakVector2D_Y_1) == 0x000880, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_BreakVector2D_Y_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, CallFunc_MakeVector2D_ReturnValue_1) == 0x000884, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::CallFunc_MakeVector2D_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, K2Node_DynamicCast_AsScale_Box_1) == 0x000890, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::K2Node_DynamicCast_AsScale_Box_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast, K2Node_DynamicCast_bSuccess_3) == 0x000898, "Member 'WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast::K2Node_DynamicCast_bSuccess_3' has a wrong offset!");

// Function WDG_WeatherForecast.WDG_WeatherForecast_C.OnHudTick
// 0x03E0 (0x03E0 - 0x0000)
struct WDG_WeatherForecast_C_OnHudTick final
{
public:
	struct FRaceHUDState                          State;                                             // 0x0000(0x03E0)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_WeatherForecast_C_OnHudTick) == 0x000008, "Wrong alignment on WDG_WeatherForecast_C_OnHudTick");
static_assert(sizeof(WDG_WeatherForecast_C_OnHudTick) == 0x0003E0, "Wrong size on WDG_WeatherForecast_C_OnHudTick");
static_assert(offsetof(WDG_WeatherForecast_C_OnHudTick, State) == 0x000000, "Member 'WDG_WeatherForecast_C_OnHudTick::State' has a wrong offset!");

// Function WDG_WeatherForecast.WDG_WeatherForecast_C.OnStartWidget
// 0x00D8 (0x00D8 - 0x0000)
struct WDG_WeatherForecast_C_OnStartWidget final
{
public:
	class UAcGameInstance*                        GameInstance;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AAcRaceGameMode*                        raceGameMode_0;                                    // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class ACarAvatar*                             CarAvatar;                                         // 0x0010(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FHUDOptions                            HUDOptions;                                        // 0x0018(0x00C0)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)
};
static_assert(alignof(WDG_WeatherForecast_C_OnStartWidget) == 0x000008, "Wrong alignment on WDG_WeatherForecast_C_OnStartWidget");
static_assert(sizeof(WDG_WeatherForecast_C_OnStartWidget) == 0x0000D8, "Wrong size on WDG_WeatherForecast_C_OnStartWidget");
static_assert(offsetof(WDG_WeatherForecast_C_OnStartWidget, GameInstance) == 0x000000, "Member 'WDG_WeatherForecast_C_OnStartWidget::GameInstance' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_OnStartWidget, raceGameMode_0) == 0x000008, "Member 'WDG_WeatherForecast_C_OnStartWidget::raceGameMode_0' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_OnStartWidget, CarAvatar) == 0x000010, "Member 'WDG_WeatherForecast_C_OnStartWidget::CarAvatar' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_OnStartWidget, HUDOptions) == 0x000018, "Member 'WDG_WeatherForecast_C_OnStartWidget::HUDOptions' has a wrong offset!");

// Function WDG_WeatherForecast.WDG_WeatherForecast_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_WeatherForecast_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_WeatherForecast_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_WeatherForecast_C_PreConstruct");
static_assert(sizeof(WDG_WeatherForecast_C_PreConstruct) == 0x000001, "Wrong size on WDG_WeatherForecast_C_PreConstruct");
static_assert(offsetof(WDG_WeatherForecast_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_WeatherForecast_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WDG_WeatherForecast.WDG_WeatherForecast_C.IsWidgetDefinitionEnabled
// 0x00D0 (0x00D0 - 0x0000)
struct WDG_WeatherForecast_C_IsWidgetDefinitionEnabled final
{
public:
	class UAcGameInstance*                        GameInstance;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FHUDOptions                            HUDOptions;                                        // 0x0008(0x00C0)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)
	bool                                          ReturnValue;                                       // 0x00C8(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x00C9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_WeatherForecast_C_IsWidgetDefinitionEnabled) == 0x000008, "Wrong alignment on WDG_WeatherForecast_C_IsWidgetDefinitionEnabled");
static_assert(sizeof(WDG_WeatherForecast_C_IsWidgetDefinitionEnabled) == 0x0000D0, "Wrong size on WDG_WeatherForecast_C_IsWidgetDefinitionEnabled");
static_assert(offsetof(WDG_WeatherForecast_C_IsWidgetDefinitionEnabled, GameInstance) == 0x000000, "Member 'WDG_WeatherForecast_C_IsWidgetDefinitionEnabled::GameInstance' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_IsWidgetDefinitionEnabled, HUDOptions) == 0x000008, "Member 'WDG_WeatherForecast_C_IsWidgetDefinitionEnabled::HUDOptions' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_IsWidgetDefinitionEnabled, ReturnValue) == 0x0000C8, "Member 'WDG_WeatherForecast_C_IsWidgetDefinitionEnabled::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_IsWidgetDefinitionEnabled, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x0000C9, "Member 'WDG_WeatherForecast_C_IsWidgetDefinitionEnabled::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");

// Function WDG_WeatherForecast.WDG_WeatherForecast_C.UpdateDifferenceIndicators
// 0x0060 (0x0060 - 0x0000)
struct WDG_WeatherForecast_C_UpdateDifferenceIndicators final
{
public:
	class UTextBlock*                             First_Icon;                                        // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UTextBlock*                             Second_Icon;                                       // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         First_Value;                                       // 0x0010(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         Second_Value;                                      // 0x0014(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UTextBlock*                             Diff_Indicator;                                    // 0x0018(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   CallFunc_GetText_ReturnValue;                      // 0x0020(0x0018)()
	bool                                          CallFunc_NearlyEqual_FloatFloat_ReturnValue;       // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Less_FloatFloat_ReturnValue;              // 0x0039(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x003A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Greater_FloatFloat_ReturnValue;           // 0x003B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Greater_FloatFloat_ReturnValue_1;         // 0x003C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_3D[0x3];                                       // 0x003D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_GetText_ReturnValue_1;                    // 0x0040(0x0018)()
	bool                                          CallFunc_EqualEqual_TextText_ReturnValue;          // 0x0058(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0059(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x005A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_WeatherForecast_C_UpdateDifferenceIndicators) == 0x000008, "Wrong alignment on WDG_WeatherForecast_C_UpdateDifferenceIndicators");
static_assert(sizeof(WDG_WeatherForecast_C_UpdateDifferenceIndicators) == 0x000060, "Wrong size on WDG_WeatherForecast_C_UpdateDifferenceIndicators");
static_assert(offsetof(WDG_WeatherForecast_C_UpdateDifferenceIndicators, First_Icon) == 0x000000, "Member 'WDG_WeatherForecast_C_UpdateDifferenceIndicators::First_Icon' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_UpdateDifferenceIndicators, Second_Icon) == 0x000008, "Member 'WDG_WeatherForecast_C_UpdateDifferenceIndicators::Second_Icon' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_UpdateDifferenceIndicators, First_Value) == 0x000010, "Member 'WDG_WeatherForecast_C_UpdateDifferenceIndicators::First_Value' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_UpdateDifferenceIndicators, Second_Value) == 0x000014, "Member 'WDG_WeatherForecast_C_UpdateDifferenceIndicators::Second_Value' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_UpdateDifferenceIndicators, Diff_Indicator) == 0x000018, "Member 'WDG_WeatherForecast_C_UpdateDifferenceIndicators::Diff_Indicator' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_UpdateDifferenceIndicators, CallFunc_GetText_ReturnValue) == 0x000020, "Member 'WDG_WeatherForecast_C_UpdateDifferenceIndicators::CallFunc_GetText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_UpdateDifferenceIndicators, CallFunc_NearlyEqual_FloatFloat_ReturnValue) == 0x000038, "Member 'WDG_WeatherForecast_C_UpdateDifferenceIndicators::CallFunc_NearlyEqual_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_UpdateDifferenceIndicators, CallFunc_Less_FloatFloat_ReturnValue) == 0x000039, "Member 'WDG_WeatherForecast_C_UpdateDifferenceIndicators::CallFunc_Less_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_UpdateDifferenceIndicators, CallFunc_Not_PreBool_ReturnValue) == 0x00003A, "Member 'WDG_WeatherForecast_C_UpdateDifferenceIndicators::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_UpdateDifferenceIndicators, CallFunc_Greater_FloatFloat_ReturnValue) == 0x00003B, "Member 'WDG_WeatherForecast_C_UpdateDifferenceIndicators::CallFunc_Greater_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_UpdateDifferenceIndicators, CallFunc_Greater_FloatFloat_ReturnValue_1) == 0x00003C, "Member 'WDG_WeatherForecast_C_UpdateDifferenceIndicators::CallFunc_Greater_FloatFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_UpdateDifferenceIndicators, CallFunc_GetText_ReturnValue_1) == 0x000040, "Member 'WDG_WeatherForecast_C_UpdateDifferenceIndicators::CallFunc_GetText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_UpdateDifferenceIndicators, CallFunc_EqualEqual_TextText_ReturnValue) == 0x000058, "Member 'WDG_WeatherForecast_C_UpdateDifferenceIndicators::CallFunc_EqualEqual_TextText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_UpdateDifferenceIndicators, CallFunc_BooleanAND_ReturnValue) == 0x000059, "Member 'WDG_WeatherForecast_C_UpdateDifferenceIndicators::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherForecast_C_UpdateDifferenceIndicators, CallFunc_BooleanAND_ReturnValue_1) == 0x00005A, "Member 'WDG_WeatherForecast_C_UpdateDifferenceIndicators::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");

}

