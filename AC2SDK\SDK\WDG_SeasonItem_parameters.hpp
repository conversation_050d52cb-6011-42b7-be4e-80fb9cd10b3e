﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SeasonItem

#include "Basic.hpp"

#include "UMG_structs.hpp"
#include "AC2_structs.hpp"
#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_SeasonItem.WDG_SeasonItem_C.ExecuteUbergraph_WDG_SeasonItem
// 0x00B0 (0x00B0 - 0x0000)
struct WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate;              // 0x0004(0x0010)(ZeroConstructor, NoDestructor)
	TDelegate<void(ESeasonType new_season)>       K2Node_CreateDelegate_OutputDelegate_1;            // 0x0014(0x0010)(ZeroConstructor, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0024(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0025(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_2;            // 0x0026(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0027(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	int32                                         CallFunc_Conv_ByteToInt_ReturnValue;               // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x002C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2D[0x3];                                       // 0x002D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_GetSeasonName_Output;                     // 0x0030(0x0018)()
	class AGameModeBase*                          CallFunc_GetGameMode_ReturnValue;                  // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESeasonType                                   K2Node_CustomEvent_new_season;                     // 0x0050(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_51[0x7];                                       // 0x0051(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class AAcMenuGameMode*                        K2Node_DynamicCast_AsAc_Menu_Game_Mode;            // 0x0058(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0060(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0061(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_62[0x6];                                       // 0x0062(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationReverse_ReturnValue;         // 0x0068(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationForward_ReturnValue;         // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_3;            // 0x0078(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_1;        // 0x0079(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	EContentType                                  CallFunc_SeasonToContentType_output;               // 0x007A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_CanPlay_Result;                           // 0x007B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x007C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_7D[0x3];                                       // 0x007D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue;             // 0x0080(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FMargin                                CallFunc_GetOffsets_ReturnValue;                   // 0x0088(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_2;        // 0x0098(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_99[0x3];                                       // 0x0099(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FMargin                                K2Node_MakeStruct_Margin;                          // 0x009C(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x00AC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem) == 0x000008, "Wrong alignment on WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem");
static_assert(sizeof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem) == 0x0000B0, "Wrong size on WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, EntryPoint) == 0x000000, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, K2Node_CreateDelegate_OutputDelegate) == 0x000004, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, K2Node_CreateDelegate_OutputDelegate_1) == 0x000014, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::K2Node_CreateDelegate_OutputDelegate_1' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, CallFunc_MakeLiteralByte_ReturnValue) == 0x000024, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000025, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, CallFunc_MakeLiteralByte_ReturnValue_2) == 0x000026, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::CallFunc_MakeLiteralByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, K2Node_Event_IsDesignTime) == 0x000027, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, CallFunc_Conv_ByteToInt_ReturnValue) == 0x000028, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::CallFunc_Conv_ByteToInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, K2Node_SwitchEnum_CmpSuccess) == 0x00002C, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, CallFunc_GetSeasonName_Output) == 0x000030, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::CallFunc_GetSeasonName_Output' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, CallFunc_GetGameMode_ReturnValue) == 0x000048, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::CallFunc_GetGameMode_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, K2Node_CustomEvent_new_season) == 0x000050, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::K2Node_CustomEvent_new_season' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, K2Node_DynamicCast_AsAc_Menu_Game_Mode) == 0x000058, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::K2Node_DynamicCast_AsAc_Menu_Game_Mode' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, K2Node_DynamicCast_bSuccess) == 0x000060, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000061, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, CallFunc_PlayAnimationReverse_ReturnValue) == 0x000068, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::CallFunc_PlayAnimationReverse_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, CallFunc_PlayAnimationForward_ReturnValue) == 0x000070, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::CallFunc_PlayAnimationForward_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, CallFunc_MakeLiteralByte_ReturnValue_3) == 0x000078, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::CallFunc_MakeLiteralByte_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, CallFunc_EqualEqual_ByteByte_ReturnValue_1) == 0x000079, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::CallFunc_EqualEqual_ByteByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, CallFunc_SeasonToContentType_output) == 0x00007A, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::CallFunc_SeasonToContentType_output' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, CallFunc_CanPlay_Result) == 0x00007B, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::CallFunc_CanPlay_Result' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, CallFunc_Not_PreBool_ReturnValue) == 0x00007C, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, CallFunc_SlotAsCanvasSlot_ReturnValue) == 0x000080, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::CallFunc_SlotAsCanvasSlot_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, CallFunc_GetOffsets_ReturnValue) == 0x000088, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::CallFunc_GetOffsets_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, CallFunc_EqualEqual_ByteByte_ReturnValue_2) == 0x000098, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::CallFunc_EqualEqual_ByteByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, K2Node_MakeStruct_Margin) == 0x00009C, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::K2Node_MakeStruct_Margin' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem, CallFunc_BooleanAND_ReturnValue) == 0x0000AC, "Member 'WDG_SeasonItem_C_ExecuteUbergraph_WDG_SeasonItem::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");

// Function WDG_SeasonItem.WDG_SeasonItem_C.OnSeasonChanged
// 0x0001 (0x0001 - 0x0000)
struct WDG_SeasonItem_C_OnSeasonChanged final
{
public:
	ESeasonType                                   new_season;                                        // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SeasonItem_C_OnSeasonChanged) == 0x000001, "Wrong alignment on WDG_SeasonItem_C_OnSeasonChanged");
static_assert(sizeof(WDG_SeasonItem_C_OnSeasonChanged) == 0x000001, "Wrong size on WDG_SeasonItem_C_OnSeasonChanged");
static_assert(offsetof(WDG_SeasonItem_C_OnSeasonChanged, new_season) == 0x000000, "Member 'WDG_SeasonItem_C_OnSeasonChanged::new_season' has a wrong offset!");

// Function WDG_SeasonItem.WDG_SeasonItem_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_SeasonItem_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SeasonItem_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_SeasonItem_C_PreConstruct");
static_assert(sizeof(WDG_SeasonItem_C_PreConstruct) == 0x000001, "Wrong size on WDG_SeasonItem_C_PreConstruct");
static_assert(offsetof(WDG_SeasonItem_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_SeasonItem_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WDG_SeasonItem.WDG_SeasonItem_C.SetSelected
// 0x0002 (0x0002 - 0x0000)
struct WDG_SeasonItem_C_SetSelected final
{
public:
	bool                                          Selected;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_NotEqual_BoolBool_ReturnValue;            // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SeasonItem_C_SetSelected) == 0x000001, "Wrong alignment on WDG_SeasonItem_C_SetSelected");
static_assert(sizeof(WDG_SeasonItem_C_SetSelected) == 0x000002, "Wrong size on WDG_SeasonItem_C_SetSelected");
static_assert(offsetof(WDG_SeasonItem_C_SetSelected, Selected) == 0x000000, "Member 'WDG_SeasonItem_C_SetSelected::Selected' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_SetSelected, CallFunc_NotEqual_BoolBool_ReturnValue) == 0x000001, "Member 'WDG_SeasonItem_C_SetSelected::CallFunc_NotEqual_BoolBool_ReturnValue' has a wrong offset!");

// Function WDG_SeasonItem.WDG_SeasonItem_C.PlayHoverAnimation
// 0x0018 (0x0018 - 0x0000)
struct WDG_SeasonItem_C_PlayHoverAnimation final
{
public:
	EUMGSequencePlayMode                          PlayMode;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_1;              // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SeasonItem_C_PlayHoverAnimation) == 0x000008, "Wrong alignment on WDG_SeasonItem_C_PlayHoverAnimation");
static_assert(sizeof(WDG_SeasonItem_C_PlayHoverAnimation) == 0x000018, "Wrong size on WDG_SeasonItem_C_PlayHoverAnimation");
static_assert(offsetof(WDG_SeasonItem_C_PlayHoverAnimation, PlayMode) == 0x000000, "Member 'WDG_SeasonItem_C_PlayHoverAnimation::PlayMode' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_PlayHoverAnimation, CallFunc_PlayAnimation_ReturnValue) == 0x000008, "Member 'WDG_SeasonItem_C_PlayHoverAnimation::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SeasonItem_C_PlayHoverAnimation, CallFunc_PlayAnimation_ReturnValue_1) == 0x000010, "Member 'WDG_SeasonItem_C_PlayHoverAnimation::CallFunc_PlayAnimation_ReturnValue_1' has a wrong offset!");

}

