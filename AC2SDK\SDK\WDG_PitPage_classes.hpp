﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PitPage

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_PitPage.WDG_PitPage_C
// 0x02A8 (0x05A0 - 0x02F8)
class UWDG_PitPage_C final : public UPausePageContainer
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02F8(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       MenuSlideOut;                                      // 0x0300(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       MenuSlideIn;                                       // 0x0308(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       PageFade;                                          // 0x0310(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UCanvasPanel*                           BottomSection;                                     // 0x0318(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UExtendedButton_C*                      btnToggleFullscreen;                               // 0x0320(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_CommunicationPanel_C*              CommsPanel;                                        // 0x0328(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_WeatherForecast_C*                 ForecastSummary;                                   // 0x0330(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UGridPanel*                             GridConditionsandTimer;                            // 0x0338(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             labelFullscreen;                                   // 0x0340(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 logo;                                              // 0x0348(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SessionCountdown_C*                SessionCountdown;                                  // 0x0350(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_UISessionTimer_C*                  sessionTimer;                                      // 0x0358(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             slotBottom;                                        // 0x0360(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           TopSection;                                        // 0x0368(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtBop;                                            // 0x0370(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtCircuitName;                                    // 0x0378(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtTrackSeason;                                    // 0x0380(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           wrapLogo;                                          // 0x0388(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	bool                                          DarkenAndBlurBackground;                           // 0x0390(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_391[0x3];                                      // 0x0391(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         NormalHeight;                                      // 0x0394(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          AllowFullscreen;                                   // 0x0398(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_399[0x7];                                      // 0x0399(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FCircuitInfo                           Circuit_Info;                                      // 0x03A0(0x01F0)(Edit, BlueprintVisible, DisableEditOnInstance)
	TMulticastInlineDelegate<void(bool isFullscreen)> OnFullScreenChanged;                           // 0x0590(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)

public:
	void ExecuteUbergraph_WDG_PitPage(int32 EntryPoint);
	void OnSetShowCircuitName(bool fullscreen);
	void OnStartPage();
	void BndEvt__btnToggleFullscreen_K2Node_ComponentBoundEvent_1_OnButtonPressed__DelegateSignature();
	void OnSetFullscreen(bool fullscreen);
	void Construct();
	void PreConstruct(bool IsDesignTime);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_PitPage_C">();
	}
	static class UWDG_PitPage_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_PitPage_C>();
	}
};
static_assert(alignof(UWDG_PitPage_C) == 0x000008, "Wrong alignment on UWDG_PitPage_C");
static_assert(sizeof(UWDG_PitPage_C) == 0x0005A0, "Wrong size on UWDG_PitPage_C");
static_assert(offsetof(UWDG_PitPage_C, UberGraphFrame) == 0x0002F8, "Member 'UWDG_PitPage_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_PitPage_C, MenuSlideOut) == 0x000300, "Member 'UWDG_PitPage_C::MenuSlideOut' has a wrong offset!");
static_assert(offsetof(UWDG_PitPage_C, MenuSlideIn) == 0x000308, "Member 'UWDG_PitPage_C::MenuSlideIn' has a wrong offset!");
static_assert(offsetof(UWDG_PitPage_C, PageFade) == 0x000310, "Member 'UWDG_PitPage_C::PageFade' has a wrong offset!");
static_assert(offsetof(UWDG_PitPage_C, BottomSection) == 0x000318, "Member 'UWDG_PitPage_C::BottomSection' has a wrong offset!");
static_assert(offsetof(UWDG_PitPage_C, btnToggleFullscreen) == 0x000320, "Member 'UWDG_PitPage_C::btnToggleFullscreen' has a wrong offset!");
static_assert(offsetof(UWDG_PitPage_C, CommsPanel) == 0x000328, "Member 'UWDG_PitPage_C::CommsPanel' has a wrong offset!");
static_assert(offsetof(UWDG_PitPage_C, ForecastSummary) == 0x000330, "Member 'UWDG_PitPage_C::ForecastSummary' has a wrong offset!");
static_assert(offsetof(UWDG_PitPage_C, GridConditionsandTimer) == 0x000338, "Member 'UWDG_PitPage_C::GridConditionsandTimer' has a wrong offset!");
static_assert(offsetof(UWDG_PitPage_C, labelFullscreen) == 0x000340, "Member 'UWDG_PitPage_C::labelFullscreen' has a wrong offset!");
static_assert(offsetof(UWDG_PitPage_C, logo) == 0x000348, "Member 'UWDG_PitPage_C::logo' has a wrong offset!");
static_assert(offsetof(UWDG_PitPage_C, SessionCountdown) == 0x000350, "Member 'UWDG_PitPage_C::SessionCountdown' has a wrong offset!");
static_assert(offsetof(UWDG_PitPage_C, sessionTimer) == 0x000358, "Member 'UWDG_PitPage_C::sessionTimer' has a wrong offset!");
static_assert(offsetof(UWDG_PitPage_C, slotBottom) == 0x000360, "Member 'UWDG_PitPage_C::slotBottom' has a wrong offset!");
static_assert(offsetof(UWDG_PitPage_C, TopSection) == 0x000368, "Member 'UWDG_PitPage_C::TopSection' has a wrong offset!");
static_assert(offsetof(UWDG_PitPage_C, txtBop) == 0x000370, "Member 'UWDG_PitPage_C::txtBop' has a wrong offset!");
static_assert(offsetof(UWDG_PitPage_C, txtCircuitName) == 0x000378, "Member 'UWDG_PitPage_C::txtCircuitName' has a wrong offset!");
static_assert(offsetof(UWDG_PitPage_C, txtTrackSeason) == 0x000380, "Member 'UWDG_PitPage_C::txtTrackSeason' has a wrong offset!");
static_assert(offsetof(UWDG_PitPage_C, wrapLogo) == 0x000388, "Member 'UWDG_PitPage_C::wrapLogo' has a wrong offset!");
static_assert(offsetof(UWDG_PitPage_C, DarkenAndBlurBackground) == 0x000390, "Member 'UWDG_PitPage_C::DarkenAndBlurBackground' has a wrong offset!");
static_assert(offsetof(UWDG_PitPage_C, NormalHeight) == 0x000394, "Member 'UWDG_PitPage_C::NormalHeight' has a wrong offset!");
static_assert(offsetof(UWDG_PitPage_C, AllowFullscreen) == 0x000398, "Member 'UWDG_PitPage_C::AllowFullscreen' has a wrong offset!");
static_assert(offsetof(UWDG_PitPage_C, Circuit_Info) == 0x0003A0, "Member 'UWDG_PitPage_C::Circuit_Info' has a wrong offset!");
static_assert(offsetof(UWDG_PitPage_C, OnFullScreenChanged) == 0x000590, "Member 'UWDG_PitPage_C::OnFullScreenChanged' has a wrong offset!");

}

