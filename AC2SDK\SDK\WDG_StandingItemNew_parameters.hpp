﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_StandingItemNew

#include "Basic.hpp"


namespace SDK::Params
{

// Function WDG_StandingItemNew.WDG_StandingItemNew_C.ExecuteUbergraph_WDG_StandingItemNew
// 0x0004 (0x0004 - 0x0000)
struct WDG_StandingItemNew_C_ExecuteUbergraph_WDG_StandingItemNew final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_StandingItemNew_C_ExecuteUbergraph_WDG_StandingItemNew) == 0x000004, "Wrong alignment on WDG_StandingItemNew_C_ExecuteUbergraph_WDG_StandingItemNew");
static_assert(sizeof(WDG_StandingItemNew_C_ExecuteUbergraph_WDG_StandingItemNew) == 0x000004, "Wrong size on WDG_StandingItemNew_C_ExecuteUbergraph_WDG_StandingItemNew");
static_assert(offsetof(WDG_StandingItemNew_C_ExecuteUbergraph_WDG_StandingItemNew, EntryPoint) == 0x000000, "Member 'WDG_StandingItemNew_C_ExecuteUbergraph_WDG_StandingItemNew::EntryPoint' has a wrong offset!");

}

