﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TvCameraSet

#include "Basic.hpp"

#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_TvCameraSet.WDG_TvCameraSet_C.ExecuteUbergraph_WDG_TvCameraSet
// 0x0550 (0x0550 - 0x0000)
struct WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoD<PERSON>ru<PERSON>, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6[0x2];                                        // 0x0006(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcGameInstance*                        K2Node_Event_gameInstance;                         // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AAcRaceGameMode*                        K2Node_Event_raceGameMode;                         // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class ACarAvatar*                             K2Node_Event_carAvatar;                            // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FHUDOptions                            K2Node_Event_hudOptions;                           // 0x0020(0x00C0)(ConstParm, NoDestructor)
	class APlayerCameraManager*                   CallFunc_GetPlayerCameraManager_ReturnValue;       // 0x00E0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FRaceHUDState                          K2Node_Event_state;                                // 0x00E8(0x03E0)(ConstParm)
	class AACPlayerCameraManager*                 K2Node_DynamicCast_AsACPlayer_Camera_Manager;      // 0x04C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x04D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x04D1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_4D2[0x6];                                      // 0x04D2(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FCameraSet                             CallFunc_GetCurrentTVCameraSet_ReturnValue;        // 0x04D8(0x0038)()
	EMainCameraMode                               CallFunc_GetMainCameraMode_ReturnValue;            // 0x0510(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_511[0x7];                                      // 0x0511(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x0518(0x0018)()
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0530(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_531[0x7];                                      // 0x0531(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_TextToUpper_ReturnValue;                  // 0x0538(0x0018)()
};
static_assert(alignof(WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet) == 0x000008, "Wrong alignment on WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet");
static_assert(sizeof(WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet) == 0x000550, "Wrong size on WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet");
static_assert(offsetof(WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet, EntryPoint) == 0x000000, "Member 'WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet, CallFunc_MakeLiteralByte_ReturnValue) == 0x000004, "Member 'WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000005, "Member 'WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet, K2Node_Event_gameInstance) == 0x000008, "Member 'WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet::K2Node_Event_gameInstance' has a wrong offset!");
static_assert(offsetof(WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet, K2Node_Event_raceGameMode) == 0x000010, "Member 'WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet::K2Node_Event_raceGameMode' has a wrong offset!");
static_assert(offsetof(WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet, K2Node_Event_carAvatar) == 0x000018, "Member 'WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet::K2Node_Event_carAvatar' has a wrong offset!");
static_assert(offsetof(WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet, K2Node_Event_hudOptions) == 0x000020, "Member 'WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet::K2Node_Event_hudOptions' has a wrong offset!");
static_assert(offsetof(WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet, CallFunc_GetPlayerCameraManager_ReturnValue) == 0x0000E0, "Member 'WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet::CallFunc_GetPlayerCameraManager_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet, K2Node_Event_state) == 0x0000E8, "Member 'WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet::K2Node_Event_state' has a wrong offset!");
static_assert(offsetof(WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet, K2Node_DynamicCast_AsACPlayer_Camera_Manager) == 0x0004C8, "Member 'WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet::K2Node_DynamicCast_AsACPlayer_Camera_Manager' has a wrong offset!");
static_assert(offsetof(WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet, K2Node_DynamicCast_bSuccess) == 0x0004D0, "Member 'WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet, CallFunc_IsValid_ReturnValue) == 0x0004D1, "Member 'WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet, CallFunc_GetCurrentTVCameraSet_ReturnValue) == 0x0004D8, "Member 'WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet::CallFunc_GetCurrentTVCameraSet_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet, CallFunc_GetMainCameraMode_ReturnValue) == 0x000510, "Member 'WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet::CallFunc_GetMainCameraMode_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet, CallFunc_Conv_StringToText_ReturnValue) == 0x000518, "Member 'WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000530, "Member 'WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet, CallFunc_TextToUpper_ReturnValue) == 0x000538, "Member 'WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet::CallFunc_TextToUpper_ReturnValue' has a wrong offset!");

// Function WDG_TvCameraSet.WDG_TvCameraSet_C.OnHudTick
// 0x03E0 (0x03E0 - 0x0000)
struct WDG_TvCameraSet_C_OnHudTick final
{
public:
	struct FRaceHUDState                          State;                                             // 0x0000(0x03E0)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_TvCameraSet_C_OnHudTick) == 0x000008, "Wrong alignment on WDG_TvCameraSet_C_OnHudTick");
static_assert(sizeof(WDG_TvCameraSet_C_OnHudTick) == 0x0003E0, "Wrong size on WDG_TvCameraSet_C_OnHudTick");
static_assert(offsetof(WDG_TvCameraSet_C_OnHudTick, State) == 0x000000, "Member 'WDG_TvCameraSet_C_OnHudTick::State' has a wrong offset!");

// Function WDG_TvCameraSet.WDG_TvCameraSet_C.OnStartWidget
// 0x00D8 (0x00D8 - 0x0000)
struct WDG_TvCameraSet_C_OnStartWidget final
{
public:
	class UAcGameInstance*                        GameInstance;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AAcRaceGameMode*                        raceGameMode;                                      // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class ACarAvatar*                             CarAvatar;                                         // 0x0010(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FHUDOptions                            HUDOptions;                                        // 0x0018(0x00C0)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)
};
static_assert(alignof(WDG_TvCameraSet_C_OnStartWidget) == 0x000008, "Wrong alignment on WDG_TvCameraSet_C_OnStartWidget");
static_assert(sizeof(WDG_TvCameraSet_C_OnStartWidget) == 0x0000D8, "Wrong size on WDG_TvCameraSet_C_OnStartWidget");
static_assert(offsetof(WDG_TvCameraSet_C_OnStartWidget, GameInstance) == 0x000000, "Member 'WDG_TvCameraSet_C_OnStartWidget::GameInstance' has a wrong offset!");
static_assert(offsetof(WDG_TvCameraSet_C_OnStartWidget, raceGameMode) == 0x000008, "Member 'WDG_TvCameraSet_C_OnStartWidget::raceGameMode' has a wrong offset!");
static_assert(offsetof(WDG_TvCameraSet_C_OnStartWidget, CarAvatar) == 0x000010, "Member 'WDG_TvCameraSet_C_OnStartWidget::CarAvatar' has a wrong offset!");
static_assert(offsetof(WDG_TvCameraSet_C_OnStartWidget, HUDOptions) == 0x000018, "Member 'WDG_TvCameraSet_C_OnStartWidget::HUDOptions' has a wrong offset!");

// Function WDG_TvCameraSet.WDG_TvCameraSet_C.IsWidgetDefinitionEnabled
// 0x00D0 (0x00D0 - 0x0000)
struct WDG_TvCameraSet_C_IsWidgetDefinitionEnabled final
{
public:
	class UAcGameInstance*                        GameInstance;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FHUDOptions                            HUDOptions;                                        // 0x0008(0x00C0)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)
	bool                                          ReturnValue;                                       // 0x00C8(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x00C9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_TvCameraSet_C_IsWidgetDefinitionEnabled) == 0x000008, "Wrong alignment on WDG_TvCameraSet_C_IsWidgetDefinitionEnabled");
static_assert(sizeof(WDG_TvCameraSet_C_IsWidgetDefinitionEnabled) == 0x0000D0, "Wrong size on WDG_TvCameraSet_C_IsWidgetDefinitionEnabled");
static_assert(offsetof(WDG_TvCameraSet_C_IsWidgetDefinitionEnabled, GameInstance) == 0x000000, "Member 'WDG_TvCameraSet_C_IsWidgetDefinitionEnabled::GameInstance' has a wrong offset!");
static_assert(offsetof(WDG_TvCameraSet_C_IsWidgetDefinitionEnabled, HUDOptions) == 0x000008, "Member 'WDG_TvCameraSet_C_IsWidgetDefinitionEnabled::HUDOptions' has a wrong offset!");
static_assert(offsetof(WDG_TvCameraSet_C_IsWidgetDefinitionEnabled, ReturnValue) == 0x0000C8, "Member 'WDG_TvCameraSet_C_IsWidgetDefinitionEnabled::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TvCameraSet_C_IsWidgetDefinitionEnabled, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x0000C9, "Member 'WDG_TvCameraSet_C_IsWidgetDefinitionEnabled::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");

}

