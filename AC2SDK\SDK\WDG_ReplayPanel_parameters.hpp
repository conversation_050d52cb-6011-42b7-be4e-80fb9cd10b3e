﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ReplayPanel

#include "Basic.hpp"


namespace SDK::Params
{

// Function WDG_ReplayPanel.WDG_ReplayPanel_C.ExecuteUbergraph_WDG_ReplayPanel
// 0x0028 (0x0028 - 0x0000)
struct WDG_ReplayPanel_C_ExecuteUbergraph_WDG_ReplayPanel final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoD<PERSON>ru<PERSON>, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_1;              // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_2;              // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_3;              // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ReplayPanel_C_ExecuteUbergraph_WDG_ReplayPanel) == 0x000008, "Wrong alignment on WDG_ReplayPanel_C_ExecuteUbergraph_WDG_ReplayPanel");
static_assert(sizeof(WDG_ReplayPanel_C_ExecuteUbergraph_WDG_ReplayPanel) == 0x000028, "Wrong size on WDG_ReplayPanel_C_ExecuteUbergraph_WDG_ReplayPanel");
static_assert(offsetof(WDG_ReplayPanel_C_ExecuteUbergraph_WDG_ReplayPanel, EntryPoint) == 0x000000, "Member 'WDG_ReplayPanel_C_ExecuteUbergraph_WDG_ReplayPanel::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_ReplayPanel_C_ExecuteUbergraph_WDG_ReplayPanel, CallFunc_PlayAnimation_ReturnValue) == 0x000008, "Member 'WDG_ReplayPanel_C_ExecuteUbergraph_WDG_ReplayPanel::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayPanel_C_ExecuteUbergraph_WDG_ReplayPanel, CallFunc_PlayAnimation_ReturnValue_1) == 0x000010, "Member 'WDG_ReplayPanel_C_ExecuteUbergraph_WDG_ReplayPanel::CallFunc_PlayAnimation_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayPanel_C_ExecuteUbergraph_WDG_ReplayPanel, CallFunc_PlayAnimation_ReturnValue_2) == 0x000018, "Member 'WDG_ReplayPanel_C_ExecuteUbergraph_WDG_ReplayPanel::CallFunc_PlayAnimation_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ReplayPanel_C_ExecuteUbergraph_WDG_ReplayPanel, CallFunc_PlayAnimation_ReturnValue_3) == 0x000020, "Member 'WDG_ReplayPanel_C_ExecuteUbergraph_WDG_ReplayPanel::CallFunc_PlayAnimation_ReturnValue_3' has a wrong offset!");

}

