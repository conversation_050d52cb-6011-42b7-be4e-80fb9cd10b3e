﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_StartingCareerPanel

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_StartingCareerPanel.WDG_StartingCareerPanel_C
// 0x0048 (0x0628 - 0x05E0)
class UWDG_StartingCareerPanel_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       OpacityAnim;                                       // 0x05E8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       ScaleAnim;                                         // 0x05F0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 BlackOverlay;                                      // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 HoverImageBox;                                     // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 NormalImageBox;                                    // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             Title;                                             // 0x0610(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	struct FLinearColor                           NewVar_0;                                          // 0x0618(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_StartingCareerPanel(int32 EntryPoint);
	void BP_MouseLeave();
	void BP_MouseOver();
	void SetSlotColorText(const struct FLinearColor& InColorAndOpacity_SpecifiedColor);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_StartingCareerPanel_C">();
	}
	static class UWDG_StartingCareerPanel_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_StartingCareerPanel_C>();
	}
};
static_assert(alignof(UWDG_StartingCareerPanel_C) == 0x000008, "Wrong alignment on UWDG_StartingCareerPanel_C");
static_assert(sizeof(UWDG_StartingCareerPanel_C) == 0x000628, "Wrong size on UWDG_StartingCareerPanel_C");
static_assert(offsetof(UWDG_StartingCareerPanel_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_StartingCareerPanel_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_StartingCareerPanel_C, OpacityAnim) == 0x0005E8, "Member 'UWDG_StartingCareerPanel_C::OpacityAnim' has a wrong offset!");
static_assert(offsetof(UWDG_StartingCareerPanel_C, ScaleAnim) == 0x0005F0, "Member 'UWDG_StartingCareerPanel_C::ScaleAnim' has a wrong offset!");
static_assert(offsetof(UWDG_StartingCareerPanel_C, BlackOverlay) == 0x0005F8, "Member 'UWDG_StartingCareerPanel_C::BlackOverlay' has a wrong offset!");
static_assert(offsetof(UWDG_StartingCareerPanel_C, HoverImageBox) == 0x000600, "Member 'UWDG_StartingCareerPanel_C::HoverImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_StartingCareerPanel_C, NormalImageBox) == 0x000608, "Member 'UWDG_StartingCareerPanel_C::NormalImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_StartingCareerPanel_C, Title) == 0x000610, "Member 'UWDG_StartingCareerPanel_C::Title' has a wrong offset!");
static_assert(offsetof(UWDG_StartingCareerPanel_C, NewVar_0) == 0x000618, "Member 'UWDG_StartingCareerPanel_C::NewVar_0' has a wrong offset!");

}

