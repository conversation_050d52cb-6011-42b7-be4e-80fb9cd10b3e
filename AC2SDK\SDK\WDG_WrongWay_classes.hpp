﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_WrongWay

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_WrongWay.WDG_WrongWay_C
// 0x0008 (0x0668 - 0x0660)
class UWDG_WrongWay_C final : public UWrongWayWidget
{
public:
	class UImage*                                 Image_34;                                          // 0x0660(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_WrongWay_C">();
	}
	static class UWDG_WrongWay_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_WrongWay_C>();
	}
};
static_assert(alignof(UWDG_WrongWay_C) == 0x000008, "Wrong alignment on UWDG_WrongWay_C");
static_assert(sizeof(UWDG_WrongWay_C) == 0x000668, "Wrong size on UWDG_WrongWay_C");
static_assert(offsetof(UWDG_WrongWay_C, Image_34) == 0x000660, "Member 'UWDG_WrongWay_C::Image_34' has a wrong offset!");

}

