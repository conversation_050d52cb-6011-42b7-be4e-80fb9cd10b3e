﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TrackMapPitPage

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "SlateCore_structs.hpp"
#include "UMG_structs.hpp"


namespace SDK::Params
{

// Function WDG_TrackMapPitPage.WDG_TrackMapPitPage_C.OnMouseButtonDown
// 0x0220 (0x0220 - 0x0000)
struct WDG_TrackMapPitPage_C_OnMouseButtonDown final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FPointerEvent                          MouseEvent;                                        // 0x0038(0x0070)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	struct FEventReply                            ReturnValue;                                       // 0x00A8(0x00B8)(Parm, OutParm, ReturnParm)
	struct FEventReply                            CallFunc_Unhandled_ReturnValue;                    // 0x0160(0x00B8)()
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0218(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_TrackMapPitPage_C_OnMouseButtonDown) == 0x000008, "Wrong alignment on WDG_TrackMapPitPage_C_OnMouseButtonDown");
static_assert(sizeof(WDG_TrackMapPitPage_C_OnMouseButtonDown) == 0x000220, "Wrong size on WDG_TrackMapPitPage_C_OnMouseButtonDown");
static_assert(offsetof(WDG_TrackMapPitPage_C_OnMouseButtonDown, MyGeometry) == 0x000000, "Member 'WDG_TrackMapPitPage_C_OnMouseButtonDown::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_TrackMapPitPage_C_OnMouseButtonDown, MouseEvent) == 0x000038, "Member 'WDG_TrackMapPitPage_C_OnMouseButtonDown::MouseEvent' has a wrong offset!");
static_assert(offsetof(WDG_TrackMapPitPage_C_OnMouseButtonDown, ReturnValue) == 0x0000A8, "Member 'WDG_TrackMapPitPage_C_OnMouseButtonDown::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TrackMapPitPage_C_OnMouseButtonDown, CallFunc_Unhandled_ReturnValue) == 0x000160, "Member 'WDG_TrackMapPitPage_C_OnMouseButtonDown::CallFunc_Unhandled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TrackMapPitPage_C_OnMouseButtonDown, CallFunc_Not_PreBool_ReturnValue) == 0x000218, "Member 'WDG_TrackMapPitPage_C_OnMouseButtonDown::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");

// Function WDG_TrackMapPitPage.WDG_TrackMapPitPage_C.IsWidgetDefinitionEnabled
// 0x00D0 (0x00D0 - 0x0000)
struct WDG_TrackMapPitPage_C_IsWidgetDefinitionEnabled final
{
public:
	class UAcGameInstance*                        GameInstance;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FHUDOptions                            HUDOptions;                                        // 0x0008(0x00C0)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)
	bool                                          ReturnValue;                                       // 0x00C8(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsWidgetDefinitionEnabled_ReturnValue;    // 0x00C9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x00CA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_TrackMapPitPage_C_IsWidgetDefinitionEnabled) == 0x000008, "Wrong alignment on WDG_TrackMapPitPage_C_IsWidgetDefinitionEnabled");
static_assert(sizeof(WDG_TrackMapPitPage_C_IsWidgetDefinitionEnabled) == 0x0000D0, "Wrong size on WDG_TrackMapPitPage_C_IsWidgetDefinitionEnabled");
static_assert(offsetof(WDG_TrackMapPitPage_C_IsWidgetDefinitionEnabled, GameInstance) == 0x000000, "Member 'WDG_TrackMapPitPage_C_IsWidgetDefinitionEnabled::GameInstance' has a wrong offset!");
static_assert(offsetof(WDG_TrackMapPitPage_C_IsWidgetDefinitionEnabled, HUDOptions) == 0x000008, "Member 'WDG_TrackMapPitPage_C_IsWidgetDefinitionEnabled::HUDOptions' has a wrong offset!");
static_assert(offsetof(WDG_TrackMapPitPage_C_IsWidgetDefinitionEnabled, ReturnValue) == 0x0000C8, "Member 'WDG_TrackMapPitPage_C_IsWidgetDefinitionEnabled::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TrackMapPitPage_C_IsWidgetDefinitionEnabled, CallFunc_IsWidgetDefinitionEnabled_ReturnValue) == 0x0000C9, "Member 'WDG_TrackMapPitPage_C_IsWidgetDefinitionEnabled::CallFunc_IsWidgetDefinitionEnabled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TrackMapPitPage_C_IsWidgetDefinitionEnabled, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x0000CA, "Member 'WDG_TrackMapPitPage_C_IsWidgetDefinitionEnabled::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");

}

