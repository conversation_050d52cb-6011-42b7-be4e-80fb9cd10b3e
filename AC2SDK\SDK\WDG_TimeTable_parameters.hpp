﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TimeTable

#include "Basic.hpp"

#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_TimeTable.WDG_TimeTable_C.ExecuteUbergraph_WDG_TimeTable
// 0x0030 (0x0030 - 0x0000)
struct WDG_TimeTable_C_ExecuteUbergraph_WDG_TimeTable final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsViewingDriverLaps_ReturnValue;          // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_6[0x2];                                        // 0x0006(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue;             // 0x0008(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FMargin                                CallFunc_GetOffsets_ReturnValue;                   // 0x0010(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor)
	struct FMargin                                K2Node_MakeStruct_Margin;                          // 0x0020(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_TimeTable_C_ExecuteUbergraph_WDG_TimeTable) == 0x000008, "Wrong alignment on WDG_TimeTable_C_ExecuteUbergraph_WDG_TimeTable");
static_assert(sizeof(WDG_TimeTable_C_ExecuteUbergraph_WDG_TimeTable) == 0x000030, "Wrong size on WDG_TimeTable_C_ExecuteUbergraph_WDG_TimeTable");
static_assert(offsetof(WDG_TimeTable_C_ExecuteUbergraph_WDG_TimeTable, EntryPoint) == 0x000000, "Member 'WDG_TimeTable_C_ExecuteUbergraph_WDG_TimeTable::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_TimeTable_C_ExecuteUbergraph_WDG_TimeTable, CallFunc_IsViewingDriverLaps_ReturnValue) == 0x000004, "Member 'WDG_TimeTable_C_ExecuteUbergraph_WDG_TimeTable::CallFunc_IsViewingDriverLaps_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TimeTable_C_ExecuteUbergraph_WDG_TimeTable, K2Node_Event_IsDesignTime) == 0x000005, "Member 'WDG_TimeTable_C_ExecuteUbergraph_WDG_TimeTable::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_TimeTable_C_ExecuteUbergraph_WDG_TimeTable, CallFunc_SlotAsCanvasSlot_ReturnValue) == 0x000008, "Member 'WDG_TimeTable_C_ExecuteUbergraph_WDG_TimeTable::CallFunc_SlotAsCanvasSlot_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TimeTable_C_ExecuteUbergraph_WDG_TimeTable, CallFunc_GetOffsets_ReturnValue) == 0x000010, "Member 'WDG_TimeTable_C_ExecuteUbergraph_WDG_TimeTable::CallFunc_GetOffsets_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TimeTable_C_ExecuteUbergraph_WDG_TimeTable, K2Node_MakeStruct_Margin) == 0x000020, "Member 'WDG_TimeTable_C_ExecuteUbergraph_WDG_TimeTable::K2Node_MakeStruct_Margin' has a wrong offset!");

// Function WDG_TimeTable.WDG_TimeTable_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_TimeTable_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_TimeTable_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_TimeTable_C_PreConstruct");
static_assert(sizeof(WDG_TimeTable_C_PreConstruct) == 0x000001, "Wrong size on WDG_TimeTable_C_PreConstruct");
static_assert(offsetof(WDG_TimeTable_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_TimeTable_C_PreConstruct::IsDesignTime' has a wrong offset!");

}

