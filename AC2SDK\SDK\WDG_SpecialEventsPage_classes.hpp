﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SpecialEventsPage

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SpecialEventsPage.WDG_SpecialEventsPage_C
// 0x01D0 (0x0908 - 0x0738)
class UWDG_SpecialEventsPage_C final : public USpecialEventsPage
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0738(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       PageSwitchSpa;                                     // 0x0740(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       ShowroomFade;                                      // 0x0748(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       PageSwitch;                                        // 0x0750(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       PageFade;                                          // 0x0758(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetSwitcher*                        Achievement;                                       // 0x0760(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SpecialEventGenericPanel_C*        assists;                                           // 0x0768(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_FooterButton_C*                    Back;                                              // 0x0770(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 bgImage;                                           // 0x0778(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Footer_C*                          Footer;                                            // 0x0780(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Header_C*                          Header;                                            // 0x0788(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_1;                                           // 0x0790(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_119;                                         // 0x0798(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_120;                                         // 0x07A0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_121;                                         // 0x07A8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_122;                                         // 0x07B0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_150;                                         // 0x07B8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_270;                                         // 0x07C0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_371;                                         // 0x07C8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgHistoryBackground;                              // 0x07D0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgInfoColor;                                      // 0x07D8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgTempOverlay;                                    // 0x07E0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Page_C*                            PageBase;                                          // 0x07E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_DLCWarningPopup_C*                 popupDLC;                                          // 0x07F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SpecialEventGenericPanel_C*        realism;                                           // 0x07F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           SingleEventCanvas;                                 // 0x0800(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_0;                                       // 0x0808(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_2;                                       // 0x0810(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_3;                                       // 0x0818(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_6;                                       // 0x0820(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_25;                                      // 0x0828(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtAssists;                                        // 0x0830(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtAssistsIcon;                                    // 0x0838(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtCircuitLength;                                  // 0x0840(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtCorners;                                        // 0x0848(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtNextPage;                                       // 0x0850(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtPosition;                                       // 0x0858(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtPreviousPage;                                   // 0x0860(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtRank;                                           // 0x0868(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtRankValue;                                      // 0x0870(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtRealism;                                        // 0x0878(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtRealismIcon;                                    // 0x0880(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtScore;                                          // 0x0888(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtScoreValue;                                     // 0x0890(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtTimeOfDay;                                      // 0x0898(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtUserId;                                         // 0x08A0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_KSONConnectionState_C*             WDG_KSONConnectionState;                           // 0x08A8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomLogo_C*                    WDG_ShowroomLogo;                                  // 0x08B0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SpecialEventsLeaderboardLine_C*    WDG_SpecialEventsLeaderboardLine;                  // 0x08B8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SpecialEventsLeaderboardLine_C*    WDG_SpecialEventsLeaderboardLine_37;               // 0x08C0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SpecialEventsLeaderboardLine_C*    WDG_SpecialEventsLeaderboardLine_107;              // 0x08C8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SpecialEventsLeaderboardLine_C*    WDG_SpecialEventsLeaderboardLine_135;              // 0x08D0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SpecialEventsLeaderboardLine_C*    WDG_SpecialEventsLeaderboardLine_172;              // 0x08D8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SpecialEventsLeaderboardLine_C*    WDG_SpecialEventsLeaderboardLine_205;              // 0x08E0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SpecialEventsLeaderboardLine_C*    WDG_SpecialEventsLeaderboardLine_244;              // 0x08E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SpecialEventsLeaderboardLine_C*    WDG_SpecialEventsLeaderboardLine_313;              // 0x08F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SpecialEventsLeaderboardLine_C*    WDG_SpecialEventsLeaderboardLine_349;              // 0x08F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	int32                                         RequiredDLCId;                                     // 0x0900(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_SpecialEventsPage(int32 EntryPoint);
	void BndEvt__popupDLC_K2Node_ComponentBoundEvent_7_OnYes__DelegateSignature();
	void OnPresetSelected(const struct FSpecialEventPreset& Preset);
	void BndEvt__btnNextPage_K2Node_ComponentBoundEvent_5_OnButtonHoverEvent__DelegateSignature();
	void BndEvt__btnNextPage_K2Node_ComponentBoundEvent_4_OnButtonHoverEvent__DelegateSignature();
	void BndEvt__btnPreviousPage_K2Node_ComponentBoundEvent_3_OnButtonHoverEvent__DelegateSignature();
	void BndEvt__btnPreviousPage_K2Node_ComponentBoundEvent_2_OnButtonHoverEvent__DelegateSignature();
	void BndEvt__btnPreviousPage_K2Node_ComponentBoundEvent_1_OnButtonClickedEvent__DelegateSignature();
	void BndEvt__btnNextPage_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature();
	void BndEvt__Button_94_K2Node_ComponentBoundEvent_35_OnButtonClickedEvent__DelegateSignature();
	void Construct();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SpecialEventsPage_C">();
	}
	static class UWDG_SpecialEventsPage_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SpecialEventsPage_C>();
	}
};
static_assert(alignof(UWDG_SpecialEventsPage_C) == 0x000008, "Wrong alignment on UWDG_SpecialEventsPage_C");
static_assert(sizeof(UWDG_SpecialEventsPage_C) == 0x000908, "Wrong size on UWDG_SpecialEventsPage_C");
static_assert(offsetof(UWDG_SpecialEventsPage_C, UberGraphFrame) == 0x000738, "Member 'UWDG_SpecialEventsPage_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, PageSwitchSpa) == 0x000740, "Member 'UWDG_SpecialEventsPage_C::PageSwitchSpa' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, ShowroomFade) == 0x000748, "Member 'UWDG_SpecialEventsPage_C::ShowroomFade' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, PageSwitch) == 0x000750, "Member 'UWDG_SpecialEventsPage_C::PageSwitch' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, PageFade) == 0x000758, "Member 'UWDG_SpecialEventsPage_C::PageFade' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, Achievement) == 0x000760, "Member 'UWDG_SpecialEventsPage_C::Achievement' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, assists) == 0x000768, "Member 'UWDG_SpecialEventsPage_C::assists' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, Back) == 0x000770, "Member 'UWDG_SpecialEventsPage_C::Back' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, bgImage) == 0x000778, "Member 'UWDG_SpecialEventsPage_C::bgImage' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, Footer) == 0x000780, "Member 'UWDG_SpecialEventsPage_C::Footer' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, Header) == 0x000788, "Member 'UWDG_SpecialEventsPage_C::Header' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, Image_1) == 0x000790, "Member 'UWDG_SpecialEventsPage_C::Image_1' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, Image_119) == 0x000798, "Member 'UWDG_SpecialEventsPage_C::Image_119' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, Image_120) == 0x0007A0, "Member 'UWDG_SpecialEventsPage_C::Image_120' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, Image_121) == 0x0007A8, "Member 'UWDG_SpecialEventsPage_C::Image_121' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, Image_122) == 0x0007B0, "Member 'UWDG_SpecialEventsPage_C::Image_122' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, Image_150) == 0x0007B8, "Member 'UWDG_SpecialEventsPage_C::Image_150' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, Image_270) == 0x0007C0, "Member 'UWDG_SpecialEventsPage_C::Image_270' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, Image_371) == 0x0007C8, "Member 'UWDG_SpecialEventsPage_C::Image_371' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, imgHistoryBackground) == 0x0007D0, "Member 'UWDG_SpecialEventsPage_C::imgHistoryBackground' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, imgInfoColor) == 0x0007D8, "Member 'UWDG_SpecialEventsPage_C::imgInfoColor' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, imgTempOverlay) == 0x0007E0, "Member 'UWDG_SpecialEventsPage_C::imgTempOverlay' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, PageBase) == 0x0007E8, "Member 'UWDG_SpecialEventsPage_C::PageBase' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, popupDLC) == 0x0007F0, "Member 'UWDG_SpecialEventsPage_C::popupDLC' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, realism) == 0x0007F8, "Member 'UWDG_SpecialEventsPage_C::realism' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, SingleEventCanvas) == 0x000800, "Member 'UWDG_SpecialEventsPage_C::SingleEventCanvas' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, TextBlock_0) == 0x000808, "Member 'UWDG_SpecialEventsPage_C::TextBlock_0' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, TextBlock_2) == 0x000810, "Member 'UWDG_SpecialEventsPage_C::TextBlock_2' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, TextBlock_3) == 0x000818, "Member 'UWDG_SpecialEventsPage_C::TextBlock_3' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, TextBlock_6) == 0x000820, "Member 'UWDG_SpecialEventsPage_C::TextBlock_6' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, TextBlock_25) == 0x000828, "Member 'UWDG_SpecialEventsPage_C::TextBlock_25' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, txtAssists) == 0x000830, "Member 'UWDG_SpecialEventsPage_C::txtAssists' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, txtAssistsIcon) == 0x000838, "Member 'UWDG_SpecialEventsPage_C::txtAssistsIcon' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, txtCircuitLength) == 0x000840, "Member 'UWDG_SpecialEventsPage_C::txtCircuitLength' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, txtCorners) == 0x000848, "Member 'UWDG_SpecialEventsPage_C::txtCorners' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, txtNextPage) == 0x000850, "Member 'UWDG_SpecialEventsPage_C::txtNextPage' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, txtPosition) == 0x000858, "Member 'UWDG_SpecialEventsPage_C::txtPosition' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, txtPreviousPage) == 0x000860, "Member 'UWDG_SpecialEventsPage_C::txtPreviousPage' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, txtRank) == 0x000868, "Member 'UWDG_SpecialEventsPage_C::txtRank' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, txtRankValue) == 0x000870, "Member 'UWDG_SpecialEventsPage_C::txtRankValue' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, txtRealism) == 0x000878, "Member 'UWDG_SpecialEventsPage_C::txtRealism' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, txtRealismIcon) == 0x000880, "Member 'UWDG_SpecialEventsPage_C::txtRealismIcon' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, txtScore) == 0x000888, "Member 'UWDG_SpecialEventsPage_C::txtScore' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, txtScoreValue) == 0x000890, "Member 'UWDG_SpecialEventsPage_C::txtScoreValue' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, txtTimeOfDay) == 0x000898, "Member 'UWDG_SpecialEventsPage_C::txtTimeOfDay' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, txtUserId) == 0x0008A0, "Member 'UWDG_SpecialEventsPage_C::txtUserId' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, WDG_KSONConnectionState) == 0x0008A8, "Member 'UWDG_SpecialEventsPage_C::WDG_KSONConnectionState' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, WDG_ShowroomLogo) == 0x0008B0, "Member 'UWDG_SpecialEventsPage_C::WDG_ShowroomLogo' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, WDG_SpecialEventsLeaderboardLine) == 0x0008B8, "Member 'UWDG_SpecialEventsPage_C::WDG_SpecialEventsLeaderboardLine' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, WDG_SpecialEventsLeaderboardLine_37) == 0x0008C0, "Member 'UWDG_SpecialEventsPage_C::WDG_SpecialEventsLeaderboardLine_37' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, WDG_SpecialEventsLeaderboardLine_107) == 0x0008C8, "Member 'UWDG_SpecialEventsPage_C::WDG_SpecialEventsLeaderboardLine_107' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, WDG_SpecialEventsLeaderboardLine_135) == 0x0008D0, "Member 'UWDG_SpecialEventsPage_C::WDG_SpecialEventsLeaderboardLine_135' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, WDG_SpecialEventsLeaderboardLine_172) == 0x0008D8, "Member 'UWDG_SpecialEventsPage_C::WDG_SpecialEventsLeaderboardLine_172' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, WDG_SpecialEventsLeaderboardLine_205) == 0x0008E0, "Member 'UWDG_SpecialEventsPage_C::WDG_SpecialEventsLeaderboardLine_205' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, WDG_SpecialEventsLeaderboardLine_244) == 0x0008E8, "Member 'UWDG_SpecialEventsPage_C::WDG_SpecialEventsLeaderboardLine_244' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, WDG_SpecialEventsLeaderboardLine_313) == 0x0008F0, "Member 'UWDG_SpecialEventsPage_C::WDG_SpecialEventsLeaderboardLine_313' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, WDG_SpecialEventsLeaderboardLine_349) == 0x0008F8, "Member 'UWDG_SpecialEventsPage_C::WDG_SpecialEventsLeaderboardLine_349' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPage_C, RequiredDLCId) == 0x000900, "Member 'UWDG_SpecialEventsPage_C::RequiredDLCId' has a wrong offset!");

}

