﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RaceStandings

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RaceStandings.WDG_RaceStandings_C
// 0x0000 (0x06E0 - 0x06E0)
class UWDG_RaceStandings_C final : public URaceRealtimeStandingWidget
{
public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RaceStandings_C">();
	}
	static class UWDG_RaceStandings_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RaceStandings_C>();
	}
};
static_assert(alignof(UWDG_RaceStandings_C) == 0x000008, "Wrong alignment on UWDG_RaceStandings_C");
static_assert(sizeof(UWDG_RaceStandings_C) == 0x0006E0, "Wrong size on UWDG_RaceStandings_C");

}

