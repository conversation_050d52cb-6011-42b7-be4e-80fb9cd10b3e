﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomTileSelector

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "UMG_structs.hpp"
#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_ShowroomTileSelector.WDG_ShowroomTileSelector_C.ExecuteUbergraph_WDG_ShowroomTileSelector
// 0x0560 (0x0560 - 0x0000)
struct WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTeamInfo                              K2Node_CustomEvent_Team;                           // 0x0008(0x0038)()
	TDelegate<void(class UWDG_ShowroomTileBase_C* Sender)> K2Node_CreateDelegate_OutputDelegate;     // 0x0040(0x0010)(ZeroConstructor, NoDestructor)
	TDelegate<void(class UWDG_ShowroomTileBase_C* Sender, class FName Key, int32 KeyInt)> K2Node_CreateDelegate_OutputDelegate_1; // 0x0050(0x0010)(ZeroConstructor, NoDestructor)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0060(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0064(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0068(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_1;                  // 0x006C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_2;                  // 0x0070(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x0074(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_2;                 // 0x0078(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_1;                   // 0x007C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_2;                   // 0x0080(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_3;                  // 0x0084(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_3;                 // 0x0088(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_3;                   // 0x008C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue;              // 0x0090(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileItem_C*                CallFunc_Create_ReturnValue;                       // 0x0098(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<class UWidget*>                        CallFunc_GetAllChildren_ReturnValue;               // 0x00A0(0x0010)(ReferenceParm, ContainsInstancedReference)
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue;                     // 0x00B0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWidget*                                CallFunc_Array_Get_Item;                           // 0x00B8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x00C0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C4[0x4];                                       // 0x00C4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomTileBase_C*                K2Node_DynamicCast_AsWDG_Showroom_Tile_Base;       // 0x00C8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x00D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x00D1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_D2[0x6];                                       // 0x00D2(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomTileBase_C*                K2Node_CustomEvent_Sender_3;                       // 0x00D8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   K2Node_CustomEvent_Key_1;                          // 0x00E0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_CustomEvent_KeyInt_1;                       // 0x00E8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_EC[0x4];                                       // 0x00EC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomTileBase_C*                K2Node_CustomEvent_Sender_2;                       // 0x00F0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<struct FModelInfo>                     K2Node_CustomEvent_Models;                         // 0x00F8(0x0010)(ConstParm, ReferenceParm)
	class FString                                 CallFunc_Conv_NameToString_ReturnValue;            // 0x0108(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	struct FModelInfo                             CallFunc_Array_Get_Item_1;                         // 0x0118(0x01A8)()
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x02C0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_1;                // 0x02C4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2C5[0x3];                                      // 0x02C5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void(class UWDG_ShowroomTileBase_C* Sender)> K2Node_CreateDelegate_OutputDelegate_2;   // 0x02C8(0x0010)(ZeroConstructor, NoDestructor)
	TArray<struct FTeamInfo>                      K2Node_CustomEvent_Teams;                          // 0x02D8(0x0010)(ConstParm, ReferenceParm)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x02E8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2E9[0x7];                                      // 0x02E9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTeamInfo                              CallFunc_Array_Get_Item_2;                         // 0x02F0(0x0038)()
	int32                                         CallFunc_Array_Length_ReturnValue_2;               // 0x0328(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_32C[0x4];                                      // 0x032C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue_1;            // 0x0330(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_2;                // 0x0338(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_339[0x7];                                      // 0x0339(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomTileItem_C*                CallFunc_Create_ReturnValue_1;                     // 0x0340(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileBase_C*                K2Node_CustomEvent_Sender_1;                       // 0x0348(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue_1;                   // 0x0350(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<class UWidget*>                        CallFunc_GetAllChildren_ReturnValue_1;             // 0x0358(0x0010)(ReferenceParm, ContainsInstancedReference)
	class UWidget*                                CallFunc_Array_Get_Item_3;                         // 0x0368(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_3;               // 0x0370(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_374[0x4];                                      // 0x0374(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomTileBase_C*                K2Node_DynamicCast_AsWDG_Showroom_Tile_Base_1;     // 0x0378(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x0380(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_Less_IntInt_ReturnValue_3;                // 0x0381(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_382[0x6];                                      // 0x0382(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomTileBase_C*                K2Node_CustomEvent_Sender;                         // 0x0388(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   K2Node_CustomEvent_Key;                            // 0x0390(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_CustomEvent_KeyInt;                         // 0x0398(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_StrStr_ReturnValue;            // 0x039C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_39D[0x3];                                      // 0x039D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void(class UWDG_ShowroomTileBase_C* Sender, class FName Key, int32 KeyInt)> K2Node_CreateDelegate_OutputDelegate_3; // 0x03A0(0x0010)(ZeroConstructor, NoDestructor)
	struct FModelInfo                             K2Node_CustomEvent_Model;                          // 0x03B0(0x01A8)()
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0558(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector) == 0x000008, "Wrong alignment on WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector");
static_assert(sizeof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector) == 0x000560, "Wrong size on WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, EntryPoint) == 0x000000, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, K2Node_CustomEvent_Team) == 0x000008, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::K2Node_CustomEvent_Team' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, K2Node_CreateDelegate_OutputDelegate) == 0x000040, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, K2Node_CreateDelegate_OutputDelegate_1) == 0x000050, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::K2Node_CreateDelegate_OutputDelegate_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, Temp_int_Array_Index_Variable) == 0x000060, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, Temp_int_Loop_Counter_Variable) == 0x000064, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_Add_IntInt_ReturnValue) == 0x000068, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, Temp_int_Loop_Counter_Variable_1) == 0x00006C, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::Temp_int_Loop_Counter_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, Temp_int_Loop_Counter_Variable_2) == 0x000070, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::Temp_int_Loop_Counter_Variable_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_Add_IntInt_ReturnValue_1) == 0x000074, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_Add_IntInt_ReturnValue_2) == 0x000078, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_Add_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, Temp_int_Array_Index_Variable_1) == 0x00007C, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::Temp_int_Array_Index_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, Temp_int_Array_Index_Variable_2) == 0x000080, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::Temp_int_Array_Index_Variable_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, Temp_int_Loop_Counter_Variable_3) == 0x000084, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::Temp_int_Loop_Counter_Variable_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_Add_IntInt_ReturnValue_3) == 0x000088, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_Add_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, Temp_int_Array_Index_Variable_3) == 0x00008C, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::Temp_int_Array_Index_Variable_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_GetOwningPlayer_ReturnValue) == 0x000090, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_GetOwningPlayer_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_Create_ReturnValue) == 0x000098, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_GetAllChildren_ReturnValue) == 0x0000A0, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_GetAllChildren_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_AddChild_ReturnValue) == 0x0000B0, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_AddChild_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_Array_Get_Item) == 0x0000B8, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_Array_Length_ReturnValue) == 0x0000C0, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, K2Node_DynamicCast_AsWDG_Showroom_Tile_Base) == 0x0000C8, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::K2Node_DynamicCast_AsWDG_Showroom_Tile_Base' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, K2Node_DynamicCast_bSuccess) == 0x0000D0, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_Less_IntInt_ReturnValue) == 0x0000D1, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, K2Node_CustomEvent_Sender_3) == 0x0000D8, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::K2Node_CustomEvent_Sender_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, K2Node_CustomEvent_Key_1) == 0x0000E0, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::K2Node_CustomEvent_Key_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, K2Node_CustomEvent_KeyInt_1) == 0x0000E8, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::K2Node_CustomEvent_KeyInt_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, K2Node_CustomEvent_Sender_2) == 0x0000F0, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::K2Node_CustomEvent_Sender_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, K2Node_CustomEvent_Models) == 0x0000F8, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::K2Node_CustomEvent_Models' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_Conv_NameToString_ReturnValue) == 0x000108, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_Conv_NameToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_Array_Get_Item_1) == 0x000118, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_Array_Length_ReturnValue_1) == 0x0002C0, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_Less_IntInt_ReturnValue_1) == 0x0002C4, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_Less_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, K2Node_CreateDelegate_OutputDelegate_2) == 0x0002C8, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::K2Node_CreateDelegate_OutputDelegate_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, K2Node_CustomEvent_Teams) == 0x0002D8, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::K2Node_CustomEvent_Teams' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_MakeLiteralByte_ReturnValue) == 0x0002E8, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_Array_Get_Item_2) == 0x0002F0, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_Array_Get_Item_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_Array_Length_ReturnValue_2) == 0x000328, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_Array_Length_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_GetOwningPlayer_ReturnValue_1) == 0x000330, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_GetOwningPlayer_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_Less_IntInt_ReturnValue_2) == 0x000338, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_Less_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_Create_ReturnValue_1) == 0x000340, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_Create_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, K2Node_CustomEvent_Sender_1) == 0x000348, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::K2Node_CustomEvent_Sender_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_AddChild_ReturnValue_1) == 0x000350, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_AddChild_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_GetAllChildren_ReturnValue_1) == 0x000358, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_GetAllChildren_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_Array_Get_Item_3) == 0x000368, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_Array_Get_Item_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_Array_Length_ReturnValue_3) == 0x000370, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_Array_Length_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, K2Node_DynamicCast_AsWDG_Showroom_Tile_Base_1) == 0x000378, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::K2Node_DynamicCast_AsWDG_Showroom_Tile_Base_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, K2Node_DynamicCast_bSuccess_1) == 0x000380, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_Less_IntInt_ReturnValue_3) == 0x000381, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_Less_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, K2Node_CustomEvent_Sender) == 0x000388, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::K2Node_CustomEvent_Sender' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, K2Node_CustomEvent_Key) == 0x000390, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::K2Node_CustomEvent_Key' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, K2Node_CustomEvent_KeyInt) == 0x000398, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::K2Node_CustomEvent_KeyInt' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_EqualEqual_StrStr_ReturnValue) == 0x00039C, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_EqualEqual_StrStr_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, K2Node_CreateDelegate_OutputDelegate_3) == 0x0003A0, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::K2Node_CreateDelegate_OutputDelegate_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, K2Node_CustomEvent_Model) == 0x0003B0, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::K2Node_CustomEvent_Model' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000558, "Member 'WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomTileSelector.WDG_ShowroomTileSelector_C.SelectTeam
// 0x0038 (0x0038 - 0x0000)
struct WDG_ShowroomTileSelector_C_SelectTeam final
{
public:
	struct FTeamInfo                              Team;                                              // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_ShowroomTileSelector_C_SelectTeam) == 0x000008, "Wrong alignment on WDG_ShowroomTileSelector_C_SelectTeam");
static_assert(sizeof(WDG_ShowroomTileSelector_C_SelectTeam) == 0x000038, "Wrong size on WDG_ShowroomTileSelector_C_SelectTeam");
static_assert(offsetof(WDG_ShowroomTileSelector_C_SelectTeam, Team) == 0x000000, "Member 'WDG_ShowroomTileSelector_C_SelectTeam::Team' has a wrong offset!");

// Function WDG_ShowroomTileSelector.WDG_ShowroomTileSelector_C.SelectModel
// 0x01A8 (0x01A8 - 0x0000)
struct WDG_ShowroomTileSelector_C_SelectModel final
{
public:
	struct FModelInfo                             Model;                                             // 0x0000(0x01A8)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_ShowroomTileSelector_C_SelectModel) == 0x000008, "Wrong alignment on WDG_ShowroomTileSelector_C_SelectModel");
static_assert(sizeof(WDG_ShowroomTileSelector_C_SelectModel) == 0x0001A8, "Wrong size on WDG_ShowroomTileSelector_C_SelectModel");
static_assert(offsetof(WDG_ShowroomTileSelector_C_SelectModel, Model) == 0x000000, "Member 'WDG_ShowroomTileSelector_C_SelectModel::Model' has a wrong offset!");

// Function WDG_ShowroomTileSelector.WDG_ShowroomTileSelector_C.OnTeamItemSelected
// 0x0018 (0x0018 - 0x0000)
struct WDG_ShowroomTileSelector_C_OnTeamItemSelected final
{
public:
	class UWDG_ShowroomTileBase_C*                Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   Key;                                               // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         KeyInt;                                            // 0x0010(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomTileSelector_C_OnTeamItemSelected) == 0x000008, "Wrong alignment on WDG_ShowroomTileSelector_C_OnTeamItemSelected");
static_assert(sizeof(WDG_ShowroomTileSelector_C_OnTeamItemSelected) == 0x000018, "Wrong size on WDG_ShowroomTileSelector_C_OnTeamItemSelected");
static_assert(offsetof(WDG_ShowroomTileSelector_C_OnTeamItemSelected, Sender) == 0x000000, "Member 'WDG_ShowroomTileSelector_C_OnTeamItemSelected::Sender' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_OnTeamItemSelected, Key) == 0x000008, "Member 'WDG_ShowroomTileSelector_C_OnTeamItemSelected::Key' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_OnTeamItemSelected, KeyInt) == 0x000010, "Member 'WDG_ShowroomTileSelector_C_OnTeamItemSelected::KeyInt' has a wrong offset!");

// Function WDG_ShowroomTileSelector.WDG_ShowroomTileSelector_C.OnTeamTileFocused
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomTileSelector_C_OnTeamTileFocused final
{
public:
	class UWDG_ShowroomTileBase_C*                Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomTileSelector_C_OnTeamTileFocused) == 0x000008, "Wrong alignment on WDG_ShowroomTileSelector_C_OnTeamTileFocused");
static_assert(sizeof(WDG_ShowroomTileSelector_C_OnTeamTileFocused) == 0x000008, "Wrong size on WDG_ShowroomTileSelector_C_OnTeamTileFocused");
static_assert(offsetof(WDG_ShowroomTileSelector_C_OnTeamTileFocused, Sender) == 0x000000, "Member 'WDG_ShowroomTileSelector_C_OnTeamTileFocused::Sender' has a wrong offset!");

// Function WDG_ShowroomTileSelector.WDG_ShowroomTileSelector_C.UpdateTeams
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomTileSelector_C_UpdateTeams final
{
public:
	TArray<struct FTeamInfo>                      Teams;                                             // 0x0000(0x0010)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_ShowroomTileSelector_C_UpdateTeams) == 0x000008, "Wrong alignment on WDG_ShowroomTileSelector_C_UpdateTeams");
static_assert(sizeof(WDG_ShowroomTileSelector_C_UpdateTeams) == 0x000010, "Wrong size on WDG_ShowroomTileSelector_C_UpdateTeams");
static_assert(offsetof(WDG_ShowroomTileSelector_C_UpdateTeams, Teams) == 0x000000, "Member 'WDG_ShowroomTileSelector_C_UpdateTeams::Teams' has a wrong offset!");

// Function WDG_ShowroomTileSelector.WDG_ShowroomTileSelector_C.UpdateModels
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomTileSelector_C_UpdateModels final
{
public:
	TArray<struct FModelInfo>                     Models_0;                                          // 0x0000(0x0010)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_ShowroomTileSelector_C_UpdateModels) == 0x000008, "Wrong alignment on WDG_ShowroomTileSelector_C_UpdateModels");
static_assert(sizeof(WDG_ShowroomTileSelector_C_UpdateModels) == 0x000010, "Wrong size on WDG_ShowroomTileSelector_C_UpdateModels");
static_assert(offsetof(WDG_ShowroomTileSelector_C_UpdateModels, Models_0) == 0x000000, "Member 'WDG_ShowroomTileSelector_C_UpdateModels::Models_0' has a wrong offset!");

// Function WDG_ShowroomTileSelector.WDG_ShowroomTileSelector_C.OnTileItemFocused
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomTileSelector_C_OnTileItemFocused final
{
public:
	class UWDG_ShowroomTileBase_C*                Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomTileSelector_C_OnTileItemFocused) == 0x000008, "Wrong alignment on WDG_ShowroomTileSelector_C_OnTileItemFocused");
static_assert(sizeof(WDG_ShowroomTileSelector_C_OnTileItemFocused) == 0x000008, "Wrong size on WDG_ShowroomTileSelector_C_OnTileItemFocused");
static_assert(offsetof(WDG_ShowroomTileSelector_C_OnTileItemFocused, Sender) == 0x000000, "Member 'WDG_ShowroomTileSelector_C_OnTileItemFocused::Sender' has a wrong offset!");

// Function WDG_ShowroomTileSelector.WDG_ShowroomTileSelector_C.OnItemSelected
// 0x0018 (0x0018 - 0x0000)
struct WDG_ShowroomTileSelector_C_OnItemSelected final
{
public:
	class UWDG_ShowroomTileBase_C*                Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   Key;                                               // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         KeyInt;                                            // 0x0010(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomTileSelector_C_OnItemSelected) == 0x000008, "Wrong alignment on WDG_ShowroomTileSelector_C_OnItemSelected");
static_assert(sizeof(WDG_ShowroomTileSelector_C_OnItemSelected) == 0x000018, "Wrong size on WDG_ShowroomTileSelector_C_OnItemSelected");
static_assert(offsetof(WDG_ShowroomTileSelector_C_OnItemSelected, Sender) == 0x000000, "Member 'WDG_ShowroomTileSelector_C_OnItemSelected::Sender' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_OnItemSelected, Key) == 0x000008, "Member 'WDG_ShowroomTileSelector_C_OnItemSelected::Key' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_OnItemSelected, KeyInt) == 0x000010, "Member 'WDG_ShowroomTileSelector_C_OnItemSelected::KeyInt' has a wrong offset!");

// Function WDG_ShowroomTileSelector.WDG_ShowroomTileSelector_C.OnPreviewKeyDown
// 0x02B8 (0x02B8 - 0x0000)
struct WDG_ShowroomTileSelector_C_OnPreviewKeyDown final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FKeyEvent                              InKeyEvent;                                        // 0x0038(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm)
	struct FEventReply                            ReturnValue;                                       // 0x0070(0x00B8)(Parm, OutParm, ReturnParm)
	struct FEventReply                            CallFunc_Handled_ReturnValue;                      // 0x0128(0x00B8)()
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue;              // 0x01E0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FEventReply                            CallFunc_Unhandled_ReturnValue;                    // 0x01E8(0x00B8)()
	bool                                          CallFunc_HasAnyChildren_ReturnValue;               // 0x02A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2A1[0x7];                                      // 0x02A1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                CallFunc_GetChildAt_ReturnValue;                   // 0x02A8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_HasFocusedDescendants_ReturnValue;        // 0x02B0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomTileSelector_C_OnPreviewKeyDown) == 0x000008, "Wrong alignment on WDG_ShowroomTileSelector_C_OnPreviewKeyDown");
static_assert(sizeof(WDG_ShowroomTileSelector_C_OnPreviewKeyDown) == 0x0002B8, "Wrong size on WDG_ShowroomTileSelector_C_OnPreviewKeyDown");
static_assert(offsetof(WDG_ShowroomTileSelector_C_OnPreviewKeyDown, MyGeometry) == 0x000000, "Member 'WDG_ShowroomTileSelector_C_OnPreviewKeyDown::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_OnPreviewKeyDown, InKeyEvent) == 0x000038, "Member 'WDG_ShowroomTileSelector_C_OnPreviewKeyDown::InKeyEvent' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_OnPreviewKeyDown, ReturnValue) == 0x000070, "Member 'WDG_ShowroomTileSelector_C_OnPreviewKeyDown::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_OnPreviewKeyDown, CallFunc_Handled_ReturnValue) == 0x000128, "Member 'WDG_ShowroomTileSelector_C_OnPreviewKeyDown::CallFunc_Handled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_OnPreviewKeyDown, CallFunc_GetOwningPlayer_ReturnValue) == 0x0001E0, "Member 'WDG_ShowroomTileSelector_C_OnPreviewKeyDown::CallFunc_GetOwningPlayer_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_OnPreviewKeyDown, CallFunc_Unhandled_ReturnValue) == 0x0001E8, "Member 'WDG_ShowroomTileSelector_C_OnPreviewKeyDown::CallFunc_Unhandled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_OnPreviewKeyDown, CallFunc_HasAnyChildren_ReturnValue) == 0x0002A0, "Member 'WDG_ShowroomTileSelector_C_OnPreviewKeyDown::CallFunc_HasAnyChildren_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_OnPreviewKeyDown, CallFunc_GetChildAt_ReturnValue) == 0x0002A8, "Member 'WDG_ShowroomTileSelector_C_OnPreviewKeyDown::CallFunc_GetChildAt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileSelector_C_OnPreviewKeyDown, CallFunc_HasFocusedDescendants_ReturnValue) == 0x0002B0, "Member 'WDG_ShowroomTileSelector_C_OnPreviewKeyDown::CallFunc_HasFocusedDescendants_ReturnValue' has a wrong offset!");

}

