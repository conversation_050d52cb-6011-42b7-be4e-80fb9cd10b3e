﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SectorTimeItem

#include "Basic.hpp"

#include "WDG_SectorTimeItem_classes.hpp"
#include "WDG_SectorTimeItem_parameters.hpp"


namespace SDK
{

// Function WDG_SectorTimeItem.WDG_SectorTimeItem_C.ExecuteUbergraph_WDG_SectorTimeItem
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SectorTimeItem_C::ExecuteUbergraph_WDG_SectorTimeItem(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SectorTimeItem_C", "ExecuteUbergraph_WDG_SectorTimeItem");

	Params::WDG_SectorTimeItem_C_ExecuteUbergraph_WDG_SectorTimeItem Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SectorTimeItem.WDG_SectorTimeItem_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SectorTimeItem_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SectorTimeItem_C", "PreConstruct");

	Params::WDG_SectorTimeItem_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}

}

