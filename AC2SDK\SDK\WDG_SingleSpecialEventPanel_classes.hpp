﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SingleSpecialEventPanel

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SingleSpecialEventPanel.WDG_SingleSpecialEventPanel_C
// 0x0090 (0x0900 - 0x0870)
class UWDG_SingleSpecialEventPanel_C final : public USpecialEventSinglePanel
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0870(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       ExpandAnimation;                                   // 0x0878(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       Scale;                                             // 0x0880(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       Opacity;                                           // 0x0888(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 BottomHighlight;                                   // 0x0890(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 HoverImageBox;                                     // 0x0898(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_1;                                           // 0x08A0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_22;                                          // 0x08A8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_150;                                         // 0x08B0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_371;                                         // 0x08B8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgHistoryBackground;                              // 0x08C0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgLocked;                                         // 0x08C8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 LeftHighlight;                                     // 0x08D0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 NormalImageBox;                                    // 0x08D8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 RightHighlight;                                    // 0x08E0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_0;                                       // 0x08E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_134;                                     // 0x08F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 TopHighlight;                                      // 0x08F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_SingleSpecialEventPanel(int32 EntryPoint);
	void OnPresetSet(const struct FSpecialEventPreset& Preset);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SingleSpecialEventPanel_C">();
	}
	static class UWDG_SingleSpecialEventPanel_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SingleSpecialEventPanel_C>();
	}
};
static_assert(alignof(UWDG_SingleSpecialEventPanel_C) == 0x000008, "Wrong alignment on UWDG_SingleSpecialEventPanel_C");
static_assert(sizeof(UWDG_SingleSpecialEventPanel_C) == 0x000900, "Wrong size on UWDG_SingleSpecialEventPanel_C");
static_assert(offsetof(UWDG_SingleSpecialEventPanel_C, UberGraphFrame) == 0x000870, "Member 'UWDG_SingleSpecialEventPanel_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SingleSpecialEventPanel_C, ExpandAnimation) == 0x000878, "Member 'UWDG_SingleSpecialEventPanel_C::ExpandAnimation' has a wrong offset!");
static_assert(offsetof(UWDG_SingleSpecialEventPanel_C, Scale) == 0x000880, "Member 'UWDG_SingleSpecialEventPanel_C::Scale' has a wrong offset!");
static_assert(offsetof(UWDG_SingleSpecialEventPanel_C, Opacity) == 0x000888, "Member 'UWDG_SingleSpecialEventPanel_C::Opacity' has a wrong offset!");
static_assert(offsetof(UWDG_SingleSpecialEventPanel_C, BottomHighlight) == 0x000890, "Member 'UWDG_SingleSpecialEventPanel_C::BottomHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_SingleSpecialEventPanel_C, HoverImageBox) == 0x000898, "Member 'UWDG_SingleSpecialEventPanel_C::HoverImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_SingleSpecialEventPanel_C, Image_1) == 0x0008A0, "Member 'UWDG_SingleSpecialEventPanel_C::Image_1' has a wrong offset!");
static_assert(offsetof(UWDG_SingleSpecialEventPanel_C, Image_22) == 0x0008A8, "Member 'UWDG_SingleSpecialEventPanel_C::Image_22' has a wrong offset!");
static_assert(offsetof(UWDG_SingleSpecialEventPanel_C, Image_150) == 0x0008B0, "Member 'UWDG_SingleSpecialEventPanel_C::Image_150' has a wrong offset!");
static_assert(offsetof(UWDG_SingleSpecialEventPanel_C, Image_371) == 0x0008B8, "Member 'UWDG_SingleSpecialEventPanel_C::Image_371' has a wrong offset!");
static_assert(offsetof(UWDG_SingleSpecialEventPanel_C, imgHistoryBackground) == 0x0008C0, "Member 'UWDG_SingleSpecialEventPanel_C::imgHistoryBackground' has a wrong offset!");
static_assert(offsetof(UWDG_SingleSpecialEventPanel_C, imgLocked) == 0x0008C8, "Member 'UWDG_SingleSpecialEventPanel_C::imgLocked' has a wrong offset!");
static_assert(offsetof(UWDG_SingleSpecialEventPanel_C, LeftHighlight) == 0x0008D0, "Member 'UWDG_SingleSpecialEventPanel_C::LeftHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_SingleSpecialEventPanel_C, NormalImageBox) == 0x0008D8, "Member 'UWDG_SingleSpecialEventPanel_C::NormalImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_SingleSpecialEventPanel_C, RightHighlight) == 0x0008E0, "Member 'UWDG_SingleSpecialEventPanel_C::RightHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_SingleSpecialEventPanel_C, TextBlock_0) == 0x0008E8, "Member 'UWDG_SingleSpecialEventPanel_C::TextBlock_0' has a wrong offset!");
static_assert(offsetof(UWDG_SingleSpecialEventPanel_C, TextBlock_134) == 0x0008F0, "Member 'UWDG_SingleSpecialEventPanel_C::TextBlock_134' has a wrong offset!");
static_assert(offsetof(UWDG_SingleSpecialEventPanel_C, TopHighlight) == 0x0008F8, "Member 'UWDG_SingleSpecialEventPanel_C::TopHighlight' has a wrong offset!");

}

