﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_MultiplayerPanel

#include "Basic.hpp"

#include "WDG_MultiplayerPanel_classes.hpp"
#include "WDG_MultiplayerPanel_parameters.hpp"


namespace SDK
{

// Function WDG_MultiplayerPanel.WDG_MultiplayerPanel_C.ExecuteUbergraph_WDG_MultiplayerPanel
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_MultiplayerPanel_C::ExecuteUbergraph_WDG_MultiplayerPanel(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPanel_C", "ExecuteUbergraph_WDG_MultiplayerPanel");

	Params::WDG_MultiplayerPanel_C_ExecuteUbergraph_WDG_MultiplayerPanel Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerPanel.WDG_MultiplayerPanel_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_MultiplayerPanel_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPanel_C", "PreConstruct");

	Params::WDG_MultiplayerPanel_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerPanel.WDG_MultiplayerPanel_C.BP_MouseOver
// (Event, Public, BlueprintEvent)

void UWDG_MultiplayerPanel_C::BP_MouseOver()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPanel_C", "BP_MouseOver");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerPanel.WDG_MultiplayerPanel_C.BP_MouseLeave
// (Event, Public, BlueprintEvent)

void UWDG_MultiplayerPanel_C::BP_MouseLeave()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerPanel_C", "BP_MouseLeave");

	UObject::ProcessEvent(Func, nullptr);
}

}

