﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomTileItemHorizontal

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "WDG_ShowroomTileBase_classes.hpp"
#include "SlateCore_structs.hpp"
#include "Engine_structs.hpp"
#include "CoreUObject_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C
// 0x01B8 (0x0C68 - 0x0AB0)
class UWDG_ShowroomTileItemHorizontal_C final : public UWDG_ShowroomTileBase_C
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame_WDG_ShowroomTileItemHorizontal_C;   // 0x0AB0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       AnimatedCollapse;                                  // 0x0AB8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UBorder*                                borderBadge;                                       // 0x0AC0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UVerticalBox*                           BoxName;                                           // 0x0AC8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           canvasCar;                                         // 0x0AD0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           canvasColor;                                       // 0x0AD8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           canvasColorTile;                                   // 0x0AE0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           canvasWrapperText;                                 // 0x0AE8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericMappedLabel_C*              CarCup;                                            // 0x0AF0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgColor;                                          // 0x0AF8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgColorBorder;                                    // 0x0B00(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgLineSelection;                                  // 0x0B08(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                mainBorder;                                        // 0x0B10(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UScaleBox*                              scaleSub;                                          // 0x0B18(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class USizeBox*                               sizeEventContainer;                                // 0x0B20(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class USizeBox*                               sizeLabel;                                         // 0x0B28(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWidgetSwitcher*                        Switcher;                                          // 0x0B30(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtColorCode;                                      // 0x0B38(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtColorName;                                      // 0x0B40(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtSubTitle;                                       // 0x0B48(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                wrapperBorder;                                     // 0x0B50(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWrapBox*                               wrapperEventTypes;                                 // 0x0B58(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	struct FSlateBrush                            DriverPhotoBackground;                             // 0x0B60(0x0088)(Edit, BlueprintVisible)
	TArray<struct FLabelTextAndColors>            EventTypeLabels;                                   // 0x0BE8(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	class FText                                   TextLabel;                                         // 0x0BF8(0x0018)(Edit, BlueprintVisible, ExposeOnSpawn)
	struct FLinearColor                           TileColor;                                         // 0x0C10(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	bool                                          ForAuxLight;                                       // 0x0C20(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, AdvancedDisplay, ExposeOnSpawn)
	uint8                                         Pad_C21[0x7];                                      // 0x0C21(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TMulticastInlineDelegate<void(class UWDG_ShowroomTileItemHorizontal_C* Sender, int32 ColorCode, const struct FLinearColor& Color)> OnColorItemForward; // 0x0C28(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	bool                                          NeverAnimate;                                      // 0x0C38(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_C39[0x3];                                      // 0x0C39(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         FixedWidth;                                        // 0x0C3C(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TMulticastInlineDelegate<void(class FName Key, class UWDG_ShowroomTileItemHorizontal_C* Sender)> OnItemNext; // 0x0C40(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void(class FName Key, class UWDG_ShowroomTileItemHorizontal_C* Sender)> OnItemPrevious; // 0x0C50(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	class UWrapBox*                               NewVar_0;                                          // 0x0C60(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_ShowroomTileItemHorizontal(int32 EntryPoint);
	void UpdateTeam(const struct FTeamInfo& TeamInfo_0);
	void UpdateModel(const struct FModelInfo& ModelInfo_0);
	void UpdateCar(const struct FCarInfo& CarInfo_0, class FName CarKey);
	void UpdateDriver(const struct FDriverInfo& DriverInfo_0, class FName DriverKey, bool isCustom);
	void UpdateColor(const struct FSkinColor& Color);
	void OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent);
	void OnNext();
	void OnPrevious();
	void OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent);
	void OnItemForwardEvent();
	void SetSelected(bool IsSelected_0);
	void Animate();
	void PreConstruct(bool IsDesignTime);
	void Construct();
	void ShouldAnimate(class FName New_Key);
	void SetTileColor(const struct FLinearColor& InColorAndOpacity);
	void SetTileColorByCode(int32 ColorCode, struct FLinearColor* Color);
	void SetLabelFixedWidth();
	void SetColorTextLabel(const class FText& InText);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ShowroomTileItemHorizontal_C">();
	}
	static class UWDG_ShowroomTileItemHorizontal_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ShowroomTileItemHorizontal_C>();
	}
};
static_assert(alignof(UWDG_ShowroomTileItemHorizontal_C) == 0x000008, "Wrong alignment on UWDG_ShowroomTileItemHorizontal_C");
static_assert(sizeof(UWDG_ShowroomTileItemHorizontal_C) == 0x000C68, "Wrong size on UWDG_ShowroomTileItemHorizontal_C");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, UberGraphFrame_WDG_ShowroomTileItemHorizontal_C) == 0x000AB0, "Member 'UWDG_ShowroomTileItemHorizontal_C::UberGraphFrame_WDG_ShowroomTileItemHorizontal_C' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, AnimatedCollapse) == 0x000AB8, "Member 'UWDG_ShowroomTileItemHorizontal_C::AnimatedCollapse' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, borderBadge) == 0x000AC0, "Member 'UWDG_ShowroomTileItemHorizontal_C::borderBadge' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, BoxName) == 0x000AC8, "Member 'UWDG_ShowroomTileItemHorizontal_C::BoxName' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, canvasCar) == 0x000AD0, "Member 'UWDG_ShowroomTileItemHorizontal_C::canvasCar' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, canvasColor) == 0x000AD8, "Member 'UWDG_ShowroomTileItemHorizontal_C::canvasColor' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, canvasColorTile) == 0x000AE0, "Member 'UWDG_ShowroomTileItemHorizontal_C::canvasColorTile' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, canvasWrapperText) == 0x000AE8, "Member 'UWDG_ShowroomTileItemHorizontal_C::canvasWrapperText' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, CarCup) == 0x000AF0, "Member 'UWDG_ShowroomTileItemHorizontal_C::CarCup' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, imgColor) == 0x000AF8, "Member 'UWDG_ShowroomTileItemHorizontal_C::imgColor' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, imgColorBorder) == 0x000B00, "Member 'UWDG_ShowroomTileItemHorizontal_C::imgColorBorder' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, imgLineSelection) == 0x000B08, "Member 'UWDG_ShowroomTileItemHorizontal_C::imgLineSelection' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, mainBorder) == 0x000B10, "Member 'UWDG_ShowroomTileItemHorizontal_C::mainBorder' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, scaleSub) == 0x000B18, "Member 'UWDG_ShowroomTileItemHorizontal_C::scaleSub' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, sizeEventContainer) == 0x000B20, "Member 'UWDG_ShowroomTileItemHorizontal_C::sizeEventContainer' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, sizeLabel) == 0x000B28, "Member 'UWDG_ShowroomTileItemHorizontal_C::sizeLabel' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, Switcher) == 0x000B30, "Member 'UWDG_ShowroomTileItemHorizontal_C::Switcher' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, txtColorCode) == 0x000B38, "Member 'UWDG_ShowroomTileItemHorizontal_C::txtColorCode' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, txtColorName) == 0x000B40, "Member 'UWDG_ShowroomTileItemHorizontal_C::txtColorName' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, txtSubTitle) == 0x000B48, "Member 'UWDG_ShowroomTileItemHorizontal_C::txtSubTitle' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, wrapperBorder) == 0x000B50, "Member 'UWDG_ShowroomTileItemHorizontal_C::wrapperBorder' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, wrapperEventTypes) == 0x000B58, "Member 'UWDG_ShowroomTileItemHorizontal_C::wrapperEventTypes' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, DriverPhotoBackground) == 0x000B60, "Member 'UWDG_ShowroomTileItemHorizontal_C::DriverPhotoBackground' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, EventTypeLabels) == 0x000BE8, "Member 'UWDG_ShowroomTileItemHorizontal_C::EventTypeLabels' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, TextLabel) == 0x000BF8, "Member 'UWDG_ShowroomTileItemHorizontal_C::TextLabel' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, TileColor) == 0x000C10, "Member 'UWDG_ShowroomTileItemHorizontal_C::TileColor' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, ForAuxLight) == 0x000C20, "Member 'UWDG_ShowroomTileItemHorizontal_C::ForAuxLight' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, OnColorItemForward) == 0x000C28, "Member 'UWDG_ShowroomTileItemHorizontal_C::OnColorItemForward' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, NeverAnimate) == 0x000C38, "Member 'UWDG_ShowroomTileItemHorizontal_C::NeverAnimate' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, FixedWidth) == 0x000C3C, "Member 'UWDG_ShowroomTileItemHorizontal_C::FixedWidth' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, OnItemNext) == 0x000C40, "Member 'UWDG_ShowroomTileItemHorizontal_C::OnItemNext' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, OnItemPrevious) == 0x000C50, "Member 'UWDG_ShowroomTileItemHorizontal_C::OnItemPrevious' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileItemHorizontal_C, NewVar_0) == 0x000C60, "Member 'UWDG_ShowroomTileItemHorizontal_C::NewVar_0' has a wrong offset!");

}

