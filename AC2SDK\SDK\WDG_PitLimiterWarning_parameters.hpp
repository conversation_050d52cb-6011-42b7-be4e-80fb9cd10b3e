﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PitLimiterWarning

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "Engine_structs.hpp"


namespace SDK::Params
{

// Function WDG_PitLimiterWarning.WDG_PitLimiterWarning_C.ExecuteUbergraph_WDG_PitLimiterWarning
// 0x0570 (0x0570 - 0x0000)
struct WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate;              // 0x0004(0x0010)(ZeroConstructor, NoDestructor)
	uint8                                         Pad_14[0x4];                                       // 0x0014(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   Temp_text_Variable;                                // 0x0018(0x0018)()
	bool                                          Temp_bool_Variable;                                // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_31[0x7];                                       // 0x0031(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue;              // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance;             // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0048(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0049(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsSpeedUnitMph_ReturnValue;               // 0x004A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_4B[0x5];                                       // 0x004B(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	struct FRaceHUDState                          K2Node_Event_state;                                // 0x0050(0x03E0)(ConstParm)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0430(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0431(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x0432(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_433[0x1];                                      // 0x0433(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_GetSpeedKMH_ReturnValue;                  // 0x0434(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_GetGearAsIndex_ReturnValue;               // 0x0438(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_FloatFloat_ReturnValue;           // 0x0439(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Greater_ByteByte_ReturnValue;             // 0x043A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_43B[0x1];                                      // 0x043B(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_GetSpeedKMH_ReturnValue_1;                // 0x043C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_isPitLimiterOn_ReturnValue;               // 0x0440(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Greater_FloatFloat_ReturnValue_1;         // 0x0441(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0442(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	ECarLocation                                  CallFunc_getCarLocation_ReturnValue;               // 0x0443(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x0444(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          K2Node_SwitchEnum_CmpSuccess_1;                    // 0x0445(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_446[0x2];                                      // 0x0446(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   Temp_text_Variable_1;                              // 0x0448(0x0018)()
	class FText                                   K2Node_Select_Default;                             // 0x0460(0x0018)()
	class UAcGameInstance*                        K2Node_Event_gameInstance;                         // 0x0478(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AAcRaceGameMode*                        K2Node_Event_raceGameMode;                         // 0x0480(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class ACarAvatar*                             K2Node_Event_carAvatar;                            // 0x0488(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FHUDOptions                            K2Node_Event_hudOptions;                           // 0x0490(0x00C0)(ConstParm, NoDestructor)
	struct FTimerHandle                           CallFunc_K2_SetTimerDelegate_ReturnValue;          // 0x0550(0x0008)(NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_GetCurrentLevelName_ReturnValue;          // 0x0558(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_K2_IsTimerActiveHandle_ReturnValue;       // 0x0568(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Array_Contains_ReturnValue;               // 0x0569(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x056A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning) == 0x000008, "Wrong alignment on WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning");
static_assert(sizeof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning) == 0x000570, "Wrong size on WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, EntryPoint) == 0x000000, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, K2Node_CreateDelegate_OutputDelegate) == 0x000004, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, Temp_text_Variable) == 0x000018, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::Temp_text_Variable' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, Temp_bool_Variable) == 0x000030, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, CallFunc_GetGameInstance_ReturnValue) == 0x000038, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::CallFunc_GetGameInstance_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, K2Node_DynamicCast_AsAc_Game_Instance) == 0x000040, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::K2Node_DynamicCast_AsAc_Game_Instance' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, K2Node_DynamicCast_bSuccess) == 0x000048, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, CallFunc_MakeLiteralByte_ReturnValue) == 0x000049, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, CallFunc_IsSpeedUnitMph_ReturnValue) == 0x00004A, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::CallFunc_IsSpeedUnitMph_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, K2Node_Event_state) == 0x000050, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::K2Node_Event_state' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, K2Node_SwitchEnum_CmpSuccess) == 0x000430, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, CallFunc_IsValid_ReturnValue) == 0x000431, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, CallFunc_IsValid_ReturnValue_1) == 0x000432, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, CallFunc_GetSpeedKMH_ReturnValue) == 0x000434, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::CallFunc_GetSpeedKMH_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, CallFunc_GetGearAsIndex_ReturnValue) == 0x000438, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::CallFunc_GetGearAsIndex_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, CallFunc_Greater_FloatFloat_ReturnValue) == 0x000439, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::CallFunc_Greater_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, CallFunc_Greater_ByteByte_ReturnValue) == 0x00043A, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::CallFunc_Greater_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, CallFunc_GetSpeedKMH_ReturnValue_1) == 0x00043C, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::CallFunc_GetSpeedKMH_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, CallFunc_isPitLimiterOn_ReturnValue) == 0x000440, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::CallFunc_isPitLimiterOn_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, CallFunc_Greater_FloatFloat_ReturnValue_1) == 0x000441, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::CallFunc_Greater_FloatFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, CallFunc_Not_PreBool_ReturnValue) == 0x000442, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, CallFunc_getCarLocation_ReturnValue) == 0x000443, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::CallFunc_getCarLocation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, CallFunc_BooleanOR_ReturnValue) == 0x000444, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, K2Node_SwitchEnum_CmpSuccess_1) == 0x000445, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::K2Node_SwitchEnum_CmpSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, Temp_text_Variable_1) == 0x000448, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::Temp_text_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, K2Node_Select_Default) == 0x000460, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, K2Node_Event_gameInstance) == 0x000478, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::K2Node_Event_gameInstance' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, K2Node_Event_raceGameMode) == 0x000480, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::K2Node_Event_raceGameMode' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, K2Node_Event_carAvatar) == 0x000488, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::K2Node_Event_carAvatar' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, K2Node_Event_hudOptions) == 0x000490, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::K2Node_Event_hudOptions' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, CallFunc_K2_SetTimerDelegate_ReturnValue) == 0x000550, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::CallFunc_K2_SetTimerDelegate_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, CallFunc_GetCurrentLevelName_ReturnValue) == 0x000558, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::CallFunc_GetCurrentLevelName_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, CallFunc_K2_IsTimerActiveHandle_ReturnValue) == 0x000568, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::CallFunc_K2_IsTimerActiveHandle_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, CallFunc_Array_Contains_ReturnValue) == 0x000569, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::CallFunc_Array_Contains_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x00056A, "Member 'WDG_PitLimiterWarning_C_ExecuteUbergraph_WDG_PitLimiterWarning::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");

// Function WDG_PitLimiterWarning.WDG_PitLimiterWarning_C.OnStartWidget
// 0x00D8 (0x00D8 - 0x0000)
struct WDG_PitLimiterWarning_C_OnStartWidget final
{
public:
	class UAcGameInstance*                        GameInstance;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AAcRaceGameMode*                        raceGameMode;                                      // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class ACarAvatar*                             CarAvatar;                                         // 0x0010(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FHUDOptions                            HUDOptions;                                        // 0x0018(0x00C0)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)
};
static_assert(alignof(WDG_PitLimiterWarning_C_OnStartWidget) == 0x000008, "Wrong alignment on WDG_PitLimiterWarning_C_OnStartWidget");
static_assert(sizeof(WDG_PitLimiterWarning_C_OnStartWidget) == 0x0000D8, "Wrong size on WDG_PitLimiterWarning_C_OnStartWidget");
static_assert(offsetof(WDG_PitLimiterWarning_C_OnStartWidget, GameInstance) == 0x000000, "Member 'WDG_PitLimiterWarning_C_OnStartWidget::GameInstance' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_OnStartWidget, raceGameMode) == 0x000008, "Member 'WDG_PitLimiterWarning_C_OnStartWidget::raceGameMode' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_OnStartWidget, CarAvatar) == 0x000010, "Member 'WDG_PitLimiterWarning_C_OnStartWidget::CarAvatar' has a wrong offset!");
static_assert(offsetof(WDG_PitLimiterWarning_C_OnStartWidget, HUDOptions) == 0x000018, "Member 'WDG_PitLimiterWarning_C_OnStartWidget::HUDOptions' has a wrong offset!");

// Function WDG_PitLimiterWarning.WDG_PitLimiterWarning_C.OnHudTick
// 0x03E0 (0x03E0 - 0x0000)
struct WDG_PitLimiterWarning_C_OnHudTick final
{
public:
	struct FRaceHUDState                          State;                                             // 0x0000(0x03E0)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_PitLimiterWarning_C_OnHudTick) == 0x000008, "Wrong alignment on WDG_PitLimiterWarning_C_OnHudTick");
static_assert(sizeof(WDG_PitLimiterWarning_C_OnHudTick) == 0x0003E0, "Wrong size on WDG_PitLimiterWarning_C_OnHudTick");
static_assert(offsetof(WDG_PitLimiterWarning_C_OnHudTick, State) == 0x000000, "Member 'WDG_PitLimiterWarning_C_OnHudTick::State' has a wrong offset!");

}

