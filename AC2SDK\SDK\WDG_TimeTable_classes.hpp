﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TimeTable

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_TimeTable.WDG_TimeTable_C
// 0x0028 (0x0738 - 0x0710)
class UWDG_TimeTable_C final : public UTimeTable
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0710(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWDG_TimeTableHeader_C*                 Header;                                            // 0x0718(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	TMulticastInlineDelegate<void()>              OnTablePopulated;                                  // 0x0720(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	bool                                          HideScrollbar;                                     // 0x0730(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)

public:
	void ExecuteUbergraph_WDG_TimeTable(int32 EntryPoint);
	void OnPopulated();
	void PreConstruct(bool IsDesignTime);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_TimeTable_C">();
	}
	static class UWDG_TimeTable_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_TimeTable_C>();
	}
};
static_assert(alignof(UWDG_TimeTable_C) == 0x000008, "Wrong alignment on UWDG_TimeTable_C");
static_assert(sizeof(UWDG_TimeTable_C) == 0x000738, "Wrong size on UWDG_TimeTable_C");
static_assert(offsetof(UWDG_TimeTable_C, UberGraphFrame) == 0x000710, "Member 'UWDG_TimeTable_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_TimeTable_C, Header) == 0x000718, "Member 'UWDG_TimeTable_C::Header' has a wrong offset!");
static_assert(offsetof(UWDG_TimeTable_C, OnTablePopulated) == 0x000720, "Member 'UWDG_TimeTable_C::OnTablePopulated' has a wrong offset!");
static_assert(offsetof(UWDG_TimeTable_C, HideScrollbar) == 0x000730, "Member 'UWDG_TimeTable_C::HideScrollbar' has a wrong offset!");

}

