﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SavedGamesPage

#include "Basic.hpp"

#include "WDG_SavedGamesPage_classes.hpp"
#include "WDG_SavedGamesPage_parameters.hpp"


namespace SDK
{

// Function WDG_SavedGamesPage.WDG_SavedGamesPage_C.ExecuteUbergraph_WDG_SavedGamesPage
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SavedGamesPage_C::ExecuteUbergraph_WDG_SavedGamesPage(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SavedGamesPage_C", "ExecuteUbergraph_WDG_SavedGamesPage");

	Params::WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SavedGamesPage.WDG_SavedGamesPage_C.OnLoad
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FAcSaveGameHeader&         SaveGame                                               (BlueprintVisible, BlueprintReadOnly, Parm)
// class UWDG_SaveGameItem_C*              Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SavedGamesPage_C::OnLoad(const struct FAcSaveGameHeader& SaveGame, class UWDG_SaveGameItem_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SavedGamesPage_C", "OnLoad");

	Params::WDG_SavedGamesPage_C_OnLoad Parms{};

	Parms.SaveGame = std::move(SaveGame);
	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SavedGamesPage.WDG_SavedGamesPage_C.OnDel
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FAcSaveGameHeader&         SaveGame                                               (BlueprintVisible, BlueprintReadOnly, Parm)
// class UWDG_SaveGameItem_C*              Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SavedGamesPage_C::OnDel(const struct FAcSaveGameHeader& SaveGame, class UWDG_SaveGameItem_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SavedGamesPage_C", "OnDel");

	Params::WDG_SavedGamesPage_C_OnDel Parms{};

	Parms.SaveGame = std::move(SaveGame);
	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SavedGamesPage.WDG_SavedGamesPage_C.OnFocusFirstFile
// (BlueprintCallable, BlueprintEvent)

void UWDG_SavedGamesPage_C::OnFocusFirstFile()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SavedGamesPage_C", "OnFocusFirstFile");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SavedGamesPage.WDG_SavedGamesPage_C.OnRemovedFromFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_SavedGamesPage_C::OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SavedGamesPage_C", "OnRemovedFromFocusPath");

	Params::WDG_SavedGamesPage_C_OnRemovedFromFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SavedGamesPage.WDG_SavedGamesPage_C.BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_5_OnYes__DelegateSignature
// (BlueprintEvent)

void UWDG_SavedGamesPage_C::BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_5_OnYes__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SavedGamesPage_C", "BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_5_OnYes__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SavedGamesPage.WDG_SavedGamesPage_C.BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_4_OnNo__DelegateSignature
// (BlueprintEvent)

void UWDG_SavedGamesPage_C::BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_4_OnNo__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SavedGamesPage_C", "BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_4_OnNo__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SavedGamesPage.WDG_SavedGamesPage_C.OnDeleteFile
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const class FString&                    Filename                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, HasGetValueTypeHash)

void UWDG_SavedGamesPage_C::OnDeleteFile(const class FString& Filename)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SavedGamesPage_C", "OnDeleteFile");

	Params::WDG_SavedGamesPage_C_OnDeleteFile Parms{};

	Parms.Filename = std::move(Filename);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SavedGamesPage.WDG_SavedGamesPage_C.BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_3_OnShow__DelegateSignature
// (BlueprintEvent)
// Parameters:
// bool                                    HasConfirmation                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SavedGamesPage_C::BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_3_OnShow__DelegateSignature(bool HasConfirmation)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SavedGamesPage_C", "BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_3_OnShow__DelegateSignature");

	Params::WDG_SavedGamesPage_C_BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_3_OnShow__DelegateSignature Parms{};

	Parms.HasConfirmation = HasConfirmation;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SavedGamesPage.WDG_SavedGamesPage_C.BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_2_OnHide__DelegateSignature
// (BlueprintEvent)

void UWDG_SavedGamesPage_C::BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_2_OnHide__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SavedGamesPage_C", "BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_2_OnHide__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SavedGamesPage.WDG_SavedGamesPage_C.OnRefreshFileList
// (BlueprintCallable, BlueprintEvent)

void UWDG_SavedGamesPage_C::OnRefreshFileList()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SavedGamesPage_C", "OnRefreshFileList");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SavedGamesPage.WDG_SavedGamesPage_C.BP_OnBackward
// (Event, Public, BlueprintEvent)

void UWDG_SavedGamesPage_C::BP_OnBackward()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SavedGamesPage_C", "BP_OnBackward");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SavedGamesPage.WDG_SavedGamesPage_C.BP_StartPage
// (Event, Public, BlueprintEvent)

void UWDG_SavedGamesPage_C::BP_StartPage()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SavedGamesPage_C", "BP_StartPage");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SavedGamesPage.WDG_SavedGamesPage_C.BndEvt__Back_K2Node_ComponentBoundEvent_1_OnClicked__DelegateSignature
// (BlueprintEvent)

void UWDG_SavedGamesPage_C::BndEvt__Back_K2Node_ComponentBoundEvent_1_OnClicked__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SavedGamesPage_C", "BndEvt__Back_K2Node_ComponentBoundEvent_1_OnClicked__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}

}

