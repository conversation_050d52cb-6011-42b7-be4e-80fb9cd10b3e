﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TyreTempsItemLeft

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_TyreTempsItemLeft.WDG_TyreTempsItemLeft_C
// 0x0008 (0x0310 - 0x0308)
class UWDG_TyreTempsItemLeft_C final : public UTyreTemps01ItemWidget
{
public:
	class URetainerBox*                           retainerInner;                                     // 0x0308(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_TyreTempsItemLeft_C">();
	}
	static class UWDG_TyreTempsItemLeft_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_TyreTempsItemLeft_C>();
	}
};
static_assert(alignof(UWDG_TyreTempsItemLeft_C) == 0x000008, "Wrong alignment on UWDG_TyreTempsItemLeft_C");
static_assert(sizeof(UWDG_TyreTempsItemLeft_C) == 0x000310, "Wrong size on UWDG_TyreTempsItemLeft_C");
static_assert(offsetof(UWDG_TyreTempsItemLeft_C, retainerInner) == 0x000308, "Member 'UWDG_TyreTempsItemLeft_C::retainerInner' has a wrong offset!");

}

