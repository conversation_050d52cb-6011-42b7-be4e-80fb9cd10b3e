﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ServerStatsItem

#include "Basic.hpp"

#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_ServerStatsItem.WDG_ServerStatsItem_C.SetText
// 0x0048 (0x0048 - 0x0000)
struct WDG_ServerStatsItem_C_SetText final
{
public:
	class FText                                   text;                                              // 0x0000(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm)
	struct FSlateColor                            Color;                                             // 0x0018(0x0028)(BlueprintVisible, BlueprintReadOnly, Parm)
	bool                                          isHighlighted;                                     // 0x0040(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ServerStatsItem_C_SetText) == 0x000008, "Wrong alignment on WDG_ServerStatsItem_C_SetText");
static_assert(sizeof(WDG_ServerStatsItem_C_SetText) == 0x000048, "Wrong size on WDG_ServerStatsItem_C_SetText");
static_assert(offsetof(WDG_ServerStatsItem_C_SetText, text) == 0x000000, "Member 'WDG_ServerStatsItem_C_SetText::text' has a wrong offset!");
static_assert(offsetof(WDG_ServerStatsItem_C_SetText, Color) == 0x000018, "Member 'WDG_ServerStatsItem_C_SetText::Color' has a wrong offset!");
static_assert(offsetof(WDG_ServerStatsItem_C_SetText, isHighlighted) == 0x000040, "Member 'WDG_ServerStatsItem_C_SetText::isHighlighted' has a wrong offset!");

}

