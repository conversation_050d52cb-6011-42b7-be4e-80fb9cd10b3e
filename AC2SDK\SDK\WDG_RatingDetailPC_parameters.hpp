﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RatingDetailPC

#include "Basic.hpp"

#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_RatingDetailPC.WDG_RatingDetailPC_C.ExecuteUbergraph_WDG_RatingDetailPC
// 0x0038 (0x0038 - 0x0000)
struct WDG_RatingDetailPC_C_ExecuteUbergraph_WDG_RatingDetailPC final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_RatingDetailPCLeaderboardSeasonItem_C* CallFunc_Create_ReturnValue;                   // 0x0008(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_Event_season;                               // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FOnlineServicesLeaderboardRank         K2Node_Event_rank;                                 // 0x0014(0x0014)(ConstParm, NoDestructor)
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue;                     // 0x0028(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_RatingDetailPC_C_ExecuteUbergraph_WDG_RatingDetailPC) == 0x000008, "Wrong alignment on WDG_RatingDetailPC_C_ExecuteUbergraph_WDG_RatingDetailPC");
static_assert(sizeof(WDG_RatingDetailPC_C_ExecuteUbergraph_WDG_RatingDetailPC) == 0x000038, "Wrong size on WDG_RatingDetailPC_C_ExecuteUbergraph_WDG_RatingDetailPC");
static_assert(offsetof(WDG_RatingDetailPC_C_ExecuteUbergraph_WDG_RatingDetailPC, EntryPoint) == 0x000000, "Member 'WDG_RatingDetailPC_C_ExecuteUbergraph_WDG_RatingDetailPC::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_RatingDetailPC_C_ExecuteUbergraph_WDG_RatingDetailPC, CallFunc_Create_ReturnValue) == 0x000008, "Member 'WDG_RatingDetailPC_C_ExecuteUbergraph_WDG_RatingDetailPC::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RatingDetailPC_C_ExecuteUbergraph_WDG_RatingDetailPC, K2Node_Event_season) == 0x000010, "Member 'WDG_RatingDetailPC_C_ExecuteUbergraph_WDG_RatingDetailPC::K2Node_Event_season' has a wrong offset!");
static_assert(offsetof(WDG_RatingDetailPC_C_ExecuteUbergraph_WDG_RatingDetailPC, K2Node_Event_rank) == 0x000014, "Member 'WDG_RatingDetailPC_C_ExecuteUbergraph_WDG_RatingDetailPC::K2Node_Event_rank' has a wrong offset!");
static_assert(offsetof(WDG_RatingDetailPC_C_ExecuteUbergraph_WDG_RatingDetailPC, CallFunc_AddChild_ReturnValue) == 0x000028, "Member 'WDG_RatingDetailPC_C_ExecuteUbergraph_WDG_RatingDetailPC::CallFunc_AddChild_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RatingDetailPC_C_ExecuteUbergraph_WDG_RatingDetailPC, CallFunc_IsValid_ReturnValue) == 0x000030, "Member 'WDG_RatingDetailPC_C_ExecuteUbergraph_WDG_RatingDetailPC::CallFunc_IsValid_ReturnValue' has a wrong offset!");

// Function WDG_RatingDetailPC.WDG_RatingDetailPC_C.CreateLeaderboardEntry
// 0x0014 (0x0014 - 0x0000)
struct WDG_RatingDetailPC_C_CreateLeaderboardEntry final
{
public:
	struct FOnlineServicesLeaderboardRank         Rank;                                              // 0x0000(0x0014)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)
};
static_assert(alignof(WDG_RatingDetailPC_C_CreateLeaderboardEntry) == 0x000004, "Wrong alignment on WDG_RatingDetailPC_C_CreateLeaderboardEntry");
static_assert(sizeof(WDG_RatingDetailPC_C_CreateLeaderboardEntry) == 0x000014, "Wrong size on WDG_RatingDetailPC_C_CreateLeaderboardEntry");
static_assert(offsetof(WDG_RatingDetailPC_C_CreateLeaderboardEntry, Rank) == 0x000000, "Member 'WDG_RatingDetailPC_C_CreateLeaderboardEntry::Rank' has a wrong offset!");

// Function WDG_RatingDetailPC.WDG_RatingDetailPC_C.CreateNewSeason
// 0x0004 (0x0004 - 0x0000)
struct WDG_RatingDetailPC_C_CreateNewSeason final
{
public:
	int32                                         Season;                                            // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_RatingDetailPC_C_CreateNewSeason) == 0x000004, "Wrong alignment on WDG_RatingDetailPC_C_CreateNewSeason");
static_assert(sizeof(WDG_RatingDetailPC_C_CreateNewSeason) == 0x000004, "Wrong size on WDG_RatingDetailPC_C_CreateNewSeason");
static_assert(offsetof(WDG_RatingDetailPC_C_CreateNewSeason, Season) == 0x000000, "Member 'WDG_RatingDetailPC_C_CreateNewSeason::Season' has a wrong offset!");

}

