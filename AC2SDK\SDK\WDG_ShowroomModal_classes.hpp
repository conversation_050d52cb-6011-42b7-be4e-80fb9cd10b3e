﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomModal

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ShowroomModal.WDG_ShowroomModal_C
// 0x0078 (0x02D8 - 0x0260)
class UWDG_ShowroomModal_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0260(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWDG_GenericBarItem_C*                  btnExit;                                           // 0x0268(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             Content;                                           // 0x0270(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                modalborder;                                       // 0x0278(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UOverlay*                               modalOverlay;                                      // 0x0280(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	TMulticastInlineDelegate<void(class UAcPanelBase* CallingPanel, bool Cancelled)> OnHide;         // 0x0288(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void(class UAcPanelBase* CallingPanel)> OnShow;                         // 0x0298(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	class UAcPanelBase*                           CallingPanel;                                      // 0x02A8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TMulticastInlineDelegate<void(class UAcPanelBase* CallingPanel)> OnCancel;                       // 0x02B0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	struct FLinearColor                           BackgroundColor;                                   // 0x02C0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          HideOnOOBClick;                                    // 0x02D0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)

public:
	void ExecuteUbergraph_WDG_ShowroomModal(int32 EntryPoint);
	void PreConstruct(bool IsDesignTime);
	void BndEvt__btnExit_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature();
	void Hide(bool Cancelled);
	void show();
	struct FEventReply OnMouseButtonDoubleClick(const struct FGeometry& InMyGeometry, const struct FPointerEvent& InMouseEvent);
	struct FEventReply OnMouseButtonDown(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent);
	void ShowByPanel(class UAcPanelBase* CallingPanel_0);
	struct FEventReply OnPreviewKeyDown(const struct FGeometry& MyGeometry, const struct FKeyEvent& InKeyEvent);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ShowroomModal_C">();
	}
	static class UWDG_ShowroomModal_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ShowroomModal_C>();
	}
};
static_assert(alignof(UWDG_ShowroomModal_C) == 0x000008, "Wrong alignment on UWDG_ShowroomModal_C");
static_assert(sizeof(UWDG_ShowroomModal_C) == 0x0002D8, "Wrong size on UWDG_ShowroomModal_C");
static_assert(offsetof(UWDG_ShowroomModal_C, UberGraphFrame) == 0x000260, "Member 'UWDG_ShowroomModal_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomModal_C, btnExit) == 0x000268, "Member 'UWDG_ShowroomModal_C::btnExit' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomModal_C, Content) == 0x000270, "Member 'UWDG_ShowroomModal_C::Content' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomModal_C, modalborder) == 0x000278, "Member 'UWDG_ShowroomModal_C::modalborder' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomModal_C, modalOverlay) == 0x000280, "Member 'UWDG_ShowroomModal_C::modalOverlay' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomModal_C, OnHide) == 0x000288, "Member 'UWDG_ShowroomModal_C::OnHide' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomModal_C, OnShow) == 0x000298, "Member 'UWDG_ShowroomModal_C::OnShow' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomModal_C, CallingPanel) == 0x0002A8, "Member 'UWDG_ShowroomModal_C::CallingPanel' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomModal_C, OnCancel) == 0x0002B0, "Member 'UWDG_ShowroomModal_C::OnCancel' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomModal_C, BackgroundColor) == 0x0002C0, "Member 'UWDG_ShowroomModal_C::BackgroundColor' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomModal_C, HideOnOOBClick) == 0x0002D0, "Member 'UWDG_ShowroomModal_C::HideOnOOBClick' has a wrong offset!");

}

