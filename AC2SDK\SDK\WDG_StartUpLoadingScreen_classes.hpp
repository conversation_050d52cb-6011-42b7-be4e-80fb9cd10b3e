﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_StartUpLoadingScreen

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_StartUpLoadingScreen.WDG_StartUpLoadingScreen_C
// 0x0048 (0x02A8 - 0x0260)
class UWDG_StartUpLoadingScreen_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0260(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       Fade;                                              // 0x0268(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 background;                                        // 0x0270(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_354;                                         // 0x0278(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtVersion;                                        // 0x0280(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	int32                                         LoadingDots;                                       // 0x0288(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         DotsTime;                                          // 0x028C(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   txtEAVersion;                                      // 0x0290(0x0018)(Edit, BlueprintVisible, DisableEditOnInstance)

public:
	void ExecuteUbergraph_WDG_StartUpLoadingScreen(int32 EntryPoint);
	void Construct();
	class FText Get_Loading_lbl_Text_0();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_StartUpLoadingScreen_C">();
	}
	static class UWDG_StartUpLoadingScreen_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_StartUpLoadingScreen_C>();
	}
};
static_assert(alignof(UWDG_StartUpLoadingScreen_C) == 0x000008, "Wrong alignment on UWDG_StartUpLoadingScreen_C");
static_assert(sizeof(UWDG_StartUpLoadingScreen_C) == 0x0002A8, "Wrong size on UWDG_StartUpLoadingScreen_C");
static_assert(offsetof(UWDG_StartUpLoadingScreen_C, UberGraphFrame) == 0x000260, "Member 'UWDG_StartUpLoadingScreen_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_StartUpLoadingScreen_C, Fade) == 0x000268, "Member 'UWDG_StartUpLoadingScreen_C::Fade' has a wrong offset!");
static_assert(offsetof(UWDG_StartUpLoadingScreen_C, background) == 0x000270, "Member 'UWDG_StartUpLoadingScreen_C::background' has a wrong offset!");
static_assert(offsetof(UWDG_StartUpLoadingScreen_C, Image_354) == 0x000278, "Member 'UWDG_StartUpLoadingScreen_C::Image_354' has a wrong offset!");
static_assert(offsetof(UWDG_StartUpLoadingScreen_C, txtVersion) == 0x000280, "Member 'UWDG_StartUpLoadingScreen_C::txtVersion' has a wrong offset!");
static_assert(offsetof(UWDG_StartUpLoadingScreen_C, LoadingDots) == 0x000288, "Member 'UWDG_StartUpLoadingScreen_C::LoadingDots' has a wrong offset!");
static_assert(offsetof(UWDG_StartUpLoadingScreen_C, DotsTime) == 0x00028C, "Member 'UWDG_StartUpLoadingScreen_C::DotsTime' has a wrong offset!");
static_assert(offsetof(UWDG_StartUpLoadingScreen_C, txtEAVersion) == 0x000290, "Member 'UWDG_StartUpLoadingScreen_C::txtEAVersion' has a wrong offset!");

}

