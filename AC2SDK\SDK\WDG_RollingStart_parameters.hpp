﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RollingStart

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "CoreUObject_structs.hpp"


namespace SDK::Params
{

// Function WDG_RollingStart.WDG_RollingStart_C.ExecuteUbergraph_WDG_RollingStart
// 0x0768 (0x0768 - 0x0000)
struct WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0006(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0007(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_1;                // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_9[0x7];                                        // 0x0009(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Conv_FloatToString_ReturnValue;           // 0x0010(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0021(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x0022(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_23[0x5];                                       // 0x0023(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Conv_IntToString_ReturnValue;             // 0x0028(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	float                                         CallFunc_Conv_IntToFloat_ReturnValue;              // 0x0038(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x003C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Less_IntInt_ReturnValue_1;                // 0x003D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_1;             // 0x003E(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_3F[0x1];                                       // 0x003F(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Conv_IntToString_ReturnValue_1;           // 0x0040(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_1;          // 0x0050(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_51[0x7];                                       // 0x0051(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Concat_StrStr_ReturnValue;                // 0x0058(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_1;              // 0x0068(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_2;          // 0x0078(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_79[0x7];                                       // 0x0079(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_2;              // 0x0080(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_3;              // 0x0090(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_3;          // 0x00A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_A1[0x7];                                       // 0x00A1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_4;              // 0x00A8(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_4;          // 0x00B8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_5;          // 0x00B9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_6;          // 0x00BA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_7;          // 0x00BB(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_8;          // 0x00BC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_9;          // 0x00BD(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_2;             // 0x00BE(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Less_IntInt_ReturnValue_2;                // 0x00BF(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	float                                         CallFunc_Subtract_FloatFloat_ReturnValue;          // 0x00C0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Divide_FloatFloat_ReturnValue;            // 0x00C4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_FloatFloat_ReturnValue;         // 0x00C8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_C9[0x7];                                       // 0x00C9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_FloatToText_ReturnValue;             // 0x00D0(0x0018)()
	bool                                          CallFunc_Less_FloatFloat_ReturnValue;              // 0x00E8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Greater_FloatFloat_ReturnValue;           // 0x00E9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_EA[0x2];                                       // 0x00EA(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_MapRangeClamped_ReturnValue;              // 0x00EC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_FloatFloat_ReturnValue_1;         // 0x00F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_F1[0x3];                                       // 0x00F1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue;          // 0x00F4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue;                 // 0x00F8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ERaceSessionPhase                             Temp_byte_Variable;                                // 0x0100(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_101[0x3];                                      // 0x0101(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         Temp_float_Variable;                               // 0x0104(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         Temp_float_Variable_1;                             // 0x0108(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         Temp_float_Variable_2;                             // 0x010C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         Temp_float_Variable_3;                             // 0x0110(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         Temp_float_Variable_4;                             // 0x0114(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         Temp_float_Variable_5;                             // 0x0118(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         Temp_float_Variable_6;                             // 0x011C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         Temp_float_Variable_7;                             // 0x0120(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0124(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_125[0x3];                                      // 0x0125(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         Temp_float_Variable_8;                             // 0x0128(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         Temp_float_Variable_9;                             // 0x012C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_2;            // 0x0130(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_3;            // 0x0131(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_4;            // 0x0132(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_5;            // 0x0133(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_6;            // 0x0134(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_7;            // 0x0135(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_8;            // 0x0136(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_9;            // 0x0137(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_10;           // 0x0138(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_11;           // 0x0139(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_12;           // 0x013A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_13;           // 0x013B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_14;           // 0x013C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_15;           // 0x013D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_16;           // 0x013E(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_17;           // 0x013F(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_18;           // 0x0140(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_19;           // 0x0141(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_20;           // 0x0142(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_21;           // 0x0143(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_22;           // 0x0144(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_23;           // 0x0145(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_24;           // 0x0146(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_25;           // 0x0147(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetChildrenCount_ReturnValue;             // 0x0148(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x014C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_26;           // 0x0150(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_27;           // 0x0151(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_28;           // 0x0152(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_153[0x1];                                      // 0x0153(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_GetChildrenCount_ReturnValue_1;           // 0x0154(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_FloatFloat_ReturnValue_1;            // 0x0158(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_159[0x3];                                      // 0x0159(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_1;            // 0x015C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x0160(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_2;                 // 0x0161(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          Temp_bool_Variable_1;                              // 0x0162(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_GreaterEqual_FloatFloat_ReturnValue;      // 0x0163(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_2;                // 0x0164(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_29;           // 0x0165(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_30;           // 0x0166(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_31;           // 0x0167(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_32;           // 0x0168(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_33;           // 0x0169(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_34;           // 0x016A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_35;           // 0x016B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_36;           // 0x016C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_37;           // 0x016D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_16E[0x2];                                      // 0x016E(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable;                                 // 0x0170(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0174(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x0178(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_3;                 // 0x0179(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_38;           // 0x017A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_39;           // 0x017B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_40;           // 0x017C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_41;           // 0x017D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_17E[0x2];                                      // 0x017E(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue;              // 0x0180(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance;             // 0x0188(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0190(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_IsSpeedUnitMph_ReturnValue;               // 0x0191(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_192[0x6];                                      // 0x0192(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class AGameModeBase*                          CallFunc_GetGameMode_ReturnValue;                  // 0x0198(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AAcRaceGameMode*                        K2Node_DynamicCast_AsAc_Race_Game_Mode;            // 0x01A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x01A8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_1A9[0x3];                                      // 0x01A9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         K2Node_Select_Default;                             // 0x01AC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FRollingStartMessage                   CallFunc_getRollingStartMessage_ReturnValue;       // 0x01B0(0x0130)(ConstParm)
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue_1;        // 0x02E0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2E4[0x4];                                      // 0x02E4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_FloatToText_ReturnValue_1;           // 0x02E8(0x0018)()
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue_2;        // 0x0300(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_304[0x4];                                      // 0x0304(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_FloatToText_ReturnValue_2;           // 0x0308(0x0018)()
	struct FRaceHUDState                          K2Node_Event_state;                                // 0x0320(0x03E0)(ConstParm)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_10;         // 0x0700(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0701(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_GetValidValue_ReturnValue;                // 0x0702(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue_4;                 // 0x0703(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_5;                 // 0x0704(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_1;        // 0x0705(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_2;        // 0x0706(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_6;                 // 0x0707(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	float                                         K2Node_Select_Default_1;                           // 0x0708(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Multiply_IntFloat_ReturnValue;            // 0x070C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue_3;        // 0x0710(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue_7;                 // 0x0714(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_715[0x3];                                      // 0x0715(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Multiply_IntFloat_ReturnValue_1;          // 0x0718(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_MapRangeClamped_ReturnValue_1;            // 0x071C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue_1;               // 0x0720(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_42;           // 0x0728(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_2;                              // 0x0729(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_3;                // 0x072A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_43;           // 0x072B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_72C[0x4];                                      // 0x072C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UCanvasPanelSlot*                       K2Node_DynamicCast_AsCanvas_Panel_Slot;            // 0x0730(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_2;                     // 0x0738(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_739[0x3];                                      // 0x0739(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Add_FloatFloat_ReturnValue;               // 0x073C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Percent_FloatFloat_ReturnValue;           // 0x0740(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_44;           // 0x0744(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_745[0x3];                                      // 0x0745(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UCanvasPanelSlot*                       K2Node_DynamicCast_AsCanvas_Panel_Slot_1;          // 0x0748(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_3;                     // 0x0750(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_Less_FloatFloat_ReturnValue_2;            // 0x0751(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_8;                 // 0x0752(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_753[0x1];                                      // 0x0753(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable_1;                               // 0x0754(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWidget*                                CallFunc_GetChildAt_ReturnValue;                   // 0x0758(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x0760(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_1;           // 0x0764(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_9;                 // 0x0765(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_45;           // 0x0766(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart) == 0x000008, "Wrong alignment on WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart");
static_assert(sizeof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart) == 0x000768, "Wrong size on WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, EntryPoint) == 0x000000, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue) == 0x000004, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000005, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, K2Node_SwitchEnum_CmpSuccess) == 0x000006, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Not_PreBool_ReturnValue) == 0x000007, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Not_PreBool_ReturnValue_1) == 0x000008, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Not_PreBool_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Conv_FloatToString_ReturnValue) == 0x000010, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Conv_FloatToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_BooleanAND_ReturnValue) == 0x000020, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Less_IntInt_ReturnValue) == 0x000021, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Greater_IntInt_ReturnValue) == 0x000022, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Conv_IntToString_ReturnValue) == 0x000028, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Conv_IntToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Conv_IntToFloat_ReturnValue) == 0x000038, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Conv_IntToFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x00003C, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Less_IntInt_ReturnValue_1) == 0x00003D, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Less_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Greater_IntInt_ReturnValue_1) == 0x00003E, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Greater_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Conv_IntToString_ReturnValue_1) == 0x000040, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Conv_IntToString_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_EqualEqual_IntInt_ReturnValue_1) == 0x000050, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_EqualEqual_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Concat_StrStr_ReturnValue) == 0x000058, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Concat_StrStr_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Concat_StrStr_ReturnValue_1) == 0x000068, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Concat_StrStr_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_EqualEqual_IntInt_ReturnValue_2) == 0x000078, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_EqualEqual_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Concat_StrStr_ReturnValue_2) == 0x000080, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Concat_StrStr_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Concat_StrStr_ReturnValue_3) == 0x000090, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Concat_StrStr_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_EqualEqual_IntInt_ReturnValue_3) == 0x0000A0, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_EqualEqual_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Concat_StrStr_ReturnValue_4) == 0x0000A8, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Concat_StrStr_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_EqualEqual_IntInt_ReturnValue_4) == 0x0000B8, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_EqualEqual_IntInt_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_EqualEqual_IntInt_ReturnValue_5) == 0x0000B9, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_EqualEqual_IntInt_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_EqualEqual_IntInt_ReturnValue_6) == 0x0000BA, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_EqualEqual_IntInt_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_EqualEqual_IntInt_ReturnValue_7) == 0x0000BB, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_EqualEqual_IntInt_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_EqualEqual_IntInt_ReturnValue_8) == 0x0000BC, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_EqualEqual_IntInt_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_EqualEqual_IntInt_ReturnValue_9) == 0x0000BD, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_EqualEqual_IntInt_ReturnValue_9' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Greater_IntInt_ReturnValue_2) == 0x0000BE, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Greater_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Less_IntInt_ReturnValue_2) == 0x0000BF, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Less_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Subtract_FloatFloat_ReturnValue) == 0x0000C0, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Subtract_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Divide_FloatFloat_ReturnValue) == 0x0000C4, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Divide_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_LessEqual_FloatFloat_ReturnValue) == 0x0000C8, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_LessEqual_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Conv_FloatToText_ReturnValue) == 0x0000D0, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Conv_FloatToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Less_FloatFloat_ReturnValue) == 0x0000E8, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Less_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Greater_FloatFloat_ReturnValue) == 0x0000E9, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Greater_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MapRangeClamped_ReturnValue) == 0x0000EC, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MapRangeClamped_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Greater_FloatFloat_ReturnValue_1) == 0x0000F0, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Greater_FloatFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Multiply_FloatFloat_ReturnValue) == 0x0000F4, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Multiply_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeVector2D_ReturnValue) == 0x0000F8, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeVector2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, Temp_byte_Variable) == 0x000100, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, Temp_float_Variable) == 0x000104, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::Temp_float_Variable' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, Temp_float_Variable_1) == 0x000108, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::Temp_float_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, Temp_float_Variable_2) == 0x00010C, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::Temp_float_Variable_2' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, Temp_float_Variable_3) == 0x000110, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::Temp_float_Variable_3' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, Temp_float_Variable_4) == 0x000114, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::Temp_float_Variable_4' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, Temp_float_Variable_5) == 0x000118, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::Temp_float_Variable_5' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, Temp_float_Variable_6) == 0x00011C, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::Temp_float_Variable_6' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, Temp_float_Variable_7) == 0x000120, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::Temp_float_Variable_7' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, Temp_bool_Variable) == 0x000124, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, Temp_float_Variable_8) == 0x000128, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::Temp_float_Variable_8' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, Temp_float_Variable_9) == 0x00012C, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::Temp_float_Variable_9' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_2) == 0x000130, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_3) == 0x000131, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_4) == 0x000132, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_5) == 0x000133, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_6) == 0x000134, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_7) == 0x000135, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_8) == 0x000136, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_9) == 0x000137, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_9' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_10) == 0x000138, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_10' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_11) == 0x000139, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_11' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_12) == 0x00013A, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_12' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_13) == 0x00013B, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_13' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_14) == 0x00013C, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_14' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_15) == 0x00013D, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_15' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_16) == 0x00013E, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_16' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_17) == 0x00013F, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_17' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_18) == 0x000140, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_18' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_19) == 0x000141, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_19' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_20) == 0x000142, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_20' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_21) == 0x000143, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_21' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_22) == 0x000144, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_22' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_23) == 0x000145, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_23' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_24) == 0x000146, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_24' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_25) == 0x000147, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_25' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_GetChildrenCount_ReturnValue) == 0x000148, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_GetChildrenCount_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Subtract_IntInt_ReturnValue) == 0x00014C, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_26) == 0x000150, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_26' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_27) == 0x000151, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_27' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_28) == 0x000152, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_28' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_GetChildrenCount_ReturnValue_1) == 0x000154, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_GetChildrenCount_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Less_FloatFloat_ReturnValue_1) == 0x000158, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Less_FloatFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Subtract_IntInt_ReturnValue_1) == 0x00015C, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Subtract_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_BooleanAND_ReturnValue_1) == 0x000160, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_BooleanAND_ReturnValue_2) == 0x000161, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_BooleanAND_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, Temp_bool_Variable_1) == 0x000162, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_GreaterEqual_FloatFloat_ReturnValue) == 0x000163, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_GreaterEqual_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Not_PreBool_ReturnValue_2) == 0x000164, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Not_PreBool_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_29) == 0x000165, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_29' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_30) == 0x000166, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_30' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_31) == 0x000167, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_31' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_32) == 0x000168, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_32' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_33) == 0x000169, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_33' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_34) == 0x00016A, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_34' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_35) == 0x00016B, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_35' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_36) == 0x00016C, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_36' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_37) == 0x00016D, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_37' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, Temp_int_Variable) == 0x000170, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Add_IntInt_ReturnValue) == 0x000174, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_LessEqual_IntInt_ReturnValue) == 0x000178, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_BooleanAND_ReturnValue_3) == 0x000179, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_BooleanAND_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_38) == 0x00017A, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_38' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_39) == 0x00017B, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_39' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_40) == 0x00017C, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_40' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_41) == 0x00017D, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_41' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_GetGameInstance_ReturnValue) == 0x000180, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_GetGameInstance_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, K2Node_DynamicCast_AsAc_Game_Instance) == 0x000188, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::K2Node_DynamicCast_AsAc_Game_Instance' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, K2Node_DynamicCast_bSuccess) == 0x000190, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_IsSpeedUnitMph_ReturnValue) == 0x000191, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_IsSpeedUnitMph_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_GetGameMode_ReturnValue) == 0x000198, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_GetGameMode_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, K2Node_DynamicCast_AsAc_Race_Game_Mode) == 0x0001A0, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::K2Node_DynamicCast_AsAc_Race_Game_Mode' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, K2Node_DynamicCast_bSuccess_1) == 0x0001A8, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, K2Node_Select_Default) == 0x0001AC, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::K2Node_Select_Default' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_getRollingStartMessage_ReturnValue) == 0x0001B0, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_getRollingStartMessage_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Multiply_FloatFloat_ReturnValue_1) == 0x0002E0, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Multiply_FloatFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Conv_FloatToText_ReturnValue_1) == 0x0002E8, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Conv_FloatToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Multiply_FloatFloat_ReturnValue_2) == 0x000300, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Multiply_FloatFloat_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Conv_FloatToText_ReturnValue_2) == 0x000308, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Conv_FloatToText_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, K2Node_Event_state) == 0x000320, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::K2Node_Event_state' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_EqualEqual_IntInt_ReturnValue_10) == 0x000700, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_EqualEqual_IntInt_ReturnValue_10' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000701, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_GetValidValue_ReturnValue) == 0x000702, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_GetValidValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_BooleanAND_ReturnValue_4) == 0x000703, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_BooleanAND_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_BooleanAND_ReturnValue_5) == 0x000704, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_BooleanAND_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_EqualEqual_ByteByte_ReturnValue_1) == 0x000705, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_EqualEqual_ByteByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_EqualEqual_ByteByte_ReturnValue_2) == 0x000706, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_EqualEqual_ByteByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_BooleanAND_ReturnValue_6) == 0x000707, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_BooleanAND_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, K2Node_Select_Default_1) == 0x000708, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::K2Node_Select_Default_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Multiply_IntFloat_ReturnValue) == 0x00070C, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Multiply_IntFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Multiply_FloatFloat_ReturnValue_3) == 0x000710, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Multiply_FloatFloat_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_BooleanAND_ReturnValue_7) == 0x000714, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_BooleanAND_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Multiply_IntFloat_ReturnValue_1) == 0x000718, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Multiply_IntFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MapRangeClamped_ReturnValue_1) == 0x00071C, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MapRangeClamped_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeVector2D_ReturnValue_1) == 0x000720, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeVector2D_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_42) == 0x000728, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_42' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, Temp_bool_Variable_2) == 0x000729, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::Temp_bool_Variable_2' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Not_PreBool_ReturnValue_3) == 0x00072A, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Not_PreBool_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_43) == 0x00072B, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_43' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, K2Node_DynamicCast_AsCanvas_Panel_Slot) == 0x000730, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::K2Node_DynamicCast_AsCanvas_Panel_Slot' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, K2Node_DynamicCast_bSuccess_2) == 0x000738, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::K2Node_DynamicCast_bSuccess_2' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Add_FloatFloat_ReturnValue) == 0x00073C, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Add_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Percent_FloatFloat_ReturnValue) == 0x000740, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Percent_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_44) == 0x000744, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_44' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, K2Node_DynamicCast_AsCanvas_Panel_Slot_1) == 0x000748, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::K2Node_DynamicCast_AsCanvas_Panel_Slot_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, K2Node_DynamicCast_bSuccess_3) == 0x000750, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::K2Node_DynamicCast_bSuccess_3' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Less_FloatFloat_ReturnValue_2) == 0x000751, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Less_FloatFloat_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_BooleanAND_ReturnValue_8) == 0x000752, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_BooleanAND_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, Temp_int_Variable_1) == 0x000754, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_GetChildAt_ReturnValue) == 0x000758, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_GetChildAt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_Add_IntInt_ReturnValue_1) == 0x000760, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_LessEqual_IntInt_ReturnValue_1) == 0x000764, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_LessEqual_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_BooleanAND_ReturnValue_9) == 0x000765, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_BooleanAND_ReturnValue_9' has a wrong offset!");
static_assert(offsetof(WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart, CallFunc_MakeLiteralByte_ReturnValue_45) == 0x000766, "Member 'WDG_RollingStart_C_ExecuteUbergraph_WDG_RollingStart::CallFunc_MakeLiteralByte_ReturnValue_45' has a wrong offset!");

// Function WDG_RollingStart.WDG_RollingStart_C.OnHudTick
// 0x03E0 (0x03E0 - 0x0000)
struct WDG_RollingStart_C_OnHudTick final
{
public:
	struct FRaceHUDState                          State;                                             // 0x0000(0x03E0)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_RollingStart_C_OnHudTick) == 0x000008, "Wrong alignment on WDG_RollingStart_C_OnHudTick");
static_assert(sizeof(WDG_RollingStart_C_OnHudTick) == 0x0003E0, "Wrong size on WDG_RollingStart_C_OnHudTick");
static_assert(offsetof(WDG_RollingStart_C_OnHudTick, State) == 0x000000, "Member 'WDG_RollingStart_C_OnHudTick::State' has a wrong offset!");

}

