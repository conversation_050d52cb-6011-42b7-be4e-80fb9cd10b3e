﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ReplayTimeLineMarker

#include "Basic.hpp"

#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ReplayTimeLineMarker.WDG_ReplayTimeLineMarker_C
// 0x0010 (0x0270 - 0x0260)
class UWDG_ReplayTimeLineMarker_C final : public UUserWidget
{
public:
	class UTextBlock*                             txtForMarker;                                      // 0x0260(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 VerticalTimeCursor;                                // 0x0268(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSki<PERSON>, NoD<PERSON>ructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ReplayTimeLineMarker_C">();
	}
	static class UWDG_ReplayTimeLineMarker_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ReplayTimeLineMarker_C>();
	}
};
static_assert(alignof(UWDG_ReplayTimeLineMarker_C) == 0x000008, "Wrong alignment on UWDG_ReplayTimeLineMarker_C");
static_assert(sizeof(UWDG_ReplayTimeLineMarker_C) == 0x000270, "Wrong size on UWDG_ReplayTimeLineMarker_C");
static_assert(offsetof(UWDG_ReplayTimeLineMarker_C, txtForMarker) == 0x000260, "Member 'UWDG_ReplayTimeLineMarker_C::txtForMarker' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayTimeLineMarker_C, VerticalTimeCursor) == 0x000268, "Member 'UWDG_ReplayTimeLineMarker_C::VerticalTimeCursor' has a wrong offset!");

}

