﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SpecialEventItem

#include "Basic.hpp"

#include "Slate_structs.hpp"
#include "SlateCore_structs.hpp"
#include "AC2_structs.hpp"
#include "CoreUObject_structs.hpp"


namespace SDK::Params
{

// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.ExecuteUbergraph_WDG_SpecialEventItem
// 0x0088 (0x0088 - 0x0000)
struct WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void(class UAcPanelBase* panel, bool mouse_over)> K2Node_CreateDelegate_OutputDelegate; // 0x0004(0x0010)(ZeroConstructor, NoDestructor)
	struct FFocusEvent                            K2Node_Event_InFocusEvent_1;                       // 0x0014(0x0008)(NoDestructor)
	struct FFocusEvent                            K2Node_Event_InFocusEvent;                         // 0x001C(0x0008)(NoDestructor)
	uint8                                         Pad_24[0x4];                                       // 0x0024(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPanelBase*                           K2Node_CustomEvent_Panel;                          // 0x0028(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_mouse_over;                     // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          K2Node_Event_mouse_enter;                          // 0x0031(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_32[0x2];                                       // 0x0032(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Multiply_IntFloat_ReturnValue;            // 0x0034(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_41[0x3];                                       // 0x0041(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_BreakVector2D_X;                          // 0x0044(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakVector2D_Y;                          // 0x0048(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4C[0x4];                                       // 0x004C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue;             // 0x0050(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FAnchors                               K2Node_MakeStruct_Anchors;                         // 0x0058(0x0010)(NoDestructor)
	struct FMargin                                K2Node_MakeStruct_Margin;                          // 0x0068(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor)
	struct FMargin                                K2Node_MakeStruct_Margin_1;                        // 0x0078(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem) == 0x000008, "Wrong alignment on WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem");
static_assert(sizeof(WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem) == 0x000088, "Wrong size on WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem");
static_assert(offsetof(WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem, EntryPoint) == 0x000000, "Member 'WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem, K2Node_CreateDelegate_OutputDelegate) == 0x000004, "Member 'WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem, K2Node_Event_InFocusEvent_1) == 0x000014, "Member 'WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem::K2Node_Event_InFocusEvent_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem, K2Node_Event_InFocusEvent) == 0x00001C, "Member 'WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem::K2Node_Event_InFocusEvent' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem, K2Node_CustomEvent_Panel) == 0x000028, "Member 'WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem::K2Node_CustomEvent_Panel' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem, K2Node_CustomEvent_mouse_over) == 0x000030, "Member 'WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem::K2Node_CustomEvent_mouse_over' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem, K2Node_Event_mouse_enter) == 0x000031, "Member 'WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem::K2Node_Event_mouse_enter' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem, CallFunc_Multiply_IntFloat_ReturnValue) == 0x000034, "Member 'WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem::CallFunc_Multiply_IntFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem, CallFunc_PlayAnimation_ReturnValue) == 0x000038, "Member 'WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem, K2Node_Event_IsDesignTime) == 0x000040, "Member 'WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem, CallFunc_BreakVector2D_X) == 0x000044, "Member 'WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem, CallFunc_BreakVector2D_Y) == 0x000048, "Member 'WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem, CallFunc_SlotAsCanvasSlot_ReturnValue) == 0x000050, "Member 'WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem::CallFunc_SlotAsCanvasSlot_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem, K2Node_MakeStruct_Anchors) == 0x000058, "Member 'WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem::K2Node_MakeStruct_Anchors' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem, K2Node_MakeStruct_Margin) == 0x000068, "Member 'WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem::K2Node_MakeStruct_Margin' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem, K2Node_MakeStruct_Margin_1) == 0x000078, "Member 'WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem::K2Node_MakeStruct_Margin_1' has a wrong offset!");

// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_SpecialEventItem_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SpecialEventItem_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_SpecialEventItem_C_PreConstruct");
static_assert(sizeof(WDG_SpecialEventItem_C_PreConstruct) == 0x000001, "Wrong size on WDG_SpecialEventItem_C_PreConstruct");
static_assert(offsetof(WDG_SpecialEventItem_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_SpecialEventItem_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.BP_MouseFocus
// 0x0001 (0x0001 - 0x0000)
struct WDG_SpecialEventItem_C_BP_MouseFocus final
{
public:
	bool                                          mouse_enter;                                       // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SpecialEventItem_C_BP_MouseFocus) == 0x000001, "Wrong alignment on WDG_SpecialEventItem_C_BP_MouseFocus");
static_assert(sizeof(WDG_SpecialEventItem_C_BP_MouseFocus) == 0x000001, "Wrong size on WDG_SpecialEventItem_C_BP_MouseFocus");
static_assert(offsetof(WDG_SpecialEventItem_C_BP_MouseFocus, mouse_enter) == 0x000000, "Member 'WDG_SpecialEventItem_C_BP_MouseFocus::mouse_enter' has a wrong offset!");

// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.ForwardM
// 0x0010 (0x0010 - 0x0000)
struct WDG_SpecialEventItem_C_ForwardM final
{
public:
	class UAcPanelBase*                           panel;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          mouse_over;                                        // 0x0008(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SpecialEventItem_C_ForwardM) == 0x000008, "Wrong alignment on WDG_SpecialEventItem_C_ForwardM");
static_assert(sizeof(WDG_SpecialEventItem_C_ForwardM) == 0x000010, "Wrong size on WDG_SpecialEventItem_C_ForwardM");
static_assert(offsetof(WDG_SpecialEventItem_C_ForwardM, panel) == 0x000000, "Member 'WDG_SpecialEventItem_C_ForwardM::panel' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_ForwardM, mouse_over) == 0x000008, "Member 'WDG_SpecialEventItem_C_ForwardM::mouse_over' has a wrong offset!");

// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.OnRemovedFromFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_SpecialEventItem_C_OnRemovedFromFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_SpecialEventItem_C_OnRemovedFromFocusPath) == 0x000004, "Wrong alignment on WDG_SpecialEventItem_C_OnRemovedFromFocusPath");
static_assert(sizeof(WDG_SpecialEventItem_C_OnRemovedFromFocusPath) == 0x000008, "Wrong size on WDG_SpecialEventItem_C_OnRemovedFromFocusPath");
static_assert(offsetof(WDG_SpecialEventItem_C_OnRemovedFromFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_SpecialEventItem_C_OnRemovedFromFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.OnAddedToFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_SpecialEventItem_C_OnAddedToFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_SpecialEventItem_C_OnAddedToFocusPath) == 0x000004, "Wrong alignment on WDG_SpecialEventItem_C_OnAddedToFocusPath");
static_assert(sizeof(WDG_SpecialEventItem_C_OnAddedToFocusPath) == 0x000008, "Wrong size on WDG_SpecialEventItem_C_OnAddedToFocusPath");
static_assert(offsetof(WDG_SpecialEventItem_C_OnAddedToFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_SpecialEventItem_C_OnAddedToFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.GetPresetData
// 0x06A0 (0x06A0 - 0x0000)
struct WDG_SpecialEventItem_C_GetPresetData final
{
public:
	class FText                                   Car_Model_Name;                                    // 0x0000(0x0018)(Parm, OutParm)
	EBrandType                                    Brand;                                             // 0x0018(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   Track_Name;                                        // 0x0020(0x0018)(Parm, OutParm)
	class FText                                   GameMode;                                          // 0x0038(0x0018)(Parm, OutParm)
	TSoftObjectPtr<class UTexture2D>              teamLogo;                                          // 0x0050(0x0028)(Parm, OutParm, HasGetValueTypeHash)
	struct FCircuitInfo                           CircuitInfo;                                       // 0x0078(0x01F0)(Edit, BlueprintVisible)
	struct FModelInfo                             ModelInfo;                                         // 0x0268(0x01A8)(Edit, BlueprintVisible)
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue;               // 0x0410(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x0418(0x0018)()
	class FText                                   CallFunc_Conv_NameToText_ReturnValue;              // 0x0430(0x0018)()
	class FText                                   CallFunc_GetCommonGameModeText_ReturnValue;        // 0x0448(0x0018)()
	struct FCarInfo                               CallFunc_GetCarInfoByName_Destination;             // 0x0460(0x00E0)()
	bool                                          CallFunc_GetCarInfoByName_ReturnValue;             // 0x0540(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_GetCircuitInfo_ReturnValue;               // 0x0541(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_GetModelInfo_ReturnValue;                 // 0x0542(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_543[0x5];                                      // 0x0543(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Conv_ByteToString_ReturnValue;            // 0x0548(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0558(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_559[0x3];                                      // 0x0559(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class FName                                   CallFunc_Conv_StringToName_ReturnValue;            // 0x055C(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_564[0x4];                                      // 0x0564(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTeamTemplate                          CallFunc_GetDataTableRowFromName_OutRow;           // 0x0568(0x0130)()
	bool                                          CallFunc_GetDataTableRowFromName_ReturnValue;      // 0x0698(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SpecialEventItem_C_GetPresetData) == 0x000008, "Wrong alignment on WDG_SpecialEventItem_C_GetPresetData");
static_assert(sizeof(WDG_SpecialEventItem_C_GetPresetData) == 0x0006A0, "Wrong size on WDG_SpecialEventItem_C_GetPresetData");
static_assert(offsetof(WDG_SpecialEventItem_C_GetPresetData, Car_Model_Name) == 0x000000, "Member 'WDG_SpecialEventItem_C_GetPresetData::Car_Model_Name' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_GetPresetData, Brand) == 0x000018, "Member 'WDG_SpecialEventItem_C_GetPresetData::Brand' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_GetPresetData, Track_Name) == 0x000020, "Member 'WDG_SpecialEventItem_C_GetPresetData::Track_Name' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_GetPresetData, GameMode) == 0x000038, "Member 'WDG_SpecialEventItem_C_GetPresetData::GameMode' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_GetPresetData, teamLogo) == 0x000050, "Member 'WDG_SpecialEventItem_C_GetPresetData::teamLogo' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_GetPresetData, CircuitInfo) == 0x000078, "Member 'WDG_SpecialEventItem_C_GetPresetData::CircuitInfo' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_GetPresetData, ModelInfo) == 0x000268, "Member 'WDG_SpecialEventItem_C_GetPresetData::ModelInfo' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_GetPresetData, CallFunc_GetMenuManager_ReturnValue) == 0x000410, "Member 'WDG_SpecialEventItem_C_GetPresetData::CallFunc_GetMenuManager_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_GetPresetData, CallFunc_Conv_StringToText_ReturnValue) == 0x000418, "Member 'WDG_SpecialEventItem_C_GetPresetData::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_GetPresetData, CallFunc_Conv_NameToText_ReturnValue) == 0x000430, "Member 'WDG_SpecialEventItem_C_GetPresetData::CallFunc_Conv_NameToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_GetPresetData, CallFunc_GetCommonGameModeText_ReturnValue) == 0x000448, "Member 'WDG_SpecialEventItem_C_GetPresetData::CallFunc_GetCommonGameModeText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_GetPresetData, CallFunc_GetCarInfoByName_Destination) == 0x000460, "Member 'WDG_SpecialEventItem_C_GetPresetData::CallFunc_GetCarInfoByName_Destination' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_GetPresetData, CallFunc_GetCarInfoByName_ReturnValue) == 0x000540, "Member 'WDG_SpecialEventItem_C_GetPresetData::CallFunc_GetCarInfoByName_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_GetPresetData, CallFunc_GetCircuitInfo_ReturnValue) == 0x000541, "Member 'WDG_SpecialEventItem_C_GetPresetData::CallFunc_GetCircuitInfo_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_GetPresetData, CallFunc_GetModelInfo_ReturnValue) == 0x000542, "Member 'WDG_SpecialEventItem_C_GetPresetData::CallFunc_GetModelInfo_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_GetPresetData, CallFunc_Conv_ByteToString_ReturnValue) == 0x000548, "Member 'WDG_SpecialEventItem_C_GetPresetData::CallFunc_Conv_ByteToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_GetPresetData, CallFunc_BooleanAND_ReturnValue) == 0x000558, "Member 'WDG_SpecialEventItem_C_GetPresetData::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_GetPresetData, CallFunc_Conv_StringToName_ReturnValue) == 0x00055C, "Member 'WDG_SpecialEventItem_C_GetPresetData::CallFunc_Conv_StringToName_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_GetPresetData, CallFunc_GetDataTableRowFromName_OutRow) == 0x000568, "Member 'WDG_SpecialEventItem_C_GetPresetData::CallFunc_GetDataTableRowFromName_OutRow' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_GetPresetData, CallFunc_GetDataTableRowFromName_ReturnValue) == 0x000698, "Member 'WDG_SpecialEventItem_C_GetPresetData::CallFunc_GetDataTableRowFromName_ReturnValue' has a wrong offset!");

// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.SetColors
// 0x0028 (0x0028 - 0x0000)
struct WDG_SpecialEventItem_C_SetColors final
{
public:
	struct FLinearColor                           CallFunc_Map_Find_Value;                           // 0x0000(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Map_Find_ReturnValue;                     // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_11[0x3];                                       // 0x0011(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           CallFunc_Map_Find_Value_1;                         // 0x0014(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Map_Find_ReturnValue_1;                   // 0x0024(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SpecialEventItem_C_SetColors) == 0x000004, "Wrong alignment on WDG_SpecialEventItem_C_SetColors");
static_assert(sizeof(WDG_SpecialEventItem_C_SetColors) == 0x000028, "Wrong size on WDG_SpecialEventItem_C_SetColors");
static_assert(offsetof(WDG_SpecialEventItem_C_SetColors, CallFunc_Map_Find_Value) == 0x000000, "Member 'WDG_SpecialEventItem_C_SetColors::CallFunc_Map_Find_Value' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_SetColors, CallFunc_Map_Find_ReturnValue) == 0x000010, "Member 'WDG_SpecialEventItem_C_SetColors::CallFunc_Map_Find_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_SetColors, CallFunc_Map_Find_Value_1) == 0x000014, "Member 'WDG_SpecialEventItem_C_SetColors::CallFunc_Map_Find_Value_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_SetColors, CallFunc_Map_Find_ReturnValue_1) == 0x000024, "Member 'WDG_SpecialEventItem_C_SetColors::CallFunc_Map_Find_ReturnValue_1' has a wrong offset!");

// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.GetMainColor
// 0x0024 (0x0024 - 0x0000)
struct WDG_SpecialEventItem_C_GetMainColor final
{
public:
	struct FLinearColor                           Value;                                             // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_Map_Find_Value;                           // 0x0010(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Map_Find_ReturnValue;                     // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SpecialEventItem_C_GetMainColor) == 0x000004, "Wrong alignment on WDG_SpecialEventItem_C_GetMainColor");
static_assert(sizeof(WDG_SpecialEventItem_C_GetMainColor) == 0x000024, "Wrong size on WDG_SpecialEventItem_C_GetMainColor");
static_assert(offsetof(WDG_SpecialEventItem_C_GetMainColor, Value) == 0x000000, "Member 'WDG_SpecialEventItem_C_GetMainColor::Value' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_GetMainColor, CallFunc_Map_Find_Value) == 0x000010, "Member 'WDG_SpecialEventItem_C_GetMainColor::CallFunc_Map_Find_Value' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_GetMainColor, CallFunc_Map_Find_ReturnValue) == 0x000020, "Member 'WDG_SpecialEventItem_C_GetMainColor::CallFunc_Map_Find_ReturnValue' has a wrong offset!");

// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.SetBackgroundImage
// 0x0248 (0x0248 - 0x0000)
struct WDG_SpecialEventItem_C_SetBackgroundImage final
{
public:
	bool                                          isWet;                                             // 0x0000(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<TSoftObjectPtr<class UTexture2D>>      Images;                                            // 0x0008(0x0010)(Edit, BlueprintVisible)
	struct FCircuitInfo                           CircuitInfo;                                       // 0x0018(0x01F0)(Edit, BlueprintVisible)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0208(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_209[0x3];                                      // 0x0209(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x020C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x0210(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_RandomIntegerInRange_ReturnValue;         // 0x0214(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TSoftObjectPtr<class UTexture2D>              CallFunc_Array_Get_Item;                           // 0x0218(0x0028)(HasGetValueTypeHash)
	bool                                          CallFunc_GetCircuitInfo_ReturnValue;               // 0x0240(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SpecialEventItem_C_SetBackgroundImage) == 0x000008, "Wrong alignment on WDG_SpecialEventItem_C_SetBackgroundImage");
static_assert(sizeof(WDG_SpecialEventItem_C_SetBackgroundImage) == 0x000248, "Wrong size on WDG_SpecialEventItem_C_SetBackgroundImage");
static_assert(offsetof(WDG_SpecialEventItem_C_SetBackgroundImage, isWet) == 0x000000, "Member 'WDG_SpecialEventItem_C_SetBackgroundImage::isWet' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_SetBackgroundImage, Images) == 0x000008, "Member 'WDG_SpecialEventItem_C_SetBackgroundImage::Images' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_SetBackgroundImage, CircuitInfo) == 0x000018, "Member 'WDG_SpecialEventItem_C_SetBackgroundImage::CircuitInfo' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_SetBackgroundImage, K2Node_SwitchEnum_CmpSuccess) == 0x000208, "Member 'WDG_SpecialEventItem_C_SetBackgroundImage::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_SetBackgroundImage, CallFunc_Array_Length_ReturnValue) == 0x00020C, "Member 'WDG_SpecialEventItem_C_SetBackgroundImage::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_SetBackgroundImage, CallFunc_Subtract_IntInt_ReturnValue) == 0x000210, "Member 'WDG_SpecialEventItem_C_SetBackgroundImage::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_SetBackgroundImage, CallFunc_RandomIntegerInRange_ReturnValue) == 0x000214, "Member 'WDG_SpecialEventItem_C_SetBackgroundImage::CallFunc_RandomIntegerInRange_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_SetBackgroundImage, CallFunc_Array_Get_Item) == 0x000218, "Member 'WDG_SpecialEventItem_C_SetBackgroundImage::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_SetBackgroundImage, CallFunc_GetCircuitInfo_ReturnValue) == 0x000240, "Member 'WDG_SpecialEventItem_C_SetBackgroundImage::CallFunc_GetCircuitInfo_ReturnValue' has a wrong offset!");

// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.SetSelected
// 0x0018 (0x0018 - 0x0000)
struct WDG_SpecialEventItem_C_SetSelected final
{
public:
	bool                                          IsSelected_0;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationReverse_ReturnValue;         // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationForward_ReturnValue;         // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SpecialEventItem_C_SetSelected) == 0x000008, "Wrong alignment on WDG_SpecialEventItem_C_SetSelected");
static_assert(sizeof(WDG_SpecialEventItem_C_SetSelected) == 0x000018, "Wrong size on WDG_SpecialEventItem_C_SetSelected");
static_assert(offsetof(WDG_SpecialEventItem_C_SetSelected, IsSelected_0) == 0x000000, "Member 'WDG_SpecialEventItem_C_SetSelected::IsSelected_0' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_SetSelected, CallFunc_PlayAnimationReverse_ReturnValue) == 0x000008, "Member 'WDG_SpecialEventItem_C_SetSelected::CallFunc_PlayAnimationReverse_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_SetSelected, CallFunc_PlayAnimationForward_ReturnValue) == 0x000010, "Member 'WDG_SpecialEventItem_C_SetSelected::CallFunc_PlayAnimationForward_ReturnValue' has a wrong offset!");

// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.UpdateFromPreset
// 0x08B8 (0x08B8 - 0x0000)
struct WDG_SpecialEventItem_C_UpdateFromPreset final
{
public:
	struct FSpecialEventPreset                    SpecialEvent_0;                                    // 0x0000(0x0240)(BlueprintVisible, BlueprintReadOnly, Parm)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0240(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0241(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_242[0x6];                                      // 0x0242(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_IntToText_ReturnValue;               // 0x0248(0x0018)()
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_1;             // 0x0260(0x0018)()
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_2;            // 0x0278(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_3;            // 0x0279(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_4;            // 0x027A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_5;            // 0x027B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_27C[0x4];                                      // 0x027C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSpecialEventUIData                    CallFunc_GetUIDataFromPreset_ReturnValue;          // 0x0280(0x0630)()
	bool                                          CallFunc_CanPlay_Result;                           // 0x08B0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_CanPlay_Result_1;                         // 0x08B1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x08B2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SpecialEventItem_C_UpdateFromPreset) == 0x000008, "Wrong alignment on WDG_SpecialEventItem_C_UpdateFromPreset");
static_assert(sizeof(WDG_SpecialEventItem_C_UpdateFromPreset) == 0x0008B8, "Wrong size on WDG_SpecialEventItem_C_UpdateFromPreset");
static_assert(offsetof(WDG_SpecialEventItem_C_UpdateFromPreset, SpecialEvent_0) == 0x000000, "Member 'WDG_SpecialEventItem_C_UpdateFromPreset::SpecialEvent_0' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_UpdateFromPreset, CallFunc_MakeLiteralByte_ReturnValue) == 0x000240, "Member 'WDG_SpecialEventItem_C_UpdateFromPreset::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_UpdateFromPreset, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000241, "Member 'WDG_SpecialEventItem_C_UpdateFromPreset::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_UpdateFromPreset, CallFunc_Conv_IntToText_ReturnValue) == 0x000248, "Member 'WDG_SpecialEventItem_C_UpdateFromPreset::CallFunc_Conv_IntToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_UpdateFromPreset, CallFunc_Conv_IntToText_ReturnValue_1) == 0x000260, "Member 'WDG_SpecialEventItem_C_UpdateFromPreset::CallFunc_Conv_IntToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_UpdateFromPreset, CallFunc_MakeLiteralByte_ReturnValue_2) == 0x000278, "Member 'WDG_SpecialEventItem_C_UpdateFromPreset::CallFunc_MakeLiteralByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_UpdateFromPreset, CallFunc_MakeLiteralByte_ReturnValue_3) == 0x000279, "Member 'WDG_SpecialEventItem_C_UpdateFromPreset::CallFunc_MakeLiteralByte_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_UpdateFromPreset, CallFunc_MakeLiteralByte_ReturnValue_4) == 0x00027A, "Member 'WDG_SpecialEventItem_C_UpdateFromPreset::CallFunc_MakeLiteralByte_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_UpdateFromPreset, CallFunc_MakeLiteralByte_ReturnValue_5) == 0x00027B, "Member 'WDG_SpecialEventItem_C_UpdateFromPreset::CallFunc_MakeLiteralByte_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_UpdateFromPreset, CallFunc_GetUIDataFromPreset_ReturnValue) == 0x000280, "Member 'WDG_SpecialEventItem_C_UpdateFromPreset::CallFunc_GetUIDataFromPreset_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_UpdateFromPreset, CallFunc_CanPlay_Result) == 0x0008B0, "Member 'WDG_SpecialEventItem_C_UpdateFromPreset::CallFunc_CanPlay_Result' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_UpdateFromPreset, CallFunc_CanPlay_Result_1) == 0x0008B1, "Member 'WDG_SpecialEventItem_C_UpdateFromPreset::CallFunc_CanPlay_Result_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_UpdateFromPreset, CallFunc_BooleanAND_ReturnValue) == 0x0008B2, "Member 'WDG_SpecialEventItem_C_UpdateFromPreset::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");

// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.ResizeTextFont
// 0x0068 (0x0068 - 0x0000)
struct WDG_SpecialEventItem_C_ResizeTextFont final
{
public:
	class UTextBlock*                             text;                                              // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Size_0;                                            // 0x0008(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C[0x4];                                        // 0x000C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateFontInfo                         K2Node_MakeStruct_SlateFontInfo;                   // 0x0010(0x0058)(UObjectWrapper, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SpecialEventItem_C_ResizeTextFont) == 0x000008, "Wrong alignment on WDG_SpecialEventItem_C_ResizeTextFont");
static_assert(sizeof(WDG_SpecialEventItem_C_ResizeTextFont) == 0x000068, "Wrong size on WDG_SpecialEventItem_C_ResizeTextFont");
static_assert(offsetof(WDG_SpecialEventItem_C_ResizeTextFont, text) == 0x000000, "Member 'WDG_SpecialEventItem_C_ResizeTextFont::text' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_ResizeTextFont, Size_0) == 0x000008, "Member 'WDG_SpecialEventItem_C_ResizeTextFont::Size_0' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventItem_C_ResizeTextFont, K2Node_MakeStruct_SlateFontInfo) == 0x000010, "Member 'WDG_SpecialEventItem_C_ResizeTextFont::K2Node_MakeStruct_SlateFontInfo' has a wrong offset!");

// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.imgZoomedOut
// 0x0008 (0x0008 - 0x0000)
struct WDG_SpecialEventItem_C_imgZoomedOut final
{
public:
	class UImage*                                 Target;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SpecialEventItem_C_imgZoomedOut) == 0x000008, "Wrong alignment on WDG_SpecialEventItem_C_imgZoomedOut");
static_assert(sizeof(WDG_SpecialEventItem_C_imgZoomedOut) == 0x000008, "Wrong size on WDG_SpecialEventItem_C_imgZoomedOut");
static_assert(offsetof(WDG_SpecialEventItem_C_imgZoomedOut, Target) == 0x000000, "Member 'WDG_SpecialEventItem_C_imgZoomedOut::Target' has a wrong offset!");

// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.imgZoomedIn
// 0x0008 (0x0008 - 0x0000)
struct WDG_SpecialEventItem_C_imgZoomedIn final
{
public:
	class UImage*                                 Target;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SpecialEventItem_C_imgZoomedIn) == 0x000008, "Wrong alignment on WDG_SpecialEventItem_C_imgZoomedIn");
static_assert(sizeof(WDG_SpecialEventItem_C_imgZoomedIn) == 0x000008, "Wrong size on WDG_SpecialEventItem_C_imgZoomedIn");
static_assert(offsetof(WDG_SpecialEventItem_C_imgZoomedIn, Target) == 0x000000, "Member 'WDG_SpecialEventItem_C_imgZoomedIn::Target' has a wrong offset!");

// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_0
// 0x0008 (0x0008 - 0x0000)
struct WDG_SpecialEventItem_C_SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_0 final
{
public:
	class UImage*                                 Target;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SpecialEventItem_C_SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_0) == 0x000008, "Wrong alignment on WDG_SpecialEventItem_C_SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_0");
static_assert(sizeof(WDG_SpecialEventItem_C_SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_0) == 0x000008, "Wrong size on WDG_SpecialEventItem_C_SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_0");
static_assert(offsetof(WDG_SpecialEventItem_C_SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_0, Target) == 0x000000, "Member 'WDG_SpecialEventItem_C_SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_0::Target' has a wrong offset!");

// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_1
// 0x0008 (0x0008 - 0x0000)
struct WDG_SpecialEventItem_C_SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_1 final
{
public:
	class UImage*                                 Target;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SpecialEventItem_C_SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_1) == 0x000008, "Wrong alignment on WDG_SpecialEventItem_C_SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_1");
static_assert(sizeof(WDG_SpecialEventItem_C_SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_1) == 0x000008, "Wrong size on WDG_SpecialEventItem_C_SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_1");
static_assert(offsetof(WDG_SpecialEventItem_C_SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_1, Target) == 0x000000, "Member 'WDG_SpecialEventItem_C_SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_1::Target' has a wrong offset!");

}

