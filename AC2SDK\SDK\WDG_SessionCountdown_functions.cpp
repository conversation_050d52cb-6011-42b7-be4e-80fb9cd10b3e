﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SessionCountdown

#include "Basic.hpp"

#include "WDG_SessionCountdown_classes.hpp"
#include "WDG_SessionCountdown_parameters.hpp"


namespace SDK
{

// Function WDG_SessionCountdown.WDG_SessionCountdown_C.ExecuteUbergraph_WDG_SessionCountdown
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SessionCountdown_C::ExecuteUbergraph_WDG_SessionCountdown(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SessionCountdown_C", "ExecuteUbergraph_WDG_SessionCountdown");

	Params::WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SessionCountdown.WDG_SessionCountdown_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SessionCountdown_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SessionCountdown_C", "PreConstruct");

	Params::WDG_SessionCountdown_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SessionCountdown.WDG_SessionCountdown_C.Destruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_SessionCountdown_C::Destruct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SessionCountdown_C", "Destruct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SessionCountdown.WDG_SessionCountdown_C.OnEverySecond
// (BlueprintCallable, BlueprintEvent)

void UWDG_SessionCountdown_C::OnEverySecond()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SessionCountdown_C", "OnEverySecond");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SessionCountdown.WDG_SessionCountdown_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_SessionCountdown_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SessionCountdown_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}

}

