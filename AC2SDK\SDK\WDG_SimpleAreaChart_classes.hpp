﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SimpleAreaChart

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SimpleAreaChart.WDG_SimpleAreaChart_C
// 0x0018 (0x02A0 - 0x0288)
class UWDG_SimpleAreaChart_C final : public USimpleAreaChart
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0288(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UCanvasPanel*                           DrawingCanvas;                                     // 0x0290(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class USimpleAreaChart*                       selfRef;                                           // 0x0298(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_SimpleAreaChart(int32 EntryPoint);
	void Construct();

	void OnPaint(struct FPaintContext& Context) const;

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SimpleAreaChart_C">();
	}
	static class UWDG_SimpleAreaChart_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SimpleAreaChart_C>();
	}
};
static_assert(alignof(UWDG_SimpleAreaChart_C) == 0x000008, "Wrong alignment on UWDG_SimpleAreaChart_C");
static_assert(sizeof(UWDG_SimpleAreaChart_C) == 0x0002A0, "Wrong size on UWDG_SimpleAreaChart_C");
static_assert(offsetof(UWDG_SimpleAreaChart_C, UberGraphFrame) == 0x000288, "Member 'UWDG_SimpleAreaChart_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SimpleAreaChart_C, DrawingCanvas) == 0x000290, "Member 'UWDG_SimpleAreaChart_C::DrawingCanvas' has a wrong offset!");
static_assert(offsetof(UWDG_SimpleAreaChart_C, selfRef) == 0x000298, "Member 'UWDG_SimpleAreaChart_C::selfRef' has a wrong offset!");

}

