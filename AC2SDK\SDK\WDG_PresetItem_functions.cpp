﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PresetItem

#include "Basic.hpp"

#include "WDG_PresetItem_classes.hpp"
#include "WDG_PresetItem_parameters.hpp"


namespace SDK
{

// Function WDG_PresetItem.WDG_PresetItem_C.ExecuteUbergraph_WDG_PresetItem
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_PresetItem_C::ExecuteUbergraph_WDG_PresetItem(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PresetItem_C", "ExecuteUbergraph_WDG_PresetItem");

	Params::WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PresetItem.WDG_PresetItem_C.BndEvt__btnDel_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_PresetItem_C::BndEvt__btnDel_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PresetItem_C", "BndEvt__btnDel_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_PresetItem.WDG_PresetItem_C.BndEvt__btnPlay_K2Node_ComponentBoundEvent_5_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_PresetItem_C::BndEvt__btnPlay_K2Node_ComponentBoundEvent_5_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PresetItem_C", "BndEvt__btnPlay_K2Node_ComponentBoundEvent_5_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_PresetItem.WDG_PresetItem_C.BP_SetHighlight
// (Event, Public, BlueprintEvent)
// Parameters:
// bool                                    highlighted                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_PresetItem_C::BP_SetHighlight(bool highlighted)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PresetItem_C", "BP_SetHighlight");

	Params::WDG_PresetItem_C_BP_SetHighlight Parms{};

	Parms.highlighted = highlighted;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PresetItem.WDG_PresetItem_C.BP_MouseOver
// (Event, Public, BlueprintEvent)

void UWDG_PresetItem_C::BP_MouseOver()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PresetItem_C", "BP_MouseOver");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_PresetItem.WDG_PresetItem_C.BP_MouseLeave
// (Event, Public, BlueprintEvent)

void UWDG_PresetItem_C::BP_MouseLeave()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PresetItem_C", "BP_MouseLeave");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_PresetItem.WDG_PresetItem_C.OnAddedToFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_PresetItem_C::OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PresetItem_C", "OnAddedToFocusPath");

	Params::WDG_PresetItem_C_OnAddedToFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PresetItem.WDG_PresetItem_C.OnRemovedFromFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_PresetItem_C::OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PresetItem_C", "OnRemovedFromFocusPath");

	Params::WDG_PresetItem_C_OnRemovedFromFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PresetItem.WDG_PresetItem_C.OnMouseFocus_Event_0
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UAcPanelBase*                     panel                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    mouse_over                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_PresetItem_C::OnMouseFocus_Event_0(class UAcPanelBase* panel, bool mouse_over)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PresetItem_C", "OnMouseFocus_Event_0");

	Params::WDG_PresetItem_C_OnMouseFocus_Event_0 Parms{};

	Parms.panel = panel;
	Parms.mouse_over = mouse_over;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PresetItem.WDG_PresetItem_C.OnInitialized
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_PresetItem_C::OnInitialized()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PresetItem_C", "OnInitialized");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_PresetItem.WDG_PresetItem_C.BndEvt__btnDel_K2Node_ComponentBoundEvent_1_OnAcPanelMouseEvent__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UAcPanelBase*                     panel                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    mouse_over                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_PresetItem_C::BndEvt__btnDel_K2Node_ComponentBoundEvent_1_OnAcPanelMouseEvent__DelegateSignature(class UAcPanelBase* panel, bool mouse_over)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PresetItem_C", "BndEvt__btnDel_K2Node_ComponentBoundEvent_1_OnAcPanelMouseEvent__DelegateSignature");

	Params::WDG_PresetItem_C_BndEvt__btnDel_K2Node_ComponentBoundEvent_1_OnAcPanelMouseEvent__DelegateSignature Parms{};

	Parms.panel = panel;
	Parms.mouse_over = mouse_over;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PresetItem.WDG_PresetItem_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_PresetItem_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PresetItem_C", "PreConstruct");

	Params::WDG_PresetItem_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PresetItem.WDG_PresetItem_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_PresetItem_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PresetItem_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}

}

