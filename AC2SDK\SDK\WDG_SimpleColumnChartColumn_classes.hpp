﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SimpleColumnChartColumn

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"
#include "UMG_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SimpleColumnChartColumn.WDG_SimpleColumnChartColumn_C
// 0x0038 (0x0618 - 0x05E0)
class UWDG_SimpleColumnChartColumn_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UBorder*                                Border_BarFill;                                    // 0x05E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoD<PERSON>ru<PERSON>, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                Border_NotHighlighted;                             // 0x05F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UGridPanel*                             GridPanel_BarHeight;                               // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	TMulticastInlineDelegate<void(class UWDG_SimpleColumnChartColumn_C* selectedColumn)> OnColumnBarSelected; // 0x0600(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	int32                                         columnBarId;                                       // 0x0610(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CurrentValue;                                      // 0x0614(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_SimpleColumnChartColumn(int32 EntryPoint);
	void OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent);
	void OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent);
	void BP_MouseOver();
	void BP_MouseLeave();
	void SetValue(float barValue, int32 barId, bool isBarNavigable);
	void SetBarHighlighted(bool isFocused);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SimpleColumnChartColumn_C">();
	}
	static class UWDG_SimpleColumnChartColumn_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SimpleColumnChartColumn_C>();
	}
};
static_assert(alignof(UWDG_SimpleColumnChartColumn_C) == 0x000008, "Wrong alignment on UWDG_SimpleColumnChartColumn_C");
static_assert(sizeof(UWDG_SimpleColumnChartColumn_C) == 0x000618, "Wrong size on UWDG_SimpleColumnChartColumn_C");
static_assert(offsetof(UWDG_SimpleColumnChartColumn_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_SimpleColumnChartColumn_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SimpleColumnChartColumn_C, Border_BarFill) == 0x0005E8, "Member 'UWDG_SimpleColumnChartColumn_C::Border_BarFill' has a wrong offset!");
static_assert(offsetof(UWDG_SimpleColumnChartColumn_C, Border_NotHighlighted) == 0x0005F0, "Member 'UWDG_SimpleColumnChartColumn_C::Border_NotHighlighted' has a wrong offset!");
static_assert(offsetof(UWDG_SimpleColumnChartColumn_C, GridPanel_BarHeight) == 0x0005F8, "Member 'UWDG_SimpleColumnChartColumn_C::GridPanel_BarHeight' has a wrong offset!");
static_assert(offsetof(UWDG_SimpleColumnChartColumn_C, OnColumnBarSelected) == 0x000600, "Member 'UWDG_SimpleColumnChartColumn_C::OnColumnBarSelected' has a wrong offset!");
static_assert(offsetof(UWDG_SimpleColumnChartColumn_C, columnBarId) == 0x000610, "Member 'UWDG_SimpleColumnChartColumn_C::columnBarId' has a wrong offset!");
static_assert(offsetof(UWDG_SimpleColumnChartColumn_C, CurrentValue) == 0x000614, "Member 'UWDG_SimpleColumnChartColumn_C::CurrentValue' has a wrong offset!");

}

