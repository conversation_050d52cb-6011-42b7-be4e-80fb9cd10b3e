﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomMaterialColorSelector

#include "Basic.hpp"

#include "WDG_ShowroomMaterialColorSelector_classes.hpp"
#include "WDG_ShowroomMaterialColorSelector_parameters.hpp"


namespace SDK
{

// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.ExecuteUbergraph_WDG_ShowroomMaterialColorSelector
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomMaterialColorSelector_C::ExecuteUbergraph_WDG_ShowroomMaterialColorSelector(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomMaterialColorSelector_C", "ExecuteUbergraph_WDG_ShowroomMaterialColorSelector");

	Params::WDG_ShowroomMaterialColorSelector_C_ExecuteUbergraph_WDG_ShowroomMaterialColorSelector Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.BndEvt__sliderMaterial_K2Node_ComponentBoundEvent_3_OnAcPanelFocusEvent__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UAcPanelBase*                     panel                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomMaterialColorSelector_C::BndEvt__sliderMaterial_K2Node_ComponentBoundEvent_3_OnAcPanelFocusEvent__DelegateSignature(class UAcPanelBase* panel)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomMaterialColorSelector_C", "BndEvt__sliderMaterial_K2Node_ComponentBoundEvent_3_OnAcPanelFocusEvent__DelegateSignature");

	Params::WDG_ShowroomMaterialColorSelector_C_BndEvt__sliderMaterial_K2Node_ComponentBoundEvent_3_OnAcPanelFocusEvent__DelegateSignature Parms{};

	Parms.panel = panel;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.BndEvt__tileColor_K2Node_ComponentBoundEvent_0_OnAcPanelFocusEvent__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UAcPanelBase*                     panel                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomMaterialColorSelector_C::BndEvt__tileColor_K2Node_ComponentBoundEvent_0_OnAcPanelFocusEvent__DelegateSignature(class UAcPanelBase* panel)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomMaterialColorSelector_C", "BndEvt__tileColor_K2Node_ComponentBoundEvent_0_OnAcPanelFocusEvent__DelegateSignature");

	Params::WDG_ShowroomMaterialColorSelector_C_BndEvt__tileColor_K2Node_ComponentBoundEvent_0_OnAcPanelFocusEvent__DelegateSignature Parms{};

	Parms.panel = panel;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.BndEvt__Color_K2Node_ComponentBoundEvent_2_OnColorItemForward__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UWDG_ShowroomTileItemHorizontal_C*Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   ColorCode_0                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FLinearColor&              Color                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomMaterialColorSelector_C::BndEvt__Color_K2Node_ComponentBoundEvent_2_OnColorItemForward__DelegateSignature(class UWDG_ShowroomTileItemHorizontal_C* Sender, int32 ColorCode_0, const struct FLinearColor& Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomMaterialColorSelector_C", "BndEvt__Color_K2Node_ComponentBoundEvent_2_OnColorItemForward__DelegateSignature");

	Params::WDG_ShowroomMaterialColorSelector_C_BndEvt__Color_K2Node_ComponentBoundEvent_2_OnColorItemForward__DelegateSignature Parms{};

	Parms.Sender = Sender;
	Parms.ColorCode_0 = ColorCode_0;
	Parms.Color = std::move(Color);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.BndEvt__Material_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UGenericSelectorItem*             Source                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_index                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_value                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomMaterialColorSelector_C::BndEvt__Material_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature(class UGenericSelectorItem* Source, int32 current_index, int32 current_value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomMaterialColorSelector_C", "BndEvt__Material_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature");

	Params::WDG_ShowroomMaterialColorSelector_C_BndEvt__Material_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature Parms{};

	Parms.Source = Source;
	Parms.current_index = current_index;
	Parms.current_value = current_value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ShowroomMaterialColorSelector_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomMaterialColorSelector_C", "PreConstruct");

	Params::WDG_ShowroomMaterialColorSelector_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.SetColor
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              InColorAndOpacity                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomMaterialColorSelector_C::SetColor(const struct FLinearColor& InColorAndOpacity)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomMaterialColorSelector_C", "SetColor");

	Params::WDG_ShowroomMaterialColorSelector_C_SetColor Parms{};

	Parms.InColorAndOpacity = std::move(InColorAndOpacity);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.SetColorByCode
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   ColorCode_0                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomMaterialColorSelector_C::SetColorByCode(int32 ColorCode_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomMaterialColorSelector_C", "SetColorByCode");

	Params::WDG_ShowroomMaterialColorSelector_C_SetColorByCode Parms{};

	Parms.ColorCode_0 = ColorCode_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.SetMaterials
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const TMap<int32, class FText>&         Source_Materials                                       (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_ShowroomMaterialColorSelector_C::SetMaterials(const TMap<int32, class FText>& Source_Materials)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomMaterialColorSelector_C", "SetMaterials");

	Params::WDG_ShowroomMaterialColorSelector_C_SetMaterials Parms{};

	Parms.Source_Materials = std::move(Source_Materials);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.UpdateMaterialSlider
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomMaterialColorSelector_C::UpdateMaterialSlider()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomMaterialColorSelector_C", "UpdateMaterialSlider");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.GetSelectedMaterial
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// int32                                   ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

int32 UWDG_ShowroomMaterialColorSelector_C::GetSelectedMaterial()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomMaterialColorSelector_C", "GetSelectedMaterial");

	Params::WDG_ShowroomMaterialColorSelector_C_GetSelectedMaterial Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.GetSelectedColor
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// int32                                   ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

int32 UWDG_ShowroomMaterialColorSelector_C::GetSelectedColor()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomMaterialColorSelector_C", "GetSelectedColor");

	Params::WDG_ShowroomMaterialColorSelector_C_GetSelectedColor Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.SetMaterial
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   MaterialKey_0                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    Supress_Event                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ShowroomMaterialColorSelector_C::SetMaterial(int32 MaterialKey_0, bool Supress_Event)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomMaterialColorSelector_C", "SetMaterial");

	Params::WDG_ShowroomMaterialColorSelector_C_SetMaterial Parms{};

	Parms.MaterialKey_0 = MaterialKey_0;
	Parms.Supress_Event = Supress_Event;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C.SetMaterialAndColor
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class FName                             MaterialKey_0                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   ColorCode_0                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomMaterialColorSelector_C::SetMaterialAndColor(class FName MaterialKey_0, int32 ColorCode_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomMaterialColorSelector_C", "SetMaterialAndColor");

	Params::WDG_ShowroomMaterialColorSelector_C_SetMaterialAndColor Parms{};

	Parms.MaterialKey_0 = MaterialKey_0;
	Parms.ColorCode_0 = ColorCode_0;

	UObject::ProcessEvent(Func, &Parms);
}

}

