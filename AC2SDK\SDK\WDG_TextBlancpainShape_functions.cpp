﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TextBlancpainShape

#include "Basic.hpp"

#include "WDG_TextBlancpainShape_classes.hpp"
#include "WDG_TextBlancpainShape_parameters.hpp"


namespace SDK
{

// Function WDG_TextBlancpainShape.WDG_TextBlancpainShape_C.SetTitleText
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const class FText&                      NewParam                                               (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_TextBlancpainShape_C::SetTitleText(const class FText& NewParam)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TextBlancpainShape_C", "SetTitleText");

	Params::WDG_TextBlancpainShape_C_SetTitleText Parms{};

	Parms.NewParam = std::move(NewParam);

	UObject::ProcessEvent(Func, &Parms);
}

}

