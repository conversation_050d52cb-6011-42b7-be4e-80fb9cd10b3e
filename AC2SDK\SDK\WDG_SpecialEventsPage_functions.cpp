﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SpecialEventsPage

#include "Basic.hpp"

#include "WDG_SpecialEventsPage_classes.hpp"
#include "WDG_SpecialEventsPage_parameters.hpp"


namespace SDK
{

// Function WDG_SpecialEventsPage.WDG_SpecialEventsPage_C.ExecuteUbergraph_WDG_SpecialEventsPage
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SpecialEventsPage_C::ExecuteUbergraph_WDG_SpecialEventsPage(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventsPage_C", "ExecuteUbergraph_WDG_SpecialEventsPage");

	Params::WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEventsPage.WDG_SpecialEventsPage_C.BndEvt__popupDLC_K2Node_ComponentBoundEvent_7_OnYes__DelegateSignature
// (BlueprintEvent)

void UWDG_SpecialEventsPage_C::BndEvt__popupDLC_K2Node_ComponentBoundEvent_7_OnYes__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventsPage_C", "BndEvt__popupDLC_K2Node_ComponentBoundEvent_7_OnYes__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEventsPage.WDG_SpecialEventsPage_C.OnPresetSelected
// (Event, Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FSpecialEventPreset&       Preset                                                 (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_SpecialEventsPage_C::OnPresetSelected(const struct FSpecialEventPreset& Preset)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventsPage_C", "OnPresetSelected");

	Params::WDG_SpecialEventsPage_C_OnPresetSelected Parms{};

	Parms.Preset = std::move(Preset);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEventsPage.WDG_SpecialEventsPage_C.BndEvt__btnNextPage_K2Node_ComponentBoundEvent_5_OnButtonHoverEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_SpecialEventsPage_C::BndEvt__btnNextPage_K2Node_ComponentBoundEvent_5_OnButtonHoverEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventsPage_C", "BndEvt__btnNextPage_K2Node_ComponentBoundEvent_5_OnButtonHoverEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEventsPage.WDG_SpecialEventsPage_C.BndEvt__btnNextPage_K2Node_ComponentBoundEvent_4_OnButtonHoverEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_SpecialEventsPage_C::BndEvt__btnNextPage_K2Node_ComponentBoundEvent_4_OnButtonHoverEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventsPage_C", "BndEvt__btnNextPage_K2Node_ComponentBoundEvent_4_OnButtonHoverEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEventsPage.WDG_SpecialEventsPage_C.BndEvt__btnPreviousPage_K2Node_ComponentBoundEvent_3_OnButtonHoverEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_SpecialEventsPage_C::BndEvt__btnPreviousPage_K2Node_ComponentBoundEvent_3_OnButtonHoverEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventsPage_C", "BndEvt__btnPreviousPage_K2Node_ComponentBoundEvent_3_OnButtonHoverEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEventsPage.WDG_SpecialEventsPage_C.BndEvt__btnPreviousPage_K2Node_ComponentBoundEvent_2_OnButtonHoverEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_SpecialEventsPage_C::BndEvt__btnPreviousPage_K2Node_ComponentBoundEvent_2_OnButtonHoverEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventsPage_C", "BndEvt__btnPreviousPage_K2Node_ComponentBoundEvent_2_OnButtonHoverEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEventsPage.WDG_SpecialEventsPage_C.BndEvt__btnPreviousPage_K2Node_ComponentBoundEvent_1_OnButtonClickedEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_SpecialEventsPage_C::BndEvt__btnPreviousPage_K2Node_ComponentBoundEvent_1_OnButtonClickedEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventsPage_C", "BndEvt__btnPreviousPage_K2Node_ComponentBoundEvent_1_OnButtonClickedEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEventsPage.WDG_SpecialEventsPage_C.BndEvt__btnNextPage_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_SpecialEventsPage_C::BndEvt__btnNextPage_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventsPage_C", "BndEvt__btnNextPage_K2Node_ComponentBoundEvent_0_OnButtonClickedEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEventsPage.WDG_SpecialEventsPage_C.BndEvt__Button_94_K2Node_ComponentBoundEvent_35_OnButtonClickedEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_SpecialEventsPage_C::BndEvt__Button_94_K2Node_ComponentBoundEvent_35_OnButtonClickedEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventsPage_C", "BndEvt__Button_94_K2Node_ComponentBoundEvent_35_OnButtonClickedEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEventsPage.WDG_SpecialEventsPage_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_SpecialEventsPage_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventsPage_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}

}

