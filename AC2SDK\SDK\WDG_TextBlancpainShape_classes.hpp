﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TextBlancpainShape

#include "Basic.hpp"

#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_TextBlancpainShape.WDG_TextBlancpainShape_C
// 0x0010 (0x0270 - 0x0260)
class UWDG_TextBlancpainShape_C final : public UUserWidget
{
public:
	class UImage*                                 Image_114;                                         // 0x0260(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtTitle;                                          // 0x0268(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoD<PERSON>ructor, PersistentInstance, HasGetValueTypeHash)

public:
	void SetTitleText(const class FText& NewParam);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_TextBlancpainShape_C">();
	}
	static class UWDG_TextBlancpainShape_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_TextBlancpainShape_C>();
	}
};
static_assert(alignof(UWDG_TextBlancpainShape_C) == 0x000008, "Wrong alignment on UWDG_TextBlancpainShape_C");
static_assert(sizeof(UWDG_TextBlancpainShape_C) == 0x000270, "Wrong size on UWDG_TextBlancpainShape_C");
static_assert(offsetof(UWDG_TextBlancpainShape_C, Image_114) == 0x000260, "Member 'UWDG_TextBlancpainShape_C::Image_114' has a wrong offset!");
static_assert(offsetof(UWDG_TextBlancpainShape_C, txtTitle) == 0x000268, "Member 'UWDG_TextBlancpainShape_C::txtTitle' has a wrong offset!");

}

