﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SetupPage

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SetupPage.WDG_SetupPage_C
// 0x03E8 (0x1F90 - 0x1BA8)
class UWDG_SetupPage_C final : public USetupPage
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x1BA8(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       PageSwitch;                                        // 0x1BB0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       PageFade;                                          // 0x1BB8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWDG_SetupGenericPanel_C*               AdvancedPanel;                                     // 0x1BC0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWidgetSwitcher*                        AeroCarSwitcher;                                   // 0x1BC8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_FooterButton_C*                    Back;                                              // 0x1BD0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SetupGenericPanel_C*               BasicPanel;                                        // 0x1BD8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  BumpFastLF;                                        // 0x1BE0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  BumpFastLR;                                        // 0x1BE8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  BumpFastRF;                                        // 0x1BF0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  BumpFastRR;                                        // 0x1BF8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  BumpSlowLF;                                        // 0x1C00(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  BumpSlowLR;                                        // 0x1C08(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  BumpSlowRF;                                        // 0x1C10(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  BumpSlowRR;                                        // 0x1C18(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  BumpStopRangeDnLF;                                 // 0x1C20(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  BumpStopRangeDnLR;                                 // 0x1C28(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  BumpStopRangeDnRF;                                 // 0x1C30(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  BumpStopRangeDnRR;                                 // 0x1C38(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  BumpStopRangeUpLF;                                 // 0x1C40(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  BumpStopRangeUpLR;                                 // 0x1C48(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  BumpStopRangeUpRF;                                 // 0x1C50(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  BumpStopRangeUpRR;                                 // 0x1C58(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  BumpStopRateDnLF;                                  // 0x1C60(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  BumpStopRateDnLR;                                  // 0x1C68(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  BumpStopRateDnRF;                                  // 0x1C70(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  BumpStopRateDnRR;                                  // 0x1C78(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  BumpStopRateUpLF;                                  // 0x1C80(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  BumpStopRateUpLR;                                  // 0x1C88(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  BumpStopRateUpRF;                                  // 0x1C90(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  BumpStopRateUpRR;                                  // 0x1C98(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  CamberLF;                                          // 0x1CA0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  CamberLR;                                          // 0x1CA8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  CamberRF;                                          // 0x1CB0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  CamberRR;                                          // 0x1CB8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCircularThrobber*                      CircularThrobber_168;                              // 0x1CC0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  Coast;                                             // 0x1CC8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_FooterButton_C*                    Confirm;                                           // 0x1CD0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           cvsMainLeft;                                       // 0x1CD8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWidgetSwitcher*                        DampersCarSwitcher;                                // 0x1CE0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWidgetSwitcher*                        ElectronicCarSwitcher;                             // 0x1CE8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ScalableButton_C*                  ElementAeroBalanceFront;                           // 0x1CF0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ScalableButton_C*                  ElementAeroBalanceRear;                            // 0x1CF8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ScalableButton_C*                  ElementDampersLF;                                  // 0x1D00(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ScalableButton_C*                  ElementDampersLR;                                  // 0x1D08(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ScalableButton_C*                  ElementDampersRF;                                  // 0x1D10(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ScalableButton_C*                  ElementDampersRR;                                  // 0x1D18(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ScalableButton_C*                  ElementElectronics;                                // 0x1D20(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ScalableButton_C*                  ElementFuelStrategy;                               // 0x1D28(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ScalableButton_C*                  ElementFuelTest;                                   // 0x1D30(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ScalableButton_C*                  ElementMechBalanceFront;                           // 0x1D38(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ScalableButton_C*                  ElementMechBalanceLF;                              // 0x1D40(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ScalableButton_C*                  ElementMechBalanceLR;                              // 0x1D48(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ScalableButton_C*                  ElementMechBalanceRear;                            // 0x1D50(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ScalableButton_C*                  ElementMechBalanceRF;                              // 0x1D58(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ScalableButton_C*                  ElementMechBalanceRR;                              // 0x1D60(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ScalableButton_C*                  ElementPitStrategy;                                // 0x1D68(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ScalableButton_C*                  ElementTyreLF;                                     // 0x1D70(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ScalableButton_C*                  ElementTyreLR;                                     // 0x1D78(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ScalableButton_C*                  ElementTyreRR;                                     // 0x1D80(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ScalableButton_C*                  ElementTyrerRF;                                    // 0x1D88(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_FilePanel_C*                       filePanel;                                         // 0x1D90(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Footer_C*                          Footer;                                            // 0x1D98(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Header_C*                          Header;                                            // 0x1DA0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 HoverImageBox;                                     // 0x1DA8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_2;                                           // 0x1DB0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_84;                                          // 0x1DB8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_143;                                         // 0x1DC0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgBentley_Continental_GT3_2016_01;                // 0x1DC8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgBentley_Continental_GT3_2018_01;                // 0x1DD0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgBMW_M6_GT3_01;                                  // 0x1DD8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgFerrari_488_GT3_01;                             // 0x1DE0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgFrontPage;                                      // 0x1DE8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgHorizontalBackColor;                            // 0x1DF0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgLamborghini_Huracan_GT3_01;                     // 0x1DF8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgMclaren_650s_GT3_01;                            // 0x1E00(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgMercedes_AMG_GT3_01;                            // 0x1E08(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgNissan_GT_R_Nismo_GT3_2017_01;                  // 0x1E10(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgNissan_GT_R_Nismo_GT3_2018_01;                  // 0x1E18(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgPorsche_991II_GT3_Cup_01;                       // 0x1E20(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgSettingBackground;                              // 0x1E28(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_MainSelectorFitToParent_C*         MainSelector;                                      // 0x1E30(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomModal_C*                   modalOverlay;                                      // 0x1E38(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 NormalImageBox;                                    // 0x1E40(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Page_C*                            PageBase;                                          // 0x1E48(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  power;                                             // 0x1E50(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWidgetSwitcher*                        PresetCarPreviewSwitch;                            // 0x1E58(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  PressureStrategyLF;                                // 0x1E60(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  PressureStrategyLR;                                // 0x1E68(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  PressureStrategyRF;                                // 0x1E70(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  PressureStrategyRR;                                // 0x1E78(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  ReboundFastLF;                                     // 0x1E80(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  ReboundFastLR;                                     // 0x1E88(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  ReboundFastRF;                                     // 0x1E90(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  ReboundFastRR;                                     // 0x1E98(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  ReboundSlowLF;                                     // 0x1EA0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  ReboundSlowLR;                                     // 0x1EA8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  ReboundSlowRF;                                     // 0x1EB0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  ReboundSlowRR;                                     // 0x1EB8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  RideHeightLF;                                      // 0x1EC0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  RideHeightLR;                                      // 0x1EC8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  RideHeightRF;                                      // 0x1ED0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  RideHeightRR;                                      // 0x1ED8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ScalableSetupPresetButton_C*       SafePreset;                                        // 0x1EE0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  ToeLF;                                             // 0x1EE8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  ToeLR;                                             // 0x1EF0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  ToeRF;                                             // 0x1EF8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  ToeRR;                                             // 0x1F00(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWidgetSwitcher*                        TyreCarSwitch;                                     // 0x1F08(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  TyrePressureLF;                                    // 0x1F10(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  TyrePressureLR;                                    // 0x1F18(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  TyrePressureRF;                                    // 0x1F20(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  TyrePressureRR;                                    // 0x1F28(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_HelpInMenu_C*                      WDG_HelpInMenu;                                    // 0x1F30(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_OptionsListWidget_C*               WDG_OptionsListWidget;                             // 0x1F38(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ScalableSetupPresetButton_C*       WDG_ScalableSetupPresetButton;                     // 0x1F40(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ScalableSetupPresetButton_C*       WDG_ScalableSetupPresetButton_56;                  // 0x1F48(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SessionCountdown_C*                WDG_SessionCountdown_C_1;                          // 0x1F50(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SetupElectronicsInfo_C*            WDG_SetupElectronicsInfo;                          // 0x1F58(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_UISessionTimer_C*                  WDG_UISessionTimer;                                // 0x1F60(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_WeatherForecast_C*                 WDG_WeatherForecast;                               // 0x1F68(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  WheelRateLF;                                       // 0x1F70(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  WheelRateLR;                                       // 0x1F78(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  WheelRateRF;                                       // 0x1F80(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericSelectorItemFitToParent_C*  WheelRateRR;                                       // 0x1F88(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_SetupPage(int32 EntryPoint);
	void BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature(class FName Filename, const class FString& DisplayName);
	void BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnDeleteUserPreset__DelegateSignature(const class FString& Filename);
	void BndEvt__filePanel_K2Node_ComponentBoundEvent_3_OnSaveUserPreset__DelegateSignature(const class FString& Filename, bool ExistingFile);
	void BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_1_OnHide__DelegateSignature(class UAcPanelBase* CallingPanel, bool Cancelled);
	void BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_0_OnShow__DelegateSignature(class UAcPanelBase* CallingPanel);
	void OnOpenFileDialog();
	void Construct();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SetupPage_C">();
	}
	static class UWDG_SetupPage_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SetupPage_C>();
	}
};
static_assert(alignof(UWDG_SetupPage_C) == 0x000008, "Wrong alignment on UWDG_SetupPage_C");
static_assert(sizeof(UWDG_SetupPage_C) == 0x001F90, "Wrong size on UWDG_SetupPage_C");
static_assert(offsetof(UWDG_SetupPage_C, UberGraphFrame) == 0x001BA8, "Member 'UWDG_SetupPage_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, PageSwitch) == 0x001BB0, "Member 'UWDG_SetupPage_C::PageSwitch' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, PageFade) == 0x001BB8, "Member 'UWDG_SetupPage_C::PageFade' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, AdvancedPanel) == 0x001BC0, "Member 'UWDG_SetupPage_C::AdvancedPanel' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, AeroCarSwitcher) == 0x001BC8, "Member 'UWDG_SetupPage_C::AeroCarSwitcher' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, Back) == 0x001BD0, "Member 'UWDG_SetupPage_C::Back' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BasicPanel) == 0x001BD8, "Member 'UWDG_SetupPage_C::BasicPanel' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BumpFastLF) == 0x001BE0, "Member 'UWDG_SetupPage_C::BumpFastLF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BumpFastLR) == 0x001BE8, "Member 'UWDG_SetupPage_C::BumpFastLR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BumpFastRF) == 0x001BF0, "Member 'UWDG_SetupPage_C::BumpFastRF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BumpFastRR) == 0x001BF8, "Member 'UWDG_SetupPage_C::BumpFastRR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BumpSlowLF) == 0x001C00, "Member 'UWDG_SetupPage_C::BumpSlowLF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BumpSlowLR) == 0x001C08, "Member 'UWDG_SetupPage_C::BumpSlowLR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BumpSlowRF) == 0x001C10, "Member 'UWDG_SetupPage_C::BumpSlowRF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BumpSlowRR) == 0x001C18, "Member 'UWDG_SetupPage_C::BumpSlowRR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BumpStopRangeDnLF) == 0x001C20, "Member 'UWDG_SetupPage_C::BumpStopRangeDnLF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BumpStopRangeDnLR) == 0x001C28, "Member 'UWDG_SetupPage_C::BumpStopRangeDnLR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BumpStopRangeDnRF) == 0x001C30, "Member 'UWDG_SetupPage_C::BumpStopRangeDnRF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BumpStopRangeDnRR) == 0x001C38, "Member 'UWDG_SetupPage_C::BumpStopRangeDnRR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BumpStopRangeUpLF) == 0x001C40, "Member 'UWDG_SetupPage_C::BumpStopRangeUpLF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BumpStopRangeUpLR) == 0x001C48, "Member 'UWDG_SetupPage_C::BumpStopRangeUpLR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BumpStopRangeUpRF) == 0x001C50, "Member 'UWDG_SetupPage_C::BumpStopRangeUpRF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BumpStopRangeUpRR) == 0x001C58, "Member 'UWDG_SetupPage_C::BumpStopRangeUpRR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BumpStopRateDnLF) == 0x001C60, "Member 'UWDG_SetupPage_C::BumpStopRateDnLF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BumpStopRateDnLR) == 0x001C68, "Member 'UWDG_SetupPage_C::BumpStopRateDnLR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BumpStopRateDnRF) == 0x001C70, "Member 'UWDG_SetupPage_C::BumpStopRateDnRF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BumpStopRateDnRR) == 0x001C78, "Member 'UWDG_SetupPage_C::BumpStopRateDnRR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BumpStopRateUpLF) == 0x001C80, "Member 'UWDG_SetupPage_C::BumpStopRateUpLF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BumpStopRateUpLR) == 0x001C88, "Member 'UWDG_SetupPage_C::BumpStopRateUpLR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BumpStopRateUpRF) == 0x001C90, "Member 'UWDG_SetupPage_C::BumpStopRateUpRF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, BumpStopRateUpRR) == 0x001C98, "Member 'UWDG_SetupPage_C::BumpStopRateUpRR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, CamberLF) == 0x001CA0, "Member 'UWDG_SetupPage_C::CamberLF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, CamberLR) == 0x001CA8, "Member 'UWDG_SetupPage_C::CamberLR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, CamberRF) == 0x001CB0, "Member 'UWDG_SetupPage_C::CamberRF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, CamberRR) == 0x001CB8, "Member 'UWDG_SetupPage_C::CamberRR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, CircularThrobber_168) == 0x001CC0, "Member 'UWDG_SetupPage_C::CircularThrobber_168' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, Coast) == 0x001CC8, "Member 'UWDG_SetupPage_C::Coast' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, Confirm) == 0x001CD0, "Member 'UWDG_SetupPage_C::Confirm' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, cvsMainLeft) == 0x001CD8, "Member 'UWDG_SetupPage_C::cvsMainLeft' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, DampersCarSwitcher) == 0x001CE0, "Member 'UWDG_SetupPage_C::DampersCarSwitcher' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ElectronicCarSwitcher) == 0x001CE8, "Member 'UWDG_SetupPage_C::ElectronicCarSwitcher' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ElementAeroBalanceFront) == 0x001CF0, "Member 'UWDG_SetupPage_C::ElementAeroBalanceFront' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ElementAeroBalanceRear) == 0x001CF8, "Member 'UWDG_SetupPage_C::ElementAeroBalanceRear' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ElementDampersLF) == 0x001D00, "Member 'UWDG_SetupPage_C::ElementDampersLF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ElementDampersLR) == 0x001D08, "Member 'UWDG_SetupPage_C::ElementDampersLR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ElementDampersRF) == 0x001D10, "Member 'UWDG_SetupPage_C::ElementDampersRF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ElementDampersRR) == 0x001D18, "Member 'UWDG_SetupPage_C::ElementDampersRR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ElementElectronics) == 0x001D20, "Member 'UWDG_SetupPage_C::ElementElectronics' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ElementFuelStrategy) == 0x001D28, "Member 'UWDG_SetupPage_C::ElementFuelStrategy' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ElementFuelTest) == 0x001D30, "Member 'UWDG_SetupPage_C::ElementFuelTest' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ElementMechBalanceFront) == 0x001D38, "Member 'UWDG_SetupPage_C::ElementMechBalanceFront' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ElementMechBalanceLF) == 0x001D40, "Member 'UWDG_SetupPage_C::ElementMechBalanceLF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ElementMechBalanceLR) == 0x001D48, "Member 'UWDG_SetupPage_C::ElementMechBalanceLR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ElementMechBalanceRear) == 0x001D50, "Member 'UWDG_SetupPage_C::ElementMechBalanceRear' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ElementMechBalanceRF) == 0x001D58, "Member 'UWDG_SetupPage_C::ElementMechBalanceRF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ElementMechBalanceRR) == 0x001D60, "Member 'UWDG_SetupPage_C::ElementMechBalanceRR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ElementPitStrategy) == 0x001D68, "Member 'UWDG_SetupPage_C::ElementPitStrategy' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ElementTyreLF) == 0x001D70, "Member 'UWDG_SetupPage_C::ElementTyreLF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ElementTyreLR) == 0x001D78, "Member 'UWDG_SetupPage_C::ElementTyreLR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ElementTyreRR) == 0x001D80, "Member 'UWDG_SetupPage_C::ElementTyreRR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ElementTyrerRF) == 0x001D88, "Member 'UWDG_SetupPage_C::ElementTyrerRF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, filePanel) == 0x001D90, "Member 'UWDG_SetupPage_C::filePanel' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, Footer) == 0x001D98, "Member 'UWDG_SetupPage_C::Footer' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, Header) == 0x001DA0, "Member 'UWDG_SetupPage_C::Header' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, HoverImageBox) == 0x001DA8, "Member 'UWDG_SetupPage_C::HoverImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, Image_2) == 0x001DB0, "Member 'UWDG_SetupPage_C::Image_2' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, Image_84) == 0x001DB8, "Member 'UWDG_SetupPage_C::Image_84' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, Image_143) == 0x001DC0, "Member 'UWDG_SetupPage_C::Image_143' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, imgBentley_Continental_GT3_2016_01) == 0x001DC8, "Member 'UWDG_SetupPage_C::imgBentley_Continental_GT3_2016_01' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, imgBentley_Continental_GT3_2018_01) == 0x001DD0, "Member 'UWDG_SetupPage_C::imgBentley_Continental_GT3_2018_01' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, imgBMW_M6_GT3_01) == 0x001DD8, "Member 'UWDG_SetupPage_C::imgBMW_M6_GT3_01' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, imgFerrari_488_GT3_01) == 0x001DE0, "Member 'UWDG_SetupPage_C::imgFerrari_488_GT3_01' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, imgFrontPage) == 0x001DE8, "Member 'UWDG_SetupPage_C::imgFrontPage' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, imgHorizontalBackColor) == 0x001DF0, "Member 'UWDG_SetupPage_C::imgHorizontalBackColor' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, imgLamborghini_Huracan_GT3_01) == 0x001DF8, "Member 'UWDG_SetupPage_C::imgLamborghini_Huracan_GT3_01' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, imgMclaren_650s_GT3_01) == 0x001E00, "Member 'UWDG_SetupPage_C::imgMclaren_650s_GT3_01' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, imgMercedes_AMG_GT3_01) == 0x001E08, "Member 'UWDG_SetupPage_C::imgMercedes_AMG_GT3_01' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, imgNissan_GT_R_Nismo_GT3_2017_01) == 0x001E10, "Member 'UWDG_SetupPage_C::imgNissan_GT_R_Nismo_GT3_2017_01' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, imgNissan_GT_R_Nismo_GT3_2018_01) == 0x001E18, "Member 'UWDG_SetupPage_C::imgNissan_GT_R_Nismo_GT3_2018_01' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, imgPorsche_991II_GT3_Cup_01) == 0x001E20, "Member 'UWDG_SetupPage_C::imgPorsche_991II_GT3_Cup_01' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, imgSettingBackground) == 0x001E28, "Member 'UWDG_SetupPage_C::imgSettingBackground' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, MainSelector) == 0x001E30, "Member 'UWDG_SetupPage_C::MainSelector' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, modalOverlay) == 0x001E38, "Member 'UWDG_SetupPage_C::modalOverlay' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, NormalImageBox) == 0x001E40, "Member 'UWDG_SetupPage_C::NormalImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, PageBase) == 0x001E48, "Member 'UWDG_SetupPage_C::PageBase' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, power) == 0x001E50, "Member 'UWDG_SetupPage_C::power' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, PresetCarPreviewSwitch) == 0x001E58, "Member 'UWDG_SetupPage_C::PresetCarPreviewSwitch' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, PressureStrategyLF) == 0x001E60, "Member 'UWDG_SetupPage_C::PressureStrategyLF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, PressureStrategyLR) == 0x001E68, "Member 'UWDG_SetupPage_C::PressureStrategyLR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, PressureStrategyRF) == 0x001E70, "Member 'UWDG_SetupPage_C::PressureStrategyRF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, PressureStrategyRR) == 0x001E78, "Member 'UWDG_SetupPage_C::PressureStrategyRR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ReboundFastLF) == 0x001E80, "Member 'UWDG_SetupPage_C::ReboundFastLF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ReboundFastLR) == 0x001E88, "Member 'UWDG_SetupPage_C::ReboundFastLR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ReboundFastRF) == 0x001E90, "Member 'UWDG_SetupPage_C::ReboundFastRF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ReboundFastRR) == 0x001E98, "Member 'UWDG_SetupPage_C::ReboundFastRR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ReboundSlowLF) == 0x001EA0, "Member 'UWDG_SetupPage_C::ReboundSlowLF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ReboundSlowLR) == 0x001EA8, "Member 'UWDG_SetupPage_C::ReboundSlowLR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ReboundSlowRF) == 0x001EB0, "Member 'UWDG_SetupPage_C::ReboundSlowRF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ReboundSlowRR) == 0x001EB8, "Member 'UWDG_SetupPage_C::ReboundSlowRR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, RideHeightLF) == 0x001EC0, "Member 'UWDG_SetupPage_C::RideHeightLF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, RideHeightLR) == 0x001EC8, "Member 'UWDG_SetupPage_C::RideHeightLR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, RideHeightRF) == 0x001ED0, "Member 'UWDG_SetupPage_C::RideHeightRF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, RideHeightRR) == 0x001ED8, "Member 'UWDG_SetupPage_C::RideHeightRR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, SafePreset) == 0x001EE0, "Member 'UWDG_SetupPage_C::SafePreset' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ToeLF) == 0x001EE8, "Member 'UWDG_SetupPage_C::ToeLF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ToeLR) == 0x001EF0, "Member 'UWDG_SetupPage_C::ToeLR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ToeRF) == 0x001EF8, "Member 'UWDG_SetupPage_C::ToeRF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, ToeRR) == 0x001F00, "Member 'UWDG_SetupPage_C::ToeRR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, TyreCarSwitch) == 0x001F08, "Member 'UWDG_SetupPage_C::TyreCarSwitch' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, TyrePressureLF) == 0x001F10, "Member 'UWDG_SetupPage_C::TyrePressureLF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, TyrePressureLR) == 0x001F18, "Member 'UWDG_SetupPage_C::TyrePressureLR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, TyrePressureRF) == 0x001F20, "Member 'UWDG_SetupPage_C::TyrePressureRF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, TyrePressureRR) == 0x001F28, "Member 'UWDG_SetupPage_C::TyrePressureRR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, WDG_HelpInMenu) == 0x001F30, "Member 'UWDG_SetupPage_C::WDG_HelpInMenu' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, WDG_OptionsListWidget) == 0x001F38, "Member 'UWDG_SetupPage_C::WDG_OptionsListWidget' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, WDG_ScalableSetupPresetButton) == 0x001F40, "Member 'UWDG_SetupPage_C::WDG_ScalableSetupPresetButton' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, WDG_ScalableSetupPresetButton_56) == 0x001F48, "Member 'UWDG_SetupPage_C::WDG_ScalableSetupPresetButton_56' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, WDG_SessionCountdown_C_1) == 0x001F50, "Member 'UWDG_SetupPage_C::WDG_SessionCountdown_C_1' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, WDG_SetupElectronicsInfo) == 0x001F58, "Member 'UWDG_SetupPage_C::WDG_SetupElectronicsInfo' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, WDG_UISessionTimer) == 0x001F60, "Member 'UWDG_SetupPage_C::WDG_UISessionTimer' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, WDG_WeatherForecast) == 0x001F68, "Member 'UWDG_SetupPage_C::WDG_WeatherForecast' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, WheelRateLF) == 0x001F70, "Member 'UWDG_SetupPage_C::WheelRateLF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, WheelRateLR) == 0x001F78, "Member 'UWDG_SetupPage_C::WheelRateLR' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, WheelRateRF) == 0x001F80, "Member 'UWDG_SetupPage_C::WheelRateRF' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPage_C, WheelRateRR) == 0x001F88, "Member 'UWDG_SetupPage_C::WheelRateRR' has a wrong offset!");

}

