﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_Subtitles

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_Subtitles.WDG_Subtitles_C
// 0x0060 (0x02C0 - 0x0260)
class UWDG_Subtitles_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0260(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UImage*                                 Image_87;                                          // 0x0268(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             SubText;                                           // 0x0270(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UAcGameInstance*                        AcGameInstance;                                    // 0x0278(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   SubtitleRow;                                       // 0x0280(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	struct FSubtitleRow                           Subtitle;                                          // 0x0288(0x0030)(Edit, BlueprintVisible, DisableEditOnInstance)
	int32                                         subCount;                                          // 0x02B8(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          firstSet;                                          // 0x02BC(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)

public:
	void ExecuteUbergraph_WDG_Subtitles(int32 EntryPoint);
	void Construct();
	void UpdateSubText(int32 playTimeMS);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_Subtitles_C">();
	}
	static class UWDG_Subtitles_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_Subtitles_C>();
	}
};
static_assert(alignof(UWDG_Subtitles_C) == 0x000008, "Wrong alignment on UWDG_Subtitles_C");
static_assert(sizeof(UWDG_Subtitles_C) == 0x0002C0, "Wrong size on UWDG_Subtitles_C");
static_assert(offsetof(UWDG_Subtitles_C, UberGraphFrame) == 0x000260, "Member 'UWDG_Subtitles_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_Subtitles_C, Image_87) == 0x000268, "Member 'UWDG_Subtitles_C::Image_87' has a wrong offset!");
static_assert(offsetof(UWDG_Subtitles_C, SubText) == 0x000270, "Member 'UWDG_Subtitles_C::SubText' has a wrong offset!");
static_assert(offsetof(UWDG_Subtitles_C, AcGameInstance) == 0x000278, "Member 'UWDG_Subtitles_C::AcGameInstance' has a wrong offset!");
static_assert(offsetof(UWDG_Subtitles_C, SubtitleRow) == 0x000280, "Member 'UWDG_Subtitles_C::SubtitleRow' has a wrong offset!");
static_assert(offsetof(UWDG_Subtitles_C, Subtitle) == 0x000288, "Member 'UWDG_Subtitles_C::Subtitle' has a wrong offset!");
static_assert(offsetof(UWDG_Subtitles_C, subCount) == 0x0002B8, "Member 'UWDG_Subtitles_C::subCount' has a wrong offset!");
static_assert(offsetof(UWDG_Subtitles_C, firstSet) == 0x0002BC, "Member 'UWDG_Subtitles_C::firstSet' has a wrong offset!");

}

