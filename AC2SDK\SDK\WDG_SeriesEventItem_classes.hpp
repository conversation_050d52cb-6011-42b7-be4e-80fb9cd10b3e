﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SeriesEventItem

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SeriesEventItem.WDG_SeriesEventItem_C
// 0x0080 (0x0660 - 0x05E0)
class UWDG_SeriesEventItem_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UBorder*                                background;                                        // 0x05E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnDel;                                            // 0x05F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnDown;                                           // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnUp;                                             // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         controls;                                          // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_41;                                      // 0x0610(0x0008)(ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class FText                                   CircuitName;                                       // 0x0618(0x0018)(Edit, BlueprintVisible, ExposeOnSpawn)
	class FName                                   PresetName;                                        // 0x0630(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	TMulticastInlineDelegate<void(class UWDG_SeriesEventItem_C* Sender)> OnRemoved;                  // 0x0638(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void(class UWDG_SeriesEventItem_C* Sender, EUINavigation Direction)> OnPositionChanged; // 0x0648(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	int32                                         CircuitIndex;                                      // 0x0658(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_SeriesEventItem(int32 EntryPoint);
	void BndEvt__WDG_SeriesEventItem_btnDown_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature(class UWDG_GenericBarItem_C* Sender);
	void BndEvt__WDG_SeriesEventItem_btnUp_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature(class UWDG_GenericBarItem_C* Sender);
	void BndEvt__WDG_SeriesEventItem_btnDel_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature(class UWDG_GenericBarItem_C* Sender);
	void OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent);
	void OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent);
	void BP_MouseLeave();
	void BP_MouseOver();
	void BP_SetHighlight(bool highlighted);
	void OnAfterConstruct();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SeriesEventItem_C">();
	}
	static class UWDG_SeriesEventItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SeriesEventItem_C>();
	}
};
static_assert(alignof(UWDG_SeriesEventItem_C) == 0x000008, "Wrong alignment on UWDG_SeriesEventItem_C");
static_assert(sizeof(UWDG_SeriesEventItem_C) == 0x000660, "Wrong size on UWDG_SeriesEventItem_C");
static_assert(offsetof(UWDG_SeriesEventItem_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_SeriesEventItem_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SeriesEventItem_C, background) == 0x0005E8, "Member 'UWDG_SeriesEventItem_C::background' has a wrong offset!");
static_assert(offsetof(UWDG_SeriesEventItem_C, btnDel) == 0x0005F0, "Member 'UWDG_SeriesEventItem_C::btnDel' has a wrong offset!");
static_assert(offsetof(UWDG_SeriesEventItem_C, btnDown) == 0x0005F8, "Member 'UWDG_SeriesEventItem_C::btnDown' has a wrong offset!");
static_assert(offsetof(UWDG_SeriesEventItem_C, btnUp) == 0x000600, "Member 'UWDG_SeriesEventItem_C::btnUp' has a wrong offset!");
static_assert(offsetof(UWDG_SeriesEventItem_C, controls) == 0x000608, "Member 'UWDG_SeriesEventItem_C::controls' has a wrong offset!");
static_assert(offsetof(UWDG_SeriesEventItem_C, TextBlock_41) == 0x000610, "Member 'UWDG_SeriesEventItem_C::TextBlock_41' has a wrong offset!");
static_assert(offsetof(UWDG_SeriesEventItem_C, CircuitName) == 0x000618, "Member 'UWDG_SeriesEventItem_C::CircuitName' has a wrong offset!");
static_assert(offsetof(UWDG_SeriesEventItem_C, PresetName) == 0x000630, "Member 'UWDG_SeriesEventItem_C::PresetName' has a wrong offset!");
static_assert(offsetof(UWDG_SeriesEventItem_C, OnRemoved) == 0x000638, "Member 'UWDG_SeriesEventItem_C::OnRemoved' has a wrong offset!");
static_assert(offsetof(UWDG_SeriesEventItem_C, OnPositionChanged) == 0x000648, "Member 'UWDG_SeriesEventItem_C::OnPositionChanged' has a wrong offset!");
static_assert(offsetof(UWDG_SeriesEventItem_C, CircuitIndex) == 0x000658, "Member 'UWDG_SeriesEventItem_C::CircuitIndex' has a wrong offset!");

}

