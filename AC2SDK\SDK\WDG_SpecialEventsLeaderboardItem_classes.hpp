﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SpecialEventsLeaderboardItem

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "AC2_classes.hpp"
#include "Engine_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SpecialEventsLeaderboardItem.WDG_SpecialEventsLeaderboardItem_C
// 0x00D8 (0x03A0 - 0x02C8)
class UWDG_SpecialEventsLeaderboardItem_C final : public USpecialEventLeaderboardItems
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C8(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       Intro;                                             // 0x02D0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSki<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	class UImage*                                 imgFlag;                                           // 0x02D8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgLine;                                           // 0x02E0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_KunosCategories_C*                 wdgKunosCategories;                                // 0x02E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	struct FOnlineServicesHotlapEntry             Entry;                                             // 0x02F0(0x0088)(Edit, BlueprintVisible, ExposeOnSpawn)
	int32                                         Position;                                          // 0x0378(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	uint8                                         Pad_37C[0x4];                                      // 0x037C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   Gap;                                               // 0x0380(0x0018)(Edit, BlueprintVisible, ExposeOnSpawn)
	bool                                          Spawn;                                             // 0x0398(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn)
	bool                                          IsHotstint;                                        // 0x0399(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn)
	uint8                                         Pad_39A[0x2];                                      // 0x039A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Index_0;                                           // 0x039C(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem(int32 EntryPoint);
	void Construct();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SpecialEventsLeaderboardItem_C">();
	}
	static class UWDG_SpecialEventsLeaderboardItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SpecialEventsLeaderboardItem_C>();
	}
};
static_assert(alignof(UWDG_SpecialEventsLeaderboardItem_C) == 0x000008, "Wrong alignment on UWDG_SpecialEventsLeaderboardItem_C");
static_assert(sizeof(UWDG_SpecialEventsLeaderboardItem_C) == 0x0003A0, "Wrong size on UWDG_SpecialEventsLeaderboardItem_C");
static_assert(offsetof(UWDG_SpecialEventsLeaderboardItem_C, UberGraphFrame) == 0x0002C8, "Member 'UWDG_SpecialEventsLeaderboardItem_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsLeaderboardItem_C, Intro) == 0x0002D0, "Member 'UWDG_SpecialEventsLeaderboardItem_C::Intro' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsLeaderboardItem_C, imgFlag) == 0x0002D8, "Member 'UWDG_SpecialEventsLeaderboardItem_C::imgFlag' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsLeaderboardItem_C, imgLine) == 0x0002E0, "Member 'UWDG_SpecialEventsLeaderboardItem_C::imgLine' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsLeaderboardItem_C, wdgKunosCategories) == 0x0002E8, "Member 'UWDG_SpecialEventsLeaderboardItem_C::wdgKunosCategories' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsLeaderboardItem_C, Entry) == 0x0002F0, "Member 'UWDG_SpecialEventsLeaderboardItem_C::Entry' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsLeaderboardItem_C, Position) == 0x000378, "Member 'UWDG_SpecialEventsLeaderboardItem_C::Position' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsLeaderboardItem_C, Gap) == 0x000380, "Member 'UWDG_SpecialEventsLeaderboardItem_C::Gap' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsLeaderboardItem_C, Spawn) == 0x000398, "Member 'UWDG_SpecialEventsLeaderboardItem_C::Spawn' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsLeaderboardItem_C, IsHotstint) == 0x000399, "Member 'UWDG_SpecialEventsLeaderboardItem_C::IsHotstint' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsLeaderboardItem_C, Index_0) == 0x00039C, "Member 'UWDG_SpecialEventsLeaderboardItem_C::Index_0' has a wrong offset!");

}

