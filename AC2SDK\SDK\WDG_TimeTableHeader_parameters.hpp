﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TimeTableHeader

#include "Basic.hpp"


namespace SDK::Params
{

// Function WDG_TimeTableHeader.WDG_TimeTableHeader_C.SetSingleCarViewing
// 0x000C (0x000C - 0x0000)
struct WDG_TimeTableHeader_C_SetSingleCarViewing final
{
public:
	bool                                          InBool;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2[0x2];                                        // 0x0002(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Conv_BoolToInt_ReturnValue;               // 0x0004(0x0004)(ZeroConstructor, Is<PERSON>lainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0009(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_TimeTableHeader_C_SetSingleCarViewing) == 0x000004, "Wrong alignment on WDG_TimeTableHeader_C_SetSingleCarViewing");
static_assert(sizeof(WDG_TimeTableHeader_C_SetSingleCarViewing) == 0x00000C, "Wrong size on WDG_TimeTableHeader_C_SetSingleCarViewing");
static_assert(offsetof(WDG_TimeTableHeader_C_SetSingleCarViewing, InBool) == 0x000000, "Member 'WDG_TimeTableHeader_C_SetSingleCarViewing::InBool' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableHeader_C_SetSingleCarViewing, CallFunc_Not_PreBool_ReturnValue) == 0x000001, "Member 'WDG_TimeTableHeader_C_SetSingleCarViewing::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableHeader_C_SetSingleCarViewing, CallFunc_Conv_BoolToInt_ReturnValue) == 0x000004, "Member 'WDG_TimeTableHeader_C_SetSingleCarViewing::CallFunc_Conv_BoolToInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableHeader_C_SetSingleCarViewing, CallFunc_MakeLiteralByte_ReturnValue) == 0x000008, "Member 'WDG_TimeTableHeader_C_SetSingleCarViewing::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableHeader_C_SetSingleCarViewing, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000009, "Member 'WDG_TimeTableHeader_C_SetSingleCarViewing::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");

// Function WDG_TimeTableHeader.WDG_TimeTableHeader_C.SetShowingLaptimes
// 0x0004 (0x0004 - 0x0000)
struct WDG_TimeTableHeader_C_SetShowingLaptimes final
{
public:
	bool                                          ShowFullTimingInfo;                                // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          ShowTimingInfo;                                    // 0x0001(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0003(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_TimeTableHeader_C_SetShowingLaptimes) == 0x000001, "Wrong alignment on WDG_TimeTableHeader_C_SetShowingLaptimes");
static_assert(sizeof(WDG_TimeTableHeader_C_SetShowingLaptimes) == 0x000004, "Wrong size on WDG_TimeTableHeader_C_SetShowingLaptimes");
static_assert(offsetof(WDG_TimeTableHeader_C_SetShowingLaptimes, ShowFullTimingInfo) == 0x000000, "Member 'WDG_TimeTableHeader_C_SetShowingLaptimes::ShowFullTimingInfo' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableHeader_C_SetShowingLaptimes, ShowTimingInfo) == 0x000001, "Member 'WDG_TimeTableHeader_C_SetShowingLaptimes::ShowTimingInfo' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableHeader_C_SetShowingLaptimes, CallFunc_MakeLiteralByte_ReturnValue) == 0x000002, "Member 'WDG_TimeTableHeader_C_SetShowingLaptimes::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableHeader_C_SetShowingLaptimes, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000003, "Member 'WDG_TimeTableHeader_C_SetShowingLaptimes::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");

// Function WDG_TimeTableHeader.WDG_TimeTableHeader_C.SetCombinedLaps
// 0x0001 (0x0001 - 0x0000)
struct WDG_TimeTableHeader_C_SetCombinedLaps final
{
public:
	bool                                          IsCombinedLaps;                                    // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_TimeTableHeader_C_SetCombinedLaps) == 0x000001, "Wrong alignment on WDG_TimeTableHeader_C_SetCombinedLaps");
static_assert(sizeof(WDG_TimeTableHeader_C_SetCombinedLaps) == 0x000001, "Wrong size on WDG_TimeTableHeader_C_SetCombinedLaps");
static_assert(offsetof(WDG_TimeTableHeader_C_SetCombinedLaps, IsCombinedLaps) == 0x000000, "Member 'WDG_TimeTableHeader_C_SetCombinedLaps::IsCombinedLaps' has a wrong offset!");

// Function WDG_TimeTableHeader.WDG_TimeTableHeader_C.SetShowingHandicap
// 0x0003 (0x0003 - 0x0000)
struct WDG_TimeTableHeader_C_SetShowingHandicap final
{
public:
	bool                                          ShowHandicaps;                                     // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_TimeTableHeader_C_SetShowingHandicap) == 0x000001, "Wrong alignment on WDG_TimeTableHeader_C_SetShowingHandicap");
static_assert(sizeof(WDG_TimeTableHeader_C_SetShowingHandicap) == 0x000003, "Wrong size on WDG_TimeTableHeader_C_SetShowingHandicap");
static_assert(offsetof(WDG_TimeTableHeader_C_SetShowingHandicap, ShowHandicaps) == 0x000000, "Member 'WDG_TimeTableHeader_C_SetShowingHandicap::ShowHandicaps' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableHeader_C_SetShowingHandicap, CallFunc_MakeLiteralByte_ReturnValue) == 0x000001, "Member 'WDG_TimeTableHeader_C_SetShowingHandicap::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TimeTableHeader_C_SetShowingHandicap, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000002, "Member 'WDG_TimeTableHeader_C_SetShowingHandicap::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");

}

