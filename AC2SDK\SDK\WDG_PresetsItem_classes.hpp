﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PresetsItem

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_PresetsItem.WDG_PresetsItem_C
// 0x0028 (0x0648 - 0x0620)
class UWDG_PresetsItem_C final : public UPresetItem
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0620(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       OpacityPanel;                                      // 0x0628(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 HoverImageBox;                                     // 0x0630(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 LineOption_bottomLine_img;                         // 0x0638(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 NormalImageBox;                                    // 0x0640(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_PresetsItem(int32 EntryPoint);
	void BP_MouseOver();
	void BP_MouseLeave();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_PresetsItem_C">();
	}
	static class UWDG_PresetsItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_PresetsItem_C>();
	}
};
static_assert(alignof(UWDG_PresetsItem_C) == 0x000008, "Wrong alignment on UWDG_PresetsItem_C");
static_assert(sizeof(UWDG_PresetsItem_C) == 0x000648, "Wrong size on UWDG_PresetsItem_C");
static_assert(offsetof(UWDG_PresetsItem_C, UberGraphFrame) == 0x000620, "Member 'UWDG_PresetsItem_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_PresetsItem_C, OpacityPanel) == 0x000628, "Member 'UWDG_PresetsItem_C::OpacityPanel' has a wrong offset!");
static_assert(offsetof(UWDG_PresetsItem_C, HoverImageBox) == 0x000630, "Member 'UWDG_PresetsItem_C::HoverImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_PresetsItem_C, LineOption_bottomLine_img) == 0x000638, "Member 'UWDG_PresetsItem_C::LineOption_bottomLine_img' has a wrong offset!");
static_assert(offsetof(UWDG_PresetsItem_C, NormalImageBox) == 0x000640, "Member 'UWDG_PresetsItem_C::NormalImageBox' has a wrong offset!");

}

