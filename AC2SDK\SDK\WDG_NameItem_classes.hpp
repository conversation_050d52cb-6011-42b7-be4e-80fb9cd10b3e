﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_NameItem

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_NameItem.WDG_NameItem_C
// 0x0000 (0x02A8 - 0x02A8)
class UWDG_NameItem_C final : public UTimeTableNameItem
{
public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_NameItem_C">();
	}
	static class UWDG_NameItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_NameItem_C>();
	}
};
static_assert(alignof(UWDG_NameItem_C) == 0x000008, "Wrong alignment on UWDG_NameItem_C");
static_assert(sizeof(UWDG_NameItem_C) == 0x0002A8, "Wrong size on UWDG_NameItem_C");

}

