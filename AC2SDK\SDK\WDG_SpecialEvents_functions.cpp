﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SpecialEvents

#include "Basic.hpp"

#include "WDG_SpecialEvents_classes.hpp"
#include "WDG_SpecialEvents_parameters.hpp"


namespace SDK
{

// Function WDG_SpecialEvents.WDG_SpecialEvents_C.ExecuteUbergraph_WDG_SpecialEvents
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SpecialEvents_C::ExecuteUbergraph_WDG_SpecialEvents(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "ExecuteUbergraph_WDG_SpecialEvents");

	Params::WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.BndEvt__popupDLC_K2Node_ComponentBoundEvent_7_OnYes__DelegateSignature
// (BlueprintEvent)

void UWDG_SpecialEvents_C::BndEvt__popupDLC_K2Node_ComponentBoundEvent_7_OnYes__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "BndEvt__popupDLC_K2Node_ComponentBoundEvent_7_OnYes__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.BndEvt__btnOldEvents_K2Node_ComponentBoundEvent_6_OnAcPanelFocusEvent__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UAcPanelBase*                     panel                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SpecialEvents_C::BndEvt__btnOldEvents_K2Node_ComponentBoundEvent_6_OnAcPanelFocusEvent__DelegateSignature(class UAcPanelBase* panel)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "BndEvt__btnOldEvents_K2Node_ComponentBoundEvent_6_OnAcPanelFocusEvent__DelegateSignature");

	Params::WDG_SpecialEvents_C_BndEvt__btnOldEvents_K2Node_ComponentBoundEvent_6_OnAcPanelFocusEvent__DelegateSignature Parms{};

	Parms.panel = panel;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.BndEvt__btnRefresh_K2Node_ComponentBoundEvent_5_OnAcPanelFocusEvent__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UAcPanelBase*                     panel                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SpecialEvents_C::BndEvt__btnRefresh_K2Node_ComponentBoundEvent_5_OnAcPanelFocusEvent__DelegateSignature(class UAcPanelBase* panel)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "BndEvt__btnRefresh_K2Node_ComponentBoundEvent_5_OnAcPanelFocusEvent__DelegateSignature");

	Params::WDG_SpecialEvents_C_BndEvt__btnRefresh_K2Node_ComponentBoundEvent_5_OnAcPanelFocusEvent__DelegateSignature Parms{};

	Parms.panel = panel;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.BndEvt__CurrentModel_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_SpecialEvents_C::BndEvt__CurrentModel_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "BndEvt__CurrentModel_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_3_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_SpecialEvents_C::BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_3_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_3_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.BndEvt__btdOldEvents_K2Node_ComponentBoundEvent_2_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_SpecialEvents_C::BndEvt__btdOldEvents_K2Node_ComponentBoundEvent_2_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "BndEvt__btdOldEvents_K2Node_ComponentBoundEvent_2_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.BndEvt__StartSessionPanel_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_SpecialEvents_C::BndEvt__StartSessionPanel_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "BndEvt__StartSessionPanel_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.OnPageReady
// (Event, Protected, BlueprintCallable, BlueprintEvent)

void UWDG_SpecialEvents_C::OnPageReady()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "OnPageReady");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.OnEventSelected
// (Event, Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FSpecialEventPreset&       Preset                                                 (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// const struct FModelInfo&                model_info                                             (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// const struct FCarInfo&                  car_info                                               (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// const struct FTeamInfo&                 team_info                                              (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// const struct FCircuitInfo&              circuit_info                                           (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_SpecialEvents_C::OnEventSelected(const struct FSpecialEventPreset& Preset, const struct FModelInfo& model_info, const struct FCarInfo& car_info, const struct FTeamInfo& team_info, const struct FCircuitInfo& circuit_info)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "OnEventSelected");

	Params::WDG_SpecialEvents_C_OnEventSelected Parms{};

	Parms.Preset = std::move(Preset);
	Parms.model_info = std::move(model_info);
	Parms.car_info = std::move(car_info);
	Parms.team_info = std::move(team_info);
	Parms.circuit_info = std::move(circuit_info);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.BndEvt__btnConfirm_K2Node_ComponentBoundEvent_1_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_SpecialEvents_C::BndEvt__btnConfirm_K2Node_ComponentBoundEvent_1_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "BndEvt__btnConfirm_K2Node_ComponentBoundEvent_1_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.BP_StartPage
// (Event, Public, BlueprintEvent)

void UWDG_SpecialEvents_C::BP_StartPage()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "BP_StartPage");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.OnSpecialEventListPopulated
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   Count                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWDG_SpecialEventItem_C*          PreviouslySelected                                     (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SpecialEvents_C::OnSpecialEventListPopulated(int32 Count, class UWDG_SpecialEventItem_C* PreviouslySelected)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "OnSpecialEventListPopulated");

	Params::WDG_SpecialEvents_C_OnSpecialEventListPopulated Parms{};

	Parms.Count = Count;
	Parms.PreviouslySelected = PreviouslySelected;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.OnSpecialEventItemAdded
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UWDG_SpecialEventItem_C*          Item                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SpecialEvents_C::OnSpecialEventItemAdded(class UWDG_SpecialEventItem_C* Item)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "OnSpecialEventItemAdded");

	Params::WDG_SpecialEvents_C_OnSpecialEventItemAdded Parms{};

	Parms.Item = Item;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.BndEvt__WDG_GenericBarItem_C_0_K2Node_ComponentBoundEvent_2_OnAcPanelFocusEvent__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UAcPanelBase*                     panel                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SpecialEvents_C::BndEvt__WDG_GenericBarItem_C_0_K2Node_ComponentBoundEvent_2_OnAcPanelFocusEvent__DelegateSignature(class UAcPanelBase* panel)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "BndEvt__WDG_GenericBarItem_C_0_K2Node_ComponentBoundEvent_2_OnAcPanelFocusEvent__DelegateSignature");

	Params::WDG_SpecialEvents_C_BndEvt__WDG_GenericBarItem_C_0_K2Node_ComponentBoundEvent_2_OnAcPanelFocusEvent__DelegateSignature Parms{};

	Parms.panel = panel;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.BndEvt__listSpecialEvents_K2Node_ComponentBoundEvent_1_AddedToFocusPath__DelegateSignature
// (BlueprintEvent)

void UWDG_SpecialEvents_C::BndEvt__listSpecialEvents_K2Node_ComponentBoundEvent_1_AddedToFocusPath__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "BndEvt__listSpecialEvents_K2Node_ComponentBoundEvent_1_AddedToFocusPath__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.BP_OnBackward
// (Event, Public, BlueprintEvent)

void UWDG_SpecialEvents_C::BP_OnBackward()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "BP_OnBackward");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.BndEvt__Back_K2Node_ComponentBoundEvent_0_OnClicked__DelegateSignature
// (BlueprintEvent)

void UWDG_SpecialEvents_C::BndEvt__Back_K2Node_ComponentBoundEvent_0_OnClicked__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "BndEvt__Back_K2Node_ComponentBoundEvent_0_OnClicked__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.OnLeaderboardRequested
// (Event, Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   ref_id                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SpecialEvents_C::OnLeaderboardRequested(int32 ref_id)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "OnLeaderboardRequested");

	Params::WDG_SpecialEvents_C_OnLeaderboardRequested Parms{};

	Parms.ref_id = ref_id;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.OnLeaderboardReceived
// (Event, Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const TArray<struct FOnlineServicesHotlapEntry>&entries                                                (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// const TArray<class FText>&              deltas                                                 (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_SpecialEvents_C::OnLeaderboardReceived(const TArray<struct FOnlineServicesHotlapEntry>& entries, const TArray<class FText>& deltas)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "OnLeaderboardReceived");

	Params::WDG_SpecialEvents_C_OnLeaderboardReceived Parms{};

	Parms.entries = std::move(entries);
	Parms.deltas = std::move(deltas);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.Populate
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// TArray<struct FSpecialEventPreset>&     SpecialEvents                                          (BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// bool                                    PastEvents                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SpecialEvents_C::Populate(TArray<struct FSpecialEventPreset>& SpecialEvents, bool PastEvents)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "Populate");

	Params::WDG_SpecialEvents_C_Populate Parms{};

	Parms.SpecialEvents = std::move(SpecialEvents);
	Parms.PastEvents = PastEvents;

	UObject::ProcessEvent(Func, &Parms);

	SpecialEvents = std::move(Parms.SpecialEvents);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.FocusToItem
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UAcPanelBase*                     Target                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SpecialEvents_C::FocusToItem(class UAcPanelBase* Target)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "FocusToItem");

	Params::WDG_SpecialEvents_C_FocusToItem Parms{};

	Parms.Target = Target;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.GetPresets
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    Debug                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
// bool                                    PastEvents                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
// TArray<struct FSpecialEventPreset>*     SpecialEvents                                          (Parm, OutParm)

void UWDG_SpecialEvents_C::GetPresets(bool Debug, bool PastEvents, TArray<struct FSpecialEventPreset>* SpecialEvents)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "GetPresets");

	Params::WDG_SpecialEvents_C_GetPresets Parms{};

	Parms.Debug = Debug;
	Parms.PastEvents = PastEvents;

	UObject::ProcessEvent(Func, &Parms);

	if (SpecialEvents != nullptr)
		*SpecialEvents = std::move(Parms.SpecialEvents);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.ItemSelected
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UWDG_SpecialEventItem_C*          Item                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    ByMouse                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SpecialEvents_C::ItemSelected(class UWDG_SpecialEventItem_C* Item, bool ByMouse)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "ItemSelected");

	Params::WDG_SpecialEvents_C_ItemSelected Parms{};

	Parms.Item = Item;
	Parms.ByMouse = ByMouse;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.SetEventsAvailable
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    IsAvailable                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SpecialEvents_C::SetEventsAvailable(bool IsAvailable)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "SetEventsAvailable");

	Params::WDG_SpecialEvents_C_SetEventsAvailable Parms{};

	Parms.IsAvailable = IsAvailable;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.SelectAndFocusListToItem
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UWDG_SpecialEventItem_C*          Item                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SpecialEvents_C::SelectAndFocusListToItem(class UWDG_SpecialEventItem_C* Item)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "SelectAndFocusListToItem");

	Params::WDG_SpecialEvents_C_SelectAndFocusListToItem Parms{};

	Parms.Item = Item;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.GetSelectedItem
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UWDG_SpecialEventItem_C**         SelectedItem                                           (Parm, OutParm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SpecialEvents_C::GetSelectedItem(class UWDG_SpecialEventItem_C** SelectedItem)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "GetSelectedItem");

	Params::WDG_SpecialEvents_C_GetSelectedItem Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (SelectedItem != nullptr)
		*SelectedItem = Parms.SelectedItem;
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.DownToSelectedItem
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// EUINavigation                           Navigation_0                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWidget*                          ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

class UWidget* UWDG_SpecialEvents_C::DownToSelectedItem(EUINavigation Navigation_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "DownToSelectedItem");

	Params::WDG_SpecialEvents_C_DownToSelectedItem Parms{};

	Parms.Navigation_0 = Navigation_0;

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.OnPreviewKeyDown
// (Event, Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FKeyEvent&                 InKeyEvent                                             (BlueprintVisible, BlueprintReadOnly, Parm)
// struct FEventReply                      ReturnValue                                            (Parm, OutParm, ReturnParm)

struct FEventReply UWDG_SpecialEvents_C::OnPreviewKeyDown(const struct FGeometry& MyGeometry, const struct FKeyEvent& InKeyEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "OnPreviewKeyDown");

	Params::WDG_SpecialEvents_C_OnPreviewKeyDown Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InKeyEvent = std::move(InKeyEvent);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.UIVisible
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_SpecialEvents_C::UIVisible()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "UIVisible");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.UIHidden
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_SpecialEvents_C::UIHidden()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "UIHidden");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.LeftToCurrentTeam
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// EUINavigation                           Navigation_0                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWidget*                          ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

class UWidget* UWDG_SpecialEvents_C::LeftToCurrentTeam(EUINavigation Navigation_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "LeftToCurrentTeam");

	Params::WDG_SpecialEvents_C_LeftToCurrentTeam Parms{};

	Parms.Navigation_0 = Navigation_0;

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.UnfadeUI
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_SpecialEvents_C::UnfadeUI()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "UnfadeUI");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.SequenceEvent__ENTRYPOINTWDG_SpecialEvents_0
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_SpecialEvents_C::SequenceEvent__ENTRYPOINTWDG_SpecialEvents_0()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "SequenceEvent__ENTRYPOINTWDG_SpecialEvents_0");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEvents.WDG_SpecialEvents_C.SequenceEvent__ENTRYPOINTWDG_SpecialEvents_1
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_SpecialEvents_C::SequenceEvent__ENTRYPOINTWDG_SpecialEvents_1()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEvents_C", "SequenceEvent__ENTRYPOINTWDG_SpecialEvents_1");

	UObject::ProcessEvent(Func, nullptr);
}

}

