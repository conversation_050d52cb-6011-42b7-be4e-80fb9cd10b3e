﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TextBlancpainShape

#include "Basic.hpp"


namespace SDK::Params
{

// Function WDG_TextBlancpainShape.WDG_TextBlancpainShape_C.SetTitleText
// 0x0018 (0x0018 - 0x0000)
struct WDG_TextBlancpainShape_C_SetTitleText final
{
public:
	class FText                                   NewParam;                                          // 0x0000(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_TextBlancpainShape_C_SetTitleText) == 0x000008, "Wrong alignment on WDG_TextBlancpainShape_C_SetTitleText");
static_assert(sizeof(WDG_TextBlancpainShape_C_SetTitleText) == 0x000018, "Wrong size on WDG_TextBlancpainShape_C_SetTitleText");
static_assert(offsetof(WDG_TextBlancpainShape_C_SetTitleText, NewParam) == 0x000000, "Member 'WDG_TextBlancpainShape_C_SetTitleText::NewParam' has a wrong offset!");

}

