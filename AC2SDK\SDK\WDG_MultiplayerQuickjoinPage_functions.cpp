﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_MultiplayerQuickjoinPage

#include "Basic.hpp"

#include "WDG_MultiplayerQuickjoinPage_classes.hpp"
#include "WDG_MultiplayerQuickjoinPage_parameters.hpp"


namespace SDK
{

// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.ExecuteUbergraph_WDG_MultiplayerQuickjoinPage
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_MultiplayerQuickjoinPage_C::ExecuteUbergraph_WDG_MultiplayerQuickjoinPage(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "ExecuteUbergraph_WDG_MultiplayerQuickjoinPage");

	Params::WDG_MultiplayerQuickjoinPage_C_ExecuteUbergraph_WDG_MultiplayerQuickjoinPage Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.OnSeasonUnavailable
// (Event, Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// EContentType                            content_type                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_MultiplayerQuickjoinPage_C::OnSeasonUnavailable(EContentType content_type)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "OnSeasonUnavailable");

	Params::WDG_MultiplayerQuickjoinPage_C_OnSeasonUnavailable Parms{};

	Parms.content_type = content_type;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.OnServerDisconnection
// (Event, Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// EDisconnectionReason                    reason                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_MultiplayerQuickjoinPage_C::OnServerDisconnection(EDisconnectionReason reason)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "OnServerDisconnection");

	Params::WDG_MultiplayerQuickjoinPage_C_OnServerDisconnection Parms{};

	Parms.reason = reason;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.ReactivateRefresh
// (BlueprintCallable, BlueprintEvent)

void UWDG_MultiplayerQuickjoinPage_C::ReactivateRefresh()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "ReactivateRefresh");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.ActivatePulseAnimation
// (BlueprintCallable, BlueprintEvent)

void UWDG_MultiplayerQuickjoinPage_C::ActivatePulseAnimation()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "ActivatePulseAnimation");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.AfterCarGroupSelectionDebounce
// (BlueprintCallable, BlueprintEvent)

void UWDG_MultiplayerQuickjoinPage_C::AfterCarGroupSelectionDebounce()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "AfterCarGroupSelectionDebounce");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_13_OnAcPanelFocusEvent__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UAcPanelBase*                     panel                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_MultiplayerQuickjoinPage_C::BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_13_OnAcPanelFocusEvent__DelegateSignature(class UAcPanelBase* panel)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_13_OnAcPanelFocusEvent__DelegateSignature");

	Params::WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_13_OnAcPanelFocusEvent__DelegateSignature Parms{};

	Parms.panel = panel;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_11_OnAcPanelFocusEvent__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UAcPanelBase*                     panel                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_MultiplayerQuickjoinPage_C::BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_11_OnAcPanelFocusEvent__DelegateSignature(class UAcPanelBase* panel)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_11_OnAcPanelFocusEvent__DelegateSignature");

	Params::WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_11_OnAcPanelFocusEvent__DelegateSignature Parms{};

	Parms.panel = panel;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_12_OnAcPanelFocusEvent__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UAcPanelBase*                     panel                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_MultiplayerQuickjoinPage_C::BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_12_OnAcPanelFocusEvent__DelegateSignature(class UAcPanelBase* panel)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_12_OnAcPanelFocusEvent__DelegateSignature");

	Params::WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_12_OnAcPanelFocusEvent__DelegateSignature Parms{};

	Parms.panel = panel;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_10_OnAcPanelFocusEvent__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UAcPanelBase*                     panel                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_MultiplayerQuickjoinPage_C::BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_10_OnAcPanelFocusEvent__DelegateSignature(class UAcPanelBase* panel)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_10_OnAcPanelFocusEvent__DelegateSignature");

	Params::WDG_MultiplayerQuickjoinPage_C_BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_10_OnAcPanelFocusEvent__DelegateSignature Parms{};

	Parms.panel = panel;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_9_OnCarGroupBlurred__DelegateSignature
// (BlueprintEvent)
// Parameters:
// EMPCarGroup                             CarGroup                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_MultiplayerQuickjoinPage_C::BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_9_OnCarGroupBlurred__DelegateSignature(EMPCarGroup CarGroup)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_9_OnCarGroupBlurred__DelegateSignature");

	Params::WDG_MultiplayerQuickjoinPage_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_9_OnCarGroupBlurred__DelegateSignature Parms{};

	Parms.CarGroup = CarGroup;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_8_OnCarGroupFocused__DelegateSignature
// (BlueprintEvent)
// Parameters:
// EMPCarGroup                             CarGroup                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_MultiplayerQuickjoinPage_C::BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_8_OnCarGroupFocused__DelegateSignature(EMPCarGroup CarGroup)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_8_OnCarGroupFocused__DelegateSignature");

	Params::WDG_MultiplayerQuickjoinPage_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_8_OnCarGroupFocused__DelegateSignature Parms{};

	Parms.CarGroup = CarGroup;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_7_OnCarGroupSelected__DelegateSignature
// (BlueprintEvent)
// Parameters:
// EMPCarGroup                             CarGroup                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_MultiplayerQuickjoinPage_C::BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_7_OnCarGroupSelected__DelegateSignature(EMPCarGroup CarGroup)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_7_OnCarGroupSelected__DelegateSignature");

	Params::WDG_MultiplayerQuickjoinPage_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_7_OnCarGroupSelected__DelegateSignature Parms{};

	Parms.CarGroup = CarGroup;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.OnCPInviteUpdate
// (HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FOnlineServicesCPInvitationState&invitation_update                                      (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_MultiplayerQuickjoinPage_C::OnCPInviteUpdate(const struct FOnlineServicesCPInvitationState& invitation_update)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "OnCPInviteUpdate");

	Params::WDG_MultiplayerQuickjoinPage_C_OnCPInviteUpdate Parms{};

	Parms.invitation_update = std::move(invitation_update);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.BP_StartPage
// (Event, Public, BlueprintEvent)

void UWDG_MultiplayerQuickjoinPage_C::BP_StartPage()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "BP_StartPage");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.OnRemovedFromFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_MultiplayerQuickjoinPage_C::OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "OnRemovedFromFocusPath");

	Params::WDG_MultiplayerQuickjoinPage_C_OnRemovedFromFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.BndEvt__btnRefresh_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_MultiplayerQuickjoinPage_C::BndEvt__btnRefresh_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "BndEvt__btnRefresh_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.UpdateServerInfo
// (Event, Protected, HasOutParams, BlueprintEvent)
// Parameters:
// const struct FOnlineServicesMPServerInfo&legacy_competition_server                              (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// const struct FOnlineServicesMPQuickjoinPanelInfo&quickjoin_info                                         (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)
// const struct FOnlineServicesCPInvitationState&competition_server_invitation                          (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_MultiplayerQuickjoinPage_C::UpdateServerInfo(const struct FOnlineServicesMPServerInfo& legacy_competition_server, const struct FOnlineServicesMPQuickjoinPanelInfo& quickjoin_info, const struct FOnlineServicesCPInvitationState& competition_server_invitation)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "UpdateServerInfo");

	Params::WDG_MultiplayerQuickjoinPage_C_UpdateServerInfo Parms{};

	Parms.legacy_competition_server = std::move(legacy_competition_server);
	Parms.quickjoin_info = std::move(quickjoin_info);
	Parms.competition_server_invitation = std::move(competition_server_invitation);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_MultiplayerQuickjoinPage_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_6_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_MultiplayerQuickjoinPage_C::BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_6_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_6_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.BndEvt__WDG_QuickjoinSlot1_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_MultiplayerQuickjoinPage_C::BndEvt__WDG_QuickjoinSlot1_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "BndEvt__WDG_QuickjoinSlot1_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.BndEvt__WDG_MPQuickjoinPublicMPSlot_K2Node_ComponentBoundEvent_3_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_MultiplayerQuickjoinPage_C::BndEvt__WDG_MPQuickjoinPublicMPSlot_K2Node_ComponentBoundEvent_3_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "BndEvt__WDG_MPQuickjoinPublicMPSlot_K2Node_ComponentBoundEvent_3_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.BndEvt__btnCarSelection_K2Node_ComponentBoundEvent_5_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_MultiplayerQuickjoinPage_C::BndEvt__btnCarSelection_K2Node_ComponentBoundEvent_5_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "BndEvt__btnCarSelection_K2Node_ComponentBoundEvent_5_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.BndEvt__btnServerList_K2Node_ComponentBoundEvent_2_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_MultiplayerQuickjoinPage_C::BndEvt__btnServerList_K2Node_ComponentBoundEvent_2_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "BndEvt__btnServerList_K2Node_ComponentBoundEvent_2_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.BndEvt__btnAdvanced_K2Node_ComponentBoundEvent_1_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_MultiplayerQuickjoinPage_C::BndEvt__btnAdvanced_K2Node_ComponentBoundEvent_1_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "BndEvt__btnAdvanced_K2Node_ComponentBoundEvent_1_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.Get_txtStatus_Text_0
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class FText                             ReturnValue                                            (Parm, OutParm, ReturnParm)

class FText UWDG_MultiplayerQuickjoinPage_C::Get_txtStatus_Text_0()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "Get_txtStatus_Text_0");

	Params::WDG_MultiplayerQuickjoinPage_C_Get_txtStatus_Text_0 Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C.Is DLC Car Available
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor)

bool UWDG_MultiplayerQuickjoinPage_C::Is_DLC_Car_Available()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerQuickjoinPage_C", "Is DLC Car Available");

	Params::WDG_MultiplayerQuickjoinPage_C_Is_DLC_Car_Available Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}

}

