﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PlayerInfoPanel

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.ExecuteUbergraph_WDG_PlayerInfoPanel
// 0x0998 (0x0998 - 0x0000)
struct WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   Temp_name_Variable;                                // 0x0004(0x0008)(ConstParm, ZeroConstructor, IsPlainOldData, NoDestru<PERSON>, HasGetValueTypeHash)
	uint8                                         Pad_C[0x4];                                        // 0x000C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue;              // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance;             // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_21[0x3];                                       // 0x0021(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FGeometry                              K2Node_Event_MyGeometry;                           // 0x0024(0x0038)(IsPlainOldData, NoDestructor)
	float                                         K2Node_Event_InDeltaTime;                          // 0x005C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x0060(0x0018)()
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_1;          // 0x0078(0x0018)()
	class FText                                   K2Node_ComponentBoundEvent_Text_5;                 // 0x0090(0x0018)(ConstParm)
	class FText                                   K2Node_ComponentBoundEvent_Text_4;                 // 0x00A8(0x0018)(ConstParm)
	class FText                                   K2Node_ComponentBoundEvent_Text_3;                 // 0x00C0(0x0018)(ConstParm)
	class FText                                   K2Node_ComponentBoundEvent_Text_2;                 // 0x00D8(0x0018)(ConstParm)
	class FText                                   K2Node_ComponentBoundEvent_Text_1;                 // 0x00F0(0x0018)(ConstParm)
	class FText                                   CallFunc_GetText_ReturnValue;                      // 0x0108(0x0018)()
	class FText                                   CallFunc_GetText_ReturnValue_1;                    // 0x0120(0x0018)()
	class FString                                 CallFunc_Conv_TextToString_ReturnValue;            // 0x0138(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_ValidateAbbreviation_Value_As_String;     // 0x0148(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_TextIsEmpty_ReturnValue;                  // 0x0158(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_159[0x7];                                      // 0x0159(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_GetText_ReturnValue_2;                    // 0x0160(0x0018)()
	class FText                                   K2Node_ComponentBoundEvent_Text;                   // 0x0178(0x0018)(ConstParm)
	class FString                                 CallFunc_Conv_TextToString_ReturnValue_1;          // 0x0190(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_TextIsEmpty_ReturnValue_1;                // 0x01A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1A1[0x7];                                      // 0x01A1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class AGameModeBase*                          CallFunc_GetGameMode_ReturnValue;                  // 0x01A8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AAcMenuGameMode*                        K2Node_DynamicCast_AsAc_Menu_Game_Mode;            // 0x01B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x01B8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_1B9[0x7];                                      // 0x01B9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FDriverInfo                            CallFunc_Map_Find_Value;                           // 0x01C0(0x00F0)()
	bool                                          CallFunc_Map_Find_ReturnValue;                     // 0x02B0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2B1[0x7];                                      // 0x02B1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class AGameModeBase*                          CallFunc_GetGameMode_ReturnValue_1;                // 0x02B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_2;          // 0x02C0(0x0018)()
	class AAcMenuGameMode*                        K2Node_DynamicCast_AsAc_Menu_Game_Mode_1;          // 0x02D8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_2;                     // 0x02E0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_2E1[0x7];                                      // 0x02E1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_GetText_ReturnValue_3;                    // 0x02E8(0x0018)()
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_3;          // 0x0300(0x0018)()
	class FString                                 CallFunc_Conv_TextToString_ReturnValue_2;          // 0x0318(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_TextIsEmpty_ReturnValue_2;                // 0x0328(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_329[0x7];                                      // 0x0329(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_4;          // 0x0330(0x0018)()
	class FText                                   CallFunc_GetText_ReturnValue_4;                    // 0x0348(0x0018)()
	class FText                                   CallFunc_GetText_ReturnValue_5;                    // 0x0360(0x0018)()
	class FString                                 CallFunc_Conv_TextToString_ReturnValue_3;          // 0x0378(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_TextToString_ReturnValue_4;          // 0x0388(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_TextIsEmpty_ReturnValue_3;                // 0x0398(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_TextIsEmpty_ReturnValue_4;                // 0x0399(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_39A[0x6];                                      // 0x039A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   K2Node_CustomEvent_Text_2;                         // 0x03A0(0x0018)(ConstParm)
	ETextCommit                                   K2Node_CustomEvent_CommitMethod_2;                 // 0x03B8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x03B9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_3BA[0x2];                                      // 0x03BA(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate;              // 0x03BC(0x0010)(ZeroConstructor, NoDestructor)
	uint8                                         Pad_3CC[0x4];                                      // 0x03CC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   K2Node_CustomEvent_Text_1;                         // 0x03D0(0x0018)(ConstParm)
	ETextCommit                                   K2Node_CustomEvent_CommitMethod_1;                 // 0x03E8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3E9[0x7];                                      // 0x03E9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue_1;            // 0x03F0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess_1;                    // 0x03F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_3F9[0x7];                                      // 0x03F9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance_1;           // 0x0400(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_3;                     // 0x0408(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_409[0x3];                                      // 0x0409(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class FName                                   Temp_name_Variable_1;                              // 0x040C(0x0008)(ConstParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_414[0x4];                                      // 0x0414(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UOnlineServices*                        CallFunc_GetOnlineServices_ReturnValue;            // 0x0418(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UGamePlatformServices*                  CallFunc_GetGamePlatformServices_ReturnValue;      // 0x0420(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FDriverInfo                            CallFunc_Map_Find_Value_1;                         // 0x0428(0x00F0)()
	bool                                          CallFunc_Map_Find_ReturnValue_1;                   // 0x0518(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_519[0x7];                                      // 0x0519(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   K2Node_CustomEvent_Text;                           // 0x0520(0x0018)(ConstParm)
	ETextCommit                                   K2Node_CustomEvent_CommitMethod;                   // 0x0538(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess_2;                    // 0x0539(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_53A[0x6];                                      // 0x053A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FDriverInfo                            K2Node_SetFieldsInStruct_StructOut;                // 0x0540(0x00F0)()
	class FText                                   CallFunc_GetText_ReturnValue_6;                    // 0x0630(0x0018)()
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_5;          // 0x0648(0x0018)()
	class FText                                   CallFunc_TextToUpper_ReturnValue;                  // 0x0660(0x0018)()
	class FString                                 CallFunc_Conv_TextToString_ReturnValue_5;          // 0x0678(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_6;          // 0x0688(0x0018)()
	class FString                                 CallFunc_Conv_NameToString_ReturnValue;            // 0x06A0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class AGameModeBase*                          CallFunc_GetGameMode_ReturnValue_2;                // 0x06B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AAcMenuGameMode*                        K2Node_DynamicCast_AsAc_Menu_Game_Mode_2;          // 0x06B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_4;                     // 0x06C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_6C1[0x7];                                      // 0x06C1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FDriverInfo                            CallFunc_Map_Find_Value_2;                         // 0x06C8(0x00F0)()
	bool                                          CallFunc_Map_Find_ReturnValue_2;                   // 0x07B8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_7B9[0x7];                                      // 0x07B9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FDriverInfo                            K2Node_SetFieldsInStruct_StructOut_1;              // 0x07C0(0x00F0)()
	bool                                          CallFunc_saveCustomDriver_ReturnValue;             // 0x08B0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_8B1[0x7];                                      // 0x08B1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue_2;            // 0x08B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance_2;           // 0x08C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_5;                     // 0x08C8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_8C9[0x7];                                      // 0x08C9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UGamePlatformServices*                  CallFunc_GetGamePlatformServices_ReturnValue_1;    // 0x08D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FGamePlatformUserAccountData           CallFunc_GetLocalUserAccount_ReturnValue;          // 0x08D8(0x00C0)(ConstParm)
};
static_assert(alignof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel) == 0x000008, "Wrong alignment on WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel");
static_assert(sizeof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel) == 0x000998, "Wrong size on WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, EntryPoint) == 0x000000, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, Temp_name_Variable) == 0x000004, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::Temp_name_Variable' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_GetGameInstance_ReturnValue) == 0x000010, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_GetGameInstance_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_DynamicCast_AsAc_Game_Instance) == 0x000018, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_DynamicCast_AsAc_Game_Instance' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_DynamicCast_bSuccess) == 0x000020, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_Event_MyGeometry) == 0x000024, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_Event_MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_Event_InDeltaTime) == 0x00005C, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_Event_InDeltaTime' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_Conv_StringToText_ReturnValue) == 0x000060, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_Conv_StringToText_ReturnValue_1) == 0x000078, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_Conv_StringToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_ComponentBoundEvent_Text_5) == 0x000090, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_ComponentBoundEvent_Text_5' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_ComponentBoundEvent_Text_4) == 0x0000A8, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_ComponentBoundEvent_Text_4' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_ComponentBoundEvent_Text_3) == 0x0000C0, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_ComponentBoundEvent_Text_3' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_ComponentBoundEvent_Text_2) == 0x0000D8, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_ComponentBoundEvent_Text_2' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_ComponentBoundEvent_Text_1) == 0x0000F0, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_ComponentBoundEvent_Text_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_GetText_ReturnValue) == 0x000108, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_GetText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_GetText_ReturnValue_1) == 0x000120, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_GetText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_Conv_TextToString_ReturnValue) == 0x000138, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_Conv_TextToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_ValidateAbbreviation_Value_As_String) == 0x000148, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_ValidateAbbreviation_Value_As_String' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_TextIsEmpty_ReturnValue) == 0x000158, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_TextIsEmpty_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_GetText_ReturnValue_2) == 0x000160, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_GetText_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_ComponentBoundEvent_Text) == 0x000178, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_ComponentBoundEvent_Text' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_Conv_TextToString_ReturnValue_1) == 0x000190, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_Conv_TextToString_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_TextIsEmpty_ReturnValue_1) == 0x0001A0, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_TextIsEmpty_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_GetGameMode_ReturnValue) == 0x0001A8, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_GetGameMode_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_DynamicCast_AsAc_Menu_Game_Mode) == 0x0001B0, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_DynamicCast_AsAc_Menu_Game_Mode' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_DynamicCast_bSuccess_1) == 0x0001B8, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_Map_Find_Value) == 0x0001C0, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_Map_Find_Value' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_Map_Find_ReturnValue) == 0x0002B0, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_Map_Find_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_GetGameMode_ReturnValue_1) == 0x0002B8, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_GetGameMode_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_Conv_StringToText_ReturnValue_2) == 0x0002C0, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_Conv_StringToText_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_DynamicCast_AsAc_Menu_Game_Mode_1) == 0x0002D8, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_DynamicCast_AsAc_Menu_Game_Mode_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_DynamicCast_bSuccess_2) == 0x0002E0, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_DynamicCast_bSuccess_2' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_GetText_ReturnValue_3) == 0x0002E8, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_GetText_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_Conv_StringToText_ReturnValue_3) == 0x000300, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_Conv_StringToText_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_Conv_TextToString_ReturnValue_2) == 0x000318, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_Conv_TextToString_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_TextIsEmpty_ReturnValue_2) == 0x000328, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_TextIsEmpty_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_Conv_StringToText_ReturnValue_4) == 0x000330, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_Conv_StringToText_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_GetText_ReturnValue_4) == 0x000348, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_GetText_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_GetText_ReturnValue_5) == 0x000360, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_GetText_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_Conv_TextToString_ReturnValue_3) == 0x000378, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_Conv_TextToString_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_Conv_TextToString_ReturnValue_4) == 0x000388, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_Conv_TextToString_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_TextIsEmpty_ReturnValue_3) == 0x000398, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_TextIsEmpty_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_TextIsEmpty_ReturnValue_4) == 0x000399, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_TextIsEmpty_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_CustomEvent_Text_2) == 0x0003A0, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_CustomEvent_Text_2' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_CustomEvent_CommitMethod_2) == 0x0003B8, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_CustomEvent_CommitMethod_2' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_SwitchEnum_CmpSuccess) == 0x0003B9, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_CreateDelegate_OutputDelegate) == 0x0003BC, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_CustomEvent_Text_1) == 0x0003D0, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_CustomEvent_Text_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_CustomEvent_CommitMethod_1) == 0x0003E8, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_CustomEvent_CommitMethod_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_GetGameInstance_ReturnValue_1) == 0x0003F0, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_GetGameInstance_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_SwitchEnum_CmpSuccess_1) == 0x0003F8, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_SwitchEnum_CmpSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_DynamicCast_AsAc_Game_Instance_1) == 0x000400, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_DynamicCast_AsAc_Game_Instance_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_DynamicCast_bSuccess_3) == 0x000408, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_DynamicCast_bSuccess_3' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, Temp_name_Variable_1) == 0x00040C, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::Temp_name_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_GetOnlineServices_ReturnValue) == 0x000418, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_GetOnlineServices_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_GetGamePlatformServices_ReturnValue) == 0x000420, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_GetGamePlatformServices_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_Map_Find_Value_1) == 0x000428, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_Map_Find_Value_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_Map_Find_ReturnValue_1) == 0x000518, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_Map_Find_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_CustomEvent_Text) == 0x000520, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_CustomEvent_Text' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_CustomEvent_CommitMethod) == 0x000538, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_CustomEvent_CommitMethod' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_SwitchEnum_CmpSuccess_2) == 0x000539, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_SwitchEnum_CmpSuccess_2' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_SetFieldsInStruct_StructOut) == 0x000540, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_SetFieldsInStruct_StructOut' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_GetText_ReturnValue_6) == 0x000630, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_GetText_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_Conv_StringToText_ReturnValue_5) == 0x000648, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_Conv_StringToText_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_TextToUpper_ReturnValue) == 0x000660, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_TextToUpper_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_Conv_TextToString_ReturnValue_5) == 0x000678, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_Conv_TextToString_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_Conv_StringToText_ReturnValue_6) == 0x000688, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_Conv_StringToText_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_Conv_NameToString_ReturnValue) == 0x0006A0, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_Conv_NameToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_GetGameMode_ReturnValue_2) == 0x0006B0, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_GetGameMode_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_DynamicCast_AsAc_Menu_Game_Mode_2) == 0x0006B8, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_DynamicCast_AsAc_Menu_Game_Mode_2' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_DynamicCast_bSuccess_4) == 0x0006C0, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_DynamicCast_bSuccess_4' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_Map_Find_Value_2) == 0x0006C8, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_Map_Find_Value_2' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_Map_Find_ReturnValue_2) == 0x0007B8, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_Map_Find_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_SetFieldsInStruct_StructOut_1) == 0x0007C0, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_SetFieldsInStruct_StructOut_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_saveCustomDriver_ReturnValue) == 0x0008B0, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_saveCustomDriver_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_GetGameInstance_ReturnValue_2) == 0x0008B8, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_GetGameInstance_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_DynamicCast_AsAc_Game_Instance_2) == 0x0008C0, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_DynamicCast_AsAc_Game_Instance_2' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, K2Node_DynamicCast_bSuccess_5) == 0x0008C8, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::K2Node_DynamicCast_bSuccess_5' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_GetGamePlatformServices_ReturnValue_1) == 0x0008D0, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_GetGamePlatformServices_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel, CallFunc_GetLocalUserAccount_ReturnValue) == 0x0008D8, "Member 'WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel::CallFunc_GetLocalUserAccount_ReturnValue' has a wrong offset!");

// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.CustomEvent_2
// 0x0020 (0x0020 - 0x0000)
struct WDG_PlayerInfoPanel_C_CustomEvent_2 final
{
public:
	class FText                                   text;                                              // 0x0000(0x0018)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	ETextCommit                                   CommitMethod;                                      // 0x0018(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_PlayerInfoPanel_C_CustomEvent_2) == 0x000008, "Wrong alignment on WDG_PlayerInfoPanel_C_CustomEvent_2");
static_assert(sizeof(WDG_PlayerInfoPanel_C_CustomEvent_2) == 0x000020, "Wrong size on WDG_PlayerInfoPanel_C_CustomEvent_2");
static_assert(offsetof(WDG_PlayerInfoPanel_C_CustomEvent_2, text) == 0x000000, "Member 'WDG_PlayerInfoPanel_C_CustomEvent_2::text' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_CustomEvent_2, CommitMethod) == 0x000018, "Member 'WDG_PlayerInfoPanel_C_CustomEvent_2::CommitMethod' has a wrong offset!");

// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.CustomEvent_1
// 0x0020 (0x0020 - 0x0000)
struct WDG_PlayerInfoPanel_C_CustomEvent_1 final
{
public:
	class FText                                   text;                                              // 0x0000(0x0018)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	ETextCommit                                   CommitMethod;                                      // 0x0018(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_PlayerInfoPanel_C_CustomEvent_1) == 0x000008, "Wrong alignment on WDG_PlayerInfoPanel_C_CustomEvent_1");
static_assert(sizeof(WDG_PlayerInfoPanel_C_CustomEvent_1) == 0x000020, "Wrong size on WDG_PlayerInfoPanel_C_CustomEvent_1");
static_assert(offsetof(WDG_PlayerInfoPanel_C_CustomEvent_1, text) == 0x000000, "Member 'WDG_PlayerInfoPanel_C_CustomEvent_1::text' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_CustomEvent_1, CommitMethod) == 0x000018, "Member 'WDG_PlayerInfoPanel_C_CustomEvent_1::CommitMethod' has a wrong offset!");

// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.CustomEvent_0
// 0x0020 (0x0020 - 0x0000)
struct WDG_PlayerInfoPanel_C_CustomEvent_0 final
{
public:
	class FText                                   text;                                              // 0x0000(0x0018)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	ETextCommit                                   CommitMethod;                                      // 0x0018(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_PlayerInfoPanel_C_CustomEvent_0) == 0x000008, "Wrong alignment on WDG_PlayerInfoPanel_C_CustomEvent_0");
static_assert(sizeof(WDG_PlayerInfoPanel_C_CustomEvent_0) == 0x000020, "Wrong size on WDG_PlayerInfoPanel_C_CustomEvent_0");
static_assert(offsetof(WDG_PlayerInfoPanel_C_CustomEvent_0, text) == 0x000000, "Member 'WDG_PlayerInfoPanel_C_CustomEvent_0::text' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_CustomEvent_0, CommitMethod) == 0x000018, "Member 'WDG_PlayerInfoPanel_C_CustomEvent_0::CommitMethod' has a wrong offset!");

// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_2_AcTextInputEvent__DelegateSignature
// 0x0018 (0x0018 - 0x0000)
struct WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_2_AcTextInputEvent__DelegateSignature final
{
public:
	class FText                                   text;                                              // 0x0000(0x0018)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_2_AcTextInputEvent__DelegateSignature) == 0x000008, "Wrong alignment on WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_2_AcTextInputEvent__DelegateSignature");
static_assert(sizeof(WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_2_AcTextInputEvent__DelegateSignature) == 0x000018, "Wrong size on WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_2_AcTextInputEvent__DelegateSignature");
static_assert(offsetof(WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_2_AcTextInputEvent__DelegateSignature, text) == 0x000000, "Member 'WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_2_AcTextInputEvent__DelegateSignature::text' has a wrong offset!");

// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_1_AcTextInputEvent__DelegateSignature
// 0x0018 (0x0018 - 0x0000)
struct WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_1_AcTextInputEvent__DelegateSignature final
{
public:
	class FText                                   text;                                              // 0x0000(0x0018)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_1_AcTextInputEvent__DelegateSignature) == 0x000008, "Wrong alignment on WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_1_AcTextInputEvent__DelegateSignature");
static_assert(sizeof(WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_1_AcTextInputEvent__DelegateSignature) == 0x000018, "Wrong size on WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_1_AcTextInputEvent__DelegateSignature");
static_assert(offsetof(WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_1_AcTextInputEvent__DelegateSignature, text) == 0x000000, "Member 'WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_1_AcTextInputEvent__DelegateSignature::text' has a wrong offset!");

// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_0_AcTextInputEvent__DelegateSignature
// 0x0018 (0x0018 - 0x0000)
struct WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_0_AcTextInputEvent__DelegateSignature final
{
public:
	class FText                                   text;                                              // 0x0000(0x0018)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_0_AcTextInputEvent__DelegateSignature) == 0x000008, "Wrong alignment on WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_0_AcTextInputEvent__DelegateSignature");
static_assert(sizeof(WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_0_AcTextInputEvent__DelegateSignature) == 0x000018, "Wrong size on WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_0_AcTextInputEvent__DelegateSignature");
static_assert(offsetof(WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_0_AcTextInputEvent__DelegateSignature, text) == 0x000000, "Member 'WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_0_AcTextInputEvent__DelegateSignature::text' has a wrong offset!");

// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_10_AcTextInputEvent__DelegateSignature
// 0x0018 (0x0018 - 0x0000)
struct WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_10_AcTextInputEvent__DelegateSignature final
{
public:
	class FText                                   text;                                              // 0x0000(0x0018)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_10_AcTextInputEvent__DelegateSignature) == 0x000008, "Wrong alignment on WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_10_AcTextInputEvent__DelegateSignature");
static_assert(sizeof(WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_10_AcTextInputEvent__DelegateSignature) == 0x000018, "Wrong size on WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_10_AcTextInputEvent__DelegateSignature");
static_assert(offsetof(WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_10_AcTextInputEvent__DelegateSignature, text) == 0x000000, "Member 'WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_10_AcTextInputEvent__DelegateSignature::text' has a wrong offset!");

// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_9_AcTextInputEvent__DelegateSignature
// 0x0018 (0x0018 - 0x0000)
struct WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_9_AcTextInputEvent__DelegateSignature final
{
public:
	class FText                                   text;                                              // 0x0000(0x0018)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_9_AcTextInputEvent__DelegateSignature) == 0x000008, "Wrong alignment on WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_9_AcTextInputEvent__DelegateSignature");
static_assert(sizeof(WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_9_AcTextInputEvent__DelegateSignature) == 0x000018, "Wrong size on WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_9_AcTextInputEvent__DelegateSignature");
static_assert(offsetof(WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_9_AcTextInputEvent__DelegateSignature, text) == 0x000000, "Member 'WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_9_AcTextInputEvent__DelegateSignature::text' has a wrong offset!");

// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_8_AcTextInputEvent__DelegateSignature
// 0x0018 (0x0018 - 0x0000)
struct WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_8_AcTextInputEvent__DelegateSignature final
{
public:
	class FText                                   text;                                              // 0x0000(0x0018)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_8_AcTextInputEvent__DelegateSignature) == 0x000008, "Wrong alignment on WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_8_AcTextInputEvent__DelegateSignature");
static_assert(sizeof(WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_8_AcTextInputEvent__DelegateSignature) == 0x000018, "Wrong size on WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_8_AcTextInputEvent__DelegateSignature");
static_assert(offsetof(WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_8_AcTextInputEvent__DelegateSignature, text) == 0x000000, "Member 'WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_8_AcTextInputEvent__DelegateSignature::text' has a wrong offset!");

// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.Tick
// 0x003C (0x003C - 0x0000)
struct WDG_PlayerInfoPanel_C_Tick final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	float                                         InDeltaTime;                                       // 0x0038(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_PlayerInfoPanel_C_Tick) == 0x000004, "Wrong alignment on WDG_PlayerInfoPanel_C_Tick");
static_assert(sizeof(WDG_PlayerInfoPanel_C_Tick) == 0x00003C, "Wrong size on WDG_PlayerInfoPanel_C_Tick");
static_assert(offsetof(WDG_PlayerInfoPanel_C_Tick, MyGeometry) == 0x000000, "Member 'WDG_PlayerInfoPanel_C_Tick::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_Tick, InDeltaTime) == 0x000038, "Member 'WDG_PlayerInfoPanel_C_Tick::InDeltaTime' has a wrong offset!");

// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.GenerateAbbreviation
// 0x00A0 (0x00A0 - 0x0000)
struct WDG_PlayerInfoPanel_C_GenerateAbbreviation final
{
public:
	class FString                                 ReturnValue;                                       // 0x0000(0x0010)(Parm, OutParm, ZeroConstructor, ReturnParm, HasGetValueTypeHash)
	class FText                                   CallFunc_GetText_ReturnValue;                      // 0x0010(0x0018)()
	class FText                                   CallFunc_GetText_ReturnValue_1;                    // 0x0028(0x0018)()
	class FString                                 CallFunc_Conv_TextToString_ReturnValue;            // 0x0040(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_TextToString_ReturnValue_1;          // 0x0050(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_GetSubstring_ReturnValue;                 // 0x0060(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_GetSubstring_ReturnValue_1;               // 0x0070(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue;                // 0x0080(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_ToUpper_ReturnValue;                      // 0x0090(0x0010)(ZeroConstructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_PlayerInfoPanel_C_GenerateAbbreviation) == 0x000008, "Wrong alignment on WDG_PlayerInfoPanel_C_GenerateAbbreviation");
static_assert(sizeof(WDG_PlayerInfoPanel_C_GenerateAbbreviation) == 0x0000A0, "Wrong size on WDG_PlayerInfoPanel_C_GenerateAbbreviation");
static_assert(offsetof(WDG_PlayerInfoPanel_C_GenerateAbbreviation, ReturnValue) == 0x000000, "Member 'WDG_PlayerInfoPanel_C_GenerateAbbreviation::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_GenerateAbbreviation, CallFunc_GetText_ReturnValue) == 0x000010, "Member 'WDG_PlayerInfoPanel_C_GenerateAbbreviation::CallFunc_GetText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_GenerateAbbreviation, CallFunc_GetText_ReturnValue_1) == 0x000028, "Member 'WDG_PlayerInfoPanel_C_GenerateAbbreviation::CallFunc_GetText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_GenerateAbbreviation, CallFunc_Conv_TextToString_ReturnValue) == 0x000040, "Member 'WDG_PlayerInfoPanel_C_GenerateAbbreviation::CallFunc_Conv_TextToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_GenerateAbbreviation, CallFunc_Conv_TextToString_ReturnValue_1) == 0x000050, "Member 'WDG_PlayerInfoPanel_C_GenerateAbbreviation::CallFunc_Conv_TextToString_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_GenerateAbbreviation, CallFunc_GetSubstring_ReturnValue) == 0x000060, "Member 'WDG_PlayerInfoPanel_C_GenerateAbbreviation::CallFunc_GetSubstring_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_GenerateAbbreviation, CallFunc_GetSubstring_ReturnValue_1) == 0x000070, "Member 'WDG_PlayerInfoPanel_C_GenerateAbbreviation::CallFunc_GetSubstring_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_GenerateAbbreviation, CallFunc_Concat_StrStr_ReturnValue) == 0x000080, "Member 'WDG_PlayerInfoPanel_C_GenerateAbbreviation::CallFunc_Concat_StrStr_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_GenerateAbbreviation, CallFunc_ToUpper_ReturnValue) == 0x000090, "Member 'WDG_PlayerInfoPanel_C_GenerateAbbreviation::CallFunc_ToUpper_ReturnValue' has a wrong offset!");

// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.ValidateAbbreviation
// 0x00A8 (0x00A8 - 0x0000)
struct WDG_PlayerInfoPanel_C_ValidateAbbreviation final
{
public:
	class FText                                   InText;                                            // 0x0000(0x0018)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	bool                                          GenerateAbbreviation;                              // 0x0018(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 Value_As_String;                                   // 0x0020(0x0010)(Parm, OutParm, ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_GenerateAbbreviation_ReturnValue;         // 0x0030(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   CallFunc_TextToUpper_ReturnValue;                  // 0x0040(0x0018)()
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x0058(0x0018)()
	class FString                                 CallFunc_Conv_TextToString_ReturnValue;            // 0x0070(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_TextToString_ReturnValue_1;          // 0x0080(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_TextToString_ReturnValue_2;          // 0x0090(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	int32                                         CallFunc_Len_ReturnValue;                          // 0x00A0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x00A4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x00A5(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_PlayerInfoPanel_C_ValidateAbbreviation) == 0x000008, "Wrong alignment on WDG_PlayerInfoPanel_C_ValidateAbbreviation");
static_assert(sizeof(WDG_PlayerInfoPanel_C_ValidateAbbreviation) == 0x0000A8, "Wrong size on WDG_PlayerInfoPanel_C_ValidateAbbreviation");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ValidateAbbreviation, InText) == 0x000000, "Member 'WDG_PlayerInfoPanel_C_ValidateAbbreviation::InText' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ValidateAbbreviation, GenerateAbbreviation) == 0x000018, "Member 'WDG_PlayerInfoPanel_C_ValidateAbbreviation::GenerateAbbreviation' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ValidateAbbreviation, Value_As_String) == 0x000020, "Member 'WDG_PlayerInfoPanel_C_ValidateAbbreviation::Value_As_String' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ValidateAbbreviation, CallFunc_GenerateAbbreviation_ReturnValue) == 0x000030, "Member 'WDG_PlayerInfoPanel_C_ValidateAbbreviation::CallFunc_GenerateAbbreviation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ValidateAbbreviation, CallFunc_TextToUpper_ReturnValue) == 0x000040, "Member 'WDG_PlayerInfoPanel_C_ValidateAbbreviation::CallFunc_TextToUpper_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ValidateAbbreviation, CallFunc_Conv_StringToText_ReturnValue) == 0x000058, "Member 'WDG_PlayerInfoPanel_C_ValidateAbbreviation::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ValidateAbbreviation, CallFunc_Conv_TextToString_ReturnValue) == 0x000070, "Member 'WDG_PlayerInfoPanel_C_ValidateAbbreviation::CallFunc_Conv_TextToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ValidateAbbreviation, CallFunc_Conv_TextToString_ReturnValue_1) == 0x000080, "Member 'WDG_PlayerInfoPanel_C_ValidateAbbreviation::CallFunc_Conv_TextToString_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ValidateAbbreviation, CallFunc_Conv_TextToString_ReturnValue_2) == 0x000090, "Member 'WDG_PlayerInfoPanel_C_ValidateAbbreviation::CallFunc_Conv_TextToString_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ValidateAbbreviation, CallFunc_Len_ReturnValue) == 0x0000A0, "Member 'WDG_PlayerInfoPanel_C_ValidateAbbreviation::CallFunc_Len_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ValidateAbbreviation, CallFunc_Less_IntInt_ReturnValue) == 0x0000A4, "Member 'WDG_PlayerInfoPanel_C_ValidateAbbreviation::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerInfoPanel_C_ValidateAbbreviation, CallFunc_BooleanAND_ReturnValue) == 0x0000A5, "Member 'WDG_PlayerInfoPanel_C_ValidateAbbreviation::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");

}

