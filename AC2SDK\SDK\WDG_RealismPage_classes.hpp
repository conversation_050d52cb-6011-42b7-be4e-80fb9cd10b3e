﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RealismPage

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RealismPage.WDG_RealismPage_C
// 0x0058 (0x05E0 - 0x0588)
class UWDG_RealismPage_C final : public URealismPage
{
public:
	class UWidgetAnimation*                       PageFade;                                          // 0x0588(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWDG_FooterButton_C*                    Back;                                              // 0x0590(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 bgImage;                                           // 0x0598(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_FooterButton_C*                    Confirm;                                           // 0x05A0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Footer_C*                          Footer;                                            // 0x05A8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Header_C*                          Header;                                            // 0x05B0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_MainSelectorFitToParent_C*         MainSelector;                                      // 0x05B8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Page_C*                            PageBase;                                          // 0x05C0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_HelpInMenu_C*                      RealismHelp;                                       // 0x05C8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_0;                                       // 0x05D0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_OptionsListWidget_C*               WDG_OptionsListWidget;                             // 0x05D8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RealismPage_C">();
	}
	static class UWDG_RealismPage_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RealismPage_C>();
	}
};
static_assert(alignof(UWDG_RealismPage_C) == 0x000008, "Wrong alignment on UWDG_RealismPage_C");
static_assert(sizeof(UWDG_RealismPage_C) == 0x0005E0, "Wrong size on UWDG_RealismPage_C");
static_assert(offsetof(UWDG_RealismPage_C, PageFade) == 0x000588, "Member 'UWDG_RealismPage_C::PageFade' has a wrong offset!");
static_assert(offsetof(UWDG_RealismPage_C, Back) == 0x000590, "Member 'UWDG_RealismPage_C::Back' has a wrong offset!");
static_assert(offsetof(UWDG_RealismPage_C, bgImage) == 0x000598, "Member 'UWDG_RealismPage_C::bgImage' has a wrong offset!");
static_assert(offsetof(UWDG_RealismPage_C, Confirm) == 0x0005A0, "Member 'UWDG_RealismPage_C::Confirm' has a wrong offset!");
static_assert(offsetof(UWDG_RealismPage_C, Footer) == 0x0005A8, "Member 'UWDG_RealismPage_C::Footer' has a wrong offset!");
static_assert(offsetof(UWDG_RealismPage_C, Header) == 0x0005B0, "Member 'UWDG_RealismPage_C::Header' has a wrong offset!");
static_assert(offsetof(UWDG_RealismPage_C, MainSelector) == 0x0005B8, "Member 'UWDG_RealismPage_C::MainSelector' has a wrong offset!");
static_assert(offsetof(UWDG_RealismPage_C, PageBase) == 0x0005C0, "Member 'UWDG_RealismPage_C::PageBase' has a wrong offset!");
static_assert(offsetof(UWDG_RealismPage_C, RealismHelp) == 0x0005C8, "Member 'UWDG_RealismPage_C::RealismHelp' has a wrong offset!");
static_assert(offsetof(UWDG_RealismPage_C, TextBlock_0) == 0x0005D0, "Member 'UWDG_RealismPage_C::TextBlock_0' has a wrong offset!");
static_assert(offsetof(UWDG_RealismPage_C, WDG_OptionsListWidget) == 0x0005D8, "Member 'UWDG_RealismPage_C::WDG_OptionsListWidget' has a wrong offset!");

}

