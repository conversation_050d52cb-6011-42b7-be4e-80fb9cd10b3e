﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SaveGameITem

#include "Basic.hpp"

#include "WDG_SaveGameITem_classes.hpp"
#include "WDG_SaveGameITem_parameters.hpp"


namespace SDK
{

// Function WDG_SaveGameITem.WDG_SaveGameItem_C.ExecuteUbergraph_WDG_SaveGameItem
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SaveGameItem_C::ExecuteUbergraph_WDG_SaveGameItem(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SaveGameItem_C", "ExecuteUbergraph_WDG_SaveGameItem");

	Params::WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SaveGameITem.WDG_SaveGameItem_C.BndEvt__btnSave_K2Node_ComponentBoundEvent_3_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_SaveGameItem_C::BndEvt__btnSave_K2Node_ComponentBoundEvent_3_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SaveGameItem_C", "BndEvt__btnSave_K2Node_ComponentBoundEvent_3_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SaveGameITem.WDG_SaveGameItem_C.BndEvt__btnDel_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_SaveGameItem_C::BndEvt__btnDel_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SaveGameItem_C", "BndEvt__btnDel_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SaveGameITem.WDG_SaveGameItem_C.BndEvt__btnPlay_K2Node_ComponentBoundEvent_5_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_SaveGameItem_C::BndEvt__btnPlay_K2Node_ComponentBoundEvent_5_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SaveGameItem_C", "BndEvt__btnPlay_K2Node_ComponentBoundEvent_5_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SaveGameITem.WDG_SaveGameItem_C.BP_SetHighlight
// (Event, Public, BlueprintEvent)
// Parameters:
// bool                                    highlighted                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SaveGameItem_C::BP_SetHighlight(bool highlighted)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SaveGameItem_C", "BP_SetHighlight");

	Params::WDG_SaveGameItem_C_BP_SetHighlight Parms{};

	Parms.highlighted = highlighted;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SaveGameITem.WDG_SaveGameItem_C.BP_MouseOver
// (Event, Public, BlueprintEvent)

void UWDG_SaveGameItem_C::BP_MouseOver()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SaveGameItem_C", "BP_MouseOver");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SaveGameITem.WDG_SaveGameItem_C.BP_MouseLeave
// (Event, Public, BlueprintEvent)

void UWDG_SaveGameItem_C::BP_MouseLeave()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SaveGameItem_C", "BP_MouseLeave");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SaveGameITem.WDG_SaveGameItem_C.OnAddedToFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_SaveGameItem_C::OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SaveGameItem_C", "OnAddedToFocusPath");

	Params::WDG_SaveGameItem_C_OnAddedToFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SaveGameITem.WDG_SaveGameItem_C.OnRemovedFromFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_SaveGameItem_C::OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SaveGameItem_C", "OnRemovedFromFocusPath");

	Params::WDG_SaveGameItem_C_OnRemovedFromFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SaveGameITem.WDG_SaveGameItem_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_SaveGameItem_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SaveGameItem_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}

}

