﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ToggleablePanel

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"
#include "UMG_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ToggleablePanel.WDG_ToggleablePanel_C
// 0x0050 (0x0630 - 0x05E0)
class UWDG_ToggleablePanel_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UExtendedButton_C*                      btnToggleDetectedDevices;                          // 0x05E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             Content;                                           // 0x05F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class USizeBox*                               mainBox;                                           // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_49;                                      // 0x0600(0x0008)(ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtToggleLabel;                                    // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class FText                                   Title;                                             // 0x0610(0x0018)(Edit, BlueprintVisible)
	bool                                          ExpandInEditor;                                    // 0x0628(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)

public:
	void ExecuteUbergraph_WDG_ToggleablePanel(int32 EntryPoint);
	void BndEvt__btnToggleDetectedDevices_K2Node_ComponentBoundEvent_1_OnButtonPressed__DelegateSignature();
	void PreConstruct(bool IsDesignTime);
	ESlateVisibility GetPanelExpanded();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ToggleablePanel_C">();
	}
	static class UWDG_ToggleablePanel_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ToggleablePanel_C>();
	}
};
static_assert(alignof(UWDG_ToggleablePanel_C) == 0x000008, "Wrong alignment on UWDG_ToggleablePanel_C");
static_assert(sizeof(UWDG_ToggleablePanel_C) == 0x000630, "Wrong size on UWDG_ToggleablePanel_C");
static_assert(offsetof(UWDG_ToggleablePanel_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_ToggleablePanel_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_ToggleablePanel_C, btnToggleDetectedDevices) == 0x0005E8, "Member 'UWDG_ToggleablePanel_C::btnToggleDetectedDevices' has a wrong offset!");
static_assert(offsetof(UWDG_ToggleablePanel_C, Content) == 0x0005F0, "Member 'UWDG_ToggleablePanel_C::Content' has a wrong offset!");
static_assert(offsetof(UWDG_ToggleablePanel_C, mainBox) == 0x0005F8, "Member 'UWDG_ToggleablePanel_C::mainBox' has a wrong offset!");
static_assert(offsetof(UWDG_ToggleablePanel_C, TextBlock_49) == 0x000600, "Member 'UWDG_ToggleablePanel_C::TextBlock_49' has a wrong offset!");
static_assert(offsetof(UWDG_ToggleablePanel_C, txtToggleLabel) == 0x000608, "Member 'UWDG_ToggleablePanel_C::txtToggleLabel' has a wrong offset!");
static_assert(offsetof(UWDG_ToggleablePanel_C, Title) == 0x000610, "Member 'UWDG_ToggleablePanel_C::Title' has a wrong offset!");
static_assert(offsetof(UWDG_ToggleablePanel_C, ExpandInEditor) == 0x000628, "Member 'UWDG_ToggleablePanel_C::ExpandInEditor' has a wrong offset!");

}

