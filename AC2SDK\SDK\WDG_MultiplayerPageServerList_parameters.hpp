﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_MultiplayerPageServerList

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "SlateCore_structs.hpp"
#include "InputCore_structs.hpp"
#include "UMG_structs.hpp"
#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.ExecuteUbergraph_WDG_MultiplayerPageServerList
// 0x0150 (0x0150 - 0x0000)
struct WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue;               // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_1;        // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_2;        // 0x0012(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x0013(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanOR_ReturnValue_1;                  // 0x0014(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	EContentType                                  K2Node_Event_content_type;                         // 0x0015(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_16[0x2];                                       // 0x0016(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_TextInput_C*                       K2Node_DynamicCast_AsWDG_Text_Input;               // 0x0018(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_21[0x7];                                       // 0x0021(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue_1;             // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcPageBase*                            CallFunc_GoToPage_ReturnValue;                     // 0x0030(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue_2;             // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EMPCarGroup                                   K2Node_ComponentBoundEvent_CarGroup;               // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_41[0x7];                                       // 0x0041(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue_3;             // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EDisconnectionReason                          K2Node_Event_reason;                               // 0x0050(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0051(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_52[0x6];                                       // 0x0052(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue_4;             // 0x0058(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_ComponentBoundEvent_IsOn_2;                 // 0x0060(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0061(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          K2Node_ComponentBoundEvent_IsOn_1;                 // 0x0062(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_63[0x5];                                       // 0x0063(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	class UGenericSelectorItem*                   K2Node_ComponentBoundEvent_source;                 // 0x0068(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_current_index;          // 0x0070(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_current_value;          // 0x0074(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_ComponentBoundEvent_IsOn;                   // 0x0078(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsFilteringActive_ReturnValue;            // 0x0079(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_7A[0x2];                                       // 0x007A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         K2Node_Event_number_of_servers_pings;              // 0x007C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_Event_total_servers;                        // 0x0080(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_84[0x4];                                       // 0x0084(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData;              // 0x0088(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData_1;            // 0x00C8(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	TArray<struct FFormatArgumentData>            K2Node_MakeArray_Array;                            // 0x0108(0x0010)(ReferenceParm)
	class FText                                   CallFunc_Format_ReturnValue;                       // 0x0118(0x0018)()
	bool                                          CallFunc_IsFilteringActive_ReturnValue_1;          // 0x0130(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_131[0x7];                                      // 0x0131(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_InteractiveFooterButton_C*         K2Node_DynamicCast_AsWDG_Interactive_Footer_Button; // 0x0138(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x0140(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_EqualEqual_BoolBool_ReturnValue;          // 0x0141(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_142[0x2];                                      // 0x0142(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         K2Node_ComponentBoundEvent_CurrentOffset;          // 0x0144(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_3;        // 0x0148(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanOR_ReturnValue_2;                  // 0x0149(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList) == 0x000008, "Wrong alignment on WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList");
static_assert(sizeof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList) == 0x000150, "Wrong size on WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, EntryPoint) == 0x000000, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, CallFunc_GetMenuManager_ReturnValue) == 0x000008, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::CallFunc_GetMenuManager_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000010, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, CallFunc_EqualEqual_ByteByte_ReturnValue_1) == 0x000011, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::CallFunc_EqualEqual_ByteByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, CallFunc_EqualEqual_ByteByte_ReturnValue_2) == 0x000012, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::CallFunc_EqualEqual_ByteByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, CallFunc_BooleanOR_ReturnValue) == 0x000013, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, CallFunc_BooleanOR_ReturnValue_1) == 0x000014, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::CallFunc_BooleanOR_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, K2Node_Event_content_type) == 0x000015, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::K2Node_Event_content_type' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, K2Node_DynamicCast_AsWDG_Text_Input) == 0x000018, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::K2Node_DynamicCast_AsWDG_Text_Input' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, K2Node_DynamicCast_bSuccess) == 0x000020, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, CallFunc_GetMenuManager_ReturnValue_1) == 0x000028, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::CallFunc_GetMenuManager_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, CallFunc_GoToPage_ReturnValue) == 0x000030, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::CallFunc_GoToPage_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, CallFunc_GetMenuManager_ReturnValue_2) == 0x000038, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::CallFunc_GetMenuManager_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, K2Node_ComponentBoundEvent_CarGroup) == 0x000040, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::K2Node_ComponentBoundEvent_CarGroup' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, CallFunc_GetMenuManager_ReturnValue_3) == 0x000048, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::CallFunc_GetMenuManager_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, K2Node_Event_reason) == 0x000050, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::K2Node_Event_reason' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, K2Node_SwitchEnum_CmpSuccess) == 0x000051, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, CallFunc_GetMenuManager_ReturnValue_4) == 0x000058, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::CallFunc_GetMenuManager_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, K2Node_ComponentBoundEvent_IsOn_2) == 0x000060, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::K2Node_ComponentBoundEvent_IsOn_2' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, CallFunc_BooleanAND_ReturnValue) == 0x000061, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, K2Node_ComponentBoundEvent_IsOn_1) == 0x000062, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::K2Node_ComponentBoundEvent_IsOn_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, K2Node_ComponentBoundEvent_source) == 0x000068, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::K2Node_ComponentBoundEvent_source' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, K2Node_ComponentBoundEvent_current_index) == 0x000070, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::K2Node_ComponentBoundEvent_current_index' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, K2Node_ComponentBoundEvent_current_value) == 0x000074, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::K2Node_ComponentBoundEvent_current_value' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, K2Node_ComponentBoundEvent_IsOn) == 0x000078, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::K2Node_ComponentBoundEvent_IsOn' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, CallFunc_IsFilteringActive_ReturnValue) == 0x000079, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::CallFunc_IsFilteringActive_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, K2Node_Event_number_of_servers_pings) == 0x00007C, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::K2Node_Event_number_of_servers_pings' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, K2Node_Event_total_servers) == 0x000080, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::K2Node_Event_total_servers' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, K2Node_MakeStruct_FormatArgumentData) == 0x000088, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::K2Node_MakeStruct_FormatArgumentData' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, K2Node_MakeStruct_FormatArgumentData_1) == 0x0000C8, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::K2Node_MakeStruct_FormatArgumentData_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, K2Node_MakeArray_Array) == 0x000108, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, CallFunc_Format_ReturnValue) == 0x000118, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::CallFunc_Format_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, CallFunc_IsFilteringActive_ReturnValue_1) == 0x000130, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::CallFunc_IsFilteringActive_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, K2Node_DynamicCast_AsWDG_Interactive_Footer_Button) == 0x000138, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::K2Node_DynamicCast_AsWDG_Interactive_Footer_Button' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, K2Node_DynamicCast_bSuccess_1) == 0x000140, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, CallFunc_EqualEqual_BoolBool_ReturnValue) == 0x000141, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::CallFunc_EqualEqual_BoolBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, K2Node_ComponentBoundEvent_CurrentOffset) == 0x000144, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::K2Node_ComponentBoundEvent_CurrentOffset' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, CallFunc_EqualEqual_ByteByte_ReturnValue_3) == 0x000148, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::CallFunc_EqualEqual_ByteByte_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList, CallFunc_BooleanOR_ReturnValue_2) == 0x000149, "Member 'WDG_MultiplayerPageServerList_C_ExecuteUbergraph_WDG_MultiplayerPageServerList::CallFunc_BooleanOR_ReturnValue_2' has a wrong offset!");

// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.BndEvt__ServerList_K2Node_ComponentBoundEvent_3_OnUserScrolledEvent__DelegateSignature
// 0x0004 (0x0004 - 0x0000)
struct WDG_MultiplayerPageServerList_C_BndEvt__ServerList_K2Node_ComponentBoundEvent_3_OnUserScrolledEvent__DelegateSignature final
{
public:
	float                                         CurrentOffset;                                     // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_MultiplayerPageServerList_C_BndEvt__ServerList_K2Node_ComponentBoundEvent_3_OnUserScrolledEvent__DelegateSignature) == 0x000004, "Wrong alignment on WDG_MultiplayerPageServerList_C_BndEvt__ServerList_K2Node_ComponentBoundEvent_3_OnUserScrolledEvent__DelegateSignature");
static_assert(sizeof(WDG_MultiplayerPageServerList_C_BndEvt__ServerList_K2Node_ComponentBoundEvent_3_OnUserScrolledEvent__DelegateSignature) == 0x000004, "Wrong size on WDG_MultiplayerPageServerList_C_BndEvt__ServerList_K2Node_ComponentBoundEvent_3_OnUserScrolledEvent__DelegateSignature");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_BndEvt__ServerList_K2Node_ComponentBoundEvent_3_OnUserScrolledEvent__DelegateSignature, CurrentOffset) == 0x000000, "Member 'WDG_MultiplayerPageServerList_C_BndEvt__ServerList_K2Node_ComponentBoundEvent_3_OnUserScrolledEvent__DelegateSignature::CurrentOffset' has a wrong offset!");

// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.ServerPinged
// 0x0008 (0x0008 - 0x0000)
struct WDG_MultiplayerPageServerList_C_ServerPinged final
{
public:
	int32                                         number_of_servers_pings;                           // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         total_servers;                                     // 0x0004(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_MultiplayerPageServerList_C_ServerPinged) == 0x000004, "Wrong alignment on WDG_MultiplayerPageServerList_C_ServerPinged");
static_assert(sizeof(WDG_MultiplayerPageServerList_C_ServerPinged) == 0x000008, "Wrong size on WDG_MultiplayerPageServerList_C_ServerPinged");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ServerPinged, number_of_servers_pings) == 0x000000, "Member 'WDG_MultiplayerPageServerList_C_ServerPinged::number_of_servers_pings' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ServerPinged, total_servers) == 0x000004, "Member 'WDG_MultiplayerPageServerList_C_ServerPinged::total_servers' has a wrong offset!");

// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.BndEvt__btnJoinable_K2Node_ComponentBoundEvent_12_OnToggled__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_MultiplayerPageServerList_C_BndEvt__btnJoinable_K2Node_ComponentBoundEvent_12_OnToggled__DelegateSignature final
{
public:
	bool                                          isOn;                                              // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_MultiplayerPageServerList_C_BndEvt__btnJoinable_K2Node_ComponentBoundEvent_12_OnToggled__DelegateSignature) == 0x000001, "Wrong alignment on WDG_MultiplayerPageServerList_C_BndEvt__btnJoinable_K2Node_ComponentBoundEvent_12_OnToggled__DelegateSignature");
static_assert(sizeof(WDG_MultiplayerPageServerList_C_BndEvt__btnJoinable_K2Node_ComponentBoundEvent_12_OnToggled__DelegateSignature) == 0x000001, "Wrong size on WDG_MultiplayerPageServerList_C_BndEvt__btnJoinable_K2Node_ComponentBoundEvent_12_OnToggled__DelegateSignature");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_BndEvt__btnJoinable_K2Node_ComponentBoundEvent_12_OnToggled__DelegateSignature, isOn) == 0x000000, "Member 'WDG_MultiplayerPageServerList_C_BndEvt__btnJoinable_K2Node_ComponentBoundEvent_12_OnToggled__DelegateSignature::isOn' has a wrong offset!");

// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.BndEvt__sliderPingLimit_K2Node_ComponentBoundEvent_11_SelectorChanged__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_MultiplayerPageServerList_C_BndEvt__sliderPingLimit_K2Node_ComponentBoundEvent_11_SelectorChanged__DelegateSignature final
{
public:
	class UGenericSelectorItem*                   Source;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_index;                                     // 0x0008(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_value;                                     // 0x000C(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_MultiplayerPageServerList_C_BndEvt__sliderPingLimit_K2Node_ComponentBoundEvent_11_SelectorChanged__DelegateSignature) == 0x000008, "Wrong alignment on WDG_MultiplayerPageServerList_C_BndEvt__sliderPingLimit_K2Node_ComponentBoundEvent_11_SelectorChanged__DelegateSignature");
static_assert(sizeof(WDG_MultiplayerPageServerList_C_BndEvt__sliderPingLimit_K2Node_ComponentBoundEvent_11_SelectorChanged__DelegateSignature) == 0x000010, "Wrong size on WDG_MultiplayerPageServerList_C_BndEvt__sliderPingLimit_K2Node_ComponentBoundEvent_11_SelectorChanged__DelegateSignature");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_BndEvt__sliderPingLimit_K2Node_ComponentBoundEvent_11_SelectorChanged__DelegateSignature, Source) == 0x000000, "Member 'WDG_MultiplayerPageServerList_C_BndEvt__sliderPingLimit_K2Node_ComponentBoundEvent_11_SelectorChanged__DelegateSignature::Source' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_BndEvt__sliderPingLimit_K2Node_ComponentBoundEvent_11_SelectorChanged__DelegateSignature, current_index) == 0x000008, "Member 'WDG_MultiplayerPageServerList_C_BndEvt__sliderPingLimit_K2Node_ComponentBoundEvent_11_SelectorChanged__DelegateSignature::current_index' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_BndEvt__sliderPingLimit_K2Node_ComponentBoundEvent_11_SelectorChanged__DelegateSignature, current_value) == 0x00000C, "Member 'WDG_MultiplayerPageServerList_C_BndEvt__sliderPingLimit_K2Node_ComponentBoundEvent_11_SelectorChanged__DelegateSignature::current_value' has a wrong offset!");

// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.BndEvt__btnSortPing_K2Node_ComponentBoundEvent_10_OnToggled__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_MultiplayerPageServerList_C_BndEvt__btnSortPing_K2Node_ComponentBoundEvent_10_OnToggled__DelegateSignature final
{
public:
	bool                                          isOn;                                              // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_MultiplayerPageServerList_C_BndEvt__btnSortPing_K2Node_ComponentBoundEvent_10_OnToggled__DelegateSignature) == 0x000001, "Wrong alignment on WDG_MultiplayerPageServerList_C_BndEvt__btnSortPing_K2Node_ComponentBoundEvent_10_OnToggled__DelegateSignature");
static_assert(sizeof(WDG_MultiplayerPageServerList_C_BndEvt__btnSortPing_K2Node_ComponentBoundEvent_10_OnToggled__DelegateSignature) == 0x000001, "Wrong size on WDG_MultiplayerPageServerList_C_BndEvt__btnSortPing_K2Node_ComponentBoundEvent_10_OnToggled__DelegateSignature");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_BndEvt__btnSortPing_K2Node_ComponentBoundEvent_10_OnToggled__DelegateSignature, isOn) == 0x000000, "Member 'WDG_MultiplayerPageServerList_C_BndEvt__btnSortPing_K2Node_ComponentBoundEvent_10_OnToggled__DelegateSignature::isOn' has a wrong offset!");

// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.BndEvt__btnSortDrivers_K2Node_ComponentBoundEvent_9_OnToggled__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_MultiplayerPageServerList_C_BndEvt__btnSortDrivers_K2Node_ComponentBoundEvent_9_OnToggled__DelegateSignature final
{
public:
	bool                                          isOn;                                              // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_MultiplayerPageServerList_C_BndEvt__btnSortDrivers_K2Node_ComponentBoundEvent_9_OnToggled__DelegateSignature) == 0x000001, "Wrong alignment on WDG_MultiplayerPageServerList_C_BndEvt__btnSortDrivers_K2Node_ComponentBoundEvent_9_OnToggled__DelegateSignature");
static_assert(sizeof(WDG_MultiplayerPageServerList_C_BndEvt__btnSortDrivers_K2Node_ComponentBoundEvent_9_OnToggled__DelegateSignature) == 0x000001, "Wrong size on WDG_MultiplayerPageServerList_C_BndEvt__btnSortDrivers_K2Node_ComponentBoundEvent_9_OnToggled__DelegateSignature");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_BndEvt__btnSortDrivers_K2Node_ComponentBoundEvent_9_OnToggled__DelegateSignature, isOn) == 0x000000, "Member 'WDG_MultiplayerPageServerList_C_BndEvt__btnSortDrivers_K2Node_ComponentBoundEvent_9_OnToggled__DelegateSignature::isOn' has a wrong offset!");

// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.OnServerDisconnection
// 0x0001 (0x0001 - 0x0000)
struct WDG_MultiplayerPageServerList_C_OnServerDisconnection final
{
public:
	EDisconnectionReason                          reason;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_MultiplayerPageServerList_C_OnServerDisconnection) == 0x000001, "Wrong alignment on WDG_MultiplayerPageServerList_C_OnServerDisconnection");
static_assert(sizeof(WDG_MultiplayerPageServerList_C_OnServerDisconnection) == 0x000001, "Wrong size on WDG_MultiplayerPageServerList_C_OnServerDisconnection");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_OnServerDisconnection, reason) == 0x000000, "Member 'WDG_MultiplayerPageServerList_C_OnServerDisconnection::reason' has a wrong offset!");

// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_2_OnCarGroupSelected__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_MultiplayerPageServerList_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_2_OnCarGroupSelected__DelegateSignature final
{
public:
	EMPCarGroup                                   CarGroup;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_MultiplayerPageServerList_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_2_OnCarGroupSelected__DelegateSignature) == 0x000001, "Wrong alignment on WDG_MultiplayerPageServerList_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_2_OnCarGroupSelected__DelegateSignature");
static_assert(sizeof(WDG_MultiplayerPageServerList_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_2_OnCarGroupSelected__DelegateSignature) == 0x000001, "Wrong size on WDG_MultiplayerPageServerList_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_2_OnCarGroupSelected__DelegateSignature");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_2_OnCarGroupSelected__DelegateSignature, CarGroup) == 0x000000, "Member 'WDG_MultiplayerPageServerList_C_BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_2_OnCarGroupSelected__DelegateSignature::CarGroup' has a wrong offset!");

// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.OnSeasonUnavailable
// 0x0001 (0x0001 - 0x0000)
struct WDG_MultiplayerPageServerList_C_OnSeasonUnavailable final
{
public:
	EContentType                                  content_type;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_MultiplayerPageServerList_C_OnSeasonUnavailable) == 0x000001, "Wrong alignment on WDG_MultiplayerPageServerList_C_OnSeasonUnavailable");
static_assert(sizeof(WDG_MultiplayerPageServerList_C_OnSeasonUnavailable) == 0x000001, "Wrong size on WDG_MultiplayerPageServerList_C_OnSeasonUnavailable");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_OnSeasonUnavailable, content_type) == 0x000000, "Member 'WDG_MultiplayerPageServerList_C_OnSeasonUnavailable::content_type' has a wrong offset!");

// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.FocusToConnect
// 0x0010 (0x0010 - 0x0000)
struct WDG_MultiplayerPageServerList_C_FocusToConnect final
{
public:
	EUINavigation                                 Navigation_0;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                ReturnValue;                                       // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_MultiplayerPageServerList_C_FocusToConnect) == 0x000008, "Wrong alignment on WDG_MultiplayerPageServerList_C_FocusToConnect");
static_assert(sizeof(WDG_MultiplayerPageServerList_C_FocusToConnect) == 0x000010, "Wrong size on WDG_MultiplayerPageServerList_C_FocusToConnect");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_FocusToConnect, Navigation_0) == 0x000000, "Member 'WDG_MultiplayerPageServerList_C_FocusToConnect::Navigation_0' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_FocusToConnect, ReturnValue) == 0x000008, "Member 'WDG_MultiplayerPageServerList_C_FocusToConnect::ReturnValue' has a wrong offset!");

// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.FocusToRefresh
// 0x0010 (0x0010 - 0x0000)
struct WDG_MultiplayerPageServerList_C_FocusToRefresh final
{
public:
	EUINavigation                                 Navigation_0;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                ReturnValue;                                       // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_MultiplayerPageServerList_C_FocusToRefresh) == 0x000008, "Wrong alignment on WDG_MultiplayerPageServerList_C_FocusToRefresh");
static_assert(sizeof(WDG_MultiplayerPageServerList_C_FocusToRefresh) == 0x000010, "Wrong size on WDG_MultiplayerPageServerList_C_FocusToRefresh");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_FocusToRefresh, Navigation_0) == 0x000000, "Member 'WDG_MultiplayerPageServerList_C_FocusToRefresh::Navigation_0' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_FocusToRefresh, ReturnValue) == 0x000008, "Member 'WDG_MultiplayerPageServerList_C_FocusToRefresh::ReturnValue' has a wrong offset!");

// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.PreConnectionCheck
// 0x02D8 (0x02D8 - 0x0000)
struct WDG_MultiplayerPageServerList_C_PreConnectionCheck final
{
public:
	struct FCarInfo                               Car;                                               // 0x0000(0x00E0)(BlueprintVisible, BlueprintReadOnly, Parm)
	struct FCircuitInfo                           circuit;                                           // 0x00E0(0x01F0)(BlueprintVisible, BlueprintReadOnly, Parm)
	bool                                          ReturnValue;                                       // 0x02D0(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_CanPlay_Result;                           // 0x02D1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_CanPlay_Result_1;                         // 0x02D2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x02D3(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_MultiplayerPageServerList_C_PreConnectionCheck) == 0x000008, "Wrong alignment on WDG_MultiplayerPageServerList_C_PreConnectionCheck");
static_assert(sizeof(WDG_MultiplayerPageServerList_C_PreConnectionCheck) == 0x0002D8, "Wrong size on WDG_MultiplayerPageServerList_C_PreConnectionCheck");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_PreConnectionCheck, Car) == 0x000000, "Member 'WDG_MultiplayerPageServerList_C_PreConnectionCheck::Car' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_PreConnectionCheck, circuit) == 0x0000E0, "Member 'WDG_MultiplayerPageServerList_C_PreConnectionCheck::circuit' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_PreConnectionCheck, ReturnValue) == 0x0002D0, "Member 'WDG_MultiplayerPageServerList_C_PreConnectionCheck::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_PreConnectionCheck, CallFunc_CanPlay_Result) == 0x0002D1, "Member 'WDG_MultiplayerPageServerList_C_PreConnectionCheck::CallFunc_CanPlay_Result' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_PreConnectionCheck, CallFunc_CanPlay_Result_1) == 0x0002D2, "Member 'WDG_MultiplayerPageServerList_C_PreConnectionCheck::CallFunc_CanPlay_Result_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_PreConnectionCheck, CallFunc_BooleanAND_ReturnValue) == 0x0002D3, "Member 'WDG_MultiplayerPageServerList_C_PreConnectionCheck::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");

// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.ShowErrorMessage
// 0x0018 (0x0018 - 0x0000)
struct WDG_MultiplayerPageServerList_C_ShowErrorMessage final
{
public:
	class FText                                   MessageText;                                       // 0x0000(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_MultiplayerPageServerList_C_ShowErrorMessage) == 0x000008, "Wrong alignment on WDG_MultiplayerPageServerList_C_ShowErrorMessage");
static_assert(sizeof(WDG_MultiplayerPageServerList_C_ShowErrorMessage) == 0x000018, "Wrong size on WDG_MultiplayerPageServerList_C_ShowErrorMessage");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_ShowErrorMessage, MessageText) == 0x000000, "Member 'WDG_MultiplayerPageServerList_C_ShowErrorMessage::MessageText' has a wrong offset!");

// Function WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C.OnPreviewKeyDown
// 0x02C8 (0x02C8 - 0x0000)
struct WDG_MultiplayerPageServerList_C_OnPreviewKeyDown final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FKeyEvent                              InKeyEvent;                                        // 0x0038(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm)
	struct FEventReply                            ReturnValue;                                       // 0x0070(0x00B8)(Parm, OutParm, ReturnParm)
	struct FKey                                   CallFunc_GetKey_ReturnValue;                       // 0x0128(0x0018)(HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue;            // 0x0140(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0141(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue_1;          // 0x0142(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue_2;          // 0x0143(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x0144(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0145(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_146[0x2];                                      // 0x0146(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_Handled_ReturnValue;                      // 0x0148(0x00B8)()
	bool                                          CallFunc_HasFocusedDescendants_ReturnValue;        // 0x0200(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_HasAnyUserFocus_ReturnValue;              // 0x0201(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanOR_ReturnValue_1;                  // 0x0202(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x0203(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_2;                 // 0x0204(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_205[0x3];                                      // 0x0205(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_Unhandled_ReturnValue;                    // 0x0208(0x00B8)()
	bool                                          CallFunc_HasFocusedDescendants_ReturnValue_1;      // 0x02C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_MultiplayerPageServerList_C_OnPreviewKeyDown) == 0x000008, "Wrong alignment on WDG_MultiplayerPageServerList_C_OnPreviewKeyDown");
static_assert(sizeof(WDG_MultiplayerPageServerList_C_OnPreviewKeyDown) == 0x0002C8, "Wrong size on WDG_MultiplayerPageServerList_C_OnPreviewKeyDown");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_OnPreviewKeyDown, MyGeometry) == 0x000000, "Member 'WDG_MultiplayerPageServerList_C_OnPreviewKeyDown::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_OnPreviewKeyDown, InKeyEvent) == 0x000038, "Member 'WDG_MultiplayerPageServerList_C_OnPreviewKeyDown::InKeyEvent' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_OnPreviewKeyDown, ReturnValue) == 0x000070, "Member 'WDG_MultiplayerPageServerList_C_OnPreviewKeyDown::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_OnPreviewKeyDown, CallFunc_GetKey_ReturnValue) == 0x000128, "Member 'WDG_MultiplayerPageServerList_C_OnPreviewKeyDown::CallFunc_GetKey_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_OnPreviewKeyDown, CallFunc_EqualEqual_KeyKey_ReturnValue) == 0x000140, "Member 'WDG_MultiplayerPageServerList_C_OnPreviewKeyDown::CallFunc_EqualEqual_KeyKey_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_OnPreviewKeyDown, CallFunc_Not_PreBool_ReturnValue) == 0x000141, "Member 'WDG_MultiplayerPageServerList_C_OnPreviewKeyDown::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_OnPreviewKeyDown, CallFunc_EqualEqual_KeyKey_ReturnValue_1) == 0x000142, "Member 'WDG_MultiplayerPageServerList_C_OnPreviewKeyDown::CallFunc_EqualEqual_KeyKey_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_OnPreviewKeyDown, CallFunc_EqualEqual_KeyKey_ReturnValue_2) == 0x000143, "Member 'WDG_MultiplayerPageServerList_C_OnPreviewKeyDown::CallFunc_EqualEqual_KeyKey_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_OnPreviewKeyDown, CallFunc_BooleanOR_ReturnValue) == 0x000144, "Member 'WDG_MultiplayerPageServerList_C_OnPreviewKeyDown::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_OnPreviewKeyDown, CallFunc_BooleanAND_ReturnValue) == 0x000145, "Member 'WDG_MultiplayerPageServerList_C_OnPreviewKeyDown::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_OnPreviewKeyDown, CallFunc_Handled_ReturnValue) == 0x000148, "Member 'WDG_MultiplayerPageServerList_C_OnPreviewKeyDown::CallFunc_Handled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_OnPreviewKeyDown, CallFunc_HasFocusedDescendants_ReturnValue) == 0x000200, "Member 'WDG_MultiplayerPageServerList_C_OnPreviewKeyDown::CallFunc_HasFocusedDescendants_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_OnPreviewKeyDown, CallFunc_HasAnyUserFocus_ReturnValue) == 0x000201, "Member 'WDG_MultiplayerPageServerList_C_OnPreviewKeyDown::CallFunc_HasAnyUserFocus_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_OnPreviewKeyDown, CallFunc_BooleanOR_ReturnValue_1) == 0x000202, "Member 'WDG_MultiplayerPageServerList_C_OnPreviewKeyDown::CallFunc_BooleanOR_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_OnPreviewKeyDown, CallFunc_BooleanAND_ReturnValue_1) == 0x000203, "Member 'WDG_MultiplayerPageServerList_C_OnPreviewKeyDown::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_OnPreviewKeyDown, CallFunc_BooleanAND_ReturnValue_2) == 0x000204, "Member 'WDG_MultiplayerPageServerList_C_OnPreviewKeyDown::CallFunc_BooleanAND_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_OnPreviewKeyDown, CallFunc_Unhandled_ReturnValue) == 0x000208, "Member 'WDG_MultiplayerPageServerList_C_OnPreviewKeyDown::CallFunc_Unhandled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPageServerList_C_OnPreviewKeyDown, CallFunc_HasFocusedDescendants_ReturnValue_1) == 0x0002C0, "Member 'WDG_MultiplayerPageServerList_C_OnPreviewKeyDown::CallFunc_HasFocusedDescendants_ReturnValue_1' has a wrong offset!");

}

