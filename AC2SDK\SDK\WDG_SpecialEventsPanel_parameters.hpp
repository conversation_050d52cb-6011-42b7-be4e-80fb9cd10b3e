﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SpecialEventsPanel

#include "Basic.hpp"

#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_SpecialEventsPanel.WDG_SpecialEventsPanel_C.ExecuteUbergraph_WDG_SpecialEventsPanel
// 0x0110 (0x0110 - 0x0000)
struct WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0008(0x0008)(ZeroConstructor, Is<PERSON>lainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_1;              // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_2;              // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_3;              // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSlateColor                            CallFunc_GetMenuColor_ReturnValue;                 // 0x0028(0x0028)()
	int32                                         CallFunc_GetChildrenCount_ReturnValue;             // 0x0050(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x0054(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_55[0x3];                                       // 0x0055(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                CallFunc_GetChildAt_ReturnValue;                   // 0x0058(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UTextBlock*                             K2Node_DynamicCast_AsText;                         // 0x0060(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0068(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_69[0x7];                                       // 0x0069(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateColor                            CallFunc_GetMenuColor_ReturnValue_1;               // 0x0070(0x0028)()
	struct FSlateColor                            CallFunc_GetMenuColor_ReturnValue_2;               // 0x0098(0x0028)()
	int32                                         CallFunc_GetChildrenCount_ReturnValue_1;           // 0x00C0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_1;             // 0x00C4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_C5[0x3];                                       // 0x00C5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                CallFunc_GetChildAt_ReturnValue_1;                 // 0x00C8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UTextBlock*                             K2Node_DynamicCast_AsText_1;                       // 0x00D0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x00D8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_D9[0x7];                                       // 0x00D9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateColor                            CallFunc_GetMenuColor_ReturnValue_3;               // 0x00E0(0x0028)()
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0108(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0109(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel) == 0x000008, "Wrong alignment on WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel");
static_assert(sizeof(WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel) == 0x000110, "Wrong size on WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel");
static_assert(offsetof(WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel, EntryPoint) == 0x000000, "Member 'WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel, CallFunc_PlayAnimation_ReturnValue) == 0x000008, "Member 'WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel, CallFunc_PlayAnimation_ReturnValue_1) == 0x000010, "Member 'WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel::CallFunc_PlayAnimation_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel, CallFunc_PlayAnimation_ReturnValue_2) == 0x000018, "Member 'WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel::CallFunc_PlayAnimation_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel, CallFunc_PlayAnimation_ReturnValue_3) == 0x000020, "Member 'WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel::CallFunc_PlayAnimation_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel, CallFunc_GetMenuColor_ReturnValue) == 0x000028, "Member 'WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel::CallFunc_GetMenuColor_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel, CallFunc_GetChildrenCount_ReturnValue) == 0x000050, "Member 'WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel::CallFunc_GetChildrenCount_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel, CallFunc_Greater_IntInt_ReturnValue) == 0x000054, "Member 'WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel, CallFunc_GetChildAt_ReturnValue) == 0x000058, "Member 'WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel::CallFunc_GetChildAt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel, K2Node_DynamicCast_AsText) == 0x000060, "Member 'WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel::K2Node_DynamicCast_AsText' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel, K2Node_DynamicCast_bSuccess) == 0x000068, "Member 'WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel, CallFunc_GetMenuColor_ReturnValue_1) == 0x000070, "Member 'WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel::CallFunc_GetMenuColor_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel, CallFunc_GetMenuColor_ReturnValue_2) == 0x000098, "Member 'WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel::CallFunc_GetMenuColor_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel, CallFunc_GetChildrenCount_ReturnValue_1) == 0x0000C0, "Member 'WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel::CallFunc_GetChildrenCount_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel, CallFunc_Greater_IntInt_ReturnValue_1) == 0x0000C4, "Member 'WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel::CallFunc_Greater_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel, CallFunc_GetChildAt_ReturnValue_1) == 0x0000C8, "Member 'WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel::CallFunc_GetChildAt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel, K2Node_DynamicCast_AsText_1) == 0x0000D0, "Member 'WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel::K2Node_DynamicCast_AsText_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel, K2Node_DynamicCast_bSuccess_1) == 0x0000D8, "Member 'WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel, CallFunc_GetMenuColor_ReturnValue_3) == 0x0000E0, "Member 'WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel::CallFunc_GetMenuColor_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel, K2Node_Event_IsDesignTime) == 0x000108, "Member 'WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel, CallFunc_IsValid_ReturnValue) == 0x000109, "Member 'WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel::CallFunc_IsValid_ReturnValue' has a wrong offset!");

// Function WDG_SpecialEventsPanel.WDG_SpecialEventsPanel_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_SpecialEventsPanel_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SpecialEventsPanel_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_SpecialEventsPanel_C_PreConstruct");
static_assert(sizeof(WDG_SpecialEventsPanel_C_PreConstruct) == 0x000001, "Wrong size on WDG_SpecialEventsPanel_C_PreConstruct");
static_assert(offsetof(WDG_SpecialEventsPanel_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_SpecialEventsPanel_C_PreConstruct::IsDesignTime' has a wrong offset!");

}

