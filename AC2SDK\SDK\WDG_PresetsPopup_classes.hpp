﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PresetsPopup

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_PresetsPopup.WDG_PresetsPopup_C
// 0x0048 (0x0740 - 0x06F8)
class UWDG_PresetsPopup_C final : public UPresetsPopup
{
public:
	class UBackgroundBlur*                        BackgroundBlur_0;                                  // 0x06F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 BackgroundTransparency;                            // 0x0700(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSki<PERSON>, NoD<PERSON>ru<PERSON>, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             Filter1Text;                                       // 0x0708(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_0;                                           // 0x0710(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_1;                                           // 0x0718(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_5;                                           // 0x0720(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_104;                                         // 0x0728(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	TMulticastInlineDelegate<void(const struct FVector2D& NewParam)> WDG_PopUp_UpdatePosition;       // 0x0730(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_PresetsPopup_C">();
	}
	static class UWDG_PresetsPopup_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_PresetsPopup_C>();
	}
};
static_assert(alignof(UWDG_PresetsPopup_C) == 0x000008, "Wrong alignment on UWDG_PresetsPopup_C");
static_assert(sizeof(UWDG_PresetsPopup_C) == 0x000740, "Wrong size on UWDG_PresetsPopup_C");
static_assert(offsetof(UWDG_PresetsPopup_C, BackgroundBlur_0) == 0x0006F8, "Member 'UWDG_PresetsPopup_C::BackgroundBlur_0' has a wrong offset!");
static_assert(offsetof(UWDG_PresetsPopup_C, BackgroundTransparency) == 0x000700, "Member 'UWDG_PresetsPopup_C::BackgroundTransparency' has a wrong offset!");
static_assert(offsetof(UWDG_PresetsPopup_C, Filter1Text) == 0x000708, "Member 'UWDG_PresetsPopup_C::Filter1Text' has a wrong offset!");
static_assert(offsetof(UWDG_PresetsPopup_C, Image_0) == 0x000710, "Member 'UWDG_PresetsPopup_C::Image_0' has a wrong offset!");
static_assert(offsetof(UWDG_PresetsPopup_C, Image_1) == 0x000718, "Member 'UWDG_PresetsPopup_C::Image_1' has a wrong offset!");
static_assert(offsetof(UWDG_PresetsPopup_C, Image_5) == 0x000720, "Member 'UWDG_PresetsPopup_C::Image_5' has a wrong offset!");
static_assert(offsetof(UWDG_PresetsPopup_C, Image_104) == 0x000728, "Member 'UWDG_PresetsPopup_C::Image_104' has a wrong offset!");
static_assert(offsetof(UWDG_PresetsPopup_C, WDG_PopUp_UpdatePosition) == 0x000730, "Member 'UWDG_PresetsPopup_C::WDG_PopUp_UpdatePosition' has a wrong offset!");

}

