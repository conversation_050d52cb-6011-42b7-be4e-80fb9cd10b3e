﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomCustomCarOptions

#include "Basic.hpp"

#include "WDG_ShowroomCustomCarOptions_classes.hpp"
#include "WDG_ShowroomCustomCarOptions_parameters.hpp"


namespace SDK
{

// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.ExecuteUbergraph_WDG_ShowroomCustomCarOptions
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::ExecuteUbergraph_WDG_ShowroomCustomCarOptions(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "ExecuteUbergraph_WDG_ShowroomCustomCarOptions");

	Params::WDG_ShowroomCustomCarOptions_C_ExecuteUbergraph_WDG_ShowroomCustomCarOptions Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__sliderBanner_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UGenericSelectorItem*             Source                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_index                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_value                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::BndEvt__sliderBanner_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature(class UGenericSelectorItem* Source, int32 current_index, int32 current_value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "BndEvt__sliderBanner_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature");

	Params::WDG_ShowroomCustomCarOptions_C_BndEvt__sliderBanner_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature Parms{};

	Parms.Source = Source;
	Parms.current_index = current_index;
	Parms.current_value = current_value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__sliderCup_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UGenericSelectorItem*             Source                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_index                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_value                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::BndEvt__sliderCup_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature(class UGenericSelectorItem* Source, int32 current_index, int32 current_value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "BndEvt__sliderCup_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature");

	Params::WDG_ShowroomCustomCarOptions_C_BndEvt__sliderCup_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature Parms{};

	Parms.Source = Source;
	Parms.current_index = current_index;
	Parms.current_value = current_value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__sliderNationality_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UGenericSelectorItem*             Source                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_index                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_value                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::BndEvt__sliderNationality_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature(class UGenericSelectorItem* Source, int32 current_index, int32 current_value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "BndEvt__sliderNationality_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature");

	Params::WDG_ShowroomCustomCarOptions_C_BndEvt__sliderNationality_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature Parms{};

	Parms.Source = Source;
	Parms.current_index = current_index;
	Parms.current_value = current_value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.OnAfterConstruct
// (Event, Public, BlueprintEvent)

void UWDG_ShowroomCustomCarOptions_C::OnAfterConstruct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "OnAfterConstruct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__selectorRim1_K2Node_ComponentBoundEvent_7_OnMaterialChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// int32                                   MaterialKey                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::BndEvt__selectorRim1_K2Node_ComponentBoundEvent_7_OnMaterialChanged__DelegateSignature(int32 MaterialKey)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "BndEvt__selectorRim1_K2Node_ComponentBoundEvent_7_OnMaterialChanged__DelegateSignature");

	Params::WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim1_K2Node_ComponentBoundEvent_7_OnMaterialChanged__DelegateSignature Parms{};

	Parms.MaterialKey = MaterialKey;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__sliderSponsor_K2Node_ComponentBoundEvent_8_SelectorChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UGenericSelectorItem*             Source                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_index                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_value                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::BndEvt__sliderSponsor_K2Node_ComponentBoundEvent_8_SelectorChanged__DelegateSignature(class UGenericSelectorItem* Source, int32 current_index, int32 current_value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "BndEvt__sliderSponsor_K2Node_ComponentBoundEvent_8_SelectorChanged__DelegateSignature");

	Params::WDG_ShowroomCustomCarOptions_C_BndEvt__sliderSponsor_K2Node_ComponentBoundEvent_8_SelectorChanged__DelegateSignature Parms{};

	Parms.Source = Source;
	Parms.current_index = current_index;
	Parms.current_value = current_value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__sliderTemplate_K2Node_ComponentBoundEvent_7_SelectorChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UGenericSelectorItem*             Source                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_index                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_value                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::BndEvt__sliderTemplate_K2Node_ComponentBoundEvent_7_SelectorChanged__DelegateSignature(class UGenericSelectorItem* Source, int32 current_index, int32 current_value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "BndEvt__sliderTemplate_K2Node_ComponentBoundEvent_7_SelectorChanged__DelegateSignature");

	Params::WDG_ShowroomCustomCarOptions_C_BndEvt__sliderTemplate_K2Node_ComponentBoundEvent_7_SelectorChanged__DelegateSignature Parms{};

	Parms.Source = Source;
	Parms.current_index = current_index;
	Parms.current_value = current_value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.OnModelUpdate
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// ECarModelType                           CarModel                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::OnModelUpdate(ECarModelType CarModel)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "OnModelUpdate");

	Params::WDG_ShowroomCustomCarOptions_C_OnModelUpdate Parms{};

	Parms.CarModel = CarModel;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__colorPalette_K2Node_ComponentBoundEvent_14_OnClosed__DelegateSignature
// (BlueprintEvent)
// Parameters:
// bool                                    Cancel                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
// class UWDG_ShowroomMaterialColorSelector_C*TargetSelector                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::BndEvt__colorPalette_K2Node_ComponentBoundEvent_14_OnClosed__DelegateSignature(bool Cancel, class UWDG_ShowroomMaterialColorSelector_C* TargetSelector)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "BndEvt__colorPalette_K2Node_ComponentBoundEvent_14_OnClosed__DelegateSignature");

	Params::WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_14_OnClosed__DelegateSignature Parms{};

	Parms.Cancel = Cancel;
	Parms.TargetSelector = TargetSelector;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__colorPalette_K2Node_ComponentBoundEvent_13_OnColorFocused__DelegateSignature
// (BlueprintEvent)
// Parameters:
// int32                                   ColorCode                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWDG_ShowroomMaterialColorSelector_C*TargetSelector                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::BndEvt__colorPalette_K2Node_ComponentBoundEvent_13_OnColorFocused__DelegateSignature(int32 ColorCode, class UWDG_ShowroomMaterialColorSelector_C* TargetSelector)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "BndEvt__colorPalette_K2Node_ComponentBoundEvent_13_OnColorFocused__DelegateSignature");

	Params::WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_13_OnColorFocused__DelegateSignature Parms{};

	Parms.ColorCode = ColorCode;
	Parms.TargetSelector = TargetSelector;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__colorPalette_K2Node_ComponentBoundEvent_6_OnColorSelected__DelegateSignature
// (BlueprintEvent)
// Parameters:
// int32                                   Code                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWDG_ShowroomMaterialColorSelector_C*TargetSelector                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::BndEvt__colorPalette_K2Node_ComponentBoundEvent_6_OnColorSelected__DelegateSignature(int32 Code, class UWDG_ShowroomMaterialColorSelector_C* TargetSelector)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "BndEvt__colorPalette_K2Node_ComponentBoundEvent_6_OnColorSelected__DelegateSignature");

	Params::WDG_ShowroomCustomCarOptions_C_BndEvt__colorPalette_K2Node_ComponentBoundEvent_6_OnColorSelected__DelegateSignature Parms{};

	Parms.Code = Code;
	Parms.TargetSelector = TargetSelector;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_5_OnSelectColor__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UWDG_ShowroomMaterialColorSelector_C*Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_5_OnSelectColor__DelegateSignature(class UWDG_ShowroomMaterialColorSelector_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_5_OnSelectColor__DelegateSignature");

	Params::WDG_ShowroomCustomCarOptions_C_BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_5_OnSelectColor__DelegateSignature Parms{};

	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__selectorRim2_K2Node_ComponentBoundEvent_4_OnSelectColor__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UWDG_ShowroomMaterialColorSelector_C*Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::BndEvt__selectorRim2_K2Node_ComponentBoundEvent_4_OnSelectColor__DelegateSignature(class UWDG_ShowroomMaterialColorSelector_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "BndEvt__selectorRim2_K2Node_ComponentBoundEvent_4_OnSelectColor__DelegateSignature");

	Params::WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim2_K2Node_ComponentBoundEvent_4_OnSelectColor__DelegateSignature Parms{};

	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__selectorRim1_K2Node_ComponentBoundEvent_3_OnSelectColor__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UWDG_ShowroomMaterialColorSelector_C*Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::BndEvt__selectorRim1_K2Node_ComponentBoundEvent_3_OnSelectColor__DelegateSignature(class UWDG_ShowroomMaterialColorSelector_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "BndEvt__selectorRim1_K2Node_ComponentBoundEvent_3_OnSelectColor__DelegateSignature");

	Params::WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim1_K2Node_ComponentBoundEvent_3_OnSelectColor__DelegateSignature Parms{};

	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_2_OnSelectColor__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UWDG_ShowroomMaterialColorSelector_C*Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_2_OnSelectColor__DelegateSignature(class UWDG_ShowroomMaterialColorSelector_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_2_OnSelectColor__DelegateSignature");

	Params::WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_2_OnSelectColor__DelegateSignature Parms{};

	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_1_OnSelectColor__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UWDG_ShowroomMaterialColorSelector_C*Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_1_OnSelectColor__DelegateSignature(class UWDG_ShowroomMaterialColorSelector_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_1_OnSelectColor__DelegateSignature");

	Params::WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_1_OnSelectColor__DelegateSignature Parms{};

	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_0_OnSelectColor__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UWDG_ShowroomMaterialColorSelector_C*Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_0_OnSelectColor__DelegateSignature(class UWDG_ShowroomMaterialColorSelector_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_0_OnSelectColor__DelegateSignature");

	Params::WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_0_OnSelectColor__DelegateSignature Parms{};

	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_12_OnMaterialChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// int32                                   MaterialKey                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_12_OnMaterialChanged__DelegateSignature(int32 MaterialKey)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_12_OnMaterialChanged__DelegateSignature");

	Params::WDG_ShowroomCustomCarOptions_C_BndEvt__selectorAuxLights_K2Node_ComponentBoundEvent_12_OnMaterialChanged__DelegateSignature Parms{};

	Parms.MaterialKey = MaterialKey;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_11_OnMaterialChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// int32                                   MaterialKey                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_11_OnMaterialChanged__DelegateSignature(int32 MaterialKey)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_11_OnMaterialChanged__DelegateSignature");

	Params::WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin3_K2Node_ComponentBoundEvent_11_OnMaterialChanged__DelegateSignature Parms{};

	Parms.MaterialKey = MaterialKey;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_10_OnMaterialChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// int32                                   MaterialKey                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_10_OnMaterialChanged__DelegateSignature(int32 MaterialKey)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_10_OnMaterialChanged__DelegateSignature");

	Params::WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin2_K2Node_ComponentBoundEvent_10_OnMaterialChanged__DelegateSignature Parms{};

	Parms.MaterialKey = MaterialKey;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_9_OnMaterialChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// int32                                   MaterialKey                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_9_OnMaterialChanged__DelegateSignature(int32 MaterialKey)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_9_OnMaterialChanged__DelegateSignature");

	Params::WDG_ShowroomCustomCarOptions_C_BndEvt__selectorSkin1_K2Node_ComponentBoundEvent_9_OnMaterialChanged__DelegateSignature Parms{};

	Parms.MaterialKey = MaterialKey;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.BndEvt__selectorRim2_K2Node_ComponentBoundEvent_8_OnMaterialChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// int32                                   MaterialKey                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::BndEvt__selectorRim2_K2Node_ComponentBoundEvent_8_OnMaterialChanged__DelegateSignature(int32 MaterialKey)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "BndEvt__selectorRim2_K2Node_ComponentBoundEvent_8_OnMaterialChanged__DelegateSignature");

	Params::WDG_ShowroomCustomCarOptions_C_BndEvt__selectorRim2_K2Node_ComponentBoundEvent_8_OnMaterialChanged__DelegateSignature Parms{};

	Parms.MaterialKey = MaterialKey;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.GetSkinTemplates
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// ECarModelType                           CarModel                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::GetSkinTemplates(ECarModelType CarModel)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "GetSkinTemplates");

	Params::WDG_ShowroomCustomCarOptions_C_GetSkinTemplates Parms{};

	Parms.CarModel = CarModel;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.updateKeySlider
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UGenericSelectorItem*             Target                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const TArray<int32>&                    SourceArray                                            (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// const class FText&                      FirstOptionText                                        (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_ShowroomCustomCarOptions_C::updateKeySlider(class UGenericSelectorItem* Target, const TArray<int32>& SourceArray, const class FText& FirstOptionText)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "updateKeySlider");

	Params::WDG_ShowroomCustomCarOptions_C_updateKeySlider Parms{};

	Parms.Target = Target;
	Parms.SourceArray = std::move(SourceArray);
	Parms.FirstOptionText = std::move(FirstOptionText);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.getAuxLights
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// ECarModelType                           CarModel                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::getAuxLights(ECarModelType CarModel)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "getAuxLights");

	Params::WDG_ShowroomCustomCarOptions_C_getAuxLights Parms{};

	Parms.CarModel = CarModel;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.getCarGraphicData
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UAcCarGraphicData**               CarGraphicData                                         (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::getCarGraphicData(class UAcCarGraphicData** CarGraphicData)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "getCarGraphicData");

	Params::WDG_ShowroomCustomCarOptions_C_getCarGraphicData Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (CarGraphicData != nullptr)
		*CarGraphicData = Parms.CarGraphicData;
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.SetCarInfo
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FCarInfo&                  CarInfo_0                                              (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_ShowroomCustomCarOptions_C::SetCarInfo(const struct FCarInfo& CarInfo_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "SetCarInfo");

	Params::WDG_ShowroomCustomCarOptions_C_SetCarInfo Parms{};

	Parms.CarInfo_0 = std::move(CarInfo_0);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.OnCarInfoUpdated
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    SetDirty                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ShowroomCustomCarOptions_C::OnCarInfoUpdated(bool SetDirty)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "OnCarInfoUpdated");

	Params::WDG_ShowroomCustomCarOptions_C_OnCarInfoUpdated Parms{};

	Parms.SetDirty = SetDirty;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.updateMaterialInSelector
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UWDG_ShowroomMaterialColorSelector_C*TargetSelector                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    IsRim                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ShowroomCustomCarOptions_C::updateMaterialInSelector(class UWDG_ShowroomMaterialColorSelector_C* TargetSelector, bool IsRim)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "updateMaterialInSelector");

	Params::WDG_ShowroomCustomCarOptions_C_updateMaterialInSelector Parms{};

	Parms.TargetSelector = TargetSelector;
	Parms.IsRim = IsRim;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.getSponsors
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// ECarModelType                           CarModel                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::getSponsors(ECarModelType CarModel)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "getSponsors");

	Params::WDG_ShowroomCustomCarOptions_C_getSponsors Parms{};

	Parms.CarModel = CarModel;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.selectorMaterialKeyAsName
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UWDG_ShowroomMaterialColorSelector_C*Selector                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class FName*                            Name_0                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::selectorMaterialKeyAsName(class UWDG_ShowroomMaterialColorSelector_C* Selector, class FName* Name_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "selectorMaterialKeyAsName");

	Params::WDG_ShowroomCustomCarOptions_C_selectorMaterialKeyAsName Parms{};

	Parms.Selector = Selector;

	UObject::ProcessEvent(Func, &Parms);

	if (Name_0 != nullptr)
		*Name_0 = Parms.Name_0;
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.selectorColorAndMaterial
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UWDG_ShowroomMaterialColorSelector_C*Selector                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32*                                  ColorCode                                              (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class FName*                            MaterialName                                           (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::selectorColorAndMaterial(class UWDG_ShowroomMaterialColorSelector_C* Selector, int32* ColorCode, class FName* MaterialName)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "selectorColorAndMaterial");

	Params::WDG_ShowroomCustomCarOptions_C_selectorColorAndMaterial Parms{};

	Parms.Selector = Selector;

	UObject::ProcessEvent(Func, &Parms);

	if (ColorCode != nullptr)
		*ColorCode = Parms.ColorCode;

	if (MaterialName != nullptr)
		*MaterialName = Parms.MaterialName;
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.OnUpdateCarInfoValues
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomCustomCarOptions_C::OnUpdateCarInfoValues()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "OnUpdateCarInfoValues");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.openColorPalette
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    for_aux_lights                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
// class UWDG_ShowroomMaterialColorSelector_C*Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWDG_ShowroomPalette_C**          colorPalette_0                                         (Parm, OutParm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::openColorPalette(bool for_aux_lights, class UWDG_ShowroomMaterialColorSelector_C* Sender, class UWDG_ShowroomPalette_C** colorPalette_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "openColorPalette");

	Params::WDG_ShowroomCustomCarOptions_C_openColorPalette Parms{};

	Parms.for_aux_lights = for_aux_lights;
	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);

	if (colorPalette_0 != nullptr)
		*colorPalette_0 = Parms.colorPalette_0;
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.ForEachPaletteWrapper
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UPanelWidget*                     Wrapper                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWDG_ShowroomMaterialColorSelector_C*Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::ForEachPaletteWrapper(class UPanelWidget* Wrapper, class UWDG_ShowroomMaterialColorSelector_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "ForEachPaletteWrapper");

	Params::WDG_ShowroomCustomCarOptions_C_ForEachPaletteWrapper Parms{};

	Parms.Wrapper = Wrapper;
	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.getNationalities
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomCustomCarOptions_C::getNationalities()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "getNationalities");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.getCups
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomCustomCarOptions_C::getCups()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "getCups");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomCustomCarOptions.WDG_ShowroomCustomCarOptions_C.getBanners
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// ECarModelType                           CarModel                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomCustomCarOptions_C::getBanners(ECarModelType CarModel)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomCustomCarOptions_C", "getBanners");

	Params::WDG_ShowroomCustomCarOptions_C_getBanners Parms{};

	Parms.CarModel = CarModel;

	UObject::ProcessEvent(Func, &Parms);
}

}

