﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_MultiplayerQuickjoinPage

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_MultiplayerQuickjoinPage.WDG_MultiplayerQuickjoinPage_C
// 0x00A0 (0x0758 - 0x06B8)
class UWDG_MultiplayerQuickjoinPage_C final : public UMultiplayerQuickjoinPage
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x06B8(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       PageFade;                                          // 0x06C0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       StatusTextPulse;                                   // 0x06C8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 bgImage;                                           // 0x06D0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_MPCarGroupSelector_C*              carGroupSelector;                                  // 0x06D8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWidgetSwitcher*                        CPWidgetSwitcher;                                  // 0x06E0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Footer_C*                          Footer;                                            // 0x06E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Header_C*                          Header;                                            // 0x06F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_130;                                         // 0x06F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgLeftSlant;                                      // 0x0700(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Page_C*                            PageContainer;                                     // 0x0708(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtStatus;                                         // 0x0710(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtToolTip;                                        // 0x0718(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_KSONConnectionState_C*             WDG_KSONConnectionState;                           // 0x0720(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_MPQuickjoinPublicMPSlot_C*         WDG_MPQuickjoinPublicMPSlot;                       // 0x0728(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_MPQuickjoinSlotCPInviteSlot_C*     WDG_MPQuickjoinSlotCPInviteSlot;                   // 0x0730(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_MPQuickjoinSlot_C*                 WDG_MPQuickjoinSlotCPLegacy;                       // 0x0738(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	struct FTimerHandle                           QuickJoinDebounceTimer;                            // 0x0740(0x0008)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	struct FTimerHandle                           PrimaryCarAnimationDebounceTimer;                  // 0x0748(0x0008)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	struct FTimerHandle                           RefreshDataButtonDebounceTimer;                    // 0x0750(0x0008)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_MultiplayerQuickjoinPage(int32 EntryPoint);
	void OnSeasonUnavailable(EContentType content_type);
	void OnServerDisconnection(EDisconnectionReason reason);
	void ReactivateRefresh();
	void ActivatePulseAnimation();
	void AfterCarGroupSelectionDebounce();
	void BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_13_OnAcPanelFocusEvent__DelegateSignature(class UAcPanelBase* panel);
	void BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_11_OnAcPanelFocusEvent__DelegateSignature(class UAcPanelBase* panel);
	void BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_12_OnAcPanelFocusEvent__DelegateSignature(class UAcPanelBase* panel);
	void BndEvt__WDG_MPQuickjoinSlotCPLegacy_K2Node_ComponentBoundEvent_10_OnAcPanelFocusEvent__DelegateSignature(class UAcPanelBase* panel);
	void BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_9_OnCarGroupBlurred__DelegateSignature(EMPCarGroup CarGroup);
	void BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_8_OnCarGroupFocused__DelegateSignature(EMPCarGroup CarGroup);
	void BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_7_OnCarGroupSelected__DelegateSignature(EMPCarGroup CarGroup);
	void OnCPInviteUpdate(const struct FOnlineServicesCPInvitationState& invitation_update);
	void BP_StartPage();
	void OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent);
	void BndEvt__btnRefresh_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature();
	void UpdateServerInfo(const struct FOnlineServicesMPServerInfo& legacy_competition_server, const struct FOnlineServicesMPQuickjoinPanelInfo& quickjoin_info, const struct FOnlineServicesCPInvitationState& competition_server_invitation);
	void Construct();
	void BndEvt__WDG_MPQuickjoinSlotCPInviteSlot_K2Node_ComponentBoundEvent_6_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__WDG_QuickjoinSlot1_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__WDG_MPQuickjoinPublicMPSlot_K2Node_ComponentBoundEvent_3_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__btnCarSelection_K2Node_ComponentBoundEvent_5_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__btnServerList_K2Node_ComponentBoundEvent_2_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__btnAdvanced_K2Node_ComponentBoundEvent_1_OnAcPanelForwardEvent__DelegateSignature();
	class FText Get_txtStatus_Text_0();
	bool Is_DLC_Car_Available();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_MultiplayerQuickjoinPage_C">();
	}
	static class UWDG_MultiplayerQuickjoinPage_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_MultiplayerQuickjoinPage_C>();
	}
};
static_assert(alignof(UWDG_MultiplayerQuickjoinPage_C) == 0x000008, "Wrong alignment on UWDG_MultiplayerQuickjoinPage_C");
static_assert(sizeof(UWDG_MultiplayerQuickjoinPage_C) == 0x000758, "Wrong size on UWDG_MultiplayerQuickjoinPage_C");
static_assert(offsetof(UWDG_MultiplayerQuickjoinPage_C, UberGraphFrame) == 0x0006B8, "Member 'UWDG_MultiplayerQuickjoinPage_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerQuickjoinPage_C, PageFade) == 0x0006C0, "Member 'UWDG_MultiplayerQuickjoinPage_C::PageFade' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerQuickjoinPage_C, StatusTextPulse) == 0x0006C8, "Member 'UWDG_MultiplayerQuickjoinPage_C::StatusTextPulse' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerQuickjoinPage_C, bgImage) == 0x0006D0, "Member 'UWDG_MultiplayerQuickjoinPage_C::bgImage' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerQuickjoinPage_C, carGroupSelector) == 0x0006D8, "Member 'UWDG_MultiplayerQuickjoinPage_C::carGroupSelector' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerQuickjoinPage_C, CPWidgetSwitcher) == 0x0006E0, "Member 'UWDG_MultiplayerQuickjoinPage_C::CPWidgetSwitcher' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerQuickjoinPage_C, Footer) == 0x0006E8, "Member 'UWDG_MultiplayerQuickjoinPage_C::Footer' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerQuickjoinPage_C, Header) == 0x0006F0, "Member 'UWDG_MultiplayerQuickjoinPage_C::Header' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerQuickjoinPage_C, Image_130) == 0x0006F8, "Member 'UWDG_MultiplayerQuickjoinPage_C::Image_130' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerQuickjoinPage_C, imgLeftSlant) == 0x000700, "Member 'UWDG_MultiplayerQuickjoinPage_C::imgLeftSlant' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerQuickjoinPage_C, PageContainer) == 0x000708, "Member 'UWDG_MultiplayerQuickjoinPage_C::PageContainer' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerQuickjoinPage_C, txtStatus) == 0x000710, "Member 'UWDG_MultiplayerQuickjoinPage_C::txtStatus' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerQuickjoinPage_C, txtToolTip) == 0x000718, "Member 'UWDG_MultiplayerQuickjoinPage_C::txtToolTip' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerQuickjoinPage_C, WDG_KSONConnectionState) == 0x000720, "Member 'UWDG_MultiplayerQuickjoinPage_C::WDG_KSONConnectionState' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerQuickjoinPage_C, WDG_MPQuickjoinPublicMPSlot) == 0x000728, "Member 'UWDG_MultiplayerQuickjoinPage_C::WDG_MPQuickjoinPublicMPSlot' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerQuickjoinPage_C, WDG_MPQuickjoinSlotCPInviteSlot) == 0x000730, "Member 'UWDG_MultiplayerQuickjoinPage_C::WDG_MPQuickjoinSlotCPInviteSlot' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerQuickjoinPage_C, WDG_MPQuickjoinSlotCPLegacy) == 0x000738, "Member 'UWDG_MultiplayerQuickjoinPage_C::WDG_MPQuickjoinSlotCPLegacy' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerQuickjoinPage_C, QuickJoinDebounceTimer) == 0x000740, "Member 'UWDG_MultiplayerQuickjoinPage_C::QuickJoinDebounceTimer' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerQuickjoinPage_C, PrimaryCarAnimationDebounceTimer) == 0x000748, "Member 'UWDG_MultiplayerQuickjoinPage_C::PrimaryCarAnimationDebounceTimer' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerQuickjoinPage_C, RefreshDataButtonDebounceTimer) == 0x000750, "Member 'UWDG_MultiplayerQuickjoinPage_C::RefreshDataButtonDebounceTimer' has a wrong offset!");

}

