﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RaceRatingDetailRacecraft

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RaceRatingDetailRacecraft.WDG_RaceRatingDetailRacecraft_C
// 0x0000 (0x0290 - 0x0290)
class UWDG_RaceRatingDetailRacecraft_C final : public URatingDetailRacecraft
{
public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RaceRatingDetailRacecraft_C">();
	}
	static class UWDG_RaceRatingDetailRacecraft_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RaceRatingDetailRacecraft_C>();
	}
};
static_assert(alignof(UWDG_RaceRatingDetailRacecraft_C) == 0x000008, "Wrong alignment on UWDG_RaceRatingDetailRacecraft_C");
static_assert(sizeof(UWDG_RaceRatingDetailRacecraft_C) == 0x000290, "Wrong size on UWDG_RaceRatingDetailRacecraft_C");

}

