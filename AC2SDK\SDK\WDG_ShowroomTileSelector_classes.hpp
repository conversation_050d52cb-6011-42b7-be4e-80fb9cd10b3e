﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomTileSelector

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "AC2_classes.hpp"
#include "Engine_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ShowroomTileSelector.WDG_ShowroomTileSelector_C
// 0x0058 (0x0638 - 0x05E0)
class UWDG_ShowroomTileSelector_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UScrollBox*                             scrollTiles;                                       // 0x05E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class U<PERSON>rapBox*                               TilelList;                                         // 0x05F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	TMulticastInlineDelegate<void(const struct FModelInfo& ModelInfo)> OnModelSelected;              // 0x05F8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void(class UUserWidget* Sender)> OnItemFocused;                         // 0x0608(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TArray<struct FModelInfo>                     Models;                                            // 0x0618(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TMulticastInlineDelegate<void(const struct FTeamInfo& TeamInfo)> OnTeamSelected;                 // 0x0628(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)

public:
	void ExecuteUbergraph_WDG_ShowroomTileSelector(int32 EntryPoint);
	void SelectTeam(const struct FTeamInfo& Team);
	void SelectModel(const struct FModelInfo& Model);
	void OnTeamItemSelected(class UWDG_ShowroomTileBase_C* Sender, class FName Key, int32 KeyInt);
	void OnTeamTileFocused(class UWDG_ShowroomTileBase_C* Sender);
	void UpdateTeams(const TArray<struct FTeamInfo>& Teams);
	void UpdateModels(const TArray<struct FModelInfo>& Models_0);
	void OnTileItemFocused(class UWDG_ShowroomTileBase_C* Sender);
	void OnItemSelected(class UWDG_ShowroomTileBase_C* Sender, class FName Key, int32 KeyInt);
	struct FEventReply OnPreviewKeyDown(const struct FGeometry& MyGeometry, const struct FKeyEvent& InKeyEvent);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ShowroomTileSelector_C">();
	}
	static class UWDG_ShowroomTileSelector_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ShowroomTileSelector_C>();
	}
};
static_assert(alignof(UWDG_ShowroomTileSelector_C) == 0x000008, "Wrong alignment on UWDG_ShowroomTileSelector_C");
static_assert(sizeof(UWDG_ShowroomTileSelector_C) == 0x000638, "Wrong size on UWDG_ShowroomTileSelector_C");
static_assert(offsetof(UWDG_ShowroomTileSelector_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_ShowroomTileSelector_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileSelector_C, scrollTiles) == 0x0005E8, "Member 'UWDG_ShowroomTileSelector_C::scrollTiles' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileSelector_C, TilelList) == 0x0005F0, "Member 'UWDG_ShowroomTileSelector_C::TilelList' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileSelector_C, OnModelSelected) == 0x0005F8, "Member 'UWDG_ShowroomTileSelector_C::OnModelSelected' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileSelector_C, OnItemFocused) == 0x000608, "Member 'UWDG_ShowroomTileSelector_C::OnItemFocused' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileSelector_C, Models) == 0x000618, "Member 'UWDG_ShowroomTileSelector_C::Models' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileSelector_C, OnTeamSelected) == 0x000628, "Member 'UWDG_ShowroomTileSelector_C::OnTeamSelected' has a wrong offset!");

}

