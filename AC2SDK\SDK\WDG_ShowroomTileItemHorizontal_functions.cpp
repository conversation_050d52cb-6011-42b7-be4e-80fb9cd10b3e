﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomTileItemHorizontal

#include "Basic.hpp"

#include "WDG_ShowroomTileItemHorizontal_classes.hpp"
#include "WDG_ShowroomTileItemHorizontal_parameters.hpp"


namespace SDK
{

// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.ExecuteUbergraph_WDG_ShowroomTileItemHorizontal
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomTileItemHorizontal_C::ExecuteUbergraph_WDG_ShowroomTileItemHorizontal(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileItemHorizontal_C", "ExecuteUbergraph_WDG_ShowroomTileItemHorizontal");

	Params::WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.UpdateTeam
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FTeamInfo&                 TeamInfo_0                                             (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_ShowroomTileItemHorizontal_C::UpdateTeam(const struct FTeamInfo& TeamInfo_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileItemHorizontal_C", "UpdateTeam");

	Params::WDG_ShowroomTileItemHorizontal_C_UpdateTeam Parms{};

	Parms.TeamInfo_0 = std::move(TeamInfo_0);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.UpdateModel
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FModelInfo&                ModelInfo_0                                            (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_ShowroomTileItemHorizontal_C::UpdateModel(const struct FModelInfo& ModelInfo_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileItemHorizontal_C", "UpdateModel");

	Params::WDG_ShowroomTileItemHorizontal_C_UpdateModel Parms{};

	Parms.ModelInfo_0 = std::move(ModelInfo_0);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.UpdateCar
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FCarInfo&                  CarInfo_0                                              (BlueprintVisible, BlueprintReadOnly, Parm)
// class FName                             CarKey                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomTileItemHorizontal_C::UpdateCar(const struct FCarInfo& CarInfo_0, class FName CarKey)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileItemHorizontal_C", "UpdateCar");

	Params::WDG_ShowroomTileItemHorizontal_C_UpdateCar Parms{};

	Parms.CarInfo_0 = std::move(CarInfo_0);
	Parms.CarKey = CarKey;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.UpdateDriver
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FDriverInfo&               DriverInfo_0                                           (BlueprintVisible, BlueprintReadOnly, Parm)
// class FName                             DriverKey                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    isCustom                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ShowroomTileItemHorizontal_C::UpdateDriver(const struct FDriverInfo& DriverInfo_0, class FName DriverKey, bool isCustom)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileItemHorizontal_C", "UpdateDriver");

	Params::WDG_ShowroomTileItemHorizontal_C_UpdateDriver Parms{};

	Parms.DriverInfo_0 = std::move(DriverInfo_0);
	Parms.DriverKey = DriverKey;
	Parms.isCustom = isCustom;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.UpdateColor
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FSkinColor&                Color                                                  (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_ShowroomTileItemHorizontal_C::UpdateColor(const struct FSkinColor& Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileItemHorizontal_C", "UpdateColor");

	Params::WDG_ShowroomTileItemHorizontal_C_UpdateColor Parms{};

	Parms.Color = std::move(Color);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.OnRemovedFromFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_ShowroomTileItemHorizontal_C::OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileItemHorizontal_C", "OnRemovedFromFocusPath");

	Params::WDG_ShowroomTileItemHorizontal_C_OnRemovedFromFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.OnNext
// (BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomTileItemHorizontal_C::OnNext()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileItemHorizontal_C", "OnNext");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.OnPrevious
// (BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomTileItemHorizontal_C::OnPrevious()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileItemHorizontal_C", "OnPrevious");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.OnAddedToFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_ShowroomTileItemHorizontal_C::OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileItemHorizontal_C", "OnAddedToFocusPath");

	Params::WDG_ShowroomTileItemHorizontal_C_OnAddedToFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.OnItemForwardEvent
// (BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomTileItemHorizontal_C::OnItemForwardEvent()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileItemHorizontal_C", "OnItemForwardEvent");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.SetSelected
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    IsSelected_0                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ShowroomTileItemHorizontal_C::SetSelected(bool IsSelected_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileItemHorizontal_C", "SetSelected");

	Params::WDG_ShowroomTileItemHorizontal_C_SetSelected Parms{};

	Parms.IsSelected_0 = IsSelected_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.Animate
// (BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomTileItemHorizontal_C::Animate()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileItemHorizontal_C", "Animate");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ShowroomTileItemHorizontal_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileItemHorizontal_C", "PreConstruct");

	Params::WDG_ShowroomTileItemHorizontal_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_ShowroomTileItemHorizontal_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileItemHorizontal_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.ShouldAnimate
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class FName                             New_Key                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomTileItemHorizontal_C::ShouldAnimate(class FName New_Key)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileItemHorizontal_C", "ShouldAnimate");

	Params::WDG_ShowroomTileItemHorizontal_C_ShouldAnimate Parms{};

	Parms.New_Key = New_Key;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.SetTileColor
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              InColorAndOpacity                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomTileItemHorizontal_C::SetTileColor(const struct FLinearColor& InColorAndOpacity)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileItemHorizontal_C", "SetTileColor");

	Params::WDG_ShowroomTileItemHorizontal_C_SetTileColor Parms{};

	Parms.InColorAndOpacity = std::move(InColorAndOpacity);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.SetTileColorByCode
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   ColorCode                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// struct FLinearColor*                    Color                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomTileItemHorizontal_C::SetTileColorByCode(int32 ColorCode, struct FLinearColor* Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileItemHorizontal_C", "SetTileColorByCode");

	Params::WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode Parms{};

	Parms.ColorCode = ColorCode;

	UObject::ProcessEvent(Func, &Parms);

	if (Color != nullptr)
		*Color = std::move(Parms.Color);
}


// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.SetLabelFixedWidth
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomTileItemHorizontal_C::SetLabelFixedWidth()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileItemHorizontal_C", "SetLabelFixedWidth");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.SetColorTextLabel
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const class FText&                      InText                                                 (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_ShowroomTileItemHorizontal_C::SetColorTextLabel(const class FText& InText)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileItemHorizontal_C", "SetColorTextLabel");

	Params::WDG_ShowroomTileItemHorizontal_C_SetColorTextLabel Parms{};

	Parms.InText = std::move(InText);

	UObject::ProcessEvent(Func, &Parms);
}

}

