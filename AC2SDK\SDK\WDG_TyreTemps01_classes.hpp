﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TyreTemps01

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_TyreTemps01.WDG_TyreTemps01_C
// 0x0048 (0x0748 - 0x0700)
class UWDG_TyreTemps01_C final : public UTyreTemps01Widget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0700(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UCanvasPanel*                           canvasPressureF;                                   // 0x0708(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           canvasPressureR;                                   // 0x0710(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           canvasTyresF;                                      // 0x0718(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           canvasTyresR;                                      // 0x0720(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_79;                                          // 0x0728(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           plhTyresF;                                         // 0x0730(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           plhTyresR;                                         // 0x0738(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	bool                                          ShouldBeVisible;                                   // 0x0740(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	bool                                          isOnline;                                          // 0x0741(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)

public:
	void ExecuteUbergraph_WDG_TyreTemps01(int32 EntryPoint);
	void OnStartWidget(class UAcGameInstance* GameInstance, class AAcRaceGameMode* raceGameMode, class ACarAvatar* CarAvatar, const struct FHUDOptions& HUDOptions);
	void OnHudTick(const struct FRaceHUDState& State);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_TyreTemps01_C">();
	}
	static class UWDG_TyreTemps01_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_TyreTemps01_C>();
	}
};
static_assert(alignof(UWDG_TyreTemps01_C) == 0x000008, "Wrong alignment on UWDG_TyreTemps01_C");
static_assert(sizeof(UWDG_TyreTemps01_C) == 0x000748, "Wrong size on UWDG_TyreTemps01_C");
static_assert(offsetof(UWDG_TyreTemps01_C, UberGraphFrame) == 0x000700, "Member 'UWDG_TyreTemps01_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_TyreTemps01_C, canvasPressureF) == 0x000708, "Member 'UWDG_TyreTemps01_C::canvasPressureF' has a wrong offset!");
static_assert(offsetof(UWDG_TyreTemps01_C, canvasPressureR) == 0x000710, "Member 'UWDG_TyreTemps01_C::canvasPressureR' has a wrong offset!");
static_assert(offsetof(UWDG_TyreTemps01_C, canvasTyresF) == 0x000718, "Member 'UWDG_TyreTemps01_C::canvasTyresF' has a wrong offset!");
static_assert(offsetof(UWDG_TyreTemps01_C, canvasTyresR) == 0x000720, "Member 'UWDG_TyreTemps01_C::canvasTyresR' has a wrong offset!");
static_assert(offsetof(UWDG_TyreTemps01_C, Image_79) == 0x000728, "Member 'UWDG_TyreTemps01_C::Image_79' has a wrong offset!");
static_assert(offsetof(UWDG_TyreTemps01_C, plhTyresF) == 0x000730, "Member 'UWDG_TyreTemps01_C::plhTyresF' has a wrong offset!");
static_assert(offsetof(UWDG_TyreTemps01_C, plhTyresR) == 0x000738, "Member 'UWDG_TyreTemps01_C::plhTyresR' has a wrong offset!");
static_assert(offsetof(UWDG_TyreTemps01_C, ShouldBeVisible) == 0x000740, "Member 'UWDG_TyreTemps01_C::ShouldBeVisible' has a wrong offset!");
static_assert(offsetof(UWDG_TyreTemps01_C, isOnline) == 0x000741, "Member 'UWDG_TyreTemps01_C::isOnline' has a wrong offset!");

}

