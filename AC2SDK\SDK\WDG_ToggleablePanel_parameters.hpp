﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ToggleablePanel

#include "Basic.hpp"

#include "UMG_structs.hpp"


namespace SDK::Params
{

// Function WDG_ToggleablePanel.WDG_ToggleablePanel_C.ExecuteUbergraph_WDG_ToggleablePanel
// 0x000C (0x000C - 0x0000)
struct WDG_ToggleablePanel_C_ExecuteUbergraph_WDG_ToggleablePanel final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsVisible_ReturnValue;                    // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0006(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_1;                // 0x0007(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_2;            // 0x0009(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_3;            // 0x000A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x000B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ToggleablePanel_C_ExecuteUbergraph_WDG_ToggleablePanel) == 0x000004, "Wrong alignment on WDG_ToggleablePanel_C_ExecuteUbergraph_WDG_ToggleablePanel");
static_assert(sizeof(WDG_ToggleablePanel_C_ExecuteUbergraph_WDG_ToggleablePanel) == 0x00000C, "Wrong size on WDG_ToggleablePanel_C_ExecuteUbergraph_WDG_ToggleablePanel");
static_assert(offsetof(WDG_ToggleablePanel_C_ExecuteUbergraph_WDG_ToggleablePanel, EntryPoint) == 0x000000, "Member 'WDG_ToggleablePanel_C_ExecuteUbergraph_WDG_ToggleablePanel::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_ToggleablePanel_C_ExecuteUbergraph_WDG_ToggleablePanel, CallFunc_MakeLiteralByte_ReturnValue) == 0x000004, "Member 'WDG_ToggleablePanel_C_ExecuteUbergraph_WDG_ToggleablePanel::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ToggleablePanel_C_ExecuteUbergraph_WDG_ToggleablePanel, CallFunc_IsVisible_ReturnValue) == 0x000005, "Member 'WDG_ToggleablePanel_C_ExecuteUbergraph_WDG_ToggleablePanel::CallFunc_IsVisible_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ToggleablePanel_C_ExecuteUbergraph_WDG_ToggleablePanel, CallFunc_Not_PreBool_ReturnValue) == 0x000006, "Member 'WDG_ToggleablePanel_C_ExecuteUbergraph_WDG_ToggleablePanel::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ToggleablePanel_C_ExecuteUbergraph_WDG_ToggleablePanel, CallFunc_Not_PreBool_ReturnValue_1) == 0x000007, "Member 'WDG_ToggleablePanel_C_ExecuteUbergraph_WDG_ToggleablePanel::CallFunc_Not_PreBool_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ToggleablePanel_C_ExecuteUbergraph_WDG_ToggleablePanel, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000008, "Member 'WDG_ToggleablePanel_C_ExecuteUbergraph_WDG_ToggleablePanel::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ToggleablePanel_C_ExecuteUbergraph_WDG_ToggleablePanel, CallFunc_MakeLiteralByte_ReturnValue_2) == 0x000009, "Member 'WDG_ToggleablePanel_C_ExecuteUbergraph_WDG_ToggleablePanel::CallFunc_MakeLiteralByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ToggleablePanel_C_ExecuteUbergraph_WDG_ToggleablePanel, CallFunc_MakeLiteralByte_ReturnValue_3) == 0x00000A, "Member 'WDG_ToggleablePanel_C_ExecuteUbergraph_WDG_ToggleablePanel::CallFunc_MakeLiteralByte_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ToggleablePanel_C_ExecuteUbergraph_WDG_ToggleablePanel, K2Node_Event_IsDesignTime) == 0x00000B, "Member 'WDG_ToggleablePanel_C_ExecuteUbergraph_WDG_ToggleablePanel::K2Node_Event_IsDesignTime' has a wrong offset!");

// Function WDG_ToggleablePanel.WDG_ToggleablePanel_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_ToggleablePanel_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ToggleablePanel_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_ToggleablePanel_C_PreConstruct");
static_assert(sizeof(WDG_ToggleablePanel_C_PreConstruct) == 0x000001, "Wrong size on WDG_ToggleablePanel_C_PreConstruct");
static_assert(offsetof(WDG_ToggleablePanel_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_ToggleablePanel_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WDG_ToggleablePanel.WDG_ToggleablePanel_C.GetPanelExpanded
// 0x0001 (0x0001 - 0x0000)
struct WDG_ToggleablePanel_C_GetPanelExpanded final
{
public:
	ESlateVisibility                              ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ToggleablePanel_C_GetPanelExpanded) == 0x000001, "Wrong alignment on WDG_ToggleablePanel_C_GetPanelExpanded");
static_assert(sizeof(WDG_ToggleablePanel_C_GetPanelExpanded) == 0x000001, "Wrong size on WDG_ToggleablePanel_C_GetPanelExpanded");
static_assert(offsetof(WDG_ToggleablePanel_C_GetPanelExpanded, ReturnValue) == 0x000000, "Member 'WDG_ToggleablePanel_C_GetPanelExpanded::ReturnValue' has a wrong offset!");

}

