﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PlayerRatingSummary

#include "Basic.hpp"

#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_PlayerRatingSummary.WDG_PlayerRatingSummary_C.ExecuteUbergraph_WDG_PlayerRatingSummary
// 0x0190 (0x0190 - 0x0000)
struct WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void(const struct FOnlineServicesDriverSummary& summary)> K2Node_CreateDelegate_OutputDelegate; // 0x0010(0x0010)(ZeroConstructor, NoDestructor)
	class UOnlineServices*                        CallFunc_GetOnlineServices_ReturnValue;            // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FOnlineServicesDriverSummary           K2Node_CustomEvent_summary;                        // 0x0028(0x0138)(ConstParm)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0160(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_161[0x3];                                      // 0x0161(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_RequestDriverSummary_ReturnValue;         // 0x0164(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x0168(0x0018)()
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x0180(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_181[0x7];                                      // 0x0181(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_1;              // 0x0188(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary) == 0x000008, "Wrong alignment on WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary");
static_assert(sizeof(WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary) == 0x000190, "Wrong size on WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary");
static_assert(offsetof(WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary, EntryPoint) == 0x000000, "Member 'WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary, CallFunc_PlayAnimation_ReturnValue) == 0x000008, "Member 'WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary, K2Node_CreateDelegate_OutputDelegate) == 0x000010, "Member 'WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary, CallFunc_GetOnlineServices_ReturnValue) == 0x000020, "Member 'WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary::CallFunc_GetOnlineServices_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary, K2Node_CustomEvent_summary) == 0x000028, "Member 'WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary::K2Node_CustomEvent_summary' has a wrong offset!");
static_assert(offsetof(WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary, CallFunc_IsValid_ReturnValue) == 0x000160, "Member 'WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary, CallFunc_RequestDriverSummary_ReturnValue) == 0x000164, "Member 'WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary::CallFunc_RequestDriverSummary_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary, CallFunc_Conv_StringToText_ReturnValue) == 0x000168, "Member 'WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x000180, "Member 'WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary, CallFunc_PlayAnimation_ReturnValue_1) == 0x000188, "Member 'WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary::CallFunc_PlayAnimation_ReturnValue_1' has a wrong offset!");

// Function WDG_PlayerRatingSummary.WDG_PlayerRatingSummary_C.DriverSummaryReceived
// 0x0138 (0x0138 - 0x0000)
struct WDG_PlayerRatingSummary_C_DriverSummaryReceived final
{
public:
	struct FOnlineServicesDriverSummary           summary;                                           // 0x0000(0x0138)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_PlayerRatingSummary_C_DriverSummaryReceived) == 0x000008, "Wrong alignment on WDG_PlayerRatingSummary_C_DriverSummaryReceived");
static_assert(sizeof(WDG_PlayerRatingSummary_C_DriverSummaryReceived) == 0x000138, "Wrong size on WDG_PlayerRatingSummary_C_DriverSummaryReceived");
static_assert(offsetof(WDG_PlayerRatingSummary_C_DriverSummaryReceived, summary) == 0x000000, "Member 'WDG_PlayerRatingSummary_C_DriverSummaryReceived::summary' has a wrong offset!");

// Function WDG_PlayerRatingSummary.WDG_PlayerRatingSummary_C.ToPercentWithoutSymbol
// 0x0040 (0x0040 - 0x0000)
struct WDG_PlayerRatingSummary_C_ToPercentWithoutSymbol final
{
public:
	float                                         Value;                                             // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   Return;                                            // 0x0008(0x0018)(Parm, OutParm)
	float                                         CallFunc_Multiply_IntFloat_ReturnValue;            // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Round_ReturnValue;                        // 0x0024(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_IntToText_ReturnValue;               // 0x0028(0x0018)()
};
static_assert(alignof(WDG_PlayerRatingSummary_C_ToPercentWithoutSymbol) == 0x000008, "Wrong alignment on WDG_PlayerRatingSummary_C_ToPercentWithoutSymbol");
static_assert(sizeof(WDG_PlayerRatingSummary_C_ToPercentWithoutSymbol) == 0x000040, "Wrong size on WDG_PlayerRatingSummary_C_ToPercentWithoutSymbol");
static_assert(offsetof(WDG_PlayerRatingSummary_C_ToPercentWithoutSymbol, Value) == 0x000000, "Member 'WDG_PlayerRatingSummary_C_ToPercentWithoutSymbol::Value' has a wrong offset!");
static_assert(offsetof(WDG_PlayerRatingSummary_C_ToPercentWithoutSymbol, Return) == 0x000008, "Member 'WDG_PlayerRatingSummary_C_ToPercentWithoutSymbol::Return' has a wrong offset!");
static_assert(offsetof(WDG_PlayerRatingSummary_C_ToPercentWithoutSymbol, CallFunc_Multiply_IntFloat_ReturnValue) == 0x000020, "Member 'WDG_PlayerRatingSummary_C_ToPercentWithoutSymbol::CallFunc_Multiply_IntFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerRatingSummary_C_ToPercentWithoutSymbol, CallFunc_Round_ReturnValue) == 0x000024, "Member 'WDG_PlayerRatingSummary_C_ToPercentWithoutSymbol::CallFunc_Round_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerRatingSummary_C_ToPercentWithoutSymbol, CallFunc_Conv_IntToText_ReturnValue) == 0x000028, "Member 'WDG_PlayerRatingSummary_C_ToPercentWithoutSymbol::CallFunc_Conv_IntToText_ReturnValue' has a wrong offset!");

}

