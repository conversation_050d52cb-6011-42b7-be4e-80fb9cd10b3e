﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_MultiplayerAdvancedSettings

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_MultiplayerAdvancedSettings.WDG_MultiplayerAdvancedSettings_C
// 0x0040 (0x06A0 - 0x0660)
class UWDG_MultiplayerAdvancedSettings_C final : public UMultiplayerAdvancedOptionsPage
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0660(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       PageFade;                                          // 0x0668(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 bgImage;                                           // 0x0670(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Footer_C*                          Footer;                                            // 0x0678(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Header_C*                          Header;                                            // 0x0680(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Page_C*                            PageBase;                                          // 0x0688(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_HelpInMenu_C*                      WDG_HelpInMenu;                                    // 0x0690(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_OptionsListWidget_C*               WDG_OptionsListWidget;                             // 0x0698(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_MultiplayerAdvancedSettings(int32 EntryPoint);
	void BP_StartPage();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_MultiplayerAdvancedSettings_C">();
	}
	static class UWDG_MultiplayerAdvancedSettings_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_MultiplayerAdvancedSettings_C>();
	}
};
static_assert(alignof(UWDG_MultiplayerAdvancedSettings_C) == 0x000008, "Wrong alignment on UWDG_MultiplayerAdvancedSettings_C");
static_assert(sizeof(UWDG_MultiplayerAdvancedSettings_C) == 0x0006A0, "Wrong size on UWDG_MultiplayerAdvancedSettings_C");
static_assert(offsetof(UWDG_MultiplayerAdvancedSettings_C, UberGraphFrame) == 0x000660, "Member 'UWDG_MultiplayerAdvancedSettings_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerAdvancedSettings_C, PageFade) == 0x000668, "Member 'UWDG_MultiplayerAdvancedSettings_C::PageFade' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerAdvancedSettings_C, bgImage) == 0x000670, "Member 'UWDG_MultiplayerAdvancedSettings_C::bgImage' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerAdvancedSettings_C, Footer) == 0x000678, "Member 'UWDG_MultiplayerAdvancedSettings_C::Footer' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerAdvancedSettings_C, Header) == 0x000680, "Member 'UWDG_MultiplayerAdvancedSettings_C::Header' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerAdvancedSettings_C, PageBase) == 0x000688, "Member 'UWDG_MultiplayerAdvancedSettings_C::PageBase' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerAdvancedSettings_C, WDG_HelpInMenu) == 0x000690, "Member 'UWDG_MultiplayerAdvancedSettings_C::WDG_HelpInMenu' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerAdvancedSettings_C, WDG_OptionsListWidget) == 0x000698, "Member 'UWDG_MultiplayerAdvancedSettings_C::WDG_OptionsListWidget' has a wrong offset!");

}

