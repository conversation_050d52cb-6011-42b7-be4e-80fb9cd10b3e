﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_Page

#include "Basic.hpp"

#include "WDG_Page_classes.hpp"
#include "WDG_Page_parameters.hpp"


namespace SDK
{

// Function WDG_Page.WDG_Page_C.ExecuteUbergraph_WDG_Page
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_Page_C::ExecuteUbergraph_WDG_Page(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_Page_C", "ExecuteUbergraph_WDG_Page");

	Params::WDG_Page_C_ExecuteUbergraph_WDG_Page Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_Page.WDG_Page_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_Page_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_Page_C", "PreConstruct");

	Params::WDG_Page_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_Page.WDG_Page_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_Page_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_Page_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_Page.WDG_Page_C.UseBlurInsteadOfBackground
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_Page_C::UseBlurInsteadOfBackground()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_Page_C", "UseBlurInsteadOfBackground");

	UObject::ProcessEvent(Func, nullptr);
}

}

