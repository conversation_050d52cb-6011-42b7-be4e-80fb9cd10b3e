﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_WeatherTypePanel

#include "Basic.hpp"

#include "WDG_WeatherTypePanel_classes.hpp"
#include "WDG_WeatherTypePanel_parameters.hpp"


namespace SDK
{

// Function WDG_WeatherTypePanel.WDG_WeatherTypePanel_C.ExecuteUbergraph_WDG_WeatherTypePanel
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_WeatherTypePanel_C::ExecuteUbergraph_WDG_WeatherTypePanel(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherTypePanel_C", "ExecuteUbergraph_WDG_WeatherTypePanel");

	Params::WDG_WeatherTypePanel_C_ExecuteUbergraph_WDG_WeatherTypePanel Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_WeatherTypePanel.WDG_WeatherTypePanel_C.Forward
// (BlueprintCallable, BlueprintEvent)

void UWDG_WeatherTypePanel_C::Forward()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherTypePanel_C", "Forward");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_WeatherTypePanel.WDG_WeatherTypePanel_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_WeatherTypePanel_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherTypePanel_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_WeatherTypePanel.WDG_WeatherTypePanel_C.OnRemovedFromFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_WeatherTypePanel_C::OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherTypePanel_C", "OnRemovedFromFocusPath");

	Params::WDG_WeatherTypePanel_C_OnRemovedFromFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_WeatherTypePanel.WDG_WeatherTypePanel_C.OnAddedToFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_WeatherTypePanel_C::OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherTypePanel_C", "OnAddedToFocusPath");

	Params::WDG_WeatherTypePanel_C_OnAddedToFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_WeatherTypePanel.WDG_WeatherTypePanel_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_WeatherTypePanel_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherTypePanel_C", "PreConstruct");

	Params::WDG_WeatherTypePanel_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_WeatherTypePanel.WDG_WeatherTypePanel_C.Set Colors Based on Selected
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    is_selected                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_WeatherTypePanel_C::Set_Colors_Based_on_Selected(bool is_selected)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherTypePanel_C", "Set Colors Based on Selected");

	Params::WDG_WeatherTypePanel_C_Set_Colors_Based_on_Selected Parms{};

	Parms.is_selected = is_selected;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_WeatherTypePanel.WDG_WeatherTypePanel_C.SetSelected
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    IsSelected_0                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
// bool*                                   Changed                                                (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_WeatherTypePanel_C::SetSelected(bool IsSelected_0, bool* Changed)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherTypePanel_C", "SetSelected");

	Params::WDG_WeatherTypePanel_C_SetSelected Parms{};

	Parms.IsSelected_0 = IsSelected_0;

	UObject::ProcessEvent(Func, &Parms);

	if (Changed != nullptr)
		*Changed = Parms.Changed;
}

}

