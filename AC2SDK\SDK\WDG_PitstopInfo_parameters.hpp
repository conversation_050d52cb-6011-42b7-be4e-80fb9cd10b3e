﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PitstopInfo

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_PitstopInfo.WDG_PitstopInfo_C.ExecuteUbergraph_WDG_PitstopInfo
// 0x0148 (0x0148 - 0x0000)
struct WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, No<PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	uint8                                         Pad_5[0x3];                                        // 0x0005(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_Conv_IntToByte_ReturnValue;               // 0x000C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D[0x3];                                        // 0x000D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FGeometry                              K2Node_Event_MyGeometry;                           // 0x0010(0x0038)(IsPlainOldData, NoDestructor)
	float                                         K2Node_Event_InDeltaTime;                          // 0x0048(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4C[0x4];                                       // 0x004C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class AGameModeBase*                          CallFunc_GetGameMode_ReturnValue;                  // 0x0050(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Add_FloatFloat_ReturnValue;               // 0x0058(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_5C[0x4];                                       // 0x005C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class AAcRaceGameMode*                        K2Node_DynamicCast_AsAc_Race_Game_Mode;            // 0x0060(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0068(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_69[0x7];                                       // 0x0069(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class ACarAvatar*                             CallFunc_BP_GetPlayerCarAvatar_ReturnValue;        // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0078(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_79[0x7];                                       // 0x0079(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_ByteToText_ReturnValue;              // 0x0080(0x0018)()
	bool                                          CallFunc_NotEqual_ByteByte_ReturnValue;            // 0x0098(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Greater_ByteByte_ReturnValue;             // 0x0099(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_9A[0x2];                                       // 0x009A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Conv_ByteToInt_ReturnValue;               // 0x009C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x00A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Conv_IntToBool_ReturnValue;               // 0x00A1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x00A2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x00A3(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	int32                                         CallFunc_Array_Get_Item;                           // 0x00A4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Array_Get_Item_1;                         // 0x00A8(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	int32                                         CallFunc_Multiply_IntInt_ReturnValue;              // 0x00B8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_BC[0x4];                                       // 0x00BC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x00C0(0x0018)()
	class FText                                   CallFunc_ConvertInt32ToFormattedTimeText_ReturnValue; // 0x00D8(0x0018)()
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x00F0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x00F4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_2;               // 0x00F8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x00FC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_FD[0x3];                                       // 0x00FD(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_DriverStintItem_C*                 CallFunc_Array_Get_Item_2;                         // 0x0100(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue;              // 0x0108(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_DriverStintItem_C*                 CallFunc_Create_ReturnValue;                       // 0x0110(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetChildrenCount_ReturnValue;             // 0x0118(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x011C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_11D[0x3];                                      // 0x011D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue;                     // 0x0120(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue;                    // 0x0128(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x012C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESessionType                                  CallFunc_GetCurrentSessionType_ReturnValue;        // 0x0130(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0131(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_1;        // 0x0132(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_133[0x1];                                      // 0x0133(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0134(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_FloatFloat_ReturnValue;           // 0x0138(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0139(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_2;            // 0x013A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_isOnline_ReturnValue;                     // 0x013B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	ERaceMandatoryPitstopType                     CallFunc_getPitStopType_ReturnValue;               // 0x013C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_3;            // 0x013D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_2;        // 0x013E(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsVisible_ReturnValue;                    // 0x013F(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0140(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsVisible_ReturnValue_1;                  // 0x0141(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsVisible_ReturnValue_2;                  // 0x0142(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_1;                // 0x0143(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_2;                // 0x0144(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_2;                 // 0x0145(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_3;                 // 0x0146(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_4;                 // 0x0147(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo) == 0x000008, "Wrong alignment on WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo");
static_assert(sizeof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo) == 0x000148, "Wrong size on WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, EntryPoint) == 0x000000, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_MakeLiteralByte_ReturnValue) == 0x000004, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, Temp_int_Array_Index_Variable) == 0x000008, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_Conv_IntToByte_ReturnValue) == 0x00000C, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_Conv_IntToByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, K2Node_Event_MyGeometry) == 0x000010, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::K2Node_Event_MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, K2Node_Event_InDeltaTime) == 0x000048, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::K2Node_Event_InDeltaTime' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_GetGameMode_ReturnValue) == 0x000050, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_GetGameMode_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_Add_FloatFloat_ReturnValue) == 0x000058, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_Add_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, K2Node_DynamicCast_AsAc_Race_Game_Mode) == 0x000060, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::K2Node_DynamicCast_AsAc_Race_Game_Mode' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, K2Node_DynamicCast_bSuccess) == 0x000068, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_BP_GetPlayerCarAvatar_ReturnValue) == 0x000070, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_BP_GetPlayerCarAvatar_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_IsValid_ReturnValue) == 0x000078, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_Conv_ByteToText_ReturnValue) == 0x000080, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_Conv_ByteToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_NotEqual_ByteByte_ReturnValue) == 0x000098, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_NotEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_Greater_ByteByte_ReturnValue) == 0x000099, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_Greater_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_Conv_ByteToInt_ReturnValue) == 0x00009C, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_Conv_ByteToInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_BooleanAND_ReturnValue) == 0x0000A0, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_Conv_IntToBool_ReturnValue) == 0x0000A1, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_Conv_IntToBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_BooleanAND_ReturnValue_1) == 0x0000A2, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x0000A3, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_Array_Get_Item) == 0x0000A4, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_Array_Get_Item_1) == 0x0000A8, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_Multiply_IntInt_ReturnValue) == 0x0000B8, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_Multiply_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_Conv_StringToText_ReturnValue) == 0x0000C0, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_ConvertInt32ToFormattedTimeText_ReturnValue) == 0x0000D8, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_ConvertInt32ToFormattedTimeText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_Array_Length_ReturnValue) == 0x0000F0, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_Array_Length_ReturnValue_1) == 0x0000F4, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_Array_Length_ReturnValue_2) == 0x0000F8, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_Array_Length_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_Greater_IntInt_ReturnValue) == 0x0000FC, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_Array_Get_Item_2) == 0x000100, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_Array_Get_Item_2' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_GetOwningPlayer_ReturnValue) == 0x000108, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_GetOwningPlayer_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_Create_ReturnValue) == 0x000110, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_GetChildrenCount_ReturnValue) == 0x000118, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_GetChildrenCount_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x00011C, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_AddChild_ReturnValue) == 0x000120, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_AddChild_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_Array_Add_ReturnValue) == 0x000128, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_Array_Add_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, Temp_int_Loop_Counter_Variable) == 0x00012C, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_GetCurrentSessionType_ReturnValue) == 0x000130, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_GetCurrentSessionType_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_Less_IntInt_ReturnValue) == 0x000131, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_EqualEqual_ByteByte_ReturnValue_1) == 0x000132, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_EqualEqual_ByteByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_Add_IntInt_ReturnValue) == 0x000134, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_Greater_FloatFloat_ReturnValue) == 0x000138, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_Greater_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000139, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_MakeLiteralByte_ReturnValue_2) == 0x00013A, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_MakeLiteralByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_isOnline_ReturnValue) == 0x00013B, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_isOnline_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_getPitStopType_ReturnValue) == 0x00013C, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_getPitStopType_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_MakeLiteralByte_ReturnValue_3) == 0x00013D, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_MakeLiteralByte_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_EqualEqual_ByteByte_ReturnValue_2) == 0x00013E, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_EqualEqual_ByteByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_IsVisible_ReturnValue) == 0x00013F, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_IsVisible_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_Not_PreBool_ReturnValue) == 0x000140, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_IsVisible_ReturnValue_1) == 0x000141, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_IsVisible_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_IsVisible_ReturnValue_2) == 0x000142, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_IsVisible_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_Not_PreBool_ReturnValue_1) == 0x000143, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_Not_PreBool_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_Not_PreBool_ReturnValue_2) == 0x000144, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_Not_PreBool_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_BooleanAND_ReturnValue_2) == 0x000145, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_BooleanAND_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_BooleanAND_ReturnValue_3) == 0x000146, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_BooleanAND_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo, CallFunc_BooleanAND_ReturnValue_4) == 0x000147, "Member 'WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo::CallFunc_BooleanAND_ReturnValue_4' has a wrong offset!");

// Function WDG_PitstopInfo.WDG_PitstopInfo_C.Tick
// 0x003C (0x003C - 0x0000)
struct WDG_PitstopInfo_C_Tick final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	float                                         InDeltaTime;                                       // 0x0038(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_PitstopInfo_C_Tick) == 0x000004, "Wrong alignment on WDG_PitstopInfo_C_Tick");
static_assert(sizeof(WDG_PitstopInfo_C_Tick) == 0x00003C, "Wrong size on WDG_PitstopInfo_C_Tick");
static_assert(offsetof(WDG_PitstopInfo_C_Tick, MyGeometry) == 0x000000, "Member 'WDG_PitstopInfo_C_Tick::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_PitstopInfo_C_Tick, InDeltaTime) == 0x000038, "Member 'WDG_PitstopInfo_C_Tick::InDeltaTime' has a wrong offset!");

}

