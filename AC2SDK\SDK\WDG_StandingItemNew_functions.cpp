﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_StandingItemNew

#include "Basic.hpp"

#include "WDG_StandingItemNew_classes.hpp"
#include "WDG_StandingItemNew_parameters.hpp"


namespace SDK
{

// Function WDG_StandingItemNew.WDG_StandingItemNew_C.ExecuteUbergraph_WDG_StandingItemNew
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_StandingItemNew_C::ExecuteUbergraph_WDG_StandingItemNew(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_StandingItemNew_C", "ExecuteUbergraph_WDG_StandingItemNew");

	Params::WDG_StandingItemNew_C_ExecuteUbergraph_WDG_StandingItemNew Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_StandingItemNew.WDG_StandingItemNew_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_StandingItemNew_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_StandingItemNew_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}

}

