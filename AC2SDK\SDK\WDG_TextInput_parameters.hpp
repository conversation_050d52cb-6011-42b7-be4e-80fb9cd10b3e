﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TextInput

#include "Basic.hpp"

#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_TextInput.WDG_TextInput_C.ExecuteUbergraph_WDG_TextInput
// 0x0120 (0x0120 - 0x0000)
struct WDG_TextInput_C_ExecuteUbergraph_WDG_TextInput final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Greater_FloatFloat_ReturnValue;           // 0x0005(0x0001)(ZeroConstruct<PERSON>, <PERSON><PERSON><PERSON><PERSON>ldD<PERSON>, NoDestructor)
	uint8                                         Pad_6[0x2];                                        // 0x0006(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FGeometry                              K2Node_Event_MyGeometry;                           // 0x0008(0x0038)(IsPlainOldData, NoDestructor)
	struct FPointerEvent                          K2Node_Event_MouseEvent_1;                         // 0x0040(0x0070)(ConstParm)
	struct FPointerEvent                          K2Node_Event_MouseEvent;                           // 0x00B0(0x0070)(ConstParm)
};
static_assert(alignof(WDG_TextInput_C_ExecuteUbergraph_WDG_TextInput) == 0x000008, "Wrong alignment on WDG_TextInput_C_ExecuteUbergraph_WDG_TextInput");
static_assert(sizeof(WDG_TextInput_C_ExecuteUbergraph_WDG_TextInput) == 0x000120, "Wrong size on WDG_TextInput_C_ExecuteUbergraph_WDG_TextInput");
static_assert(offsetof(WDG_TextInput_C_ExecuteUbergraph_WDG_TextInput, EntryPoint) == 0x000000, "Member 'WDG_TextInput_C_ExecuteUbergraph_WDG_TextInput::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_TextInput_C_ExecuteUbergraph_WDG_TextInput, K2Node_Event_IsDesignTime) == 0x000004, "Member 'WDG_TextInput_C_ExecuteUbergraph_WDG_TextInput::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_TextInput_C_ExecuteUbergraph_WDG_TextInput, CallFunc_Greater_FloatFloat_ReturnValue) == 0x000005, "Member 'WDG_TextInput_C_ExecuteUbergraph_WDG_TextInput::CallFunc_Greater_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TextInput_C_ExecuteUbergraph_WDG_TextInput, K2Node_Event_MyGeometry) == 0x000008, "Member 'WDG_TextInput_C_ExecuteUbergraph_WDG_TextInput::K2Node_Event_MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_TextInput_C_ExecuteUbergraph_WDG_TextInput, K2Node_Event_MouseEvent_1) == 0x000040, "Member 'WDG_TextInput_C_ExecuteUbergraph_WDG_TextInput::K2Node_Event_MouseEvent_1' has a wrong offset!");
static_assert(offsetof(WDG_TextInput_C_ExecuteUbergraph_WDG_TextInput, K2Node_Event_MouseEvent) == 0x0000B0, "Member 'WDG_TextInput_C_ExecuteUbergraph_WDG_TextInput::K2Node_Event_MouseEvent' has a wrong offset!");

// Function WDG_TextInput.WDG_TextInput_C.OnMouseLeave
// 0x0070 (0x0070 - 0x0000)
struct WDG_TextInput_C_OnMouseLeave final
{
public:
	struct FPointerEvent                          MouseEvent;                                        // 0x0000(0x0070)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_TextInput_C_OnMouseLeave) == 0x000008, "Wrong alignment on WDG_TextInput_C_OnMouseLeave");
static_assert(sizeof(WDG_TextInput_C_OnMouseLeave) == 0x000070, "Wrong size on WDG_TextInput_C_OnMouseLeave");
static_assert(offsetof(WDG_TextInput_C_OnMouseLeave, MouseEvent) == 0x000000, "Member 'WDG_TextInput_C_OnMouseLeave::MouseEvent' has a wrong offset!");

// Function WDG_TextInput.WDG_TextInput_C.OnMouseEnter
// 0x00A8 (0x00A8 - 0x0000)
struct WDG_TextInput_C_OnMouseEnter final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FPointerEvent                          MouseEvent;                                        // 0x0038(0x0070)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_TextInput_C_OnMouseEnter) == 0x000008, "Wrong alignment on WDG_TextInput_C_OnMouseEnter");
static_assert(sizeof(WDG_TextInput_C_OnMouseEnter) == 0x0000A8, "Wrong size on WDG_TextInput_C_OnMouseEnter");
static_assert(offsetof(WDG_TextInput_C_OnMouseEnter, MyGeometry) == 0x000000, "Member 'WDG_TextInput_C_OnMouseEnter::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_TextInput_C_OnMouseEnter, MouseEvent) == 0x000038, "Member 'WDG_TextInput_C_OnMouseEnter::MouseEvent' has a wrong offset!");

// Function WDG_TextInput.WDG_TextInput_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_TextInput_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_TextInput_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_TextInput_C_PreConstruct");
static_assert(sizeof(WDG_TextInput_C_PreConstruct) == 0x000001, "Wrong size on WDG_TextInput_C_PreConstruct");
static_assert(offsetof(WDG_TextInput_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_TextInput_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WDG_TextInput.WDG_TextInput_C.PlayPulseAnimation
// 0x0010 (0x0010 - 0x0000)
struct WDG_TextInput_C_PlayPulseAnimation final
{
public:
	int32                                         NumLoopsToPlay;                                    // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         PlaybackSpeed;                                     // 0x0004(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_TextInput_C_PlayPulseAnimation) == 0x000008, "Wrong alignment on WDG_TextInput_C_PlayPulseAnimation");
static_assert(sizeof(WDG_TextInput_C_PlayPulseAnimation) == 0x000010, "Wrong size on WDG_TextInput_C_PlayPulseAnimation");
static_assert(offsetof(WDG_TextInput_C_PlayPulseAnimation, NumLoopsToPlay) == 0x000000, "Member 'WDG_TextInput_C_PlayPulseAnimation::NumLoopsToPlay' has a wrong offset!");
static_assert(offsetof(WDG_TextInput_C_PlayPulseAnimation, PlaybackSpeed) == 0x000004, "Member 'WDG_TextInput_C_PlayPulseAnimation::PlaybackSpeed' has a wrong offset!");
static_assert(offsetof(WDG_TextInput_C_PlayPulseAnimation, CallFunc_PlayAnimation_ReturnValue) == 0x000008, "Member 'WDG_TextInput_C_PlayPulseAnimation::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");

}

