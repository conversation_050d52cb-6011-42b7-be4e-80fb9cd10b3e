﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ServerInfoItem

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "SlateCore_structs.hpp"
#include "CoreUObject_structs.hpp"


namespace SDK::Params
{

// Function WDG_ServerInfoItem.WDG_ServerInfoItem_C.ExecuteUbergraph_WDG_ServerInfoItem
// 0x0238 (0x0238 - 0x0000)
struct WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ServerInfoSessionItem_C*           CallFunc_Create_ReturnValue;                       // 0x0008(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FOnlineServicesMPServerInfo            K2Node_Event_receivedServerInfo;                   // 0x0010(0x00B0)()
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue;                     // 0x00C0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x00C8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x00CC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	EContentType                                  CallFunc_MPCarGroupToContentId_ReturnValue;        // 0x00CD(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_CanPlay_Result;                           // 0x00CE(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x00CF(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	class FName                                   CallFunc_Conv_StringToName_ReturnValue;            // 0x00D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FDateTime                              CallFunc_UtcNow_ReturnValue;                       // 0x00D8(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FDateTime                              CallFunc_UtcNow_ReturnValue_1;                     // 0x00E0(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FTimespan                              CallFunc_Subtract_DateTimeDateTime_ReturnValue;    // 0x00E8(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DateTimeDateTime_ReturnValue;     // 0x00F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_F1[0x3];                                       // 0x00F1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_BreakTimespan_Days;                       // 0x00F4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakTimespan_Hours;                      // 0x00F8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakTimespan_Minutes;                    // 0x00FC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakTimespan_Seconds;                    // 0x0100(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakTimespan_Milliseconds;               // 0x0104(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_mouse_enter;                          // 0x0108(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_109[0x3];                                      // 0x0109(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Array_Index_Variable;                     // 0x010C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FOnlineServicesMPServerSessionInfo     CallFunc_Array_Get_Item;                           // 0x0110(0x000C)(NoDestructor)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x011C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0120(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_121[0x3];                                      // 0x0121(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0124(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Map_Find_Value;                           // 0x0128(0x0018)()
	bool                                          CallFunc_Map_Find_ReturnValue;                     // 0x0140(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_141[0x7];                                      // 0x0141(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue;              // 0x0148(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FGeometry                              K2Node_Event_MyGeometry;                           // 0x0150(0x0038)(IsPlainOldData, NoDestructor)
	float                                         K2Node_Event_InDeltaTime;                          // 0x0188(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_LocalToViewport_PixelPosition;            // 0x018C(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_LocalToViewport_ViewportPosition;         // 0x0194(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x019C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_19D[0x3];                                      // 0x019D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_BreakVector2D_X;                          // 0x01A0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakVector2D_Y;                          // 0x01A4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_FloatFloat_ReturnValue;              // 0x01A8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Greater_FloatFloat_ReturnValue;           // 0x01A9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x01AA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1AB[0x5];                                      // 0x01AB(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateColor                            K2Node_MakeStruct_SlateColor;                      // 0x01B0(0x0028)()
	struct FSlateFontInfo                         K2Node_MakeStruct_SlateFontInfo;                   // 0x01D8(0x0058)(UObjectWrapper, HasGetValueTypeHash)
	bool                                          CallFunc_CanPlay_Result_1;                         // 0x0230(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_2;                 // 0x0231(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_GetCircuitInfo_ReturnValue;               // 0x0232(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem) == 0x000008, "Wrong alignment on WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem");
static_assert(sizeof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem) == 0x000238, "Wrong size on WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, EntryPoint) == 0x000000, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_Create_ReturnValue) == 0x000008, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, K2Node_Event_receivedServerInfo) == 0x000010, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::K2Node_Event_receivedServerInfo' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_AddChild_ReturnValue) == 0x0000C0, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_AddChild_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_Array_Length_ReturnValue) == 0x0000C8, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_Not_PreBool_ReturnValue) == 0x0000CC, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_MPCarGroupToContentId_ReturnValue) == 0x0000CD, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_MPCarGroupToContentId_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_CanPlay_Result) == 0x0000CE, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_CanPlay_Result' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, K2Node_SwitchEnum_CmpSuccess) == 0x0000CF, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_Conv_StringToName_ReturnValue) == 0x0000D0, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_Conv_StringToName_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_UtcNow_ReturnValue) == 0x0000D8, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_UtcNow_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_UtcNow_ReturnValue_1) == 0x0000E0, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_UtcNow_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_Subtract_DateTimeDateTime_ReturnValue) == 0x0000E8, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_Subtract_DateTimeDateTime_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_Greater_DateTimeDateTime_ReturnValue) == 0x0000F0, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_Greater_DateTimeDateTime_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_BreakTimespan_Days) == 0x0000F4, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_BreakTimespan_Days' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_BreakTimespan_Hours) == 0x0000F8, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_BreakTimespan_Hours' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_BreakTimespan_Minutes) == 0x0000FC, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_BreakTimespan_Minutes' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_BreakTimespan_Seconds) == 0x000100, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_BreakTimespan_Seconds' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_BreakTimespan_Milliseconds) == 0x000104, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_BreakTimespan_Milliseconds' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, K2Node_Event_mouse_enter) == 0x000108, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::K2Node_Event_mouse_enter' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, Temp_int_Array_Index_Variable) == 0x00010C, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_Array_Get_Item) == 0x000110, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, Temp_int_Loop_Counter_Variable) == 0x00011C, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_Less_IntInt_ReturnValue) == 0x000120, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_Add_IntInt_ReturnValue) == 0x000124, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_Map_Find_Value) == 0x000128, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_Map_Find_Value' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_Map_Find_ReturnValue) == 0x000140, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_Map_Find_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_GetOwningPlayer_ReturnValue) == 0x000148, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_GetOwningPlayer_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, K2Node_Event_MyGeometry) == 0x000150, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::K2Node_Event_MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, K2Node_Event_InDeltaTime) == 0x000188, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::K2Node_Event_InDeltaTime' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_LocalToViewport_PixelPosition) == 0x00018C, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_LocalToViewport_PixelPosition' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_LocalToViewport_ViewportPosition) == 0x000194, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_LocalToViewport_ViewportPosition' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_BooleanAND_ReturnValue) == 0x00019C, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_BreakVector2D_X) == 0x0001A0, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_BreakVector2D_Y) == 0x0001A4, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_Less_FloatFloat_ReturnValue) == 0x0001A8, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_Less_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_Greater_FloatFloat_ReturnValue) == 0x0001A9, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_Greater_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_BooleanAND_ReturnValue_1) == 0x0001AA, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, K2Node_MakeStruct_SlateColor) == 0x0001B0, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::K2Node_MakeStruct_SlateColor' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, K2Node_MakeStruct_SlateFontInfo) == 0x0001D8, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::K2Node_MakeStruct_SlateFontInfo' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_CanPlay_Result_1) == 0x000230, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_CanPlay_Result_1' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_BooleanAND_ReturnValue_2) == 0x000231, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_BooleanAND_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem, CallFunc_GetCircuitInfo_ReturnValue) == 0x000232, "Member 'WDG_ServerInfoItem_C_ExecuteUbergraph_WDG_ServerInfoItem::CallFunc_GetCircuitInfo_ReturnValue' has a wrong offset!");

// Function WDG_ServerInfoItem.WDG_ServerInfoItem_C.Tick
// 0x003C (0x003C - 0x0000)
struct WDG_ServerInfoItem_C_Tick final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	float                                         InDeltaTime;                                       // 0x0038(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ServerInfoItem_C_Tick) == 0x000004, "Wrong alignment on WDG_ServerInfoItem_C_Tick");
static_assert(sizeof(WDG_ServerInfoItem_C_Tick) == 0x00003C, "Wrong size on WDG_ServerInfoItem_C_Tick");
static_assert(offsetof(WDG_ServerInfoItem_C_Tick, MyGeometry) == 0x000000, "Member 'WDG_ServerInfoItem_C_Tick::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_ServerInfoItem_C_Tick, InDeltaTime) == 0x000038, "Member 'WDG_ServerInfoItem_C_Tick::InDeltaTime' has a wrong offset!");

// Function WDG_ServerInfoItem.WDG_ServerInfoItem_C.BP_MouseFocus
// 0x0001 (0x0001 - 0x0000)
struct WDG_ServerInfoItem_C_BP_MouseFocus final
{
public:
	bool                                          mouse_enter;                                       // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ServerInfoItem_C_BP_MouseFocus) == 0x000001, "Wrong alignment on WDG_ServerInfoItem_C_BP_MouseFocus");
static_assert(sizeof(WDG_ServerInfoItem_C_BP_MouseFocus) == 0x000001, "Wrong size on WDG_ServerInfoItem_C_BP_MouseFocus");
static_assert(offsetof(WDG_ServerInfoItem_C_BP_MouseFocus, mouse_enter) == 0x000000, "Member 'WDG_ServerInfoItem_C_BP_MouseFocus::mouse_enter' has a wrong offset!");

// Function WDG_ServerInfoItem.WDG_ServerInfoItem_C.OnSetServerInfoComplete
// 0x00B0 (0x00B0 - 0x0000)
struct WDG_ServerInfoItem_C_OnSetServerInfoComplete final
{
public:
	struct FOnlineServicesMPServerInfo            receivedServerInfo;                                // 0x0000(0x00B0)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_ServerInfoItem_C_OnSetServerInfoComplete) == 0x000008, "Wrong alignment on WDG_ServerInfoItem_C_OnSetServerInfoComplete");
static_assert(sizeof(WDG_ServerInfoItem_C_OnSetServerInfoComplete) == 0x0000B0, "Wrong size on WDG_ServerInfoItem_C_OnSetServerInfoComplete");
static_assert(offsetof(WDG_ServerInfoItem_C_OnSetServerInfoComplete, receivedServerInfo) == 0x000000, "Member 'WDG_ServerInfoItem_C_OnSetServerInfoComplete::receivedServerInfo' has a wrong offset!");

}

