﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomLogo

#include "Basic.hpp"

#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ShowroomLogo.WDG_ShowroomLogo_C
// 0x0008 (0x0268 - 0x0260)
class UWDG_ShowroomLogo_C final : public UUserWidget
{
public:
	class UImage*                                 ACCLogoShowroom;                                   // 0x0260(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ShowroomLogo_C">();
	}
	static class UWDG_ShowroomLogo_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ShowroomLogo_C>();
	}
};
static_assert(alignof(UWDG_ShowroomLogo_C) == 0x000008, "Wrong alignment on UWDG_ShowroomLogo_C");
static_assert(sizeof(UWDG_ShowroomLogo_C) == 0x000268, "Wrong size on UWDG_ShowroomLogo_C");
static_assert(offsetof(UWDG_ShowroomLogo_C, ACCLogoShowroom) == 0x000260, "Member 'UWDG_ShowroomLogo_C::ACCLogoShowroom' has a wrong offset!");

}

