﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SeasonItem

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"
#include "CoreUObject_structs.hpp"
#include "UMG_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SeasonItem.WDG_SeasonItem_C
// 0x01B8 (0x0798 - 0x05E0)
class UWDG_SeasonItem_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       HoverNormal;                                       // 0x05E8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSki<PERSON>, NoD<PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	class UWidgetAnimation*                       HoverSelected;                                     // 0x05F0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       Hover;                                             // 0x05F8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UScaleBox*                              BackgroundImage;                                   // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                bdrMain;                                           // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                brdAltGradient;                                    // 0x0610(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                brdGradient;                                       // 0x0618(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                brdHeader;                                         // 0x0620(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                brdSeason;                                         // 0x0628(0x0008)(ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                brdText;                                           // 0x0630(0x0008)(ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 img2018;                                           // 0x0638(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 img2019;                                           // 0x0640(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 img2020;                                           // 0x0648(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 img2021;                                           // 0x0650(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 img2022;                                           // 0x0658(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 img2023;                                           // 0x0660(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 img2024;                                           // 0x0668(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgBGT;                                            // 0x0670(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgGT2;                                            // 0x0678(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgGT4;                                            // 0x0680(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgIGTC;                                           // 0x0688(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgLocked;                                         // 0x0690(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgOpen;                                           // 0x0698(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 LogoBGT;                                           // 0x06A0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 LogoBGTDark;                                       // 0x06A8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 LogoGT2;                                           // 0x06B0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 LogoGT2Dark;                                       // 0x06B8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 LogoGT4;                                           // 0x06C0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 LogoGT4Dark;                                       // 0x06C8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 logoIGTC;                                          // 0x06D0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 LogoIGTCDark;                                      // 0x06D8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 LogoOpen;                                          // 0x06E0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 LogoOpenDark;                                      // 0x06E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class USizeBox*                               sizeWrapper;                                       // 0x06F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWidgetSwitcher*                        SwitcherImage;                                     // 0x06F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWidgetSwitcher*                        SwitcherLogo;                                      // 0x0700(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWidgetSwitcher*                        SwitcherLogoDark;                                  // 0x0708(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtSeasonName;                                     // 0x0710(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           wrapperLock;                                       // 0x0718(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         wrapperSeason;                                     // 0x0720(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	ESeasonType                                   SeasonType;                                        // 0x0728(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          UseCustomText;                                     // 0x0729(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          UseCustomImage;                                    // 0x072A(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          LogoVisible;                                       // 0x072B(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_72C[0x4];                                      // 0x072C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CustomText;                                        // 0x0730(0x0018)(Edit, BlueprintVisible)
	struct FLinearColor                           backgoundColor;                                    // 0x0748(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           TextColor;                                         // 0x0758(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          IsSelected;                                        // 0x0768(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_769[0x7];                                      // 0x0769(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class AAcMenuGameMode*                        GameMode;                                          // 0x0770(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         Playback_Speed;                                    // 0x0778(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_77C[0x4];                                      // 0x077C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TMulticastInlineDelegate<void(ESeasonType Season)> SeasonNotChanged;                             // 0x0780(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	bool                                          Lockable;                                          // 0x0790(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)

public:
	void ExecuteUbergraph_WDG_SeasonItem(int32 EntryPoint);
	void OnAfterConstruct();
	void OnSeasonChanged(ESeasonType new_season);
	void OnForwardPressed();
	void BP_MouseLeave();
	void BP_MouseOver();
	void PreConstruct(bool IsDesignTime);
	void SetSelected(bool Selected);
	void PlayHoverAnimation(EUMGSequencePlayMode PlayMode);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SeasonItem_C">();
	}
	static class UWDG_SeasonItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SeasonItem_C>();
	}
};
static_assert(alignof(UWDG_SeasonItem_C) == 0x000008, "Wrong alignment on UWDG_SeasonItem_C");
static_assert(sizeof(UWDG_SeasonItem_C) == 0x000798, "Wrong size on UWDG_SeasonItem_C");
static_assert(offsetof(UWDG_SeasonItem_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_SeasonItem_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, HoverNormal) == 0x0005E8, "Member 'UWDG_SeasonItem_C::HoverNormal' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, HoverSelected) == 0x0005F0, "Member 'UWDG_SeasonItem_C::HoverSelected' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, Hover) == 0x0005F8, "Member 'UWDG_SeasonItem_C::Hover' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, BackgroundImage) == 0x000600, "Member 'UWDG_SeasonItem_C::BackgroundImage' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, bdrMain) == 0x000608, "Member 'UWDG_SeasonItem_C::bdrMain' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, brdAltGradient) == 0x000610, "Member 'UWDG_SeasonItem_C::brdAltGradient' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, brdGradient) == 0x000618, "Member 'UWDG_SeasonItem_C::brdGradient' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, brdHeader) == 0x000620, "Member 'UWDG_SeasonItem_C::brdHeader' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, brdSeason) == 0x000628, "Member 'UWDG_SeasonItem_C::brdSeason' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, brdText) == 0x000630, "Member 'UWDG_SeasonItem_C::brdText' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, img2018) == 0x000638, "Member 'UWDG_SeasonItem_C::img2018' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, img2019) == 0x000640, "Member 'UWDG_SeasonItem_C::img2019' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, img2020) == 0x000648, "Member 'UWDG_SeasonItem_C::img2020' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, img2021) == 0x000650, "Member 'UWDG_SeasonItem_C::img2021' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, img2022) == 0x000658, "Member 'UWDG_SeasonItem_C::img2022' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, img2023) == 0x000660, "Member 'UWDG_SeasonItem_C::img2023' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, img2024) == 0x000668, "Member 'UWDG_SeasonItem_C::img2024' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, imgBGT) == 0x000670, "Member 'UWDG_SeasonItem_C::imgBGT' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, imgGT2) == 0x000678, "Member 'UWDG_SeasonItem_C::imgGT2' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, imgGT4) == 0x000680, "Member 'UWDG_SeasonItem_C::imgGT4' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, imgIGTC) == 0x000688, "Member 'UWDG_SeasonItem_C::imgIGTC' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, imgLocked) == 0x000690, "Member 'UWDG_SeasonItem_C::imgLocked' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, imgOpen) == 0x000698, "Member 'UWDG_SeasonItem_C::imgOpen' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, LogoBGT) == 0x0006A0, "Member 'UWDG_SeasonItem_C::LogoBGT' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, LogoBGTDark) == 0x0006A8, "Member 'UWDG_SeasonItem_C::LogoBGTDark' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, LogoGT2) == 0x0006B0, "Member 'UWDG_SeasonItem_C::LogoGT2' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, LogoGT2Dark) == 0x0006B8, "Member 'UWDG_SeasonItem_C::LogoGT2Dark' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, LogoGT4) == 0x0006C0, "Member 'UWDG_SeasonItem_C::LogoGT4' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, LogoGT4Dark) == 0x0006C8, "Member 'UWDG_SeasonItem_C::LogoGT4Dark' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, logoIGTC) == 0x0006D0, "Member 'UWDG_SeasonItem_C::logoIGTC' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, LogoIGTCDark) == 0x0006D8, "Member 'UWDG_SeasonItem_C::LogoIGTCDark' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, LogoOpen) == 0x0006E0, "Member 'UWDG_SeasonItem_C::LogoOpen' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, LogoOpenDark) == 0x0006E8, "Member 'UWDG_SeasonItem_C::LogoOpenDark' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, sizeWrapper) == 0x0006F0, "Member 'UWDG_SeasonItem_C::sizeWrapper' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, SwitcherImage) == 0x0006F8, "Member 'UWDG_SeasonItem_C::SwitcherImage' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, SwitcherLogo) == 0x000700, "Member 'UWDG_SeasonItem_C::SwitcherLogo' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, SwitcherLogoDark) == 0x000708, "Member 'UWDG_SeasonItem_C::SwitcherLogoDark' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, txtSeasonName) == 0x000710, "Member 'UWDG_SeasonItem_C::txtSeasonName' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, wrapperLock) == 0x000718, "Member 'UWDG_SeasonItem_C::wrapperLock' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, wrapperSeason) == 0x000720, "Member 'UWDG_SeasonItem_C::wrapperSeason' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, SeasonType) == 0x000728, "Member 'UWDG_SeasonItem_C::SeasonType' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, UseCustomText) == 0x000729, "Member 'UWDG_SeasonItem_C::UseCustomText' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, UseCustomImage) == 0x00072A, "Member 'UWDG_SeasonItem_C::UseCustomImage' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, LogoVisible) == 0x00072B, "Member 'UWDG_SeasonItem_C::LogoVisible' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, CustomText) == 0x000730, "Member 'UWDG_SeasonItem_C::CustomText' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, backgoundColor) == 0x000748, "Member 'UWDG_SeasonItem_C::backgoundColor' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, TextColor) == 0x000758, "Member 'UWDG_SeasonItem_C::TextColor' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, IsSelected) == 0x000768, "Member 'UWDG_SeasonItem_C::IsSelected' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, GameMode) == 0x000770, "Member 'UWDG_SeasonItem_C::GameMode' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, Playback_Speed) == 0x000778, "Member 'UWDG_SeasonItem_C::Playback_Speed' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, SeasonNotChanged) == 0x000780, "Member 'UWDG_SeasonItem_C::SeasonNotChanged' has a wrong offset!");
static_assert(offsetof(UWDG_SeasonItem_C, Lockable) == 0x000790, "Member 'UWDG_SeasonItem_C::Lockable' has a wrong offset!");

}

