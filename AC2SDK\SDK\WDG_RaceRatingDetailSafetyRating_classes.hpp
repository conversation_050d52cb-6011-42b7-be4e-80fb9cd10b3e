﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RaceRatingDetailSafetyRating

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RaceRatingDetailSafetyRating.WDG_RaceRatingDetailSafetyRating_C
// 0x0000 (0x0298 - 0x0298)
class UWDG_RaceRatingDetailSafetyRating_C final : public URatingDetailSafetyRating
{
public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RaceRatingDetailSafetyRating_C">();
	}
	static class UWDG_RaceRatingDetailSafetyRating_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RaceRatingDetailSafetyRating_C>();
	}
};
static_assert(alignof(UWDG_RaceRatingDetailSafetyRating_C) == 0x000008, "Wrong alignment on UWDG_RaceRatingDetailSafetyRating_C");
static_assert(sizeof(UWDG_RaceRatingDetailSafetyRating_C) == 0x000298, "Wrong size on UWDG_RaceRatingDetailSafetyRating_C");

}

