﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RatingDetailPC

#include "Basic.hpp"

#include "WDG_RatingDetailPC_classes.hpp"
#include "WDG_RatingDetailPC_parameters.hpp"


namespace SDK
{

// Function WDG_RatingDetailPC.WDG_RatingDetailPC_C.ExecuteUbergraph_WDG_RatingDetailPC
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_RatingDetailPC_C::ExecuteUbergraph_WDG_RatingDetailPC(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_RatingDetailPC_C", "ExecuteUbergraph_WDG_RatingDetailPC");

	Params::WDG_RatingDetailPC_C_ExecuteUbergraph_WDG_RatingDetailPC Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_RatingDetailPC.WDG_RatingDetailPC_C.ClearLeaderboardList
// (Event, Protected, BlueprintEvent)

void UWDG_RatingDetailPC_C::ClearLeaderboardList()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_RatingDetailPC_C", "ClearLeaderboardList");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_RatingDetailPC.WDG_RatingDetailPC_C.CreateLeaderboardEntry
// (Event, Protected, HasOutParams, BlueprintEvent)
// Parameters:
// const struct FOnlineServicesLeaderboardRank&Rank                                                   (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)

void UWDG_RatingDetailPC_C::CreateLeaderboardEntry(const struct FOnlineServicesLeaderboardRank& Rank)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_RatingDetailPC_C", "CreateLeaderboardEntry");

	Params::WDG_RatingDetailPC_C_CreateLeaderboardEntry Parms{};

	Parms.Rank = std::move(Rank);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_RatingDetailPC.WDG_RatingDetailPC_C.CreateNewSeason
// (Event, Protected, BlueprintEvent)
// Parameters:
// int32                                   Season                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_RatingDetailPC_C::CreateNewSeason(int32 Season)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_RatingDetailPC_C", "CreateNewSeason");

	Params::WDG_RatingDetailPC_C_CreateNewSeason Parms{};

	Parms.Season = Season;

	UObject::ProcessEvent(Func, &Parms);
}

}

