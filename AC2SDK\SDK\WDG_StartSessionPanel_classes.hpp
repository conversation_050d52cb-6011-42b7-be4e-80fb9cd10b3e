﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_StartSessionPanel

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_StartSessionPanel.WDG_StartSessionPanel_C
// 0x0058 (0x0658 - 0x0600)
class UWDG_StartSessionPanel_C final : public UStartSessionPanel
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0600(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       Scale;                                             // 0x0608(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       Opacity;                                           // 0x0610(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 BottomHighlight;                                   // 0x0618(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 HoverImageBox;                                     // 0x0620(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgFlags;                                          // 0x0628(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 LeftHighlight;                                     // 0x0630(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 NormalImageBox;                                    // 0x0638(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 RightHighlight;                                    // 0x0640(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 TopHighlight;                                      // 0x0648(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	bool                                          IsImageHidden;                                     // 0x0650(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn)

public:
	void ExecuteUbergraph_WDG_StartSessionPanel(int32 EntryPoint);
	void PreConstruct(bool IsDesignTime);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_StartSessionPanel_C">();
	}
	static class UWDG_StartSessionPanel_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_StartSessionPanel_C>();
	}
};
static_assert(alignof(UWDG_StartSessionPanel_C) == 0x000008, "Wrong alignment on UWDG_StartSessionPanel_C");
static_assert(sizeof(UWDG_StartSessionPanel_C) == 0x000658, "Wrong size on UWDG_StartSessionPanel_C");
static_assert(offsetof(UWDG_StartSessionPanel_C, UberGraphFrame) == 0x000600, "Member 'UWDG_StartSessionPanel_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_StartSessionPanel_C, Scale) == 0x000608, "Member 'UWDG_StartSessionPanel_C::Scale' has a wrong offset!");
static_assert(offsetof(UWDG_StartSessionPanel_C, Opacity) == 0x000610, "Member 'UWDG_StartSessionPanel_C::Opacity' has a wrong offset!");
static_assert(offsetof(UWDG_StartSessionPanel_C, BottomHighlight) == 0x000618, "Member 'UWDG_StartSessionPanel_C::BottomHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_StartSessionPanel_C, HoverImageBox) == 0x000620, "Member 'UWDG_StartSessionPanel_C::HoverImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_StartSessionPanel_C, imgFlags) == 0x000628, "Member 'UWDG_StartSessionPanel_C::imgFlags' has a wrong offset!");
static_assert(offsetof(UWDG_StartSessionPanel_C, LeftHighlight) == 0x000630, "Member 'UWDG_StartSessionPanel_C::LeftHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_StartSessionPanel_C, NormalImageBox) == 0x000638, "Member 'UWDG_StartSessionPanel_C::NormalImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_StartSessionPanel_C, RightHighlight) == 0x000640, "Member 'UWDG_StartSessionPanel_C::RightHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_StartSessionPanel_C, TopHighlight) == 0x000648, "Member 'UWDG_StartSessionPanel_C::TopHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_StartSessionPanel_C, IsImageHidden) == 0x000650, "Member 'UWDG_StartSessionPanel_C::IsImageHidden' has a wrong offset!");

}

