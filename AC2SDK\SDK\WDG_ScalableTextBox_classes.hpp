﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ScalableTextBox

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "SlateCore_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ScalableTextBox.WDG_ScalableTextBox_C
// 0x00A8 (0x0308 - 0x0260)
class UWDG_ScalableTextBox_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0260(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       HideAnim;                                          // 0x0268(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 Image_0;                                           // 0x0270(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgTopBackground;                                  // 0x0278(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             NamedSlot_0;                                       // 0x0280(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtInfo;                                           // 0x0288(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtText;                                           // 0x0290(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtTitle;                                          // 0x0298(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UVerticalBox*                           vbox;                                              // 0x02A0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	struct FSlateFontInfo                         TitleFont;                                         // 0x02A8(0x0058)(Edit, BlueprintVisible, DisableEditOnInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtToInsert;                                       // 0x0300(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_ScalableTextBox(int32 EntryPoint);
	void Construct();
	void AddText(const class FText& text, int32 FontSize);
	void AddToVBox(class UWidget* Content);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ScalableTextBox_C">();
	}
	static class UWDG_ScalableTextBox_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ScalableTextBox_C>();
	}
};
static_assert(alignof(UWDG_ScalableTextBox_C) == 0x000008, "Wrong alignment on UWDG_ScalableTextBox_C");
static_assert(sizeof(UWDG_ScalableTextBox_C) == 0x000308, "Wrong size on UWDG_ScalableTextBox_C");
static_assert(offsetof(UWDG_ScalableTextBox_C, UberGraphFrame) == 0x000260, "Member 'UWDG_ScalableTextBox_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_ScalableTextBox_C, HideAnim) == 0x000268, "Member 'UWDG_ScalableTextBox_C::HideAnim' has a wrong offset!");
static_assert(offsetof(UWDG_ScalableTextBox_C, Image_0) == 0x000270, "Member 'UWDG_ScalableTextBox_C::Image_0' has a wrong offset!");
static_assert(offsetof(UWDG_ScalableTextBox_C, imgTopBackground) == 0x000278, "Member 'UWDG_ScalableTextBox_C::imgTopBackground' has a wrong offset!");
static_assert(offsetof(UWDG_ScalableTextBox_C, NamedSlot_0) == 0x000280, "Member 'UWDG_ScalableTextBox_C::NamedSlot_0' has a wrong offset!");
static_assert(offsetof(UWDG_ScalableTextBox_C, txtInfo) == 0x000288, "Member 'UWDG_ScalableTextBox_C::txtInfo' has a wrong offset!");
static_assert(offsetof(UWDG_ScalableTextBox_C, txtText) == 0x000290, "Member 'UWDG_ScalableTextBox_C::txtText' has a wrong offset!");
static_assert(offsetof(UWDG_ScalableTextBox_C, txtTitle) == 0x000298, "Member 'UWDG_ScalableTextBox_C::txtTitle' has a wrong offset!");
static_assert(offsetof(UWDG_ScalableTextBox_C, vbox) == 0x0002A0, "Member 'UWDG_ScalableTextBox_C::vbox' has a wrong offset!");
static_assert(offsetof(UWDG_ScalableTextBox_C, TitleFont) == 0x0002A8, "Member 'UWDG_ScalableTextBox_C::TitleFont' has a wrong offset!");
static_assert(offsetof(UWDG_ScalableTextBox_C, txtToInsert) == 0x000300, "Member 'UWDG_ScalableTextBox_C::txtToInsert' has a wrong offset!");

}

