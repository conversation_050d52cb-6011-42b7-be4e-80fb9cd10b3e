﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_MPQuickjoinSlot

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_MPQuickjoinSlot.WDG_MPQuickjoinSlot_C
// 0x0210 (0x07F0 - 0x05E0)
class UWDG_MPQuickjoinSlot_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       AnimScale;                                         // 0x05E8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, <PERSON>S<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	class UWidgetAnimation*                       AnimOpacity;                                       // 0x05F0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UBorder*                                borderWaitingIndicator;                            // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 BottomHighlight;                                   // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 HoverImageBox;                                     // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgDivisor;                                        // 0x0610(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgEvent;                                          // 0x0618(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgEventColor;                                     // 0x0620(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgInfoColor;                                      // 0x0628(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 LeftHighlight;                                     // 0x0630(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 NormalImageBox;                                    // 0x0638(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 RightHighlight;                                    // 0x0640(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UThrobber*                              Throbber_385;                                      // 0x0648(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 TopHighlight;                                      // 0x0650(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtElement01;                                      // 0x0658(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtElement02;                                      // 0x0660(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtElement03;                                      // 0x0668(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtElement04;                                      // 0x0670(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtElement05;                                      // 0x0678(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtElement06;                                      // 0x0680(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtElement07;                                      // 0x0688(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtElement08;                                      // 0x0690(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtEventHeader;                                    // 0x0698(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtFreePracticeLength;                             // 0x06A0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtPing;                                           // 0x06A8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtQualifyLength;                                  // 0x06B0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtRaceLength;                                     // 0x06B8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtRequirementCompetition;                         // 0x06C0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtRequirementSafetyRating;                        // 0x06C8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtRequirementTrackMedals;                         // 0x06D0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtSearching;                                      // 0x06D8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtServerType;                                     // 0x06E0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtTrackName;                                      // 0x06E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	struct FSlateColor                            RedRatingColor;                                    // 0x06F0(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance)
	struct FSlateColor                            WhiteRatingColor;                                  // 0x0718(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance)
	struct FOnlineServicesMPServerInfo            cpServer;                                          // 0x0740(0x00B0)(Edit, BlueprintVisible, DisableEditOnInstance)

public:
	void ExecuteUbergraph_WDG_MPQuickjoinSlot(int32 EntryPoint);
	void BP_MouseLeave();
	void BP_MouseOver();
	void Tick(const struct FGeometry& MyGeometry, float InDeltaTime);
	void SetServer(const struct FOnlineServicesMPServerInfo& ServerInfo, const class FText& waitingIndicatorText);
	void GetMinutesRemainingText(const struct FDateTime& SessionEndUtc, class FText* SessionEndText);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_MPQuickjoinSlot_C">();
	}
	static class UWDG_MPQuickjoinSlot_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_MPQuickjoinSlot_C>();
	}
};
static_assert(alignof(UWDG_MPQuickjoinSlot_C) == 0x000008, "Wrong alignment on UWDG_MPQuickjoinSlot_C");
static_assert(sizeof(UWDG_MPQuickjoinSlot_C) == 0x0007F0, "Wrong size on UWDG_MPQuickjoinSlot_C");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_MPQuickjoinSlot_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, AnimScale) == 0x0005E8, "Member 'UWDG_MPQuickjoinSlot_C::AnimScale' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, AnimOpacity) == 0x0005F0, "Member 'UWDG_MPQuickjoinSlot_C::AnimOpacity' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, borderWaitingIndicator) == 0x0005F8, "Member 'UWDG_MPQuickjoinSlot_C::borderWaitingIndicator' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, BottomHighlight) == 0x000600, "Member 'UWDG_MPQuickjoinSlot_C::BottomHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, HoverImageBox) == 0x000608, "Member 'UWDG_MPQuickjoinSlot_C::HoverImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, imgDivisor) == 0x000610, "Member 'UWDG_MPQuickjoinSlot_C::imgDivisor' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, imgEvent) == 0x000618, "Member 'UWDG_MPQuickjoinSlot_C::imgEvent' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, imgEventColor) == 0x000620, "Member 'UWDG_MPQuickjoinSlot_C::imgEventColor' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, imgInfoColor) == 0x000628, "Member 'UWDG_MPQuickjoinSlot_C::imgInfoColor' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, LeftHighlight) == 0x000630, "Member 'UWDG_MPQuickjoinSlot_C::LeftHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, NormalImageBox) == 0x000638, "Member 'UWDG_MPQuickjoinSlot_C::NormalImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, RightHighlight) == 0x000640, "Member 'UWDG_MPQuickjoinSlot_C::RightHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, Throbber_385) == 0x000648, "Member 'UWDG_MPQuickjoinSlot_C::Throbber_385' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, TopHighlight) == 0x000650, "Member 'UWDG_MPQuickjoinSlot_C::TopHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, txtElement01) == 0x000658, "Member 'UWDG_MPQuickjoinSlot_C::txtElement01' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, txtElement02) == 0x000660, "Member 'UWDG_MPQuickjoinSlot_C::txtElement02' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, txtElement03) == 0x000668, "Member 'UWDG_MPQuickjoinSlot_C::txtElement03' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, txtElement04) == 0x000670, "Member 'UWDG_MPQuickjoinSlot_C::txtElement04' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, txtElement05) == 0x000678, "Member 'UWDG_MPQuickjoinSlot_C::txtElement05' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, txtElement06) == 0x000680, "Member 'UWDG_MPQuickjoinSlot_C::txtElement06' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, txtElement07) == 0x000688, "Member 'UWDG_MPQuickjoinSlot_C::txtElement07' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, txtElement08) == 0x000690, "Member 'UWDG_MPQuickjoinSlot_C::txtElement08' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, txtEventHeader) == 0x000698, "Member 'UWDG_MPQuickjoinSlot_C::txtEventHeader' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, txtFreePracticeLength) == 0x0006A0, "Member 'UWDG_MPQuickjoinSlot_C::txtFreePracticeLength' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, txtPing) == 0x0006A8, "Member 'UWDG_MPQuickjoinSlot_C::txtPing' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, txtQualifyLength) == 0x0006B0, "Member 'UWDG_MPQuickjoinSlot_C::txtQualifyLength' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, txtRaceLength) == 0x0006B8, "Member 'UWDG_MPQuickjoinSlot_C::txtRaceLength' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, txtRequirementCompetition) == 0x0006C0, "Member 'UWDG_MPQuickjoinSlot_C::txtRequirementCompetition' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, txtRequirementSafetyRating) == 0x0006C8, "Member 'UWDG_MPQuickjoinSlot_C::txtRequirementSafetyRating' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, txtRequirementTrackMedals) == 0x0006D0, "Member 'UWDG_MPQuickjoinSlot_C::txtRequirementTrackMedals' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, txtSearching) == 0x0006D8, "Member 'UWDG_MPQuickjoinSlot_C::txtSearching' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, txtServerType) == 0x0006E0, "Member 'UWDG_MPQuickjoinSlot_C::txtServerType' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, txtTrackName) == 0x0006E8, "Member 'UWDG_MPQuickjoinSlot_C::txtTrackName' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, RedRatingColor) == 0x0006F0, "Member 'UWDG_MPQuickjoinSlot_C::RedRatingColor' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, WhiteRatingColor) == 0x000718, "Member 'UWDG_MPQuickjoinSlot_C::WhiteRatingColor' has a wrong offset!");
static_assert(offsetof(UWDG_MPQuickjoinSlot_C, cpServer) == 0x000740, "Member 'UWDG_MPQuickjoinSlot_C::cpServer' has a wrong offset!");

}

