﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TrackMapFull

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_TrackMapFull.WDG_TrackMapFull_C
// 0x0000 (0x06F0 - 0x06F0)
class UWDG_TrackMapFull_C final : public UMapWidget
{
public:
	struct FEventReply OnMouseButtonDown(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent);
	bool IsWidgetDefinitionEnabled(class UAcGameInstance* GameInstance, const struct FHUDOptions& HUDOptions);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_TrackMapFull_C">();
	}
	static class UWDG_TrackMapFull_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_TrackMapFull_C>();
	}
};
static_assert(alignof(UWDG_TrackMapFull_C) == 0x000008, "Wrong alignment on UWDG_TrackMapFull_C");
static_assert(sizeof(UWDG_TrackMapFull_C) == 0x0006F0, "Wrong size on UWDG_TrackMapFull_C");

}

