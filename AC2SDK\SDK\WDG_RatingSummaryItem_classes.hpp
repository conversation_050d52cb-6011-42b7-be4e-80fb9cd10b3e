﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RatingSummaryItem

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RatingSummaryItem.WDG_RatingSummaryItem_C
// 0x0048 (0x02A8 - 0x0260)
class UWDG_RatingSummaryItem_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0260(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UProgressBar*                           Bar;                                               // 0x0268(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgLock;                                           // 0x0270(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtLabel;                                          // 0x0278(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtValue;                                          // 0x0280(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class FText                                   Label;                                             // 0x0288(0x0018)(Edit, BlueprintVisible, ExposeOnSpawn)
	int32                                         Value;                                             // 0x02A0(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_RatingSummaryItem(int32 EntryPoint);
	void PreConstruct(bool IsDesignTime);
	class FText SetValueText();
	float GetValueAsPercent();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RatingSummaryItem_C">();
	}
	static class UWDG_RatingSummaryItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RatingSummaryItem_C>();
	}
};
static_assert(alignof(UWDG_RatingSummaryItem_C) == 0x000008, "Wrong alignment on UWDG_RatingSummaryItem_C");
static_assert(sizeof(UWDG_RatingSummaryItem_C) == 0x0002A8, "Wrong size on UWDG_RatingSummaryItem_C");
static_assert(offsetof(UWDG_RatingSummaryItem_C, UberGraphFrame) == 0x000260, "Member 'UWDG_RatingSummaryItem_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_RatingSummaryItem_C, Bar) == 0x000268, "Member 'UWDG_RatingSummaryItem_C::Bar' has a wrong offset!");
static_assert(offsetof(UWDG_RatingSummaryItem_C, imgLock) == 0x000270, "Member 'UWDG_RatingSummaryItem_C::imgLock' has a wrong offset!");
static_assert(offsetof(UWDG_RatingSummaryItem_C, txtLabel) == 0x000278, "Member 'UWDG_RatingSummaryItem_C::txtLabel' has a wrong offset!");
static_assert(offsetof(UWDG_RatingSummaryItem_C, txtValue) == 0x000280, "Member 'UWDG_RatingSummaryItem_C::txtValue' has a wrong offset!");
static_assert(offsetof(UWDG_RatingSummaryItem_C, Label) == 0x000288, "Member 'UWDG_RatingSummaryItem_C::Label' has a wrong offset!");
static_assert(offsetof(UWDG_RatingSummaryItem_C, Value) == 0x0002A0, "Member 'UWDG_RatingSummaryItem_C::Value' has a wrong offset!");

}

