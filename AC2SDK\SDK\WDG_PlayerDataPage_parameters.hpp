﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PlayerDataPage

#include "Basic.hpp"

#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_PlayerDataPage.WDG_PlayerDataPage_C.ExecuteUbergraph_WDG_PlayerDataPage
// 0x01C8 (0x01C8 - 0x0000)
struct WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   K2Node_ComponentBoundEvent_Text;                   // 0x0008(0x0018)(ConstParm)
	ETextCommit                                   K2Node_ComponentBoundEvent_CommitMethod;           // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0021(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_22[0x6];                                       // 0x0022(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   K2Node_ComponentBoundEvent_Text_5;                 // 0x0028(0x0018)(ConstParm)
	class FText                                   K2Node_ComponentBoundEvent_Text_4;                 // 0x0040(0x0018)(ConstParm)
	class FString                                 CallFunc_Conv_TextToString_ReturnValue;            // 0x0058(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_TextToString_ReturnValue_1;          // 0x0068(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   K2Node_ComponentBoundEvent_Text_3;                 // 0x0078(0x0018)(ConstParm)
	class FString                                 CallFunc_Conv_TextToString_ReturnValue_2;          // 0x0090(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x00A0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_1;            // 0x00A4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_GetSubstring_ReturnValue;                 // 0x00A8(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_GetSubstring_ReturnValue_1;               // 0x00B8(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x00C8(0x0018)()
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_1;          // 0x00E0(0x0018)()
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_2;            // 0x00F8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_FC[0x4];                                       // 0x00FC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_GetText_ReturnValue;                      // 0x0100(0x0018)()
	class FString                                 CallFunc_GetSubstring_ReturnValue_2;               // 0x0118(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_TextToString_ReturnValue_3;          // 0x0128(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_2;          // 0x0138(0x0018)()
	int32                                         CallFunc_Len_ReturnValue;                          // 0x0150(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x0154(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_155[0x3];                                      // 0x0155(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_GetText_ReturnValue_1;                    // 0x0158(0x0018)()
	class FString                                 CallFunc_Conv_TextToString_ReturnValue_4;          // 0x0170(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	int32                                         CallFunc_Len_ReturnValue_1;                        // 0x0180(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_1;          // 0x0184(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_185[0x3];                                      // 0x0185(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   K2Node_ComponentBoundEvent_Text_2;                 // 0x0188(0x0018)(ConstParm)
	ETextCommit                                   K2Node_ComponentBoundEvent_CommitMethod_2;         // 0x01A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess_1;                    // 0x01A1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1A2[0x6];                                      // 0x01A2(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   K2Node_ComponentBoundEvent_Text_1;                 // 0x01A8(0x0018)(ConstParm)
	ETextCommit                                   K2Node_ComponentBoundEvent_CommitMethod_1;         // 0x01C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess_2;                    // 0x01C1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage) == 0x000008, "Wrong alignment on WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage");
static_assert(sizeof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage) == 0x0001C8, "Wrong size on WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, EntryPoint) == 0x000000, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, K2Node_ComponentBoundEvent_Text) == 0x000008, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::K2Node_ComponentBoundEvent_Text' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, K2Node_ComponentBoundEvent_CommitMethod) == 0x000020, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::K2Node_ComponentBoundEvent_CommitMethod' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, K2Node_SwitchEnum_CmpSuccess) == 0x000021, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, K2Node_ComponentBoundEvent_Text_5) == 0x000028, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::K2Node_ComponentBoundEvent_Text_5' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, K2Node_ComponentBoundEvent_Text_4) == 0x000040, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::K2Node_ComponentBoundEvent_Text_4' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, CallFunc_Conv_TextToString_ReturnValue) == 0x000058, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::CallFunc_Conv_TextToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, CallFunc_Conv_TextToString_ReturnValue_1) == 0x000068, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::CallFunc_Conv_TextToString_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, K2Node_ComponentBoundEvent_Text_3) == 0x000078, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::K2Node_ComponentBoundEvent_Text_3' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, CallFunc_Conv_TextToString_ReturnValue_2) == 0x000090, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::CallFunc_Conv_TextToString_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, CallFunc_Subtract_IntInt_ReturnValue) == 0x0000A0, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, CallFunc_Subtract_IntInt_ReturnValue_1) == 0x0000A4, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::CallFunc_Subtract_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, CallFunc_GetSubstring_ReturnValue) == 0x0000A8, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::CallFunc_GetSubstring_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, CallFunc_GetSubstring_ReturnValue_1) == 0x0000B8, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::CallFunc_GetSubstring_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, CallFunc_Conv_StringToText_ReturnValue) == 0x0000C8, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, CallFunc_Conv_StringToText_ReturnValue_1) == 0x0000E0, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::CallFunc_Conv_StringToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, CallFunc_Subtract_IntInt_ReturnValue_2) == 0x0000F8, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::CallFunc_Subtract_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, CallFunc_GetText_ReturnValue) == 0x000100, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::CallFunc_GetText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, CallFunc_GetSubstring_ReturnValue_2) == 0x000118, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::CallFunc_GetSubstring_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, CallFunc_Conv_TextToString_ReturnValue_3) == 0x000128, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::CallFunc_Conv_TextToString_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, CallFunc_Conv_StringToText_ReturnValue_2) == 0x000138, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::CallFunc_Conv_StringToText_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, CallFunc_Len_ReturnValue) == 0x000150, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::CallFunc_Len_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x000154, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, CallFunc_GetText_ReturnValue_1) == 0x000158, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::CallFunc_GetText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, CallFunc_Conv_TextToString_ReturnValue_4) == 0x000170, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::CallFunc_Conv_TextToString_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, CallFunc_Len_ReturnValue_1) == 0x000180, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::CallFunc_Len_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, CallFunc_EqualEqual_IntInt_ReturnValue_1) == 0x000184, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::CallFunc_EqualEqual_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, K2Node_ComponentBoundEvent_Text_2) == 0x000188, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::K2Node_ComponentBoundEvent_Text_2' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, K2Node_ComponentBoundEvent_CommitMethod_2) == 0x0001A0, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::K2Node_ComponentBoundEvent_CommitMethod_2' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, K2Node_SwitchEnum_CmpSuccess_1) == 0x0001A1, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::K2Node_SwitchEnum_CmpSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, K2Node_ComponentBoundEvent_Text_1) == 0x0001A8, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::K2Node_ComponentBoundEvent_Text_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, K2Node_ComponentBoundEvent_CommitMethod_1) == 0x0001C0, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::K2Node_ComponentBoundEvent_CommitMethod_1' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage, K2Node_SwitchEnum_CmpSuccess_2) == 0x0001C1, "Member 'WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage::K2Node_SwitchEnum_CmpSuccess_2' has a wrong offset!");

// Function WDG_PlayerDataPage.WDG_PlayerDataPage_C.BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_5_OnEditableTextCommittedEvent__DelegateSignature
// 0x0020 (0x0020 - 0x0000)
struct WDG_PlayerDataPage_C_BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_5_OnEditableTextCommittedEvent__DelegateSignature final
{
public:
	class FText                                   text;                                              // 0x0000(0x0018)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	ETextCommit                                   CommitMethod;                                      // 0x0018(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_PlayerDataPage_C_BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_5_OnEditableTextCommittedEvent__DelegateSignature) == 0x000008, "Wrong alignment on WDG_PlayerDataPage_C_BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_5_OnEditableTextCommittedEvent__DelegateSignature");
static_assert(sizeof(WDG_PlayerDataPage_C_BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_5_OnEditableTextCommittedEvent__DelegateSignature) == 0x000020, "Wrong size on WDG_PlayerDataPage_C_BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_5_OnEditableTextCommittedEvent__DelegateSignature");
static_assert(offsetof(WDG_PlayerDataPage_C_BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_5_OnEditableTextCommittedEvent__DelegateSignature, text) == 0x000000, "Member 'WDG_PlayerDataPage_C_BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_5_OnEditableTextCommittedEvent__DelegateSignature::text' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_5_OnEditableTextCommittedEvent__DelegateSignature, CommitMethod) == 0x000018, "Member 'WDG_PlayerDataPage_C_BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_5_OnEditableTextCommittedEvent__DelegateSignature::CommitMethod' has a wrong offset!");

// Function WDG_PlayerDataPage.WDG_PlayerDataPage_C.BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_4_OnEditableTextCommittedEvent__DelegateSignature
// 0x0020 (0x0020 - 0x0000)
struct WDG_PlayerDataPage_C_BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_4_OnEditableTextCommittedEvent__DelegateSignature final
{
public:
	class FText                                   text;                                              // 0x0000(0x0018)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	ETextCommit                                   CommitMethod;                                      // 0x0018(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_PlayerDataPage_C_BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_4_OnEditableTextCommittedEvent__DelegateSignature) == 0x000008, "Wrong alignment on WDG_PlayerDataPage_C_BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_4_OnEditableTextCommittedEvent__DelegateSignature");
static_assert(sizeof(WDG_PlayerDataPage_C_BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_4_OnEditableTextCommittedEvent__DelegateSignature) == 0x000020, "Wrong size on WDG_PlayerDataPage_C_BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_4_OnEditableTextCommittedEvent__DelegateSignature");
static_assert(offsetof(WDG_PlayerDataPage_C_BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_4_OnEditableTextCommittedEvent__DelegateSignature, text) == 0x000000, "Member 'WDG_PlayerDataPage_C_BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_4_OnEditableTextCommittedEvent__DelegateSignature::text' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_4_OnEditableTextCommittedEvent__DelegateSignature, CommitMethod) == 0x000018, "Member 'WDG_PlayerDataPage_C_BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_4_OnEditableTextCommittedEvent__DelegateSignature::CommitMethod' has a wrong offset!");

// Function WDG_PlayerDataPage.WDG_PlayerDataPage_C.BndEvt__txtNameValue_K2Node_ComponentBoundEvent_3_OnEditableTextCommittedEvent__DelegateSignature
// 0x0020 (0x0020 - 0x0000)
struct WDG_PlayerDataPage_C_BndEvt__txtNameValue_K2Node_ComponentBoundEvent_3_OnEditableTextCommittedEvent__DelegateSignature final
{
public:
	class FText                                   text;                                              // 0x0000(0x0018)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	ETextCommit                                   CommitMethod;                                      // 0x0018(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_PlayerDataPage_C_BndEvt__txtNameValue_K2Node_ComponentBoundEvent_3_OnEditableTextCommittedEvent__DelegateSignature) == 0x000008, "Wrong alignment on WDG_PlayerDataPage_C_BndEvt__txtNameValue_K2Node_ComponentBoundEvent_3_OnEditableTextCommittedEvent__DelegateSignature");
static_assert(sizeof(WDG_PlayerDataPage_C_BndEvt__txtNameValue_K2Node_ComponentBoundEvent_3_OnEditableTextCommittedEvent__DelegateSignature) == 0x000020, "Wrong size on WDG_PlayerDataPage_C_BndEvt__txtNameValue_K2Node_ComponentBoundEvent_3_OnEditableTextCommittedEvent__DelegateSignature");
static_assert(offsetof(WDG_PlayerDataPage_C_BndEvt__txtNameValue_K2Node_ComponentBoundEvent_3_OnEditableTextCommittedEvent__DelegateSignature, text) == 0x000000, "Member 'WDG_PlayerDataPage_C_BndEvt__txtNameValue_K2Node_ComponentBoundEvent_3_OnEditableTextCommittedEvent__DelegateSignature::text' has a wrong offset!");
static_assert(offsetof(WDG_PlayerDataPage_C_BndEvt__txtNameValue_K2Node_ComponentBoundEvent_3_OnEditableTextCommittedEvent__DelegateSignature, CommitMethod) == 0x000018, "Member 'WDG_PlayerDataPage_C_BndEvt__txtNameValue_K2Node_ComponentBoundEvent_3_OnEditableTextCommittedEvent__DelegateSignature::CommitMethod' has a wrong offset!");

// Function WDG_PlayerDataPage.WDG_PlayerDataPage_C.BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_2_OnEditableTextChangedEvent__DelegateSignature
// 0x0018 (0x0018 - 0x0000)
struct WDG_PlayerDataPage_C_BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_2_OnEditableTextChangedEvent__DelegateSignature final
{
public:
	class FText                                   text;                                              // 0x0000(0x0018)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_PlayerDataPage_C_BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_2_OnEditableTextChangedEvent__DelegateSignature) == 0x000008, "Wrong alignment on WDG_PlayerDataPage_C_BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_2_OnEditableTextChangedEvent__DelegateSignature");
static_assert(sizeof(WDG_PlayerDataPage_C_BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_2_OnEditableTextChangedEvent__DelegateSignature) == 0x000018, "Wrong size on WDG_PlayerDataPage_C_BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_2_OnEditableTextChangedEvent__DelegateSignature");
static_assert(offsetof(WDG_PlayerDataPage_C_BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_2_OnEditableTextChangedEvent__DelegateSignature, text) == 0x000000, "Member 'WDG_PlayerDataPage_C_BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_2_OnEditableTextChangedEvent__DelegateSignature::text' has a wrong offset!");

// Function WDG_PlayerDataPage.WDG_PlayerDataPage_C.BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_1_OnEditableTextChangedEvent__DelegateSignature
// 0x0018 (0x0018 - 0x0000)
struct WDG_PlayerDataPage_C_BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_1_OnEditableTextChangedEvent__DelegateSignature final
{
public:
	class FText                                   text;                                              // 0x0000(0x0018)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_PlayerDataPage_C_BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_1_OnEditableTextChangedEvent__DelegateSignature) == 0x000008, "Wrong alignment on WDG_PlayerDataPage_C_BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_1_OnEditableTextChangedEvent__DelegateSignature");
static_assert(sizeof(WDG_PlayerDataPage_C_BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_1_OnEditableTextChangedEvent__DelegateSignature) == 0x000018, "Wrong size on WDG_PlayerDataPage_C_BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_1_OnEditableTextChangedEvent__DelegateSignature");
static_assert(offsetof(WDG_PlayerDataPage_C_BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_1_OnEditableTextChangedEvent__DelegateSignature, text) == 0x000000, "Member 'WDG_PlayerDataPage_C_BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_1_OnEditableTextChangedEvent__DelegateSignature::text' has a wrong offset!");

// Function WDG_PlayerDataPage.WDG_PlayerDataPage_C.BndEvt__txtNameValue_K2Node_ComponentBoundEvent_0_OnEditableTextChangedEvent__DelegateSignature
// 0x0018 (0x0018 - 0x0000)
struct WDG_PlayerDataPage_C_BndEvt__txtNameValue_K2Node_ComponentBoundEvent_0_OnEditableTextChangedEvent__DelegateSignature final
{
public:
	class FText                                   text;                                              // 0x0000(0x0018)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_PlayerDataPage_C_BndEvt__txtNameValue_K2Node_ComponentBoundEvent_0_OnEditableTextChangedEvent__DelegateSignature) == 0x000008, "Wrong alignment on WDG_PlayerDataPage_C_BndEvt__txtNameValue_K2Node_ComponentBoundEvent_0_OnEditableTextChangedEvent__DelegateSignature");
static_assert(sizeof(WDG_PlayerDataPage_C_BndEvt__txtNameValue_K2Node_ComponentBoundEvent_0_OnEditableTextChangedEvent__DelegateSignature) == 0x000018, "Wrong size on WDG_PlayerDataPage_C_BndEvt__txtNameValue_K2Node_ComponentBoundEvent_0_OnEditableTextChangedEvent__DelegateSignature");
static_assert(offsetof(WDG_PlayerDataPage_C_BndEvt__txtNameValue_K2Node_ComponentBoundEvent_0_OnEditableTextChangedEvent__DelegateSignature, text) == 0x000000, "Member 'WDG_PlayerDataPage_C_BndEvt__txtNameValue_K2Node_ComponentBoundEvent_0_OnEditableTextChangedEvent__DelegateSignature::text' has a wrong offset!");

}

