﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_WeatherForecast

#include "Basic.hpp"

#include "WDG_WeatherForecast_classes.hpp"
#include "WDG_WeatherForecast_parameters.hpp"


namespace SDK
{

// Function WDG_WeatherForecast.WDG_WeatherForecast_C.ExecuteUbergraph_WDG_WeatherForecast
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_WeatherForecast_C::ExecuteUbergraph_WDG_WeatherForecast(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherForecast_C", "ExecuteUbergraph_WDG_WeatherForecast");

	Params::WDG_WeatherForecast_C_ExecuteUbergraph_WDG_WeatherForecast Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_WeatherForecast.WDG_WeatherForecast_C.OnHudTick
// (Event, Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FRaceHUDState&             State                                                  (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_WeatherForecast_C::OnHudTick(const struct FRaceHUDState& State)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherForecast_C", "OnHudTick");

	Params::WDG_WeatherForecast_C_OnHudTick Parms{};

	Parms.State = std::move(State);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_WeatherForecast.WDG_WeatherForecast_C.OnStartWidget
// (Event, Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UAcGameInstance*                  GameInstance                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class AAcRaceGameMode*                  raceGameMode_0                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class ACarAvatar*                       CarAvatar                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FHUDOptions&               HUDOptions                                             (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)

void UWDG_WeatherForecast_C::OnStartWidget(class UAcGameInstance* GameInstance, class AAcRaceGameMode* raceGameMode_0, class ACarAvatar* CarAvatar, const struct FHUDOptions& HUDOptions)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherForecast_C", "OnStartWidget");

	Params::WDG_WeatherForecast_C_OnStartWidget Parms{};

	Parms.GameInstance = GameInstance;
	Parms.raceGameMode_0 = raceGameMode_0;
	Parms.CarAvatar = CarAvatar;
	Parms.HUDOptions = std::move(HUDOptions);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_WeatherForecast.WDG_WeatherForecast_C.Destruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_WeatherForecast_C::Destruct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherForecast_C", "Destruct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_WeatherForecast.WDG_WeatherForecast_C.OnEverySecond
// (BlueprintCallable, BlueprintEvent)

void UWDG_WeatherForecast_C::OnEverySecond()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherForecast_C", "OnEverySecond");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_WeatherForecast.WDG_WeatherForecast_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_WeatherForecast_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherForecast_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_WeatherForecast.WDG_WeatherForecast_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_WeatherForecast_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherForecast_C", "PreConstruct");

	Params::WDG_WeatherForecast_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_WeatherForecast.WDG_WeatherForecast_C.IsWidgetDefinitionEnabled
// (Event, Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UAcGameInstance*                  GameInstance                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FHUDOptions&               HUDOptions                                             (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor)

bool UWDG_WeatherForecast_C::IsWidgetDefinitionEnabled(class UAcGameInstance* GameInstance, const struct FHUDOptions& HUDOptions)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherForecast_C", "IsWidgetDefinitionEnabled");

	Params::WDG_WeatherForecast_C_IsWidgetDefinitionEnabled Parms{};

	Parms.GameInstance = GameInstance;
	Parms.HUDOptions = std::move(HUDOptions);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_WeatherForecast.WDG_WeatherForecast_C.UpdateDifferenceIndicators
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UTextBlock*                       First_Icon                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UTextBlock*                       Second_Icon                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// float                                   First_Value                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// float                                   Second_Value                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UTextBlock*                       Diff_Indicator                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_WeatherForecast_C::UpdateDifferenceIndicators(class UTextBlock* First_Icon, class UTextBlock* Second_Icon, float First_Value, float Second_Value, class UTextBlock* Diff_Indicator)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_WeatherForecast_C", "UpdateDifferenceIndicators");

	Params::WDG_WeatherForecast_C_UpdateDifferenceIndicators Parms{};

	Parms.First_Icon = First_Icon;
	Parms.Second_Icon = Second_Icon;
	Parms.First_Value = First_Value;
	Parms.Second_Value = Second_Value;
	Parms.Diff_Indicator = Diff_Indicator;

	UObject::ProcessEvent(Func, &Parms);
}

}

