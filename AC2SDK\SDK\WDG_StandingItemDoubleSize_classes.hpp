﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_StandingItemDoubleSize

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_StandingItemDoubleSize.WDG_StandingItemDoubleSize_C
// 0x0018 (0x03C0 - 0x03A8)
class UWDG_StandingItemDoubleSize_C final : public URaceStandingDetailedItems
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x03A8(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       Open;                                              // 0x03B0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       DriverCar;                                         // 0x03B8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_StandingItemDoubleSize(int32 EntryPoint);
	void Construct();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_StandingItemDoubleSize_C">();
	}
	static class UWDG_StandingItemDoubleSize_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_StandingItemDoubleSize_C>();
	}
};
static_assert(alignof(UWDG_StandingItemDoubleSize_C) == 0x000008, "Wrong alignment on UWDG_StandingItemDoubleSize_C");
static_assert(sizeof(UWDG_StandingItemDoubleSize_C) == 0x0003C0, "Wrong size on UWDG_StandingItemDoubleSize_C");
static_assert(offsetof(UWDG_StandingItemDoubleSize_C, UberGraphFrame) == 0x0003A8, "Member 'UWDG_StandingItemDoubleSize_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_StandingItemDoubleSize_C, Open) == 0x0003B0, "Member 'UWDG_StandingItemDoubleSize_C::Open' has a wrong offset!");
static_assert(offsetof(UWDG_StandingItemDoubleSize_C, DriverCar) == 0x0003B8, "Member 'UWDG_StandingItemDoubleSize_C::DriverCar' has a wrong offset!");

}

