﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_OptionsListWidget

#include "Basic.hpp"

#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_OptionsListWidget.WDG_OptionsListWidget_C
// 0x0010 (0x0270 - 0x0260)
class UWDG_OptionsListWidget_C final : public UUserWidget
{
public:
	class UNamedSlot*                             OptionsSlot;                                       // 0x0260(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             SelectorSlot;                                      // 0x0268(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_OptionsListWidget_C">();
	}
	static class UWDG_OptionsListWidget_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_OptionsListWidget_C>();
	}
};
static_assert(alignof(UWDG_OptionsListWidget_C) == 0x000008, "Wrong alignment on UWDG_OptionsListWidget_C");
static_assert(sizeof(UWDG_OptionsListWidget_C) == 0x000270, "Wrong size on UWDG_OptionsListWidget_C");
static_assert(offsetof(UWDG_OptionsListWidget_C, OptionsSlot) == 0x000260, "Member 'UWDG_OptionsListWidget_C::OptionsSlot' has a wrong offset!");
static_assert(offsetof(UWDG_OptionsListWidget_C, SelectorSlot) == 0x000268, "Member 'UWDG_OptionsListWidget_C::SelectorSlot' has a wrong offset!");

}

