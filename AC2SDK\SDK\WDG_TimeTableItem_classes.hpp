﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TimeTableItem

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_TimeTableItem.WDG_TimeTableItem_C
// 0x0040 (0x0730 - 0x06F0)
class UWDG_TimeTableItem_C final : public UTimeTableItem
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x06F0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       DriverCar;                                         // 0x06F8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UCanvasPanel*                           canvasClassIndicator;                              // 0x0700(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class USizeBox*                               Handicap;                                          // 0x0708(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgClassIndicator;                                 // 0x0710(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_PenaltyIndicator_C*                Penalty;                                           // 0x0718(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtBallast;                                        // 0x0720(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtRestrictor;                                     // 0x0728(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_TimeTableItem(int32 EntryPoint);
	void OnTimeTableItemUpdate(const struct FTimeTableEntry& Entry);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_TimeTableItem_C">();
	}
	static class UWDG_TimeTableItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_TimeTableItem_C>();
	}
};
static_assert(alignof(UWDG_TimeTableItem_C) == 0x000008, "Wrong alignment on UWDG_TimeTableItem_C");
static_assert(sizeof(UWDG_TimeTableItem_C) == 0x000730, "Wrong size on UWDG_TimeTableItem_C");
static_assert(offsetof(UWDG_TimeTableItem_C, UberGraphFrame) == 0x0006F0, "Member 'UWDG_TimeTableItem_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_TimeTableItem_C, DriverCar) == 0x0006F8, "Member 'UWDG_TimeTableItem_C::DriverCar' has a wrong offset!");
static_assert(offsetof(UWDG_TimeTableItem_C, canvasClassIndicator) == 0x000700, "Member 'UWDG_TimeTableItem_C::canvasClassIndicator' has a wrong offset!");
static_assert(offsetof(UWDG_TimeTableItem_C, Handicap) == 0x000708, "Member 'UWDG_TimeTableItem_C::Handicap' has a wrong offset!");
static_assert(offsetof(UWDG_TimeTableItem_C, imgClassIndicator) == 0x000710, "Member 'UWDG_TimeTableItem_C::imgClassIndicator' has a wrong offset!");
static_assert(offsetof(UWDG_TimeTableItem_C, Penalty) == 0x000718, "Member 'UWDG_TimeTableItem_C::Penalty' has a wrong offset!");
static_assert(offsetof(UWDG_TimeTableItem_C, txtBallast) == 0x000720, "Member 'UWDG_TimeTableItem_C::txtBallast' has a wrong offset!");
static_assert(offsetof(UWDG_TimeTableItem_C, txtRestrictor) == 0x000728, "Member 'UWDG_TimeTableItem_C::txtRestrictor' has a wrong offset!");

}

