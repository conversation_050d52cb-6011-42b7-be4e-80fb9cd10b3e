﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SpecialEventGenericPanel

#include "Basic.hpp"

#include "WDG_SpecialEventGenericPanel_classes.hpp"
#include "WDG_SpecialEventGenericPanel_parameters.hpp"


namespace SDK
{

// Function WDG_SpecialEventGenericPanel.WDG_SpecialEventGenericPanel_C.ExecuteUbergraph_WDG_SpecialEventGenericPanel
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SpecialEventGenericPanel_C::ExecuteUbergraph_WDG_SpecialEventGenericPanel(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventGenericPanel_C", "ExecuteUbergraph_WDG_SpecialEventGenericPanel");

	Params::WDG_SpecialEventGenericPanel_C_ExecuteUbergraph_WDG_SpecialEventGenericPanel Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEventGenericPanel.WDG_SpecialEventGenericPanel_C.BP_MouseLeave
// (Event, Public, BlueprintEvent)

void UWDG_SpecialEventGenericPanel_C::BP_MouseLeave()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventGenericPanel_C", "BP_MouseLeave");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEventGenericPanel.WDG_SpecialEventGenericPanel_C.BP_MouseOver
// (Event, Public, BlueprintEvent)

void UWDG_SpecialEventGenericPanel_C::BP_MouseOver()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventGenericPanel_C", "BP_MouseOver");

	UObject::ProcessEvent(Func, nullptr);
}

}

