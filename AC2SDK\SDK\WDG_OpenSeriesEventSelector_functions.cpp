﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_OpenSeriesEventSelector

#include "Basic.hpp"

#include "WDG_OpenSeriesEventSelector_classes.hpp"
#include "WDG_OpenSeriesEventSelector_parameters.hpp"


namespace SDK
{

// Function WDG_OpenSeriesEventSelector.WDG_OpenSeriesEventSelector_C.ExecuteUbergraph_WDG_OpenSeriesEventSelector
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_OpenSeriesEventSelector_C::ExecuteUbergraph_WDG_OpenSeriesEventSelector(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_OpenSeriesEventSelector_C", "ExecuteUbergraph_WDG_OpenSeriesEventSelector");

	Params::WDG_OpenSeriesEventSelector_C_ExecuteUbergraph_WDG_OpenSeriesEventSelector Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_OpenSeriesEventSelector.WDG_OpenSeriesEventSelector_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_OpenSeriesEventSelector_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_OpenSeriesEventSelector_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_OpenSeriesEventSelector.WDG_OpenSeriesEventSelector_C.AddCircuit
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UWDG_GenericBarItem_C*            Item                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_OpenSeriesEventSelector_C::AddCircuit(class UWDG_GenericBarItem_C* Item)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_OpenSeriesEventSelector_C", "AddCircuit");

	Params::WDG_OpenSeriesEventSelector_C_AddCircuit Parms{};

	Parms.Item = Item;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_OpenSeriesEventSelector.WDG_OpenSeriesEventSelector_C.DelCircuit
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UWDG_SeriesEventItem_C*           Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_OpenSeriesEventSelector_C::DelCircuit(class UWDG_SeriesEventItem_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_OpenSeriesEventSelector_C", "DelCircuit");

	Params::WDG_OpenSeriesEventSelector_C_DelCircuit Parms{};

	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_OpenSeriesEventSelector.WDG_OpenSeriesEventSelector_C.ReOrderCircuit
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UWDG_SeriesEventItem_C*           Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// EUINavigation                           Direction                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_OpenSeriesEventSelector_C::ReOrderCircuit(class UWDG_SeriesEventItem_C* Sender, EUINavigation Direction)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_OpenSeriesEventSelector_C", "ReOrderCircuit");

	Params::WDG_OpenSeriesEventSelector_C_ReOrderCircuit Parms{};

	Parms.Sender = Sender;
	Parms.Direction = Direction;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_OpenSeriesEventSelector.WDG_OpenSeriesEventSelector_C.PopulateSelectedCircuits
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void UWDG_OpenSeriesEventSelector_C::PopulateSelectedCircuits()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_OpenSeriesEventSelector_C", "PopulateSelectedCircuits");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_OpenSeriesEventSelector.WDG_OpenSeriesEventSelector_C.DoCustomNavigation_0
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// EUINavigation                           Navigation_0                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWidget*                          ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

class UWidget* UWDG_OpenSeriesEventSelector_C::DoCustomNavigation_0(EUINavigation Navigation_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_OpenSeriesEventSelector_C", "DoCustomNavigation_0");

	Params::WDG_OpenSeriesEventSelector_C_DoCustomNavigation_0 Parms{};

	Parms.Navigation_0 = Navigation_0;

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}

}

