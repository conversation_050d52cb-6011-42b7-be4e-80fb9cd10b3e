﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PlayerInfoPanel

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C
// 0x0148 (0x03A8 - 0x0260)
class UWDG_PlayerInfoPanel_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0260(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       PageFade;                                          // 0x0268(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnUpdate;                                         // 0x0270(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgFirstNameBgr;                                   // 0x0278(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgLastNameBgr;                                    // 0x0280(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgNickNameBgr;                                    // 0x0288(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_TextInput_C*                       inputAbbreviation;                                 // 0x0290(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_TextInput_C*                       inputFirstName;                                    // 0x0298(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_TextInput_C*                       inputLastName;                                     // 0x02A0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UEditableText*                          txtLastnameValue;                                  // 0x02A8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UEditableText*                          txtNameValue;                                      // 0x02B0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UEditableText*                          txtNicknameValue;                                  // 0x02B8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	struct FGamePlatformUserAccountData           UserAccountData;                                   // 0x02C0(0x00C0)(Edit, BlueprintVisible, DisableEditOnInstance)
	TMulticastInlineDelegate<void(const struct FGamePlatformUserAccountData& UserAccountData)> Driver_Info_Updated; // 0x0380(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void()>              Drive_Info_Panel_Enter_Pressed;                    // 0x0390(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	class FName                                   DriverProfileFilename;                             // 0x03A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_PlayerInfoPanel(int32 EntryPoint);
	void UpdateButtonPressed();
	void BndEvt__btnUpdate_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature();
	void CustomEvent_2(const class FText& text, ETextCommit CommitMethod);
	void CustomEvent_1(const class FText& text, ETextCommit CommitMethod);
	void CustomEvent_0(const class FText& text, ETextCommit CommitMethod);
	void BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_2_AcTextInputEvent__DelegateSignature(const class FText& text);
	void BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_1_AcTextInputEvent__DelegateSignature(const class FText& text);
	void BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_0_AcTextInputEvent__DelegateSignature(const class FText& text);
	void BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_10_AcTextInputEvent__DelegateSignature(const class FText& text);
	void BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_9_AcTextInputEvent__DelegateSignature(const class FText& text);
	void BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_8_AcTextInputEvent__DelegateSignature(const class FText& text);
	void Construct();
	void Tick(const struct FGeometry& MyGeometry, float InDeltaTime);
	class FString GenerateAbbreviation();
	void ValidateAbbreviation(const class FText& InText, bool GenerateAbbreviation, class FString* Value_As_String);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_PlayerInfoPanel_C">();
	}
	static class UWDG_PlayerInfoPanel_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_PlayerInfoPanel_C>();
	}
};
static_assert(alignof(UWDG_PlayerInfoPanel_C) == 0x000008, "Wrong alignment on UWDG_PlayerInfoPanel_C");
static_assert(sizeof(UWDG_PlayerInfoPanel_C) == 0x0003A8, "Wrong size on UWDG_PlayerInfoPanel_C");
static_assert(offsetof(UWDG_PlayerInfoPanel_C, UberGraphFrame) == 0x000260, "Member 'UWDG_PlayerInfoPanel_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerInfoPanel_C, PageFade) == 0x000268, "Member 'UWDG_PlayerInfoPanel_C::PageFade' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerInfoPanel_C, btnUpdate) == 0x000270, "Member 'UWDG_PlayerInfoPanel_C::btnUpdate' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerInfoPanel_C, imgFirstNameBgr) == 0x000278, "Member 'UWDG_PlayerInfoPanel_C::imgFirstNameBgr' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerInfoPanel_C, imgLastNameBgr) == 0x000280, "Member 'UWDG_PlayerInfoPanel_C::imgLastNameBgr' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerInfoPanel_C, imgNickNameBgr) == 0x000288, "Member 'UWDG_PlayerInfoPanel_C::imgNickNameBgr' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerInfoPanel_C, inputAbbreviation) == 0x000290, "Member 'UWDG_PlayerInfoPanel_C::inputAbbreviation' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerInfoPanel_C, inputFirstName) == 0x000298, "Member 'UWDG_PlayerInfoPanel_C::inputFirstName' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerInfoPanel_C, inputLastName) == 0x0002A0, "Member 'UWDG_PlayerInfoPanel_C::inputLastName' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerInfoPanel_C, txtLastnameValue) == 0x0002A8, "Member 'UWDG_PlayerInfoPanel_C::txtLastnameValue' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerInfoPanel_C, txtNameValue) == 0x0002B0, "Member 'UWDG_PlayerInfoPanel_C::txtNameValue' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerInfoPanel_C, txtNicknameValue) == 0x0002B8, "Member 'UWDG_PlayerInfoPanel_C::txtNicknameValue' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerInfoPanel_C, UserAccountData) == 0x0002C0, "Member 'UWDG_PlayerInfoPanel_C::UserAccountData' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerInfoPanel_C, Driver_Info_Updated) == 0x000380, "Member 'UWDG_PlayerInfoPanel_C::Driver_Info_Updated' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerInfoPanel_C, Drive_Info_Panel_Enter_Pressed) == 0x000390, "Member 'UWDG_PlayerInfoPanel_C::Drive_Info_Panel_Enter_Pressed' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerInfoPanel_C, DriverProfileFilename) == 0x0003A0, "Member 'UWDG_PlayerInfoPanel_C::DriverProfileFilename' has a wrong offset!");

}

