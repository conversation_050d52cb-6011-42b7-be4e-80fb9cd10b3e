﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_YesNoPopup

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "CoreUObject_structs.hpp"


namespace SDK::Params
{

// Function WDG_YesNoPopup.WDG_YesNoPopup_C.ExecuteUbergraph_WDG_YesNoPopup
// 0x0028 (0x0028 - 0x0000)
struct WDG_YesNoPopup_C_ExecuteUbergraph_WDG_YesNoPopup final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FFocusEvent                            K2Node_Event_InFocusEvent;                         // 0x0004(0x0008)(NoDestructor)
	bool                                          CallFunc_SetFocusOnCurrentPanel_ReturnValue;       // 0x000C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_D[0x3];                                        // 0x000D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue;              // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance;             // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_IsHMDEnabled_ReturnValue;                 // 0x0021(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_YesNoPopup_C_ExecuteUbergraph_WDG_YesNoPopup) == 0x000008, "Wrong alignment on WDG_YesNoPopup_C_ExecuteUbergraph_WDG_YesNoPopup");
static_assert(sizeof(WDG_YesNoPopup_C_ExecuteUbergraph_WDG_YesNoPopup) == 0x000028, "Wrong size on WDG_YesNoPopup_C_ExecuteUbergraph_WDG_YesNoPopup");
static_assert(offsetof(WDG_YesNoPopup_C_ExecuteUbergraph_WDG_YesNoPopup, EntryPoint) == 0x000000, "Member 'WDG_YesNoPopup_C_ExecuteUbergraph_WDG_YesNoPopup::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_YesNoPopup_C_ExecuteUbergraph_WDG_YesNoPopup, K2Node_Event_InFocusEvent) == 0x000004, "Member 'WDG_YesNoPopup_C_ExecuteUbergraph_WDG_YesNoPopup::K2Node_Event_InFocusEvent' has a wrong offset!");
static_assert(offsetof(WDG_YesNoPopup_C_ExecuteUbergraph_WDG_YesNoPopup, CallFunc_SetFocusOnCurrentPanel_ReturnValue) == 0x00000C, "Member 'WDG_YesNoPopup_C_ExecuteUbergraph_WDG_YesNoPopup::CallFunc_SetFocusOnCurrentPanel_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_YesNoPopup_C_ExecuteUbergraph_WDG_YesNoPopup, CallFunc_GetGameInstance_ReturnValue) == 0x000010, "Member 'WDG_YesNoPopup_C_ExecuteUbergraph_WDG_YesNoPopup::CallFunc_GetGameInstance_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_YesNoPopup_C_ExecuteUbergraph_WDG_YesNoPopup, K2Node_DynamicCast_AsAc_Game_Instance) == 0x000018, "Member 'WDG_YesNoPopup_C_ExecuteUbergraph_WDG_YesNoPopup::K2Node_DynamicCast_AsAc_Game_Instance' has a wrong offset!");
static_assert(offsetof(WDG_YesNoPopup_C_ExecuteUbergraph_WDG_YesNoPopup, K2Node_DynamicCast_bSuccess) == 0x000020, "Member 'WDG_YesNoPopup_C_ExecuteUbergraph_WDG_YesNoPopup::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_YesNoPopup_C_ExecuteUbergraph_WDG_YesNoPopup, CallFunc_IsHMDEnabled_ReturnValue) == 0x000021, "Member 'WDG_YesNoPopup_C_ExecuteUbergraph_WDG_YesNoPopup::CallFunc_IsHMDEnabled_ReturnValue' has a wrong offset!");

// Function WDG_YesNoPopup.WDG_YesNoPopup_C.OnAddedToFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_YesNoPopup_C_OnAddedToFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_YesNoPopup_C_OnAddedToFocusPath) == 0x000004, "Wrong alignment on WDG_YesNoPopup_C_OnAddedToFocusPath");
static_assert(sizeof(WDG_YesNoPopup_C_OnAddedToFocusPath) == 0x000008, "Wrong size on WDG_YesNoPopup_C_OnAddedToFocusPath");
static_assert(offsetof(WDG_YesNoPopup_C_OnAddedToFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_YesNoPopup_C_OnAddedToFocusPath::InFocusEvent' has a wrong offset!");

}

