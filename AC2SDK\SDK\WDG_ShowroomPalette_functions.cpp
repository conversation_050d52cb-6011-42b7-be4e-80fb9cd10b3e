﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomPalette

#include "Basic.hpp"

#include "WDG_ShowroomPalette_classes.hpp"
#include "WDG_ShowroomPalette_parameters.hpp"


namespace SDK
{

// Function WDG_ShowroomPalette.WDG_ShowroomPalette_C.ExecuteUbergraph_WDG_ShowroomPalette
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPalette_C::ExecuteUbergraph_WDG_ShowroomPalette(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPalette_C", "ExecuteUbergraph_WDG_ShowroomPalette");

	Params::WDG_ShowroomPalette_C_ExecuteUbergraph_WDG_ShowroomPalette Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPalette.WDG_ShowroomPalette_C.BndEvt__btnExit_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UWDG_GenericBarItem_C*            Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPalette_C::BndEvt__btnExit_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature(class UWDG_GenericBarItem_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPalette_C", "BndEvt__btnExit_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature");

	Params::WDG_ShowroomPalette_C_BndEvt__btnExit_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature Parms{};

	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPalette.WDG_ShowroomPalette_C.BP_OnChildrenBackward
// (Event, Public, BlueprintEvent)
// Parameters:
// class UAcPanelBase*                     Child                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPalette_C::BP_OnChildrenBackward(class UAcPanelBase* Child)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPalette_C", "BP_OnChildrenBackward");

	Params::WDG_ShowroomPalette_C_BP_OnChildrenBackward Parms{};

	Parms.Child = Child;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPalette.WDG_ShowroomPalette_C.Populate
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    Special                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
// TArray<struct FSkinColor>&              colors                                                 (BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_ShowroomPalette_C::Populate(bool Special, TArray<struct FSkinColor>& colors)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPalette_C", "Populate");

	Params::WDG_ShowroomPalette_C_Populate Parms{};

	Parms.Special = Special;
	Parms.colors = std::move(colors);

	UObject::ProcessEvent(Func, &Parms);

	colors = std::move(Parms.colors);
}


// Function WDG_ShowroomPalette.WDG_ShowroomPalette_C.CreateColorTile
// (Private, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FSkinColor&                SkinColor                                              (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// class UWDG_ShowroomTileItemHorizontal_C*ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

class UWDG_ShowroomTileItemHorizontal_C* UWDG_ShowroomPalette_C::CreateColorTile(const struct FSkinColor& SkinColor)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPalette_C", "CreateColorTile");

	Params::WDG_ShowroomPalette_C_CreateColorTile Parms{};

	Parms.SkinColor = std::move(SkinColor);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ShowroomPalette.WDG_ShowroomPalette_C.ColorSelected
// (Private, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UWDG_ShowroomTileItemHorizontal_C*Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   Key                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FLinearColor&              Color                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPalette_C::ColorSelected(class UWDG_ShowroomTileItemHorizontal_C* Sender, int32 Key, const struct FLinearColor& Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPalette_C", "ColorSelected");

	Params::WDG_ShowroomPalette_C_ColorSelected Parms{};

	Parms.Sender = Sender;
	Parms.Key = Key;
	Parms.Color = std::move(Color);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPalette.WDG_ShowroomPalette_C.Open
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    for_aux_lights                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
// class UWDG_ShowroomMaterialColorSelector_C*Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWDG_ShowroomTileItemHorizontal_C**FocusedTile                                            (Parm, OutParm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPalette_C::Open(bool for_aux_lights, class UWDG_ShowroomMaterialColorSelector_C* Sender, class UWDG_ShowroomTileItemHorizontal_C** FocusedTile)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPalette_C", "Open");

	Params::WDG_ShowroomPalette_C_Open Parms{};

	Parms.for_aux_lights = for_aux_lights;
	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);

	if (FocusedTile != nullptr)
		*FocusedTile = Parms.FocusedTile;
}


// Function WDG_ShowroomPalette.WDG_ShowroomPalette_C.Close
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    Cancel                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ShowroomPalette_C::Close(bool Cancel)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPalette_C", "Close");

	Params::WDG_ShowroomPalette_C_Close Parms{};

	Parms.Cancel = Cancel;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPalette.WDG_ShowroomPalette_C.ColorFocused
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UWDG_ShowroomTileBase_C*          Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPalette_C::ColorFocused(class UWDG_ShowroomTileBase_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPalette_C", "ColorFocused");

	Params::WDG_ShowroomPalette_C_ColorFocused Parms{};

	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPalette.WDG_ShowroomPalette_C.OnPreviewKeyDown
// (Event, Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FKeyEvent&                 InKeyEvent                                             (BlueprintVisible, BlueprintReadOnly, Parm)
// struct FEventReply                      ReturnValue                                            (Parm, OutParm, ReturnParm)

struct FEventReply UWDG_ShowroomPalette_C::OnPreviewKeyDown(const struct FGeometry& MyGeometry, const struct FKeyEvent& InKeyEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPalette_C", "OnPreviewKeyDown");

	Params::WDG_ShowroomPalette_C_OnPreviewKeyDown Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InKeyEvent = std::move(InKeyEvent);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ShowroomPalette.WDG_ShowroomPalette_C.getAllColors
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomPalette_C::getAllColors()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPalette_C", "getAllColors");

	UObject::ProcessEvent(Func, nullptr);
}

}

