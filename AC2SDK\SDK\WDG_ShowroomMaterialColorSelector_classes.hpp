﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomMaterialColorSelector

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "SlateCore_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ShowroomMaterialColorSelector.WDG_ShowroomMaterialColorSelector_C
// 0x0130 (0x0390 - 0x0260)
class UWDG_ShowroomMaterialColorSelector_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0260(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWDG_HorizontalSlider_C*                sliderMaterial;                                    // 0x0268(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      TileColor;                                         // 0x0270(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class FText                                   Label;                                             // 0x0278(0x0018)(Edit, BlueprintVisible, ExposeOnSpawn)
	struct FLinearColor                           SelectedColor;                                     // 0x0290(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	TMulticastInlineDelegate<void(class UWDG_ShowroomMaterialColorSelector_C* Sender)> OnSelectColor; // 0x02A0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void(int32 MaterialKey)> OnMaterialChanged;                             // 0x02B0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	int32                                         ColorCode;                                         // 0x02C0(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         MaterialKey;                                       // 0x02C4(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TMap<int32, class FText>                      Materials;                                         // 0x02C8(0x0050)(Edit, BlueprintVisible)
	bool                                          HasMaterialSelector;                               // 0x0318(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          HasColorSelector;                                  // 0x0319(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_31A[0x6];                                      // 0x031A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateFontInfo                         FontStyle;                                         // 0x0320(0x0058)(Edit, BlueprintVisible, HasGetValueTypeHash)
	bool                                          requireHighlightForInputHandling;                  // 0x0378(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_379[0x7];                                      // 0x0379(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TMulticastInlineDelegate<void(class UUserWidget* Sender, class UAcPanelBase* SubSender)> OnFocus; // 0x0380(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)

public:
	void ExecuteUbergraph_WDG_ShowroomMaterialColorSelector(int32 EntryPoint);
	void BndEvt__sliderMaterial_K2Node_ComponentBoundEvent_3_OnAcPanelFocusEvent__DelegateSignature(class UAcPanelBase* panel);
	void BndEvt__tileColor_K2Node_ComponentBoundEvent_0_OnAcPanelFocusEvent__DelegateSignature(class UAcPanelBase* panel);
	void BndEvt__Color_K2Node_ComponentBoundEvent_2_OnColorItemForward__DelegateSignature(class UWDG_ShowroomTileItemHorizontal_C* Sender, int32 ColorCode_0, const struct FLinearColor& Color);
	void BndEvt__Material_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature(class UGenericSelectorItem* Source, int32 current_index, int32 current_value);
	void PreConstruct(bool IsDesignTime);
	void SetColor(const struct FLinearColor& InColorAndOpacity);
	void SetColorByCode(int32 ColorCode_0);
	void SetMaterials(const TMap<int32, class FText>& Source_Materials);
	void UpdateMaterialSlider();
	int32 GetSelectedMaterial();
	int32 GetSelectedColor();
	void SetMaterial(int32 MaterialKey_0, bool Supress_Event);
	void SetMaterialAndColor(class FName MaterialKey_0, int32 ColorCode_0);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ShowroomMaterialColorSelector_C">();
	}
	static class UWDG_ShowroomMaterialColorSelector_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ShowroomMaterialColorSelector_C>();
	}
};
static_assert(alignof(UWDG_ShowroomMaterialColorSelector_C) == 0x000008, "Wrong alignment on UWDG_ShowroomMaterialColorSelector_C");
static_assert(sizeof(UWDG_ShowroomMaterialColorSelector_C) == 0x000390, "Wrong size on UWDG_ShowroomMaterialColorSelector_C");
static_assert(offsetof(UWDG_ShowroomMaterialColorSelector_C, UberGraphFrame) == 0x000260, "Member 'UWDG_ShowroomMaterialColorSelector_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomMaterialColorSelector_C, sliderMaterial) == 0x000268, "Member 'UWDG_ShowroomMaterialColorSelector_C::sliderMaterial' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomMaterialColorSelector_C, TileColor) == 0x000270, "Member 'UWDG_ShowroomMaterialColorSelector_C::TileColor' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomMaterialColorSelector_C, Label) == 0x000278, "Member 'UWDG_ShowroomMaterialColorSelector_C::Label' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomMaterialColorSelector_C, SelectedColor) == 0x000290, "Member 'UWDG_ShowroomMaterialColorSelector_C::SelectedColor' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomMaterialColorSelector_C, OnSelectColor) == 0x0002A0, "Member 'UWDG_ShowroomMaterialColorSelector_C::OnSelectColor' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomMaterialColorSelector_C, OnMaterialChanged) == 0x0002B0, "Member 'UWDG_ShowroomMaterialColorSelector_C::OnMaterialChanged' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomMaterialColorSelector_C, ColorCode) == 0x0002C0, "Member 'UWDG_ShowroomMaterialColorSelector_C::ColorCode' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomMaterialColorSelector_C, MaterialKey) == 0x0002C4, "Member 'UWDG_ShowroomMaterialColorSelector_C::MaterialKey' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomMaterialColorSelector_C, Materials) == 0x0002C8, "Member 'UWDG_ShowroomMaterialColorSelector_C::Materials' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomMaterialColorSelector_C, HasMaterialSelector) == 0x000318, "Member 'UWDG_ShowroomMaterialColorSelector_C::HasMaterialSelector' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomMaterialColorSelector_C, HasColorSelector) == 0x000319, "Member 'UWDG_ShowroomMaterialColorSelector_C::HasColorSelector' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomMaterialColorSelector_C, FontStyle) == 0x000320, "Member 'UWDG_ShowroomMaterialColorSelector_C::FontStyle' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomMaterialColorSelector_C, requireHighlightForInputHandling) == 0x000378, "Member 'UWDG_ShowroomMaterialColorSelector_C::requireHighlightForInputHandling' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomMaterialColorSelector_C, OnFocus) == 0x000380, "Member 'UWDG_ShowroomMaterialColorSelector_C::OnFocus' has a wrong offset!");

}

