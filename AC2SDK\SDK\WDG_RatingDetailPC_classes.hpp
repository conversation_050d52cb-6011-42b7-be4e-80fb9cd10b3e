﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RatingDetailPC

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RatingDetailPC.WDG_RatingDetailPC_C
// 0x0028 (0x0618 - 0x05F0)
class UWDG_RatingDetailPC_C final : public URatingPCDetail
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05F0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UImage*                                 imgBackground;                                     // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_RatingDetailPCLeaderboardSeasonItem_C* WDG_RatingDetailPCLeaderboardSeasonItem;       // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_RatingDetailPCLeaderboardSeasonItem_C* WDG_RatingDetailPCLeaderboardSeasonItem_0;     // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_RatingDetailPCLeaderboardSeasonItem_C* LastSeasonItem;                                // 0x0610(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_RatingDetailPC(int32 EntryPoint);
	void ClearLeaderboardList();
	void CreateLeaderboardEntry(const struct FOnlineServicesLeaderboardRank& Rank);
	void CreateNewSeason(int32 Season);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RatingDetailPC_C">();
	}
	static class UWDG_RatingDetailPC_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RatingDetailPC_C>();
	}
};
static_assert(alignof(UWDG_RatingDetailPC_C) == 0x000008, "Wrong alignment on UWDG_RatingDetailPC_C");
static_assert(sizeof(UWDG_RatingDetailPC_C) == 0x000618, "Wrong size on UWDG_RatingDetailPC_C");
static_assert(offsetof(UWDG_RatingDetailPC_C, UberGraphFrame) == 0x0005F0, "Member 'UWDG_RatingDetailPC_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailPC_C, imgBackground) == 0x0005F8, "Member 'UWDG_RatingDetailPC_C::imgBackground' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailPC_C, WDG_RatingDetailPCLeaderboardSeasonItem) == 0x000600, "Member 'UWDG_RatingDetailPC_C::WDG_RatingDetailPCLeaderboardSeasonItem' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailPC_C, WDG_RatingDetailPCLeaderboardSeasonItem_0) == 0x000608, "Member 'UWDG_RatingDetailPC_C::WDG_RatingDetailPCLeaderboardSeasonItem_0' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailPC_C, LastSeasonItem) == 0x000610, "Member 'UWDG_RatingDetailPC_C::LastSeasonItem' has a wrong offset!");

}

