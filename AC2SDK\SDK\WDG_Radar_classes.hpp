﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_Radar

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_Radar.WDG_Radar_C
// 0x0018 (0x06D0 - 0x06B8)
class UWDG_Radar_C final : public URadarWidget
{
public:
	class UWidgetAnimation*                       animVisibility;                                    // 0x06B8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 imgBullseye;                                       // 0x06C0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class URetainerBox*                           RetainerBox_0;                                     // 0x06C8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_Radar_C">();
	}
	static class UWDG_Radar_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_Radar_C>();
	}
};
static_assert(alignof(UWDG_Radar_C) == 0x000008, "Wrong alignment on UWDG_Radar_C");
static_assert(sizeof(UWDG_Radar_C) == 0x0006D0, "Wrong size on UWDG_Radar_C");
static_assert(offsetof(UWDG_Radar_C, animVisibility) == 0x0006B8, "Member 'UWDG_Radar_C::animVisibility' has a wrong offset!");
static_assert(offsetof(UWDG_Radar_C, imgBullseye) == 0x0006C0, "Member 'UWDG_Radar_C::imgBullseye' has a wrong offset!");
static_assert(offsetof(UWDG_Radar_C, RetainerBox_0) == 0x0006C8, "Member 'UWDG_Radar_C::RetainerBox_0' has a wrong offset!");

}

