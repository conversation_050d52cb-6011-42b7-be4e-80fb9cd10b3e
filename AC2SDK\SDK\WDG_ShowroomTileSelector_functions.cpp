﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomTileSelector

#include "Basic.hpp"

#include "WDG_ShowroomTileSelector_classes.hpp"
#include "WDG_ShowroomTileSelector_parameters.hpp"


namespace SDK
{

// Function WDG_ShowroomTileSelector.WDG_ShowroomTileSelector_C.ExecuteUbergraph_WDG_ShowroomTileSelector
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomTileSelector_C::ExecuteUbergraph_WDG_ShowroomTileSelector(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileSelector_C", "ExecuteUbergraph_WDG_ShowroomTileSelector");

	Params::WDG_ShowroomTileSelector_C_ExecuteUbergraph_WDG_ShowroomTileSelector Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileSelector.WDG_ShowroomTileSelector_C.SelectTeam
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FTeamInfo&                 Team                                                   (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_ShowroomTileSelector_C::SelectTeam(const struct FTeamInfo& Team)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileSelector_C", "SelectTeam");

	Params::WDG_ShowroomTileSelector_C_SelectTeam Parms{};

	Parms.Team = std::move(Team);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileSelector.WDG_ShowroomTileSelector_C.SelectModel
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FModelInfo&                Model                                                  (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_ShowroomTileSelector_C::SelectModel(const struct FModelInfo& Model)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileSelector_C", "SelectModel");

	Params::WDG_ShowroomTileSelector_C_SelectModel Parms{};

	Parms.Model = std::move(Model);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileSelector.WDG_ShowroomTileSelector_C.OnTeamItemSelected
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UWDG_ShowroomTileBase_C*          Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class FName                             Key                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   KeyInt                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomTileSelector_C::OnTeamItemSelected(class UWDG_ShowroomTileBase_C* Sender, class FName Key, int32 KeyInt)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileSelector_C", "OnTeamItemSelected");

	Params::WDG_ShowroomTileSelector_C_OnTeamItemSelected Parms{};

	Parms.Sender = Sender;
	Parms.Key = Key;
	Parms.KeyInt = KeyInt;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileSelector.WDG_ShowroomTileSelector_C.OnTeamTileFocused
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UWDG_ShowroomTileBase_C*          Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomTileSelector_C::OnTeamTileFocused(class UWDG_ShowroomTileBase_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileSelector_C", "OnTeamTileFocused");

	Params::WDG_ShowroomTileSelector_C_OnTeamTileFocused Parms{};

	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileSelector.WDG_ShowroomTileSelector_C.UpdateTeams
// (HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const TArray<struct FTeamInfo>&         Teams                                                  (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_ShowroomTileSelector_C::UpdateTeams(const TArray<struct FTeamInfo>& Teams)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileSelector_C", "UpdateTeams");

	Params::WDG_ShowroomTileSelector_C_UpdateTeams Parms{};

	Parms.Teams = std::move(Teams);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileSelector.WDG_ShowroomTileSelector_C.UpdateModels
// (HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const TArray<struct FModelInfo>&        Models_0                                               (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_ShowroomTileSelector_C::UpdateModels(const TArray<struct FModelInfo>& Models_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileSelector_C", "UpdateModels");

	Params::WDG_ShowroomTileSelector_C_UpdateModels Parms{};

	Parms.Models_0 = std::move(Models_0);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileSelector.WDG_ShowroomTileSelector_C.OnTileItemFocused
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UWDG_ShowroomTileBase_C*          Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomTileSelector_C::OnTileItemFocused(class UWDG_ShowroomTileBase_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileSelector_C", "OnTileItemFocused");

	Params::WDG_ShowroomTileSelector_C_OnTileItemFocused Parms{};

	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileSelector.WDG_ShowroomTileSelector_C.OnItemSelected
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UWDG_ShowroomTileBase_C*          Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class FName                             Key                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   KeyInt                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomTileSelector_C::OnItemSelected(class UWDG_ShowroomTileBase_C* Sender, class FName Key, int32 KeyInt)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileSelector_C", "OnItemSelected");

	Params::WDG_ShowroomTileSelector_C_OnItemSelected Parms{};

	Parms.Sender = Sender;
	Parms.Key = Key;
	Parms.KeyInt = KeyInt;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileSelector.WDG_ShowroomTileSelector_C.OnPreviewKeyDown
// (Event, Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FKeyEvent&                 InKeyEvent                                             (BlueprintVisible, BlueprintReadOnly, Parm)
// struct FEventReply                      ReturnValue                                            (Parm, OutParm, ReturnParm)

struct FEventReply UWDG_ShowroomTileSelector_C::OnPreviewKeyDown(const struct FGeometry& MyGeometry, const struct FKeyEvent& InKeyEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileSelector_C", "OnPreviewKeyDown");

	Params::WDG_ShowroomTileSelector_C_OnPreviewKeyDown Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InKeyEvent = std::move(InKeyEvent);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}

}

