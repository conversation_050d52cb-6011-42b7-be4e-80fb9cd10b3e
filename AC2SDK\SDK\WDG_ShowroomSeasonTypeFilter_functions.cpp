﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomSeasonTypeFilter

#include "Basic.hpp"

#include "WDG_ShowroomSeasonTypeFilter_classes.hpp"
#include "WDG_ShowroomSeasonTypeFilter_parameters.hpp"


namespace SDK
{

// Function WDG_ShowroomSeasonTypeFilter.WDG_ShowroomSeasonTypeFilter_C.ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomSeasonTypeFilter_C::ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomSeasonTypeFilter_C", "ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter");

	Params::WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomSeasonTypeFilter.WDG_ShowroomSeasonTypeFilter_C.OnAfterConstruct
// (Event, Public, BlueprintEvent)

void UWDG_ShowroomSeasonTypeFilter_C::OnAfterConstruct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomSeasonTypeFilter_C", "OnAfterConstruct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomSeasonTypeFilter.WDG_ShowroomSeasonTypeFilter_C.OnRemovedFromFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_ShowroomSeasonTypeFilter_C::OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomSeasonTypeFilter_C", "OnRemovedFromFocusPath");

	Params::WDG_ShowroomSeasonTypeFilter_C_OnRemovedFromFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomSeasonTypeFilter.WDG_ShowroomSeasonTypeFilter_C.OnAddedToFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_ShowroomSeasonTypeFilter_C::OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomSeasonTypeFilter_C", "OnAddedToFocusPath");

	Params::WDG_ShowroomSeasonTypeFilter_C_OnAddedToFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomSeasonTypeFilter.WDG_ShowroomSeasonTypeFilter_C.OnForward_Event_0
// (BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomSeasonTypeFilter_C::OnForward_Event_0()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomSeasonTypeFilter_C", "OnForward_Event_0");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomSeasonTypeFilter.WDG_ShowroomSeasonTypeFilter_C.Hide
// (BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomSeasonTypeFilter_C::Hide()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomSeasonTypeFilter_C", "Hide");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomSeasonTypeFilter.WDG_ShowroomSeasonTypeFilter_C.show
// (BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomSeasonTypeFilter_C::show()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomSeasonTypeFilter_C", "show");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomSeasonTypeFilter.WDG_ShowroomSeasonTypeFilter_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ShowroomSeasonTypeFilter_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomSeasonTypeFilter_C", "PreConstruct");

	Params::WDG_ShowroomSeasonTypeFilter_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomSeasonTypeFilter.WDG_ShowroomSeasonTypeFilter_C.UpdateTextLabel
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomSeasonTypeFilter_C::UpdateTextLabel()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomSeasonTypeFilter_C", "UpdateTextLabel");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomSeasonTypeFilter.WDG_ShowroomSeasonTypeFilter_C.SetActiveFilter
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// ESeasonType                             activeFilter_0                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomSeasonTypeFilter_C::SetActiveFilter(ESeasonType activeFilter_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomSeasonTypeFilter_C", "SetActiveFilter");

	Params::WDG_ShowroomSeasonTypeFilter_C_SetActiveFilter Parms{};

	Parms.activeFilter_0 = activeFilter_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomSeasonTypeFilter.WDG_ShowroomSeasonTypeFilter_C.ItemSelected
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UWDG_GenericBarItem_C*            Item                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomSeasonTypeFilter_C::ItemSelected(class UWDG_GenericBarItem_C* Item)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomSeasonTypeFilter_C", "ItemSelected");

	Params::WDG_ShowroomSeasonTypeFilter_C_ItemSelected Parms{};

	Parms.Item = Item;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomSeasonTypeFilter.WDG_ShowroomSeasonTypeFilter_C.CreateItem
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// ESeasonType                             Season                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWDG_GenericBarItem_C**           Item                                                   (Parm, OutParm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomSeasonTypeFilter_C::CreateItem(ESeasonType Season, class UWDG_GenericBarItem_C** Item)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomSeasonTypeFilter_C", "CreateItem");

	Params::WDG_ShowroomSeasonTypeFilter_C_CreateItem Parms{};

	Parms.Season = Season;

	UObject::ProcessEvent(Func, &Parms);

	if (Item != nullptr)
		*Item = Parms.Item;
}


// Function WDG_ShowroomSeasonTypeFilter.WDG_ShowroomSeasonTypeFilter_C.Populate
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// TArray<ESeasonType>&                    Source                                                 (BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_ShowroomSeasonTypeFilter_C::Populate(TArray<ESeasonType>& Source)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomSeasonTypeFilter_C", "Populate");

	Params::WDG_ShowroomSeasonTypeFilter_C_Populate Parms{};

	Parms.Source = std::move(Source);

	UObject::ProcessEvent(Func, &Parms);

	Source = std::move(Parms.Source);
}


// Function WDG_ShowroomSeasonTypeFilter.WDG_ShowroomSeasonTypeFilter_C.SetSizeHolderIfLonger
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const class FText&                      text                                                   (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_ShowroomSeasonTypeFilter_C::SetSizeHolderIfLonger(const class FText& text)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomSeasonTypeFilter_C", "SetSizeHolderIfLonger");

	Params::WDG_ShowroomSeasonTypeFilter_C_SetSizeHolderIfLonger Parms{};

	Parms.text = std::move(text);

	UObject::ProcessEvent(Func, &Parms);
}

}

