﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomFilter

#include "Basic.hpp"

#include "WDG_ShowroomFilter_classes.hpp"
#include "WDG_ShowroomFilter_parameters.hpp"


namespace SDK
{

// Function WDG_ShowroomFilter.WDG_ShowroomFilter_C.ExecuteUbergraph_WDG_ShowroomFilter
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomFilter_C::ExecuteUbergraph_WDG_ShowroomFilter(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomFilter_C", "ExecuteUbergraph_WDG_ShowroomFilter");

	Params::WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomFilter.WDG_ShowroomFilter_C.BndEvt__btnOfficial_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_ShowroomFilter_C::BndEvt__btnOfficial_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomFilter_C", "BndEvt__btnOfficial_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomFilter.WDG_ShowroomFilter_C.OnRemovedFromFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_ShowroomFilter_C::OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomFilter_C", "OnRemovedFromFocusPath");

	Params::WDG_ShowroomFilter_C_OnRemovedFromFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomFilter.WDG_ShowroomFilter_C.OnAddedToFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_ShowroomFilter_C::OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomFilter_C", "OnAddedToFocusPath");

	Params::WDG_ShowroomFilter_C_OnAddedToFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomFilter.WDG_ShowroomFilter_C.OnForward_Event_0
// (BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomFilter_C::OnForward_Event_0()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomFilter_C", "OnForward_Event_0");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomFilter.WDG_ShowroomFilter_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_ShowroomFilter_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomFilter_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomFilter.WDG_ShowroomFilter_C.BndEvt__btnAll_K2Node_ComponentBoundEvent_3_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_ShowroomFilter_C::BndEvt__btnAll_K2Node_ComponentBoundEvent_3_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomFilter_C", "BndEvt__btnAll_K2Node_ComponentBoundEvent_3_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomFilter.WDG_ShowroomFilter_C.BndEvt__btnSprint_K2Node_ComponentBoundEvent_2_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_ShowroomFilter_C::BndEvt__btnSprint_K2Node_ComponentBoundEvent_2_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomFilter_C", "BndEvt__btnSprint_K2Node_ComponentBoundEvent_2_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomFilter.WDG_ShowroomFilter_C.BndEvt__btnEndurance_K2Node_ComponentBoundEvent_1_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_ShowroomFilter_C::BndEvt__btnEndurance_K2Node_ComponentBoundEvent_1_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomFilter_C", "BndEvt__btnEndurance_K2Node_ComponentBoundEvent_1_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomFilter.WDG_ShowroomFilter_C.BndEvt__btnCustom_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_ShowroomFilter_C::BndEvt__btnCustom_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomFilter_C", "BndEvt__btnCustom_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomFilter.WDG_ShowroomFilter_C.Hide
// (BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomFilter_C::Hide()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomFilter_C", "Hide");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomFilter.WDG_ShowroomFilter_C.show
// (BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomFilter_C::show()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomFilter_C", "show");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomFilter.WDG_ShowroomFilter_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ShowroomFilter_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomFilter_C", "PreConstruct");

	Params::WDG_ShowroomFilter_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomFilter.WDG_ShowroomFilter_C.SetFilters
// (BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomFilter_C::SetFilters()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomFilter_C", "SetFilters");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomFilter.WDG_ShowroomFilter_C.UpdateTextLabel
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomFilter_C::UpdateTextLabel()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomFilter_C", "UpdateTextLabel");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomFilter.WDG_ShowroomFilter_C.SetActiveFilter
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// EShowroomCarFilterType                  activeFilter_0                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomFilter_C::SetActiveFilter(EShowroomCarFilterType activeFilter_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomFilter_C", "SetActiveFilter");

	Params::WDG_ShowroomFilter_C_SetActiveFilter Parms{};

	Parms.activeFilter_0 = activeFilter_0;

	UObject::ProcessEvent(Func, &Parms);
}

}

