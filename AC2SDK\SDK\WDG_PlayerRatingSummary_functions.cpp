﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PlayerRatingSummary

#include "Basic.hpp"

#include "WDG_PlayerRatingSummary_classes.hpp"
#include "WDG_PlayerRatingSummary_parameters.hpp"


namespace SDK
{

// Function WDG_PlayerRatingSummary.WDG_PlayerRatingSummary_C.ExecuteUbergraph_WDG_PlayerRatingSummary
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_PlayerRatingSummary_C::ExecuteUbergraph_WDG_PlayerRatingSummary(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerRatingSummary_C", "ExecuteUbergraph_WDG_PlayerRatingSummary");

	Params::WDG_PlayerRatingSummary_C_ExecuteUbergraph_WDG_PlayerRatingSummary Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PlayerRatingSummary.WDG_PlayerRatingSummary_C.DriverSummaryReceived
// (HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FOnlineServicesDriverSummary&summary                                                (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_PlayerRatingSummary_C::DriverSummaryReceived(const struct FOnlineServicesDriverSummary& summary)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerRatingSummary_C", "DriverSummaryReceived");

	Params::WDG_PlayerRatingSummary_C_DriverSummaryReceived Parms{};

	Parms.summary = std::move(summary);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PlayerRatingSummary.WDG_PlayerRatingSummary_C.OnAfterConstruct
// (Event, Public, BlueprintEvent)

void UWDG_PlayerRatingSummary_C::OnAfterConstruct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerRatingSummary_C", "OnAfterConstruct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_PlayerRatingSummary.WDG_PlayerRatingSummary_C.ToPercentWithoutSymbol
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// float                                   Value                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class FText*                            Return                                                 (Parm, OutParm)

void UWDG_PlayerRatingSummary_C::ToPercentWithoutSymbol(float Value, class FText* Return)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerRatingSummary_C", "ToPercentWithoutSymbol");

	Params::WDG_PlayerRatingSummary_C_ToPercentWithoutSymbol Parms{};

	Parms.Value = Value;

	UObject::ProcessEvent(Func, &Parms);

	if (Return != nullptr)
		*Return = std::move(Parms.Return);
}

}

