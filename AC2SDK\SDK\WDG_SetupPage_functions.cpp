﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SetupPage

#include "Basic.hpp"

#include "WDG_SetupPage_classes.hpp"
#include "WDG_SetupPage_parameters.hpp"


namespace SDK
{

// Function WDG_SetupPage.WDG_SetupPage_C.ExecuteUbergraph_WDG_SetupPage
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SetupPage_C::ExecuteUbergraph_WDG_SetupPage(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SetupPage_C", "ExecuteUbergraph_WDG_SetupPage");

	Params::WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SetupPage.WDG_SetupPage_C.BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class FName                             Filename                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const class FString&                    DisplayName                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, HasGetValueTypeHash)

void UWDG_SetupPage_C::BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature(class FName Filename, const class FString& DisplayName)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SetupPage_C", "BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature");

	Params::WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature Parms{};

	Parms.Filename = Filename;
	Parms.DisplayName = std::move(DisplayName);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SetupPage.WDG_SetupPage_C.BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnDeleteUserPreset__DelegateSignature
// (BlueprintEvent)
// Parameters:
// const class FString&                    Filename                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, HasGetValueTypeHash)

void UWDG_SetupPage_C::BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnDeleteUserPreset__DelegateSignature(const class FString& Filename)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SetupPage_C", "BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnDeleteUserPreset__DelegateSignature");

	Params::WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnDeleteUserPreset__DelegateSignature Parms{};

	Parms.Filename = std::move(Filename);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SetupPage.WDG_SetupPage_C.BndEvt__filePanel_K2Node_ComponentBoundEvent_3_OnSaveUserPreset__DelegateSignature
// (BlueprintEvent)
// Parameters:
// const class FString&                    Filename                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, HasGetValueTypeHash)
// bool                                    ExistingFile                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SetupPage_C::BndEvt__filePanel_K2Node_ComponentBoundEvent_3_OnSaveUserPreset__DelegateSignature(const class FString& Filename, bool ExistingFile)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SetupPage_C", "BndEvt__filePanel_K2Node_ComponentBoundEvent_3_OnSaveUserPreset__DelegateSignature");

	Params::WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_3_OnSaveUserPreset__DelegateSignature Parms{};

	Parms.Filename = std::move(Filename);
	Parms.ExistingFile = ExistingFile;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SetupPage.WDG_SetupPage_C.BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_1_OnHide__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UAcPanelBase*                     CallingPanel                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    Cancelled                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SetupPage_C::BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_1_OnHide__DelegateSignature(class UAcPanelBase* CallingPanel, bool Cancelled)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SetupPage_C", "BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_1_OnHide__DelegateSignature");

	Params::WDG_SetupPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_1_OnHide__DelegateSignature Parms{};

	Parms.CallingPanel = CallingPanel;
	Parms.Cancelled = Cancelled;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SetupPage.WDG_SetupPage_C.BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_0_OnShow__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UAcPanelBase*                     CallingPanel                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SetupPage_C::BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_0_OnShow__DelegateSignature(class UAcPanelBase* CallingPanel)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SetupPage_C", "BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_0_OnShow__DelegateSignature");

	Params::WDG_SetupPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_0_OnShow__DelegateSignature Parms{};

	Parms.CallingPanel = CallingPanel;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SetupPage.WDG_SetupPage_C.OnOpenFileDialog
// (Event, Protected, BlueprintCallable, BlueprintEvent)

void UWDG_SetupPage_C::OnOpenFileDialog()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SetupPage_C", "OnOpenFileDialog");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SetupPage.WDG_SetupPage_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_SetupPage_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SetupPage_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}

}

