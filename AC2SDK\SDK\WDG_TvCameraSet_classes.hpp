﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TvCameraSet

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_TvCameraSet.WDG_TvCameraSet_C
// 0x0020 (0x0678 - 0x0658)
class UWDG_TvCameraSet_C final : public UAcRaceWidgetBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0658(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UTextBlock*                             txtCameraSetName;                                  // 0x0660(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class AACPlayerCameraManager*                 CameraManager;                                     // 0x0668(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         LastTVSet;                                         // 0x0670(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EMainCameraMode                               CurrentCameraMode;                                 // 0x0674(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_TvCameraSet(int32 EntryPoint);
	void OnHudTick(const struct FRaceHUDState& State);
	void OnStartWidget(class UAcGameInstance* GameInstance, class AAcRaceGameMode* raceGameMode, class ACarAvatar* CarAvatar, const struct FHUDOptions& HUDOptions);
	bool IsWidgetDefinitionEnabled(class UAcGameInstance* GameInstance, const struct FHUDOptions& HUDOptions);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_TvCameraSet_C">();
	}
	static class UWDG_TvCameraSet_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_TvCameraSet_C>();
	}
};
static_assert(alignof(UWDG_TvCameraSet_C) == 0x000008, "Wrong alignment on UWDG_TvCameraSet_C");
static_assert(sizeof(UWDG_TvCameraSet_C) == 0x000678, "Wrong size on UWDG_TvCameraSet_C");
static_assert(offsetof(UWDG_TvCameraSet_C, UberGraphFrame) == 0x000658, "Member 'UWDG_TvCameraSet_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_TvCameraSet_C, txtCameraSetName) == 0x000660, "Member 'UWDG_TvCameraSet_C::txtCameraSetName' has a wrong offset!");
static_assert(offsetof(UWDG_TvCameraSet_C, CameraManager) == 0x000668, "Member 'UWDG_TvCameraSet_C::CameraManager' has a wrong offset!");
static_assert(offsetof(UWDG_TvCameraSet_C, LastTVSet) == 0x000670, "Member 'UWDG_TvCameraSet_C::LastTVSet' has a wrong offset!");
static_assert(offsetof(UWDG_TvCameraSet_C, CurrentCameraMode) == 0x000674, "Member 'UWDG_TvCameraSet_C::CurrentCameraMode' has a wrong offset!");

}

