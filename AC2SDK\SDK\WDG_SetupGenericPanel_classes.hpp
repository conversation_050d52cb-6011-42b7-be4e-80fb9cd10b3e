﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SetupGenericPanel

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SetupGenericPanel.WDG_SetupGenericPanel_C
// 0x0058 (0x0638 - 0x05E0)
class UWDG_SetupGenericPanel_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       OpacityAnim;                                       // 0x05E8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       ScaleAnim;                                         // 0x05F0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 HoverImageBox;                                     // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 NormalImageBox;                                    // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtFirstRow;                                       // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtSecondRow;                                      // 0x0610(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtTitle;                                          // 0x0618(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class FText                                   MainTitle;                                         // 0x0620(0x0018)(Edit, BlueprintVisible, ExposeOnSpawn)

public:
	void ExecuteUbergraph_WDG_SetupGenericPanel(int32 EntryPoint);
	void BP_MouseLeave();
	void BP_MouseOver();
	void Construct();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SetupGenericPanel_C">();
	}
	static class UWDG_SetupGenericPanel_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SetupGenericPanel_C>();
	}
};
static_assert(alignof(UWDG_SetupGenericPanel_C) == 0x000008, "Wrong alignment on UWDG_SetupGenericPanel_C");
static_assert(sizeof(UWDG_SetupGenericPanel_C) == 0x000638, "Wrong size on UWDG_SetupGenericPanel_C");
static_assert(offsetof(UWDG_SetupGenericPanel_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_SetupGenericPanel_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SetupGenericPanel_C, OpacityAnim) == 0x0005E8, "Member 'UWDG_SetupGenericPanel_C::OpacityAnim' has a wrong offset!");
static_assert(offsetof(UWDG_SetupGenericPanel_C, ScaleAnim) == 0x0005F0, "Member 'UWDG_SetupGenericPanel_C::ScaleAnim' has a wrong offset!");
static_assert(offsetof(UWDG_SetupGenericPanel_C, HoverImageBox) == 0x0005F8, "Member 'UWDG_SetupGenericPanel_C::HoverImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_SetupGenericPanel_C, NormalImageBox) == 0x000600, "Member 'UWDG_SetupGenericPanel_C::NormalImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_SetupGenericPanel_C, txtFirstRow) == 0x000608, "Member 'UWDG_SetupGenericPanel_C::txtFirstRow' has a wrong offset!");
static_assert(offsetof(UWDG_SetupGenericPanel_C, txtSecondRow) == 0x000610, "Member 'UWDG_SetupGenericPanel_C::txtSecondRow' has a wrong offset!");
static_assert(offsetof(UWDG_SetupGenericPanel_C, txtTitle) == 0x000618, "Member 'UWDG_SetupGenericPanel_C::txtTitle' has a wrong offset!");
static_assert(offsetof(UWDG_SetupGenericPanel_C, MainTitle) == 0x000620, "Member 'UWDG_SetupGenericPanel_C::MainTitle' has a wrong offset!");

}

