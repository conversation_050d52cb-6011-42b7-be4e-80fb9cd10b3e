﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WPB_RaceTransitionPageNew

#include "Basic.hpp"

#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WPB_RaceTransitionPageNew.WPB_RaceTransitionPageNew_C.ExecuteUbergraph_WPB_RaceTransitionPageNew
// 0x0100 (0x0100 - 0x0000)
struct WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue;              // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance;             // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_IsHMDEnabled_ReturnValue;                 // 0x0019(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1A[0x6];                                       // 0x001A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue_1;            // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_TextToString_ReturnValue;            // 0x0028(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance_1;           // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_41[0x7];                                       // 0x0041(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Concat_StrStr_ReturnValue;                // 0x0048(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_GetCurrentGameVersion_ReturnValue;        // 0x0058(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	struct FGeometry                              K2Node_Event_MyGeometry;                           // 0x0068(0x0038)(IsPlainOldData, NoDestructor)
	float                                         K2Node_Event_InDeltaTime;                          // 0x00A0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_A4[0x4];                                       // 0x00A4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x00A8(0x0018)()
	class FString                                 CallFunc_Conv_TextToString_ReturnValue_1;          // 0x00C0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_isReplaySaving_ReturnValue;               // 0x00D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_D1[0x7];                                       // 0x00D1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_1;              // 0x00D8(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_1;          // 0x00E8(0x0018)()
};
static_assert(alignof(WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew) == 0x000008, "Wrong alignment on WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew");
static_assert(sizeof(WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew) == 0x000100, "Wrong size on WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew");
static_assert(offsetof(WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew, EntryPoint) == 0x000000, "Member 'WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew::EntryPoint' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew, CallFunc_GetGameInstance_ReturnValue) == 0x000008, "Member 'WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew::CallFunc_GetGameInstance_ReturnValue' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew, K2Node_DynamicCast_AsAc_Game_Instance) == 0x000010, "Member 'WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew::K2Node_DynamicCast_AsAc_Game_Instance' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew, K2Node_DynamicCast_bSuccess) == 0x000018, "Member 'WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew, CallFunc_IsHMDEnabled_ReturnValue) == 0x000019, "Member 'WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew::CallFunc_IsHMDEnabled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew, CallFunc_GetGameInstance_ReturnValue_1) == 0x000020, "Member 'WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew::CallFunc_GetGameInstance_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew, CallFunc_Conv_TextToString_ReturnValue) == 0x000028, "Member 'WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew::CallFunc_Conv_TextToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew, K2Node_DynamicCast_AsAc_Game_Instance_1) == 0x000038, "Member 'WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew::K2Node_DynamicCast_AsAc_Game_Instance_1' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew, K2Node_DynamicCast_bSuccess_1) == 0x000040, "Member 'WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew, CallFunc_Concat_StrStr_ReturnValue) == 0x000048, "Member 'WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew::CallFunc_Concat_StrStr_ReturnValue' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew, CallFunc_GetCurrentGameVersion_ReturnValue) == 0x000058, "Member 'WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew::CallFunc_GetCurrentGameVersion_ReturnValue' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew, K2Node_Event_MyGeometry) == 0x000068, "Member 'WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew::K2Node_Event_MyGeometry' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew, K2Node_Event_InDeltaTime) == 0x0000A0, "Member 'WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew::K2Node_Event_InDeltaTime' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew, CallFunc_Conv_StringToText_ReturnValue) == 0x0000A8, "Member 'WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew, CallFunc_Conv_TextToString_ReturnValue_1) == 0x0000C0, "Member 'WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew::CallFunc_Conv_TextToString_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew, CallFunc_isReplaySaving_ReturnValue) == 0x0000D0, "Member 'WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew::CallFunc_isReplaySaving_ReturnValue' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew, CallFunc_Concat_StrStr_ReturnValue_1) == 0x0000D8, "Member 'WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew::CallFunc_Concat_StrStr_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew, CallFunc_Conv_StringToText_ReturnValue_1) == 0x0000E8, "Member 'WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew::CallFunc_Conv_StringToText_ReturnValue_1' has a wrong offset!");

// Function WPB_RaceTransitionPageNew.WPB_RaceTransitionPageNew_C.Tick
// 0x003C (0x003C - 0x0000)
struct WPB_RaceTransitionPageNew_C_Tick final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	float                                         InDeltaTime;                                       // 0x0038(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WPB_RaceTransitionPageNew_C_Tick) == 0x000004, "Wrong alignment on WPB_RaceTransitionPageNew_C_Tick");
static_assert(sizeof(WPB_RaceTransitionPageNew_C_Tick) == 0x00003C, "Wrong size on WPB_RaceTransitionPageNew_C_Tick");
static_assert(offsetof(WPB_RaceTransitionPageNew_C_Tick, MyGeometry) == 0x000000, "Member 'WPB_RaceTransitionPageNew_C_Tick::MyGeometry' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPageNew_C_Tick, InDeltaTime) == 0x000038, "Member 'WPB_RaceTransitionPageNew_C_Tick::InDeltaTime' has a wrong offset!");

}

