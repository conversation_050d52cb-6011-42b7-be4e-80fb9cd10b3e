﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SpecialEventList

#include "Basic.hpp"

#include "WDG_SpecialEventList_classes.hpp"
#include "WDG_SpecialEventList_parameters.hpp"


namespace SDK
{

// Function WDG_SpecialEventList.WDG_SpecialEventList_C.ExecuteUbergraph_WDG_SpecialEventList
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SpecialEventList_C::ExecuteUbergraph_WDG_SpecialEventList(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventList_C", "ExecuteUbergraph_WDG_SpecialEventList");

	Params::WDG_SpecialEventList_C_ExecuteUbergraph_WDG_SpecialEventList Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEventList.WDG_SpecialEventList_C.OnAddedToFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_SpecialEventList_C::OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventList_C", "OnAddedToFocusPath");

	Params::WDG_SpecialEventList_C_OnAddedToFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}

}

