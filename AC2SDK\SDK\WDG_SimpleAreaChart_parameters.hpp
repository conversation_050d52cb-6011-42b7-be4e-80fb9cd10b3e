﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SimpleAreaChart

#include "Basic.hpp"

#include "UMG_structs.hpp"


namespace SDK::Params
{

// Function WDG_SimpleAreaChart.WDG_SimpleAreaChart_C.ExecuteUbergraph_WDG_SimpleAreaChart
// 0x0004 (0x0004 - 0x0000)
struct WDG_SimpleAreaChart_C_ExecuteUbergraph_WDG_SimpleAreaChart final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SimpleAreaChart_C_ExecuteUbergraph_WDG_SimpleAreaChart) == 0x000004, "Wrong alignment on WDG_SimpleAreaChart_C_ExecuteUbergraph_WDG_SimpleAreaChart");
static_assert(sizeof(WDG_SimpleAreaChart_C_ExecuteUbergraph_WDG_SimpleAreaChart) == 0x000004, "Wrong size on WDG_SimpleAreaChart_C_ExecuteUbergraph_WDG_SimpleAreaChart");
static_assert(offsetof(WDG_SimpleAreaChart_C_ExecuteUbergraph_WDG_SimpleAreaChart, EntryPoint) == 0x000000, "Member 'WDG_SimpleAreaChart_C_ExecuteUbergraph_WDG_SimpleAreaChart::EntryPoint' has a wrong offset!");

// Function WDG_SimpleAreaChart.WDG_SimpleAreaChart_C.OnPaint
// 0x0030 (0x0030 - 0x0000)
struct WDG_SimpleAreaChart_C_OnPaint final
{
public:
	struct FPaintContext                          Context;                                           // 0x0000(0x0030)(BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)
};
static_assert(alignof(WDG_SimpleAreaChart_C_OnPaint) == 0x000008, "Wrong alignment on WDG_SimpleAreaChart_C_OnPaint");
static_assert(sizeof(WDG_SimpleAreaChart_C_OnPaint) == 0x000030, "Wrong size on WDG_SimpleAreaChart_C_OnPaint");
static_assert(offsetof(WDG_SimpleAreaChart_C_OnPaint, Context) == 0x000000, "Member 'WDG_SimpleAreaChart_C_OnPaint::Context' has a wrong offset!");

}

