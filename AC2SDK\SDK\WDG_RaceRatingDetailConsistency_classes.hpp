﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RaceRatingDetailConsistency

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RaceRatingDetailConsistency.WDG_RaceRatingDetailConsistency_C
// 0x0000 (0x02A8 - 0x02A8)
class UWDG_RaceRatingDetailConsistency_C final : public URatingDetailConsistency
{
public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RaceRatingDetailConsistency_C">();
	}
	static class UWDG_RaceRatingDetailConsistency_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RaceRatingDetailConsistency_C>();
	}
};
static_assert(alignof(UWDG_RaceRatingDetailConsistency_C) == 0x000008, "Wrong alignment on UWDG_RaceRatingDetailConsistency_C");
static_assert(sizeof(UWDG_RaceRatingDetailConsistency_C) == 0x0002A8, "Wrong size on UWDG_RaceRatingDetailConsistency_C");

}

