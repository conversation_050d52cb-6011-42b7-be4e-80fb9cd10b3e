﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_WeatherForecast

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "AC2_classes.hpp"
#include "Engine_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_WeatherForecast.WDG_WeatherForecast_C
// 0x00F0 (0x0748 - 0x0658)
class UWDG_WeatherForecast_C : public UAcRaceWidgetBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0658(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UImage*                                 imgCar;                                            // 0x0660(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             Label10Min;                                        // 0x0668(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             Label30Min;                                        // 0x0670(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           Temps;                                             // 0x0678(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_10;                                      // 0x0680(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           TrackConditions;                                   // 0x0688(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txt10Min;                                          // 0x0690(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txt30Min;                                          // 0x0698(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtAmbientTemp;                                    // 0x06A0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtCurrent;                                        // 0x06A8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtDiff10Min;                                      // 0x06B0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtDiff30Min;                                      // 0x06B8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtTrackConditions;                                // 0x06C0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtTrackTemp;                                      // 0x06C8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtWindSpeed;                                      // 0x06D0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           WindConditions;                                    // 0x06D8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 WindDirArrow;                                      // 0x06E0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class AAcRaceGameMode*                        raceGameMode;                                      // 0x06E8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FWeatherStatus                         WeatherStatus;                                     // 0x06F0(0x0020)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor)
	bool                                          NoRain;                                            // 0x0710(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	bool                                          AutohideWithGoodWeather;                           // 0x0711(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn)
	bool                                          ShowTrackConditions;                               // 0x0712(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn)
	uint8                                         Pad_713[0x1];                                      // 0x0713(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTrackStatus                           TrackStatus;                                       // 0x0714(0x001C)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor)
	bool                                          ShouldBeVisible;                                   // 0x0730(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	bool                                          isPlayerCar;                                       // 0x0731(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	bool                                          isOnline;                                          // 0x0732(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	bool                                          IsHudWidget;                                       // 0x0733(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	float                                         Drizzle;                                           // 0x0734(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         LightRain;                                         // 0x0738(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         MediumRain;                                        // 0x073C(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         HeavyRain;                                         // 0x0740(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Permanent;                                         // 0x0744(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)

public:
	void ExecuteUbergraph_WDG_WeatherForecast(int32 EntryPoint);
	void OnHudTick(const struct FRaceHUDState& State);
	void OnStartWidget(class UAcGameInstance* GameInstance, class AAcRaceGameMode* raceGameMode_0, class ACarAvatar* CarAvatar, const struct FHUDOptions& HUDOptions);
	void Destruct();
	void OnEverySecond();
	void Construct();
	void PreConstruct(bool IsDesignTime);
	bool IsWidgetDefinitionEnabled(class UAcGameInstance* GameInstance, const struct FHUDOptions& HUDOptions);
	void UpdateDifferenceIndicators(class UTextBlock* First_Icon, class UTextBlock* Second_Icon, float First_Value, float Second_Value, class UTextBlock* Diff_Indicator);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_WeatherForecast_C">();
	}
	static class UWDG_WeatherForecast_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_WeatherForecast_C>();
	}
};
static_assert(alignof(UWDG_WeatherForecast_C) == 0x000008, "Wrong alignment on UWDG_WeatherForecast_C");
static_assert(sizeof(UWDG_WeatherForecast_C) == 0x000748, "Wrong size on UWDG_WeatherForecast_C");
static_assert(offsetof(UWDG_WeatherForecast_C, UberGraphFrame) == 0x000658, "Member 'UWDG_WeatherForecast_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, imgCar) == 0x000660, "Member 'UWDG_WeatherForecast_C::imgCar' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, Label10Min) == 0x000668, "Member 'UWDG_WeatherForecast_C::Label10Min' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, Label30Min) == 0x000670, "Member 'UWDG_WeatherForecast_C::Label30Min' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, Temps) == 0x000678, "Member 'UWDG_WeatherForecast_C::Temps' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, TextBlock_10) == 0x000680, "Member 'UWDG_WeatherForecast_C::TextBlock_10' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, TrackConditions) == 0x000688, "Member 'UWDG_WeatherForecast_C::TrackConditions' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, txt10Min) == 0x000690, "Member 'UWDG_WeatherForecast_C::txt10Min' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, txt30Min) == 0x000698, "Member 'UWDG_WeatherForecast_C::txt30Min' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, txtAmbientTemp) == 0x0006A0, "Member 'UWDG_WeatherForecast_C::txtAmbientTemp' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, txtCurrent) == 0x0006A8, "Member 'UWDG_WeatherForecast_C::txtCurrent' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, txtDiff10Min) == 0x0006B0, "Member 'UWDG_WeatherForecast_C::txtDiff10Min' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, txtDiff30Min) == 0x0006B8, "Member 'UWDG_WeatherForecast_C::txtDiff30Min' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, txtTrackConditions) == 0x0006C0, "Member 'UWDG_WeatherForecast_C::txtTrackConditions' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, txtTrackTemp) == 0x0006C8, "Member 'UWDG_WeatherForecast_C::txtTrackTemp' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, txtWindSpeed) == 0x0006D0, "Member 'UWDG_WeatherForecast_C::txtWindSpeed' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, WindConditions) == 0x0006D8, "Member 'UWDG_WeatherForecast_C::WindConditions' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, WindDirArrow) == 0x0006E0, "Member 'UWDG_WeatherForecast_C::WindDirArrow' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, raceGameMode) == 0x0006E8, "Member 'UWDG_WeatherForecast_C::raceGameMode' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, WeatherStatus) == 0x0006F0, "Member 'UWDG_WeatherForecast_C::WeatherStatus' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, NoRain) == 0x000710, "Member 'UWDG_WeatherForecast_C::NoRain' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, AutohideWithGoodWeather) == 0x000711, "Member 'UWDG_WeatherForecast_C::AutohideWithGoodWeather' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, ShowTrackConditions) == 0x000712, "Member 'UWDG_WeatherForecast_C::ShowTrackConditions' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, TrackStatus) == 0x000714, "Member 'UWDG_WeatherForecast_C::TrackStatus' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, ShouldBeVisible) == 0x000730, "Member 'UWDG_WeatherForecast_C::ShouldBeVisible' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, isPlayerCar) == 0x000731, "Member 'UWDG_WeatherForecast_C::isPlayerCar' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, isOnline) == 0x000732, "Member 'UWDG_WeatherForecast_C::isOnline' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, IsHudWidget) == 0x000733, "Member 'UWDG_WeatherForecast_C::IsHudWidget' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, Drizzle) == 0x000734, "Member 'UWDG_WeatherForecast_C::Drizzle' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, LightRain) == 0x000738, "Member 'UWDG_WeatherForecast_C::LightRain' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, MediumRain) == 0x00073C, "Member 'UWDG_WeatherForecast_C::MediumRain' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, HeavyRain) == 0x000740, "Member 'UWDG_WeatherForecast_C::HeavyRain' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherForecast_C, Permanent) == 0x000744, "Member 'UWDG_WeatherForecast_C::Permanent' has a wrong offset!");

}

