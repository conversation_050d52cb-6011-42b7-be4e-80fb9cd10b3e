﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TrackMapCarItem

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_TrackMapCarItem.WDG_TrackMapCarItem_C
// 0x0018 (0x02A8 - 0x0290)
class UWDG_TrackMapCarItem_C final : public UCarMapItem
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0290(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	struct FVector2D                              OutlineSize;                                       // 0x0298(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              IconSize;                                          // 0x02A0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_TrackMapCarItem(int32 EntryPoint);
	void PositionChanged(int32 Position_0);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_TrackMapCarItem_C">();
	}
	static class UWDG_TrackMapCarItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_TrackMapCarItem_C>();
	}
};
static_assert(alignof(UWDG_TrackMapCarItem_C) == 0x000008, "Wrong alignment on UWDG_TrackMapCarItem_C");
static_assert(sizeof(UWDG_TrackMapCarItem_C) == 0x0002A8, "Wrong size on UWDG_TrackMapCarItem_C");
static_assert(offsetof(UWDG_TrackMapCarItem_C, UberGraphFrame) == 0x000290, "Member 'UWDG_TrackMapCarItem_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_TrackMapCarItem_C, OutlineSize) == 0x000298, "Member 'UWDG_TrackMapCarItem_C::OutlineSize' has a wrong offset!");
static_assert(offsetof(UWDG_TrackMapCarItem_C, IconSize) == 0x0002A0, "Member 'UWDG_TrackMapCarItem_C::IconSize' has a wrong offset!");

}

