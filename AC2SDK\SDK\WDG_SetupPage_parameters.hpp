﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SetupPage

#include "Basic.hpp"

#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_SetupPage.WDG_SetupPage_C.ExecuteUbergraph_WDG_SetupPage
// 0x00C0 (0x00C0 - 0x0000)
struct WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue;               // 0x0008(0x0008)(ZeroConstructor, Is<PERSON>lainOldData, NoDestru<PERSON>, HasGetValueTypeHash)
	class UAcPanelBase*                           K2Node_ComponentBoundEvent_CallingPanel_1;         // 0x0010(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcPanelBase*                           K2Node_ComponentBoundEvent_CallingPanel;           // 0x0018(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_ComponentBoundEvent_Cancelled;              // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_21[0x7];                                       // 0x0021(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<struct FFileItem>                      CallFunc_GetSetups_ReturnValue;                    // 0x0028(0x0010)(ReferenceParm)
	class FString                                 K2Node_ComponentBoundEvent_Filename_2;             // 0x0038(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          K2Node_ComponentBoundEvent_ExistingFile;           // 0x0048(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_49[0x7];                                       // 0x0049(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<struct FFileItem>                      CallFunc_GetSetups_ReturnValue_1;                  // 0x0050(0x0010)(ReferenceParm)
	bool                                          CallFunc_SaveSetup_ReturnValue;                    // 0x0060(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_61[0x7];                                       // 0x0061(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 K2Node_ComponentBoundEvent_Filename_1;             // 0x0068(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_DeleteSetup_ReturnValue;                  // 0x0078(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_79[0x7];                                       // 0x0079(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<struct FFileItem>                      CallFunc_GetSetups_ReturnValue_2;                  // 0x0080(0x0010)(ReferenceParm)
	class FName                                   K2Node_ComponentBoundEvent_Filename;               // 0x0090(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 K2Node_ComponentBoundEvent_DisplayName;            // 0x0098(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_NameToString_ReturnValue;            // 0x00A8(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_LoadSetup_ReturnValue;                    // 0x00B8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage) == 0x000008, "Wrong alignment on WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage");
static_assert(sizeof(WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage) == 0x0000C0, "Wrong size on WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage");
static_assert(offsetof(WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage, EntryPoint) == 0x000000, "Member 'WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage, CallFunc_GetMenuManager_ReturnValue) == 0x000008, "Member 'WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage::CallFunc_GetMenuManager_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage, K2Node_ComponentBoundEvent_CallingPanel_1) == 0x000010, "Member 'WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage::K2Node_ComponentBoundEvent_CallingPanel_1' has a wrong offset!");
static_assert(offsetof(WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage, K2Node_ComponentBoundEvent_CallingPanel) == 0x000018, "Member 'WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage::K2Node_ComponentBoundEvent_CallingPanel' has a wrong offset!");
static_assert(offsetof(WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage, K2Node_ComponentBoundEvent_Cancelled) == 0x000020, "Member 'WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage::K2Node_ComponentBoundEvent_Cancelled' has a wrong offset!");
static_assert(offsetof(WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage, CallFunc_GetSetups_ReturnValue) == 0x000028, "Member 'WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage::CallFunc_GetSetups_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage, K2Node_ComponentBoundEvent_Filename_2) == 0x000038, "Member 'WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage::K2Node_ComponentBoundEvent_Filename_2' has a wrong offset!");
static_assert(offsetof(WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage, K2Node_ComponentBoundEvent_ExistingFile) == 0x000048, "Member 'WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage::K2Node_ComponentBoundEvent_ExistingFile' has a wrong offset!");
static_assert(offsetof(WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage, CallFunc_GetSetups_ReturnValue_1) == 0x000050, "Member 'WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage::CallFunc_GetSetups_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage, CallFunc_SaveSetup_ReturnValue) == 0x000060, "Member 'WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage::CallFunc_SaveSetup_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage, K2Node_ComponentBoundEvent_Filename_1) == 0x000068, "Member 'WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage::K2Node_ComponentBoundEvent_Filename_1' has a wrong offset!");
static_assert(offsetof(WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage, CallFunc_DeleteSetup_ReturnValue) == 0x000078, "Member 'WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage::CallFunc_DeleteSetup_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage, CallFunc_GetSetups_ReturnValue_2) == 0x000080, "Member 'WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage::CallFunc_GetSetups_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage, K2Node_ComponentBoundEvent_Filename) == 0x000090, "Member 'WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage::K2Node_ComponentBoundEvent_Filename' has a wrong offset!");
static_assert(offsetof(WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage, K2Node_ComponentBoundEvent_DisplayName) == 0x000098, "Member 'WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage::K2Node_ComponentBoundEvent_DisplayName' has a wrong offset!");
static_assert(offsetof(WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage, CallFunc_Conv_NameToString_ReturnValue) == 0x0000A8, "Member 'WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage::CallFunc_Conv_NameToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage, CallFunc_LoadSetup_ReturnValue) == 0x0000B8, "Member 'WDG_SetupPage_C_ExecuteUbergraph_WDG_SetupPage::CallFunc_LoadSetup_ReturnValue' has a wrong offset!");

// Function WDG_SetupPage.WDG_SetupPage_C.BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature
// 0x0018 (0x0018 - 0x0000)
struct WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature final
{
public:
	class FName                                   Filename;                                          // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 DisplayName;                                       // 0x0008(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature) == 0x000008, "Wrong alignment on WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature");
static_assert(sizeof(WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature) == 0x000018, "Wrong size on WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature");
static_assert(offsetof(WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature, Filename) == 0x000000, "Member 'WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature::Filename' has a wrong offset!");
static_assert(offsetof(WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature, DisplayName) == 0x000008, "Member 'WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature::DisplayName' has a wrong offset!");

// Function WDG_SetupPage.WDG_SetupPage_C.BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnDeleteUserPreset__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnDeleteUserPreset__DelegateSignature final
{
public:
	class FString                                 Filename;                                          // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnDeleteUserPreset__DelegateSignature) == 0x000008, "Wrong alignment on WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnDeleteUserPreset__DelegateSignature");
static_assert(sizeof(WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnDeleteUserPreset__DelegateSignature) == 0x000010, "Wrong size on WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnDeleteUserPreset__DelegateSignature");
static_assert(offsetof(WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnDeleteUserPreset__DelegateSignature, Filename) == 0x000000, "Member 'WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnDeleteUserPreset__DelegateSignature::Filename' has a wrong offset!");

// Function WDG_SetupPage.WDG_SetupPage_C.BndEvt__filePanel_K2Node_ComponentBoundEvent_3_OnSaveUserPreset__DelegateSignature
// 0x0018 (0x0018 - 0x0000)
struct WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_3_OnSaveUserPreset__DelegateSignature final
{
public:
	class FString                                 Filename;                                          // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, HasGetValueTypeHash)
	bool                                          ExistingFile;                                      // 0x0010(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_3_OnSaveUserPreset__DelegateSignature) == 0x000008, "Wrong alignment on WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_3_OnSaveUserPreset__DelegateSignature");
static_assert(sizeof(WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_3_OnSaveUserPreset__DelegateSignature) == 0x000018, "Wrong size on WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_3_OnSaveUserPreset__DelegateSignature");
static_assert(offsetof(WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_3_OnSaveUserPreset__DelegateSignature, Filename) == 0x000000, "Member 'WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_3_OnSaveUserPreset__DelegateSignature::Filename' has a wrong offset!");
static_assert(offsetof(WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_3_OnSaveUserPreset__DelegateSignature, ExistingFile) == 0x000010, "Member 'WDG_SetupPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_3_OnSaveUserPreset__DelegateSignature::ExistingFile' has a wrong offset!");

// Function WDG_SetupPage.WDG_SetupPage_C.BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_1_OnHide__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_SetupPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_1_OnHide__DelegateSignature final
{
public:
	class UAcPanelBase*                           CallingPanel;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Cancelled;                                         // 0x0008(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SetupPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_1_OnHide__DelegateSignature) == 0x000008, "Wrong alignment on WDG_SetupPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_1_OnHide__DelegateSignature");
static_assert(sizeof(WDG_SetupPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_1_OnHide__DelegateSignature) == 0x000010, "Wrong size on WDG_SetupPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_1_OnHide__DelegateSignature");
static_assert(offsetof(WDG_SetupPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_1_OnHide__DelegateSignature, CallingPanel) == 0x000000, "Member 'WDG_SetupPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_1_OnHide__DelegateSignature::CallingPanel' has a wrong offset!");
static_assert(offsetof(WDG_SetupPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_1_OnHide__DelegateSignature, Cancelled) == 0x000008, "Member 'WDG_SetupPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_1_OnHide__DelegateSignature::Cancelled' has a wrong offset!");

// Function WDG_SetupPage.WDG_SetupPage_C.BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_0_OnShow__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_SetupPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_0_OnShow__DelegateSignature final
{
public:
	class UAcPanelBase*                           CallingPanel;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SetupPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_0_OnShow__DelegateSignature) == 0x000008, "Wrong alignment on WDG_SetupPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_0_OnShow__DelegateSignature");
static_assert(sizeof(WDG_SetupPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_0_OnShow__DelegateSignature) == 0x000008, "Wrong size on WDG_SetupPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_0_OnShow__DelegateSignature");
static_assert(offsetof(WDG_SetupPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_0_OnShow__DelegateSignature, CallingPanel) == 0x000000, "Member 'WDG_SetupPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_0_OnShow__DelegateSignature::CallingPanel' has a wrong offset!");

}

