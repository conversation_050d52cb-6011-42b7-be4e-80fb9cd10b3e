﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_WeatherAndTrackConditionsPage

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "UMG_structs.hpp"
#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage
// 0x0820 (0x0820 - 0x0000)
struct WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue;              // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance;             // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_19[0x3];                                       // 0x0019(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue;          // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue;                       // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_SaveMenuState_ReturnValue;                // 0x0024(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0025(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_26[0x2];                                       // 0x0026(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue_1;        // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue_1;                     // 0x002C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue_2;                     // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_34[0x4];                                       // 0x0034(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue_1;            // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue_2;            // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance_1;           // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x0050(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_51[0x7];                                       // 0x0051(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance_2;           // 0x0058(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_2;                     // 0x0060(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_61[0x3];                                       // 0x0061(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTrackStatus                           CallFunc_getTrackStatusForUI_ReturnValue;          // 0x0064(0x001C)(NoDestructor)
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue_3;            // 0x0080(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance_3;           // 0x0088(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_3;                     // 0x0090(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_91[0x7];                                       // 0x0091(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue_4;            // 0x0098(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance_4;           // 0x00A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_4;                     // 0x00A8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_A9[0x3];                                       // 0x00A9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FWeatherData                           K2Node_MakeStruct_WeatherData;                     // 0x00AC(0x001C)(NoDestructor)
	struct FWeatherStatus                         CallFunc_getWeatherStatusForUI_ReturnValue;        // 0x00C8(0x0020)(NoDestructor)
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue_5;            // 0x00E8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance_5;           // 0x00F0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_5;                     // 0x00F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_F9[0x3];                                       // 0x00F9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTrackStatus                           CallFunc_getTrackStatusForUI_ReturnValue_1;        // 0x00FC(0x001C)(NoDestructor)
	bool                                          CallFunc_GreaterEqual_IntInt_ReturnValue;          // 0x0118(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0119(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x011A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_11B[0x1];                                      // 0x011B(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue_2;        // 0x011C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue_3;                     // 0x0120(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FWeatherData                           K2Node_SetFieldsInStruct_StructOut;                // 0x0124(0x001C)(NoDestructor, UObjectWrapper)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x0140(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_141[0x3];                                      // 0x0141(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue_3;        // 0x0144(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue_4;        // 0x0148(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue_4;                     // 0x014C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue_5;                     // 0x0150(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue_5;        // 0x0154(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue_6;                     // 0x0158(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FWeatherData                           K2Node_MakeStruct_WeatherData_1;                   // 0x015C(0x001C)(NoDestructor)
	int32                                         Temp_int_Variable;                                 // 0x0178(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x017C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue_6;            // 0x0180(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance_6;           // 0x0188(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_6;                     // 0x0190(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_191[0x3];                                      // 0x0191(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FWeatherStatus                         CallFunc_getWeatherStatusForUI_ReturnValue_1;      // 0x0194(0x0020)(NoDestructor)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_1;          // 0x01B4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_1;        // 0x01B5(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1B6[0x2];                                      // 0x01B6(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue_7;            // 0x01B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance_7;           // 0x01C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_7;                     // 0x01C8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x01C9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1CA[0x2];                                      // 0x01CA(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue_6;        // 0x01CC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue_7;                     // 0x01D0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FWeatherData                           K2Node_SetFieldsInStruct_StructOut_1;              // 0x01D4(0x001C)(NoDestructor, UObjectWrapper)
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue_8;            // 0x01F0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void(class UGenericSelectorItem* Source, int32 current_index, int32 current_value)> K2Node_CreateDelegate_OutputDelegate; // 0x01F8(0x0010)(ZeroConstructor, NoDestructor)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance_8;           // 0x0208(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_8;                     // 0x0210(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_IsValid_ReturnValue_2;                    // 0x0211(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_212[0x2];                                      // 0x0212(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue_7;        // 0x0214(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue_8;                     // 0x0218(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue_8;        // 0x021C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue_9;        // 0x0220(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue_9;                     // 0x0224(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue_10;                    // 0x0228(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void(class UGenericSelectorItem* Source, int32 current_index, int32 current_value)> K2Node_CreateDelegate_OutputDelegate_1; // 0x022C(0x0010)(ZeroConstructor, NoDestructor)
	uint8                                         Pad_23C[0x4];                                      // 0x023C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue_9;            // 0x0240(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance_9;           // 0x0248(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_9;                     // 0x0250(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_IsValid_ReturnValue_3;                    // 0x0251(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_252[0x2];                                      // 0x0252(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void(class UGenericSelectorItem* Source, int32 current_index, int32 current_value)> K2Node_CreateDelegate_OutputDelegate_2; // 0x0254(0x0010)(ZeroConstructor, NoDestructor)
	bool                                          Temp_bool_Variable;                                // 0x0264(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0265(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_266[0x2];                                      // 0x0266(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void(class UGenericSelectorItem* Source, int32 current_index, int32 current_value)> K2Node_CreateDelegate_OutputDelegate_3; // 0x0268(0x0010)(ZeroConstructor, NoDestructor)
	int32                                         CallFunc_GetChildrenCount_ReturnValue;             // 0x0278(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x027C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetChildrenCount_ReturnValue_1;           // 0x0280(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_1;            // 0x0284(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void(class UGenericSelectorItem* Source, int32 current_index, int32 current_value)> K2Node_CreateDelegate_OutputDelegate_4; // 0x0288(0x0010)(ZeroConstructor, NoDestructor)
	int32                                         Temp_int_Variable_1;                               // 0x0298(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_29C[0x4];                                      // 0x029C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Conv_NameToString_ReturnValue;            // 0x02A0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class UWidget*                                CallFunc_GetChildAt_ReturnValue;                   // 0x02B0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue;                // 0x02B8(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class UWDG_WeatherTypePanel_C*                K2Node_DynamicCast_AsWDG_Weather_Type_Panel;       // 0x02C8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_10;                    // 0x02D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_2D1[0x3];                                      // 0x02D1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x02D4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x02D8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x02D9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2DA[0x6];                                      // 0x02DA(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue_10;           // 0x02E0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance_10;          // 0x02E8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_11;                    // 0x02F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_isChamp_ReturnValue;                      // 0x02F1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          Temp_bool_Variable_1;                              // 0x02F2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_1;                // 0x02F3(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2F4[0x4];                                      // 0x02F4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Conv_NameToString_ReturnValue_1;          // 0x02F8(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_1;              // 0x0308(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue_11;           // 0x0318(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance_11;          // 0x0320(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_12;                    // 0x0328(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_isChamp_ReturnValue_1;                    // 0x0329(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_32A[0x2];                                      // 0x032A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void(EWeatherPresetType weatherType)> K2Node_CreateDelegate_OutputDelegate_5;          // 0x032C(0x0010)(ZeroConstructor, NoDestructor)
	uint8                                         Pad_33C[0x4];                                      // 0x033C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Conv_NameToString_ReturnValue_2;          // 0x0340(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_2;              // 0x0350(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate_6;            // 0x0360(0x0010)(ZeroConstructor, NoDestructor)
	int32                                         Temp_int_Variable_2;                               // 0x0370(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_374[0x4];                                      // 0x0374(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                CallFunc_GetChildAt_ReturnValue_1;                 // 0x0378(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate_7;            // 0x0380(0x0010)(ZeroConstructor, NoDestructor)
	class UWDG_WeatherTypePanel_C*                K2Node_DynamicCast_AsWDG_Weather_Type_Panel_1;     // 0x0390(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_13;                    // 0x0398(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_SetSelected_Changed;                      // 0x0399(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_39A[0x2];                                      // 0x039A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_2;                 // 0x039C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_1;           // 0x03A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_2;                 // 0x03A1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          Temp_bool_Variable_2;                              // 0x03A2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_2;                // 0x03A3(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	int32                                         CallFunc_GetChildrenCount_ReturnValue_2;           // 0x03A4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_2;            // 0x03A8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetChildrenCount_ReturnValue_3;           // 0x03AC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_3;            // 0x03B0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void(EWeatherPresetType weatherType, class UWDG_WeatherTypePanel_C* Source)> K2Node_CreateDelegate_OutputDelegate_8; // 0x03B4(0x0010)(ZeroConstructor, NoDestructor)
	uint8                                         Pad_3C4[0x4];                                      // 0x03C4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue_12;           // 0x03C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance_12;          // 0x03D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_14;                    // 0x03D8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_3D9[0x3];                                      // 0x03D9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable_3;                               // 0x03DC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   Temp_name_Variable;                                // 0x03E0(0x0008)(ConstParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_3;                 // 0x03E8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3EC[0x4];                                      // 0x03EC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                CallFunc_GetChildAt_ReturnValue_2;                 // 0x03F0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetChildrenCount_ReturnValue_4;           // 0x03F8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3FC[0x4];                                      // 0x03FC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_WeatherTypePanel_C*                K2Node_DynamicCast_AsWDG_Weather_Type_Panel_2;     // 0x0400(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_15;                    // 0x0408(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_409[0x3];                                      // 0x0409(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_4;            // 0x040C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_2;           // 0x0410(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          Temp_bool_Variable_3;                              // 0x0411(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_412[0x2];                                      // 0x0412(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class FName                                   Temp_name_Variable_1;                              // 0x0414(0x0008)(ConstParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_3;                // 0x041C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_41D[0x3];                                      // 0x041D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_GetChildrenCount_ReturnValue_5;           // 0x0420(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_5;            // 0x0424(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWidget*                                CallFunc_GetChildAt_ReturnValue_3;                 // 0x0428(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetChildrenCount_ReturnValue_6;           // 0x0430(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_434[0x4];                                      // 0x0434(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPanelBase*                           K2Node_DynamicCast_AsAc_Panel_Base;                // 0x0438(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_16;                    // 0x0440(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_441[0x3];                                      // 0x0441(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_6;            // 0x0444(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_3;           // 0x0448(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_StrStr_ReturnValue;            // 0x0449(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_3;                 // 0x044A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_44B[0x1];                                      // 0x044B(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	class FName                                   Temp_name_Variable_2;                              // 0x044C(0x0008)(ConstParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_4;                               // 0x0454(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWidget*                                CallFunc_GetChildAt_ReturnValue_4;                 // 0x0458(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcPanelBase*                           K2Node_DynamicCast_AsAc_Panel_Base_1;              // 0x0460(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_17;                    // 0x0468(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_469[0x3];                                      // 0x0469(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_4;                 // 0x046C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_StrStr_ReturnValue_1;          // 0x0470(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_4;           // 0x0471(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_4;                 // 0x0472(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_GetValidValue_ReturnValue;                // 0x0473(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FTrackStatus                           CallFunc_GetTrackStatus_Destination;               // 0x0474(0x001C)(NoDestructor)
	bool                                          CallFunc_GetTrackStatus_ReturnValue;               // 0x0490(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_2;          // 0x0491(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_492[0x6];                                      // 0x0492(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue_13;           // 0x0498(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance_13;          // 0x04A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_18;                    // 0x04A8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_4A9[0x7];                                      // 0x04A9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue_14;           // 0x04B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance_14;          // 0x04B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_19;                    // 0x04C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_4C1[0x3];                                      // 0x04C1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FWeatherData                           CallFunc_getWeatherDataForUI_ReturnValue;          // 0x04C4(0x001C)(NoDestructor)
	EWeatherPresetType                            K2Node_CustomEvent_WeatherType_1;                  // 0x04E0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4E1[0x7];                                      // 0x04E1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_WeatherTypePanel_C*                K2Node_CustomEvent_source_5;                       // 0x04E8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ObjectObject_ReturnValue;      // 0x04F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsVisible_ReturnValue;                    // 0x04F1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          Temp_bool_Variable_4;                              // 0x04F2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_4F3[0x5];                                      // 0x04F3(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	class UClass*                                 CallFunc_Map_Find_Value;                           // 0x04F8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Map_Find_ReturnValue;                     // 0x0500(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_501[0x7];                                      // 0x0501(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPageBase*                            CallFunc_GoToPage_ReturnValue;                     // 0x0508(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_4;                // 0x0510(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_511[0x7];                                      // 0x0511(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Conv_IntToString_ReturnValue;             // 0x0518(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class UClass*                                 CallFunc_Map_Find_Value_1;                         // 0x0528(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Map_Find_ReturnValue_1;                   // 0x0530(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_531[0x7];                                      // 0x0531(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPageBase*                            CallFunc_GoToPage_ReturnValue_1;                   // 0x0538(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_IntToString_ReturnValue_1;           // 0x0540(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class UClass*                                 CallFunc_Map_Find_Value_2;                         // 0x0550(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Map_Find_ReturnValue_2;                   // 0x0558(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_559[0x7];                                      // 0x0559(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPageBase*                            CallFunc_GoToPage_ReturnValue_2;                   // 0x0560(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue_10;       // 0x0568(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue_11;       // 0x056C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue_11;                    // 0x0570(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue_12;                    // 0x0574(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GreaterEqual_IntInt_ReturnValue_1;        // 0x0578(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_5;                 // 0x0579(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_57A[0x2];                                      // 0x057A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_FTrunc_ReturnValue_13;                    // 0x057C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue;               // 0x0580(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_5;                               // 0x0588(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EWeatherPresetType                            K2Node_CustomEvent_WeatherType;                    // 0x058C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_58D[0x3];                                      // 0x058D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                CallFunc_GetChildAt_ReturnValue_5;                 // 0x0590(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_2;        // 0x0598(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_599[0x7];                                      // 0x0599(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPanelBase*                           K2Node_DynamicCast_AsAc_Panel_Base_2;              // 0x05A0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_20;                    // 0x05A8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_3;        // 0x05A9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_StrStr_ReturnValue_2;          // 0x05AA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_4;        // 0x05AB(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_5;        // 0x05AC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_6;        // 0x05AD(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_5AE[0x2];                                      // 0x05AE(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FWeatherStatus                         CallFunc_GetWeatherSettings_Destination;           // 0x05B0(0x0020)(NoDestructor)
	bool                                          CallFunc_GetWeatherSettings_ReturnValue;           // 0x05D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_6;                 // 0x05D1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_5D2[0x2];                                      // 0x05D2(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Conv_ByteToInt_ReturnValue;               // 0x05D4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_Conv_IntToByte_ReturnValue;               // 0x05D8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_GetValidValue_ReturnValue_1;              // 0x05D9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_5DA[0x2];                                      // 0x05DA(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_5;                 // 0x05DC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_5;           // 0x05E0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_5E1[0x7];                                      // 0x05E1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue_1;             // 0x05E8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue_7;                 // 0x05F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_7;        // 0x05F1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_8;        // 0x05F2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_9;        // 0x05F3(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_10;       // 0x05F4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_8;                 // 0x05F5(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_11;       // 0x05F6(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_9;                 // 0x05F7(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_12;       // 0x05F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_SetSelected_Changed_1;                    // 0x05F9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_5FA[0x2];                                      // 0x05FA(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FWeatherData                           CallFunc_GetWeatherData_Destination;               // 0x05FC(0x001C)(NoDestructor)
	bool                                          CallFunc_GetWeatherData_ReturnValue;               // 0x0618(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_619[0x3];                                      // 0x0619(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FWeatherStatus                         CallFunc_GetWeatherSettings_Destination_1;         // 0x061C(0x0020)(NoDestructor)
	bool                                          CallFunc_GetWeatherSettings_ReturnValue_1;         // 0x063C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_63D[0x3];                                      // 0x063D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Conv_ByteToInt_ReturnValue_1;             // 0x0640(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_Conv_IntToByte_ReturnValue_1;             // 0x0644(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_5;                              // 0x0645(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_GetValidValue_ReturnValue_2;              // 0x0646(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_5;                // 0x0647(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_10;                // 0x0648(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          Temp_bool_Variable_6;                              // 0x0649(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_6;                // 0x064A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_64B[0x1];                                      // 0x064B(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_GetBiasedWetnessPuddles_MaxWetnessPuddles; // 0x064C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_GetBiasedWetnessPuddles_MaxWetnessPuddles_1; // 0x0650(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue_12;       // 0x0654(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue_14;                    // 0x0658(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_KSRandom_ReturnValue;                     // 0x065C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x0660(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_661[0x3];                                      // 0x0661(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_KSRandom_ReturnValue_1;                   // 0x0664(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_13;       // 0x0668(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_669[0x3];                                      // 0x0669(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue_13;       // 0x066C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue_14;       // 0x0670(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue_15;                    // 0x0674(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue_16;                    // 0x0678(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_14;       // 0x067C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_67D[0x3];                                      // 0x067D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UGenericSelectorItem*                   K2Node_ComponentBoundEvent_source_3;               // 0x0680(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_current_index_3;        // 0x0688(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_current_value_3;        // 0x068C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_3;          // 0x0690(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_7;                // 0x0691(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_11;                // 0x0692(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_12;                // 0x0693(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_13;                // 0x0694(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_695[0x3];                                      // 0x0695(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Conv_BoolToInt_ReturnValue;               // 0x0698(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_69C[0x4];                                      // 0x069C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UGenericSelectorItem*                   K2Node_ComponentBoundEvent_source_2;               // 0x06A0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_current_index_2;        // 0x06A8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_current_value_2;        // 0x06AC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Conv_IntToFloat_ReturnValue;              // 0x06B0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6B4[0x4];                                      // 0x06B4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UGenericSelectorItem*                   K2Node_ComponentBoundEvent_source_1;               // 0x06B8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_current_index_1;        // 0x06C0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_current_value_1;        // 0x06C4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Divide_FloatFloat_ReturnValue;            // 0x06C8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Conv_IntToFloat_ReturnValue_1;            // 0x06CC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Divide_FloatFloat_ReturnValue_1;          // 0x06D0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6D4[0x4];                                      // 0x06D4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UGenericSelectorItem*                   K2Node_ComponentBoundEvent_source;                 // 0x06D8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_current_index;          // 0x06E0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_current_value;          // 0x06E4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Conv_IntToFloat_ReturnValue_2;            // 0x06E8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_6;                               // 0x06EC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Divide_FloatFloat_ReturnValue_2;          // 0x06F0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6F4[0x4];                                      // 0x06F4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                CallFunc_GetChildAt_ReturnValue_6;                 // 0x06F8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcPanelBase*                           K2Node_DynamicCast_AsAc_Panel_Base_3;              // 0x0700(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_21;                    // 0x0708(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_709[0x3];                                      // 0x0709(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_6;                 // 0x070C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_StrStr_ReturnValue_3;          // 0x0710(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_14;                // 0x0711(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_6;           // 0x0712(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_15;                // 0x0713(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_714[0x4];                                      // 0x0714(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue_15;           // 0x0718(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance_15;          // 0x0720(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_22;                    // 0x0728(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_729[0x3];                                      // 0x0729(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FWeatherData                           CallFunc_getWeatherDataForUI_ReturnValue_1;        // 0x072C(0x001C)(NoDestructor)
	uint8                                         CallFunc_GetValidValue_ReturnValue_3;              // 0x0748(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_749[0x3];                                      // 0x0749(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTrackStatus                           CallFunc_GetTrackStatus_Destination_1;             // 0x074C(0x001C)(NoDestructor)
	bool                                          CallFunc_GetTrackStatus_ReturnValue_1;             // 0x0768(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_769[0x7];                                      // 0x0769(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UGenericSelectorItem*                   K2Node_CustomEvent_source_4;                       // 0x0770(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_CustomEvent_current_index_4;                // 0x0778(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_CustomEvent_current_value_4;                // 0x077C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Conv_IntToFloat_ReturnValue_3;            // 0x0780(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_7;           // 0x0784(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_785[0x3];                                      // 0x0785(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Divide_FloatFloat_ReturnValue_3;          // 0x0788(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_8;                // 0x078C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_78D[0x3];                                      // 0x078D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UGenericSelectorItem*                   K2Node_CustomEvent_source_3;                       // 0x0790(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_CustomEvent_current_index_3;                // 0x0798(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_CustomEvent_current_value_3;                // 0x079C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Conv_IntToFloat_ReturnValue_4;            // 0x07A0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Divide_FloatFloat_ReturnValue_4;          // 0x07A4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UGenericSelectorItem*                   K2Node_CustomEvent_source_2;                       // 0x07A8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_CustomEvent_current_index_2;                // 0x07B0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_CustomEvent_current_value_2;                // 0x07B4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Conv_IntToFloat_ReturnValue_5;            // 0x07B8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_7BC[0x4];                                      // 0x07BC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue_16;           // 0x07C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance_16;          // 0x07C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_23;                    // 0x07D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_7D1[0x7];                                      // 0x07D1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UGenericSelectorItem*                   K2Node_CustomEvent_source_1;                       // 0x07D8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_CustomEvent_current_index_1;                // 0x07E0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_CustomEvent_current_value_1;                // 0x07E4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FWeatherData                           CallFunc_GetWeatherData_Destination_1;             // 0x07E8(0x001C)(NoDestructor)
	bool                                          CallFunc_GetWeatherData_ReturnValue_1;             // 0x0804(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_805[0x3];                                      // 0x0805(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UGenericSelectorItem*                   K2Node_CustomEvent_source;                         // 0x0808(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_CustomEvent_current_index;                  // 0x0810(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_CustomEvent_current_value;                  // 0x0814(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Conv_IntToFloat_ReturnValue_6;            // 0x0818(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Divide_FloatFloat_ReturnValue_5;          // 0x081C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage) == 0x000008, "Wrong alignment on WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage");
static_assert(sizeof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage) == 0x000820, "Wrong size on WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, EntryPoint) == 0x000000, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetGameInstance_ReturnValue) == 0x000008, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetGameInstance_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_AsAc_Game_Instance) == 0x000010, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_AsAc_Game_Instance' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_bSuccess) == 0x000018, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Multiply_FloatFloat_ReturnValue) == 0x00001C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Multiply_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_FTrunc_ReturnValue) == 0x000020, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_FTrunc_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_SaveMenuState_ReturnValue) == 0x000024, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_SaveMenuState_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_IsValid_ReturnValue) == 0x000025, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Multiply_FloatFloat_ReturnValue_1) == 0x000028, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Multiply_FloatFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_FTrunc_ReturnValue_1) == 0x00002C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_FTrunc_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_FTrunc_ReturnValue_2) == 0x000030, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_FTrunc_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetGameInstance_ReturnValue_1) == 0x000038, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetGameInstance_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetGameInstance_ReturnValue_2) == 0x000040, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetGameInstance_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_AsAc_Game_Instance_1) == 0x000048, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_AsAc_Game_Instance_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_bSuccess_1) == 0x000050, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_AsAc_Game_Instance_2) == 0x000058, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_AsAc_Game_Instance_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_bSuccess_2) == 0x000060, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_bSuccess_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_getTrackStatusForUI_ReturnValue) == 0x000064, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_getTrackStatusForUI_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetGameInstance_ReturnValue_3) == 0x000080, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetGameInstance_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_AsAc_Game_Instance_3) == 0x000088, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_AsAc_Game_Instance_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_bSuccess_3) == 0x000090, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_bSuccess_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetGameInstance_ReturnValue_4) == 0x000098, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetGameInstance_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_AsAc_Game_Instance_4) == 0x0000A0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_AsAc_Game_Instance_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_bSuccess_4) == 0x0000A8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_bSuccess_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_MakeStruct_WeatherData) == 0x0000AC, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_MakeStruct_WeatherData' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_getWeatherStatusForUI_ReturnValue) == 0x0000C8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_getWeatherStatusForUI_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetGameInstance_ReturnValue_5) == 0x0000E8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetGameInstance_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_AsAc_Game_Instance_5) == 0x0000F0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_AsAc_Game_Instance_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_bSuccess_5) == 0x0000F8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_bSuccess_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_getTrackStatusForUI_ReturnValue_1) == 0x0000FC, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_getTrackStatusForUI_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GreaterEqual_IntInt_ReturnValue) == 0x000118, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GreaterEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000119, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_BooleanAND_ReturnValue) == 0x00011A, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Multiply_FloatFloat_ReturnValue_2) == 0x00011C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Multiply_FloatFloat_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_FTrunc_ReturnValue_3) == 0x000120, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_FTrunc_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_SetFieldsInStruct_StructOut) == 0x000124, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_SetFieldsInStruct_StructOut' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x000140, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Multiply_FloatFloat_ReturnValue_3) == 0x000144, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Multiply_FloatFloat_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Multiply_FloatFloat_ReturnValue_4) == 0x000148, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Multiply_FloatFloat_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_FTrunc_ReturnValue_4) == 0x00014C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_FTrunc_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_FTrunc_ReturnValue_5) == 0x000150, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_FTrunc_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Multiply_FloatFloat_ReturnValue_5) == 0x000154, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Multiply_FloatFloat_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_FTrunc_ReturnValue_6) == 0x000158, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_FTrunc_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_MakeStruct_WeatherData_1) == 0x00015C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_MakeStruct_WeatherData_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, Temp_int_Variable) == 0x000178, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Add_IntInt_ReturnValue) == 0x00017C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetGameInstance_ReturnValue_6) == 0x000180, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetGameInstance_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_AsAc_Game_Instance_6) == 0x000188, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_AsAc_Game_Instance_6' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_bSuccess_6) == 0x000190, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_bSuccess_6' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_getWeatherStatusForUI_ReturnValue_1) == 0x000194, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_getWeatherStatusForUI_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_EqualEqual_IntInt_ReturnValue_1) == 0x0001B4, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_EqualEqual_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_EqualEqual_ByteByte_ReturnValue_1) == 0x0001B5, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_EqualEqual_ByteByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetGameInstance_ReturnValue_7) == 0x0001B8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetGameInstance_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_AsAc_Game_Instance_7) == 0x0001C0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_AsAc_Game_Instance_7' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_bSuccess_7) == 0x0001C8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_bSuccess_7' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_IsValid_ReturnValue_1) == 0x0001C9, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Multiply_FloatFloat_ReturnValue_6) == 0x0001CC, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Multiply_FloatFloat_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_FTrunc_ReturnValue_7) == 0x0001D0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_FTrunc_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_SetFieldsInStruct_StructOut_1) == 0x0001D4, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_SetFieldsInStruct_StructOut_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetGameInstance_ReturnValue_8) == 0x0001F0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetGameInstance_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CreateDelegate_OutputDelegate) == 0x0001F8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_AsAc_Game_Instance_8) == 0x000208, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_AsAc_Game_Instance_8' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_bSuccess_8) == 0x000210, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_bSuccess_8' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_IsValid_ReturnValue_2) == 0x000211, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_IsValid_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Multiply_FloatFloat_ReturnValue_7) == 0x000214, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Multiply_FloatFloat_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_FTrunc_ReturnValue_8) == 0x000218, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_FTrunc_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Multiply_FloatFloat_ReturnValue_8) == 0x00021C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Multiply_FloatFloat_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Multiply_FloatFloat_ReturnValue_9) == 0x000220, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Multiply_FloatFloat_ReturnValue_9' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_FTrunc_ReturnValue_9) == 0x000224, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_FTrunc_ReturnValue_9' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_FTrunc_ReturnValue_10) == 0x000228, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_FTrunc_ReturnValue_10' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CreateDelegate_OutputDelegate_1) == 0x00022C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CreateDelegate_OutputDelegate_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetGameInstance_ReturnValue_9) == 0x000240, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetGameInstance_ReturnValue_9' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_AsAc_Game_Instance_9) == 0x000248, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_AsAc_Game_Instance_9' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_bSuccess_9) == 0x000250, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_bSuccess_9' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_IsValid_ReturnValue_3) == 0x000251, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_IsValid_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CreateDelegate_OutputDelegate_2) == 0x000254, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CreateDelegate_OutputDelegate_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, Temp_bool_Variable) == 0x000264, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Not_PreBool_ReturnValue) == 0x000265, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CreateDelegate_OutputDelegate_3) == 0x000268, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CreateDelegate_OutputDelegate_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetChildrenCount_ReturnValue) == 0x000278, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetChildrenCount_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Subtract_IntInt_ReturnValue) == 0x00027C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetChildrenCount_ReturnValue_1) == 0x000280, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetChildrenCount_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Subtract_IntInt_ReturnValue_1) == 0x000284, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Subtract_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CreateDelegate_OutputDelegate_4) == 0x000288, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CreateDelegate_OutputDelegate_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, Temp_int_Variable_1) == 0x000298, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Conv_NameToString_ReturnValue) == 0x0002A0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Conv_NameToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetChildAt_ReturnValue) == 0x0002B0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetChildAt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Concat_StrStr_ReturnValue) == 0x0002B8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Concat_StrStr_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_AsWDG_Weather_Type_Panel) == 0x0002C8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_AsWDG_Weather_Type_Panel' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_bSuccess_10) == 0x0002D0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_bSuccess_10' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Add_IntInt_ReturnValue_1) == 0x0002D4, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_LessEqual_IntInt_ReturnValue) == 0x0002D8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_BooleanAND_ReturnValue_1) == 0x0002D9, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetGameInstance_ReturnValue_10) == 0x0002E0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetGameInstance_ReturnValue_10' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_AsAc_Game_Instance_10) == 0x0002E8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_AsAc_Game_Instance_10' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_bSuccess_11) == 0x0002F0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_bSuccess_11' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_isChamp_ReturnValue) == 0x0002F1, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_isChamp_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, Temp_bool_Variable_1) == 0x0002F2, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Not_PreBool_ReturnValue_1) == 0x0002F3, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Not_PreBool_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Conv_NameToString_ReturnValue_1) == 0x0002F8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Conv_NameToString_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Concat_StrStr_ReturnValue_1) == 0x000308, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Concat_StrStr_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetGameInstance_ReturnValue_11) == 0x000318, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetGameInstance_ReturnValue_11' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_AsAc_Game_Instance_11) == 0x000320, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_AsAc_Game_Instance_11' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_bSuccess_12) == 0x000328, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_bSuccess_12' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_isChamp_ReturnValue_1) == 0x000329, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_isChamp_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CreateDelegate_OutputDelegate_5) == 0x00032C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CreateDelegate_OutputDelegate_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Conv_NameToString_ReturnValue_2) == 0x000340, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Conv_NameToString_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Concat_StrStr_ReturnValue_2) == 0x000350, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Concat_StrStr_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CreateDelegate_OutputDelegate_6) == 0x000360, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CreateDelegate_OutputDelegate_6' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, Temp_int_Variable_2) == 0x000370, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::Temp_int_Variable_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetChildAt_ReturnValue_1) == 0x000378, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetChildAt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CreateDelegate_OutputDelegate_7) == 0x000380, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CreateDelegate_OutputDelegate_7' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_AsWDG_Weather_Type_Panel_1) == 0x000390, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_AsWDG_Weather_Type_Panel_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_bSuccess_13) == 0x000398, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_bSuccess_13' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_SetSelected_Changed) == 0x000399, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_SetSelected_Changed' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Add_IntInt_ReturnValue_2) == 0x00039C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Add_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_LessEqual_IntInt_ReturnValue_1) == 0x0003A0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_LessEqual_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_BooleanAND_ReturnValue_2) == 0x0003A1, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_BooleanAND_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, Temp_bool_Variable_2) == 0x0003A2, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::Temp_bool_Variable_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Not_PreBool_ReturnValue_2) == 0x0003A3, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Not_PreBool_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetChildrenCount_ReturnValue_2) == 0x0003A4, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetChildrenCount_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Subtract_IntInt_ReturnValue_2) == 0x0003A8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Subtract_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetChildrenCount_ReturnValue_3) == 0x0003AC, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetChildrenCount_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Subtract_IntInt_ReturnValue_3) == 0x0003B0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Subtract_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CreateDelegate_OutputDelegate_8) == 0x0003B4, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CreateDelegate_OutputDelegate_8' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetGameInstance_ReturnValue_12) == 0x0003C8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetGameInstance_ReturnValue_12' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_AsAc_Game_Instance_12) == 0x0003D0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_AsAc_Game_Instance_12' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_bSuccess_14) == 0x0003D8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_bSuccess_14' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, Temp_int_Variable_3) == 0x0003DC, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::Temp_int_Variable_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, Temp_name_Variable) == 0x0003E0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::Temp_name_Variable' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Add_IntInt_ReturnValue_3) == 0x0003E8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Add_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetChildAt_ReturnValue_2) == 0x0003F0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetChildAt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetChildrenCount_ReturnValue_4) == 0x0003F8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetChildrenCount_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_AsWDG_Weather_Type_Panel_2) == 0x000400, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_AsWDG_Weather_Type_Panel_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_bSuccess_15) == 0x000408, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_bSuccess_15' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Subtract_IntInt_ReturnValue_4) == 0x00040C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Subtract_IntInt_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_LessEqual_IntInt_ReturnValue_2) == 0x000410, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_LessEqual_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, Temp_bool_Variable_3) == 0x000411, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::Temp_bool_Variable_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, Temp_name_Variable_1) == 0x000414, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::Temp_name_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Not_PreBool_ReturnValue_3) == 0x00041C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Not_PreBool_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetChildrenCount_ReturnValue_5) == 0x000420, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetChildrenCount_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Subtract_IntInt_ReturnValue_5) == 0x000424, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Subtract_IntInt_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetChildAt_ReturnValue_3) == 0x000428, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetChildAt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetChildrenCount_ReturnValue_6) == 0x000430, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetChildrenCount_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_AsAc_Panel_Base) == 0x000438, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_AsAc_Panel_Base' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_bSuccess_16) == 0x000440, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_bSuccess_16' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Subtract_IntInt_ReturnValue_6) == 0x000444, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Subtract_IntInt_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_LessEqual_IntInt_ReturnValue_3) == 0x000448, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_LessEqual_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_EqualEqual_StrStr_ReturnValue) == 0x000449, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_EqualEqual_StrStr_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_BooleanAND_ReturnValue_3) == 0x00044A, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_BooleanAND_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, Temp_name_Variable_2) == 0x00044C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::Temp_name_Variable_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, Temp_int_Variable_4) == 0x000454, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::Temp_int_Variable_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetChildAt_ReturnValue_4) == 0x000458, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetChildAt_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_AsAc_Panel_Base_1) == 0x000460, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_AsAc_Panel_Base_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_bSuccess_17) == 0x000468, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_bSuccess_17' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Add_IntInt_ReturnValue_4) == 0x00046C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Add_IntInt_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_EqualEqual_StrStr_ReturnValue_1) == 0x000470, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_EqualEqual_StrStr_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_LessEqual_IntInt_ReturnValue_4) == 0x000471, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_LessEqual_IntInt_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_BooleanAND_ReturnValue_4) == 0x000472, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_BooleanAND_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetValidValue_ReturnValue) == 0x000473, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetValidValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetTrackStatus_Destination) == 0x000474, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetTrackStatus_Destination' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetTrackStatus_ReturnValue) == 0x000490, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetTrackStatus_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_EqualEqual_IntInt_ReturnValue_2) == 0x000491, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_EqualEqual_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetGameInstance_ReturnValue_13) == 0x000498, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetGameInstance_ReturnValue_13' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_AsAc_Game_Instance_13) == 0x0004A0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_AsAc_Game_Instance_13' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_bSuccess_18) == 0x0004A8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_bSuccess_18' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetGameInstance_ReturnValue_14) == 0x0004B0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetGameInstance_ReturnValue_14' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_AsAc_Game_Instance_14) == 0x0004B8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_AsAc_Game_Instance_14' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_bSuccess_19) == 0x0004C0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_bSuccess_19' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_getWeatherDataForUI_ReturnValue) == 0x0004C4, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_getWeatherDataForUI_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CustomEvent_WeatherType_1) == 0x0004E0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CustomEvent_WeatherType_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CustomEvent_source_5) == 0x0004E8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CustomEvent_source_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_EqualEqual_ObjectObject_ReturnValue) == 0x0004F0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_EqualEqual_ObjectObject_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_IsVisible_ReturnValue) == 0x0004F1, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_IsVisible_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, Temp_bool_Variable_4) == 0x0004F2, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::Temp_bool_Variable_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Map_Find_Value) == 0x0004F8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Map_Find_Value' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Map_Find_ReturnValue) == 0x000500, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Map_Find_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GoToPage_ReturnValue) == 0x000508, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GoToPage_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Not_PreBool_ReturnValue_4) == 0x000510, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Not_PreBool_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Conv_IntToString_ReturnValue) == 0x000518, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Conv_IntToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Map_Find_Value_1) == 0x000528, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Map_Find_Value_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Map_Find_ReturnValue_1) == 0x000530, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Map_Find_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GoToPage_ReturnValue_1) == 0x000538, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GoToPage_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Conv_IntToString_ReturnValue_1) == 0x000540, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Conv_IntToString_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Map_Find_Value_2) == 0x000550, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Map_Find_Value_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Map_Find_ReturnValue_2) == 0x000558, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Map_Find_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GoToPage_ReturnValue_2) == 0x000560, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GoToPage_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Multiply_FloatFloat_ReturnValue_10) == 0x000568, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Multiply_FloatFloat_ReturnValue_10' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Multiply_FloatFloat_ReturnValue_11) == 0x00056C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Multiply_FloatFloat_ReturnValue_11' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_FTrunc_ReturnValue_11) == 0x000570, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_FTrunc_ReturnValue_11' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_FTrunc_ReturnValue_12) == 0x000574, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_FTrunc_ReturnValue_12' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GreaterEqual_IntInt_ReturnValue_1) == 0x000578, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GreaterEqual_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_BooleanAND_ReturnValue_5) == 0x000579, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_BooleanAND_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_FTrunc_ReturnValue_13) == 0x00057C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_FTrunc_ReturnValue_13' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetMenuManager_ReturnValue) == 0x000580, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetMenuManager_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, Temp_int_Variable_5) == 0x000588, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::Temp_int_Variable_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CustomEvent_WeatherType) == 0x00058C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CustomEvent_WeatherType' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetChildAt_ReturnValue_5) == 0x000590, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetChildAt_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_EqualEqual_ByteByte_ReturnValue_2) == 0x000598, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_EqualEqual_ByteByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_AsAc_Panel_Base_2) == 0x0005A0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_AsAc_Panel_Base_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_bSuccess_20) == 0x0005A8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_bSuccess_20' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_EqualEqual_ByteByte_ReturnValue_3) == 0x0005A9, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_EqualEqual_ByteByte_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_EqualEqual_StrStr_ReturnValue_2) == 0x0005AA, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_EqualEqual_StrStr_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_EqualEqual_ByteByte_ReturnValue_4) == 0x0005AB, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_EqualEqual_ByteByte_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_EqualEqual_ByteByte_ReturnValue_5) == 0x0005AC, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_EqualEqual_ByteByte_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_EqualEqual_ByteByte_ReturnValue_6) == 0x0005AD, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_EqualEqual_ByteByte_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetWeatherSettings_Destination) == 0x0005B0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetWeatherSettings_Destination' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetWeatherSettings_ReturnValue) == 0x0005D0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetWeatherSettings_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_BooleanAND_ReturnValue_6) == 0x0005D1, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_BooleanAND_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Conv_ByteToInt_ReturnValue) == 0x0005D4, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Conv_ByteToInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Conv_IntToByte_ReturnValue) == 0x0005D8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Conv_IntToByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetValidValue_ReturnValue_1) == 0x0005D9, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetValidValue_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Add_IntInt_ReturnValue_5) == 0x0005DC, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Add_IntInt_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_LessEqual_IntInt_ReturnValue_5) == 0x0005E0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_LessEqual_IntInt_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetMenuManager_ReturnValue_1) == 0x0005E8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetMenuManager_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_BooleanAND_ReturnValue_7) == 0x0005F0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_BooleanAND_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_EqualEqual_ByteByte_ReturnValue_7) == 0x0005F1, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_EqualEqual_ByteByte_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_EqualEqual_ByteByte_ReturnValue_8) == 0x0005F2, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_EqualEqual_ByteByte_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_EqualEqual_ByteByte_ReturnValue_9) == 0x0005F3, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_EqualEqual_ByteByte_ReturnValue_9' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_EqualEqual_ByteByte_ReturnValue_10) == 0x0005F4, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_EqualEqual_ByteByte_ReturnValue_10' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_BooleanAND_ReturnValue_8) == 0x0005F5, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_BooleanAND_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_EqualEqual_ByteByte_ReturnValue_11) == 0x0005F6, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_EqualEqual_ByteByte_ReturnValue_11' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_BooleanAND_ReturnValue_9) == 0x0005F7, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_BooleanAND_ReturnValue_9' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_EqualEqual_ByteByte_ReturnValue_12) == 0x0005F8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_EqualEqual_ByteByte_ReturnValue_12' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_SetSelected_Changed_1) == 0x0005F9, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_SetSelected_Changed_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetWeatherData_Destination) == 0x0005FC, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetWeatherData_Destination' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetWeatherData_ReturnValue) == 0x000618, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetWeatherData_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetWeatherSettings_Destination_1) == 0x00061C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetWeatherSettings_Destination_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetWeatherSettings_ReturnValue_1) == 0x00063C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetWeatherSettings_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Conv_ByteToInt_ReturnValue_1) == 0x000640, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Conv_ByteToInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Conv_IntToByte_ReturnValue_1) == 0x000644, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Conv_IntToByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, Temp_bool_Variable_5) == 0x000645, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::Temp_bool_Variable_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetValidValue_ReturnValue_2) == 0x000646, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetValidValue_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Not_PreBool_ReturnValue_5) == 0x000647, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Not_PreBool_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_BooleanAND_ReturnValue_10) == 0x000648, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_BooleanAND_ReturnValue_10' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, Temp_bool_Variable_6) == 0x000649, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::Temp_bool_Variable_6' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Not_PreBool_ReturnValue_6) == 0x00064A, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Not_PreBool_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetBiasedWetnessPuddles_MaxWetnessPuddles) == 0x00064C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetBiasedWetnessPuddles_MaxWetnessPuddles' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetBiasedWetnessPuddles_MaxWetnessPuddles_1) == 0x000650, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetBiasedWetnessPuddles_MaxWetnessPuddles_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Multiply_FloatFloat_ReturnValue_12) == 0x000654, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Multiply_FloatFloat_ReturnValue_12' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_FTrunc_ReturnValue_14) == 0x000658, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_FTrunc_ReturnValue_14' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_KSRandom_ReturnValue) == 0x00065C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_KSRandom_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Greater_IntInt_ReturnValue) == 0x000660, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_KSRandom_ReturnValue_1) == 0x000664, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_KSRandom_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_EqualEqual_ByteByte_ReturnValue_13) == 0x000668, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_EqualEqual_ByteByte_ReturnValue_13' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Multiply_FloatFloat_ReturnValue_13) == 0x00066C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Multiply_FloatFloat_ReturnValue_13' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Multiply_FloatFloat_ReturnValue_14) == 0x000670, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Multiply_FloatFloat_ReturnValue_14' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_FTrunc_ReturnValue_15) == 0x000674, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_FTrunc_ReturnValue_15' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_FTrunc_ReturnValue_16) == 0x000678, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_FTrunc_ReturnValue_16' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_EqualEqual_ByteByte_ReturnValue_14) == 0x00067C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_EqualEqual_ByteByte_ReturnValue_14' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_ComponentBoundEvent_source_3) == 0x000680, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_ComponentBoundEvent_source_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_ComponentBoundEvent_current_index_3) == 0x000688, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_ComponentBoundEvent_current_index_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_ComponentBoundEvent_current_value_3) == 0x00068C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_ComponentBoundEvent_current_value_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_EqualEqual_IntInt_ReturnValue_3) == 0x000690, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_EqualEqual_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Not_PreBool_ReturnValue_7) == 0x000691, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Not_PreBool_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_BooleanAND_ReturnValue_11) == 0x000692, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_BooleanAND_ReturnValue_11' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_BooleanAND_ReturnValue_12) == 0x000693, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_BooleanAND_ReturnValue_12' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_BooleanAND_ReturnValue_13) == 0x000694, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_BooleanAND_ReturnValue_13' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Conv_BoolToInt_ReturnValue) == 0x000698, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Conv_BoolToInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_ComponentBoundEvent_source_2) == 0x0006A0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_ComponentBoundEvent_source_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_ComponentBoundEvent_current_index_2) == 0x0006A8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_ComponentBoundEvent_current_index_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_ComponentBoundEvent_current_value_2) == 0x0006AC, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_ComponentBoundEvent_current_value_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Conv_IntToFloat_ReturnValue) == 0x0006B0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Conv_IntToFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_ComponentBoundEvent_source_1) == 0x0006B8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_ComponentBoundEvent_source_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_ComponentBoundEvent_current_index_1) == 0x0006C0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_ComponentBoundEvent_current_index_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_ComponentBoundEvent_current_value_1) == 0x0006C4, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_ComponentBoundEvent_current_value_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Divide_FloatFloat_ReturnValue) == 0x0006C8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Divide_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Conv_IntToFloat_ReturnValue_1) == 0x0006CC, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Conv_IntToFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Divide_FloatFloat_ReturnValue_1) == 0x0006D0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Divide_FloatFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_ComponentBoundEvent_source) == 0x0006D8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_ComponentBoundEvent_source' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_ComponentBoundEvent_current_index) == 0x0006E0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_ComponentBoundEvent_current_index' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_ComponentBoundEvent_current_value) == 0x0006E4, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_ComponentBoundEvent_current_value' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Conv_IntToFloat_ReturnValue_2) == 0x0006E8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Conv_IntToFloat_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, Temp_int_Variable_6) == 0x0006EC, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::Temp_int_Variable_6' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Divide_FloatFloat_ReturnValue_2) == 0x0006F0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Divide_FloatFloat_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetChildAt_ReturnValue_6) == 0x0006F8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetChildAt_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_AsAc_Panel_Base_3) == 0x000700, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_AsAc_Panel_Base_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_bSuccess_21) == 0x000708, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_bSuccess_21' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Add_IntInt_ReturnValue_6) == 0x00070C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Add_IntInt_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_EqualEqual_StrStr_ReturnValue_3) == 0x000710, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_EqualEqual_StrStr_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_BooleanAND_ReturnValue_14) == 0x000711, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_BooleanAND_ReturnValue_14' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_LessEqual_IntInt_ReturnValue_6) == 0x000712, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_LessEqual_IntInt_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_BooleanAND_ReturnValue_15) == 0x000713, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_BooleanAND_ReturnValue_15' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetGameInstance_ReturnValue_15) == 0x000718, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetGameInstance_ReturnValue_15' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_AsAc_Game_Instance_15) == 0x000720, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_AsAc_Game_Instance_15' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_bSuccess_22) == 0x000728, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_bSuccess_22' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_getWeatherDataForUI_ReturnValue_1) == 0x00072C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_getWeatherDataForUI_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetValidValue_ReturnValue_3) == 0x000748, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetValidValue_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetTrackStatus_Destination_1) == 0x00074C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetTrackStatus_Destination_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetTrackStatus_ReturnValue_1) == 0x000768, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetTrackStatus_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CustomEvent_source_4) == 0x000770, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CustomEvent_source_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CustomEvent_current_index_4) == 0x000778, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CustomEvent_current_index_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CustomEvent_current_value_4) == 0x00077C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CustomEvent_current_value_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Conv_IntToFloat_ReturnValue_3) == 0x000780, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Conv_IntToFloat_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_LessEqual_IntInt_ReturnValue_7) == 0x000784, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_LessEqual_IntInt_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Divide_FloatFloat_ReturnValue_3) == 0x000788, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Divide_FloatFloat_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Not_PreBool_ReturnValue_8) == 0x00078C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Not_PreBool_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CustomEvent_source_3) == 0x000790, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CustomEvent_source_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CustomEvent_current_index_3) == 0x000798, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CustomEvent_current_index_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CustomEvent_current_value_3) == 0x00079C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CustomEvent_current_value_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Conv_IntToFloat_ReturnValue_4) == 0x0007A0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Conv_IntToFloat_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Divide_FloatFloat_ReturnValue_4) == 0x0007A4, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Divide_FloatFloat_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CustomEvent_source_2) == 0x0007A8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CustomEvent_source_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CustomEvent_current_index_2) == 0x0007B0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CustomEvent_current_index_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CustomEvent_current_value_2) == 0x0007B4, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CustomEvent_current_value_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Conv_IntToFloat_ReturnValue_5) == 0x0007B8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Conv_IntToFloat_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetGameInstance_ReturnValue_16) == 0x0007C0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetGameInstance_ReturnValue_16' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_AsAc_Game_Instance_16) == 0x0007C8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_AsAc_Game_Instance_16' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_DynamicCast_bSuccess_23) == 0x0007D0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_DynamicCast_bSuccess_23' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CustomEvent_source_1) == 0x0007D8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CustomEvent_source_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CustomEvent_current_index_1) == 0x0007E0, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CustomEvent_current_index_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CustomEvent_current_value_1) == 0x0007E4, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CustomEvent_current_value_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetWeatherData_Destination_1) == 0x0007E8, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetWeatherData_Destination_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_GetWeatherData_ReturnValue_1) == 0x000804, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_GetWeatherData_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CustomEvent_source) == 0x000808, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CustomEvent_source' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CustomEvent_current_index) == 0x000810, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CustomEvent_current_index' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, K2Node_CustomEvent_current_value) == 0x000814, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::K2Node_CustomEvent_current_value' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Conv_IntToFloat_ReturnValue_6) == 0x000818, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Conv_IntToFloat_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage, CallFunc_Divide_FloatFloat_ReturnValue_5) == 0x00081C, "Member 'WDG_WeatherAndTrackConditionsPage_C_ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage::CallFunc_Divide_FloatFloat_ReturnValue_5' has a wrong offset!");

// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.Variability Changed
// 0x0010 (0x0010 - 0x0000)
struct WDG_WeatherAndTrackConditionsPage_C_Variability_Changed final
{
public:
	class UGenericSelectorItem*                   Source;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_index;                                     // 0x0008(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_value;                                     // 0x000C(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_WeatherAndTrackConditionsPage_C_Variability_Changed) == 0x000008, "Wrong alignment on WDG_WeatherAndTrackConditionsPage_C_Variability_Changed");
static_assert(sizeof(WDG_WeatherAndTrackConditionsPage_C_Variability_Changed) == 0x000010, "Wrong size on WDG_WeatherAndTrackConditionsPage_C_Variability_Changed");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_Variability_Changed, Source) == 0x000000, "Member 'WDG_WeatherAndTrackConditionsPage_C_Variability_Changed::Source' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_Variability_Changed, current_index) == 0x000008, "Member 'WDG_WeatherAndTrackConditionsPage_C_Variability_Changed::current_index' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_Variability_Changed, current_value) == 0x00000C, "Member 'WDG_WeatherAndTrackConditionsPage_C_Variability_Changed::current_value' has a wrong offset!");

// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.Dynamic Weather Changed
// 0x0010 (0x0010 - 0x0000)
struct WDG_WeatherAndTrackConditionsPage_C_Dynamic_Weather_Changed final
{
public:
	class UGenericSelectorItem*                   Source;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_index;                                     // 0x0008(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_value;                                     // 0x000C(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_WeatherAndTrackConditionsPage_C_Dynamic_Weather_Changed) == 0x000008, "Wrong alignment on WDG_WeatherAndTrackConditionsPage_C_Dynamic_Weather_Changed");
static_assert(sizeof(WDG_WeatherAndTrackConditionsPage_C_Dynamic_Weather_Changed) == 0x000010, "Wrong size on WDG_WeatherAndTrackConditionsPage_C_Dynamic_Weather_Changed");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_Dynamic_Weather_Changed, Source) == 0x000000, "Member 'WDG_WeatherAndTrackConditionsPage_C_Dynamic_Weather_Changed::Source' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_Dynamic_Weather_Changed, current_index) == 0x000008, "Member 'WDG_WeatherAndTrackConditionsPage_C_Dynamic_Weather_Changed::current_index' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_Dynamic_Weather_Changed, current_value) == 0x00000C, "Member 'WDG_WeatherAndTrackConditionsPage_C_Dynamic_Weather_Changed::current_value' has a wrong offset!");

// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.Ambient Temperature Changed
// 0x0010 (0x0010 - 0x0000)
struct WDG_WeatherAndTrackConditionsPage_C_Ambient_Temperature_Changed final
{
public:
	class UGenericSelectorItem*                   Source;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_index;                                     // 0x0008(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_value;                                     // 0x000C(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_WeatherAndTrackConditionsPage_C_Ambient_Temperature_Changed) == 0x000008, "Wrong alignment on WDG_WeatherAndTrackConditionsPage_C_Ambient_Temperature_Changed");
static_assert(sizeof(WDG_WeatherAndTrackConditionsPage_C_Ambient_Temperature_Changed) == 0x000010, "Wrong size on WDG_WeatherAndTrackConditionsPage_C_Ambient_Temperature_Changed");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_Ambient_Temperature_Changed, Source) == 0x000000, "Member 'WDG_WeatherAndTrackConditionsPage_C_Ambient_Temperature_Changed::Source' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_Ambient_Temperature_Changed, current_index) == 0x000008, "Member 'WDG_WeatherAndTrackConditionsPage_C_Ambient_Temperature_Changed::current_index' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_Ambient_Temperature_Changed, current_value) == 0x00000C, "Member 'WDG_WeatherAndTrackConditionsPage_C_Ambient_Temperature_Changed::current_value' has a wrong offset!");

// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.CustomEvent_0
// 0x0010 (0x0010 - 0x0000)
struct WDG_WeatherAndTrackConditionsPage_C_CustomEvent_0 final
{
public:
	class UGenericSelectorItem*                   Source;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_index;                                     // 0x0008(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_value;                                     // 0x000C(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_WeatherAndTrackConditionsPage_C_CustomEvent_0) == 0x000008, "Wrong alignment on WDG_WeatherAndTrackConditionsPage_C_CustomEvent_0");
static_assert(sizeof(WDG_WeatherAndTrackConditionsPage_C_CustomEvent_0) == 0x000010, "Wrong size on WDG_WeatherAndTrackConditionsPage_C_CustomEvent_0");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CustomEvent_0, Source) == 0x000000, "Member 'WDG_WeatherAndTrackConditionsPage_C_CustomEvent_0::Source' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CustomEvent_0, current_index) == 0x000008, "Member 'WDG_WeatherAndTrackConditionsPage_C_CustomEvent_0::current_index' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CustomEvent_0, current_value) == 0x00000C, "Member 'WDG_WeatherAndTrackConditionsPage_C_CustomEvent_0::current_value' has a wrong offset!");

// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.Weather Variability Changed
// 0x0010 (0x0010 - 0x0000)
struct WDG_WeatherAndTrackConditionsPage_C_Weather_Variability_Changed final
{
public:
	class UGenericSelectorItem*                   Source;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_index;                                     // 0x0008(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_value;                                     // 0x000C(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_WeatherAndTrackConditionsPage_C_Weather_Variability_Changed) == 0x000008, "Wrong alignment on WDG_WeatherAndTrackConditionsPage_C_Weather_Variability_Changed");
static_assert(sizeof(WDG_WeatherAndTrackConditionsPage_C_Weather_Variability_Changed) == 0x000010, "Wrong size on WDG_WeatherAndTrackConditionsPage_C_Weather_Variability_Changed");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_Weather_Variability_Changed, Source) == 0x000000, "Member 'WDG_WeatherAndTrackConditionsPage_C_Weather_Variability_Changed::Source' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_Weather_Variability_Changed, current_index) == 0x000008, "Member 'WDG_WeatherAndTrackConditionsPage_C_Weather_Variability_Changed::current_index' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_Weather_Variability_Changed, current_value) == 0x00000C, "Member 'WDG_WeatherAndTrackConditionsPage_C_Weather_Variability_Changed::current_value' has a wrong offset!");

// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.BndEvt__sliderStandingWater_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderStandingWater_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature final
{
public:
	class UGenericSelectorItem*                   Source;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_index;                                     // 0x0008(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_value;                                     // 0x000C(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderStandingWater_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature) == 0x000008, "Wrong alignment on WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderStandingWater_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature");
static_assert(sizeof(WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderStandingWater_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature) == 0x000010, "Wrong size on WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderStandingWater_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderStandingWater_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature, Source) == 0x000000, "Member 'WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderStandingWater_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature::Source' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderStandingWater_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature, current_index) == 0x000008, "Member 'WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderStandingWater_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature::current_index' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderStandingWater_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature, current_value) == 0x00000C, "Member 'WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderStandingWater_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature::current_value' has a wrong offset!");

// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.BndEvt__sliderGripLevel_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderGripLevel_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature final
{
public:
	class UGenericSelectorItem*                   Source;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_index;                                     // 0x0008(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_value;                                     // 0x000C(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderGripLevel_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature) == 0x000008, "Wrong alignment on WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderGripLevel_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature");
static_assert(sizeof(WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderGripLevel_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature) == 0x000010, "Wrong size on WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderGripLevel_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderGripLevel_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature, Source) == 0x000000, "Member 'WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderGripLevel_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature::Source' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderGripLevel_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature, current_index) == 0x000008, "Member 'WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderGripLevel_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature::current_index' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderGripLevel_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature, current_value) == 0x00000C, "Member 'WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderGripLevel_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature::current_value' has a wrong offset!");

// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.BndEvt__sliderSurface_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderSurface_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature final
{
public:
	class UGenericSelectorItem*                   Source;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_index;                                     // 0x0008(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_value;                                     // 0x000C(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderSurface_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature) == 0x000008, "Wrong alignment on WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderSurface_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature");
static_assert(sizeof(WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderSurface_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature) == 0x000010, "Wrong size on WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderSurface_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderSurface_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature, Source) == 0x000000, "Member 'WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderSurface_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature::Source' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderSurface_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature, current_index) == 0x000008, "Member 'WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderSurface_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature::current_index' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderSurface_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature, current_value) == 0x00000C, "Member 'WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderSurface_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature::current_value' has a wrong offset!");

// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.BndEvt__sliderRandomWeather_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderRandomWeather_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature final
{
public:
	class UGenericSelectorItem*                   Source;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_index;                                     // 0x0008(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_value;                                     // 0x000C(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderRandomWeather_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature) == 0x000008, "Wrong alignment on WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderRandomWeather_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature");
static_assert(sizeof(WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderRandomWeather_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature) == 0x000010, "Wrong size on WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderRandomWeather_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderRandomWeather_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature, Source) == 0x000000, "Member 'WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderRandomWeather_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature::Source' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderRandomWeather_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature, current_index) == 0x000008, "Member 'WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderRandomWeather_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature::current_index' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderRandomWeather_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature, current_value) == 0x00000C, "Member 'WDG_WeatherAndTrackConditionsPage_C_BndEvt__sliderRandomWeather_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature::current_value' has a wrong offset!");

// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.Weather Preset Changed
// 0x0001 (0x0001 - 0x0000)
struct WDG_WeatherAndTrackConditionsPage_C_Weather_Preset_Changed final
{
public:
	EWeatherPresetType                            weatherType;                                       // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_WeatherAndTrackConditionsPage_C_Weather_Preset_Changed) == 0x000001, "Wrong alignment on WDG_WeatherAndTrackConditionsPage_C_Weather_Preset_Changed");
static_assert(sizeof(WDG_WeatherAndTrackConditionsPage_C_Weather_Preset_Changed) == 0x000001, "Wrong size on WDG_WeatherAndTrackConditionsPage_C_Weather_Preset_Changed");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_Weather_Preset_Changed, weatherType) == 0x000000, "Member 'WDG_WeatherAndTrackConditionsPage_C_Weather_Preset_Changed::weatherType' has a wrong offset!");

// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.CustomEvent_1
// 0x0010 (0x0010 - 0x0000)
struct WDG_WeatherAndTrackConditionsPage_C_CustomEvent_1 final
{
public:
	EWeatherPresetType                            weatherType;                                       // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_WeatherTypePanel_C*                Source;                                            // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_WeatherAndTrackConditionsPage_C_CustomEvent_1) == 0x000008, "Wrong alignment on WDG_WeatherAndTrackConditionsPage_C_CustomEvent_1");
static_assert(sizeof(WDG_WeatherAndTrackConditionsPage_C_CustomEvent_1) == 0x000010, "Wrong size on WDG_WeatherAndTrackConditionsPage_C_CustomEvent_1");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CustomEvent_1, weatherType) == 0x000000, "Member 'WDG_WeatherAndTrackConditionsPage_C_CustomEvent_1::weatherType' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CustomEvent_1, Source) == 0x000008, "Member 'WDG_WeatherAndTrackConditionsPage_C_CustomEvent_1::Source' has a wrong offset!");

// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.CloudRainRules
// 0x0050 (0x0050 - 0x0000)
struct WDG_WeatherAndTrackConditionsPage_C_CloudRainRules final
{
public:
	bool                                          ByCloudCover;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1[0x3];                                        // 0x0001(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Conv_IntToFloat_ReturnValue;              // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetValue_ReturnValue;                     // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetValue_ReturnValue_1;                   // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_12[0x2];                                       // 0x0012(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Conv_IntToFloat_ReturnValue_1;            // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Conv_IntToFloat_ReturnValue_2;            // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetValue_ReturnValue_2;                   // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetValue_ReturnValue_3;                   // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetValue_ReturnValue_4;                   // 0x0024(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Conv_IntToFloat_ReturnValue_3;            // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GreaterEqual_IntInt_ReturnValue;          // 0x002C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2D[0x3];                                       // 0x002D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Conv_IntToFloat_ReturnValue_4;            // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_ReMapValue_ReturnValue;                   // 0x0034(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_39[0x3];                                       // 0x0039(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_FTrunc_ReturnValue;                       // 0x003C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_41[0x3];                                       // 0x0041(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_ReMapValue_ReturnValue_1;                 // 0x0044(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue_1;                     // 0x0048(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x004C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_WeatherAndTrackConditionsPage_C_CloudRainRules) == 0x000004, "Wrong alignment on WDG_WeatherAndTrackConditionsPage_C_CloudRainRules");
static_assert(sizeof(WDG_WeatherAndTrackConditionsPage_C_CloudRainRules) == 0x000050, "Wrong size on WDG_WeatherAndTrackConditionsPage_C_CloudRainRules");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CloudRainRules, ByCloudCover) == 0x000000, "Member 'WDG_WeatherAndTrackConditionsPage_C_CloudRainRules::ByCloudCover' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CloudRainRules, CallFunc_Conv_IntToFloat_ReturnValue) == 0x000004, "Member 'WDG_WeatherAndTrackConditionsPage_C_CloudRainRules::CallFunc_Conv_IntToFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CloudRainRules, CallFunc_GetValue_ReturnValue) == 0x000008, "Member 'WDG_WeatherAndTrackConditionsPage_C_CloudRainRules::CallFunc_GetValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CloudRainRules, CallFunc_GetValue_ReturnValue_1) == 0x00000C, "Member 'WDG_WeatherAndTrackConditionsPage_C_CloudRainRules::CallFunc_GetValue_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CloudRainRules, CallFunc_LessEqual_IntInt_ReturnValue) == 0x000010, "Member 'WDG_WeatherAndTrackConditionsPage_C_CloudRainRules::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CloudRainRules, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x000011, "Member 'WDG_WeatherAndTrackConditionsPage_C_CloudRainRules::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CloudRainRules, CallFunc_Conv_IntToFloat_ReturnValue_1) == 0x000014, "Member 'WDG_WeatherAndTrackConditionsPage_C_CloudRainRules::CallFunc_Conv_IntToFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CloudRainRules, CallFunc_Conv_IntToFloat_ReturnValue_2) == 0x000018, "Member 'WDG_WeatherAndTrackConditionsPage_C_CloudRainRules::CallFunc_Conv_IntToFloat_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CloudRainRules, CallFunc_GetValue_ReturnValue_2) == 0x00001C, "Member 'WDG_WeatherAndTrackConditionsPage_C_CloudRainRules::CallFunc_GetValue_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CloudRainRules, CallFunc_GetValue_ReturnValue_3) == 0x000020, "Member 'WDG_WeatherAndTrackConditionsPage_C_CloudRainRules::CallFunc_GetValue_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CloudRainRules, CallFunc_GetValue_ReturnValue_4) == 0x000024, "Member 'WDG_WeatherAndTrackConditionsPage_C_CloudRainRules::CallFunc_GetValue_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CloudRainRules, CallFunc_Conv_IntToFloat_ReturnValue_3) == 0x000028, "Member 'WDG_WeatherAndTrackConditionsPage_C_CloudRainRules::CallFunc_Conv_IntToFloat_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CloudRainRules, CallFunc_GreaterEqual_IntInt_ReturnValue) == 0x00002C, "Member 'WDG_WeatherAndTrackConditionsPage_C_CloudRainRules::CallFunc_GreaterEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CloudRainRules, CallFunc_Conv_IntToFloat_ReturnValue_4) == 0x000030, "Member 'WDG_WeatherAndTrackConditionsPage_C_CloudRainRules::CallFunc_Conv_IntToFloat_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CloudRainRules, CallFunc_ReMapValue_ReturnValue) == 0x000034, "Member 'WDG_WeatherAndTrackConditionsPage_C_CloudRainRules::CallFunc_ReMapValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CloudRainRules, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000038, "Member 'WDG_WeatherAndTrackConditionsPage_C_CloudRainRules::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CloudRainRules, CallFunc_FTrunc_ReturnValue) == 0x00003C, "Member 'WDG_WeatherAndTrackConditionsPage_C_CloudRainRules::CallFunc_FTrunc_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CloudRainRules, CallFunc_Greater_IntInt_ReturnValue) == 0x000040, "Member 'WDG_WeatherAndTrackConditionsPage_C_CloudRainRules::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CloudRainRules, CallFunc_ReMapValue_ReturnValue_1) == 0x000044, "Member 'WDG_WeatherAndTrackConditionsPage_C_CloudRainRules::CallFunc_ReMapValue_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CloudRainRules, CallFunc_FTrunc_ReturnValue_1) == 0x000048, "Member 'WDG_WeatherAndTrackConditionsPage_C_CloudRainRules::CallFunc_FTrunc_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_CloudRainRules, CallFunc_Less_IntInt_ReturnValue) == 0x00004C, "Member 'WDG_WeatherAndTrackConditionsPage_C_CloudRainRules::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");

// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.WetnessRules
// 0x0050 (0x0050 - 0x0000)
struct WDG_WeatherAndTrackConditionsPage_C_WetnessRules final
{
public:
	bool                                          ByWetness;                                         // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1[0x3];                                        // 0x0001(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_GetValue_ReturnValue;                     // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Conv_IntToFloat_ReturnValue;              // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetValue_ReturnValue_1;                   // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Conv_IntToFloat_ReturnValue_1;            // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0014(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_15[0x3];                                       // 0x0015(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_GetValue_ReturnValue_2;                   // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Conv_IntToFloat_ReturnValue_2;            // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetValue_ReturnValue_3;                   // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetValue_ReturnValue_4;                   // 0x0024(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Conv_IntToFloat_ReturnValue_3;            // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GreaterEqual_IntInt_ReturnValue;          // 0x002C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2D[0x3];                                       // 0x002D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Conv_IntToFloat_ReturnValue_4;            // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_ReMapValue_ReturnValue;                   // 0x0034(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_39[0x3];                                       // 0x0039(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_FTrunc_ReturnValue;                       // 0x003C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_41[0x3];                                       // 0x0041(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_ReMapValue_ReturnValue_1;                 // 0x0044(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_FTrunc_ReturnValue_1;                     // 0x0048(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_1;                // 0x004C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_WeatherAndTrackConditionsPage_C_WetnessRules) == 0x000004, "Wrong alignment on WDG_WeatherAndTrackConditionsPage_C_WetnessRules");
static_assert(sizeof(WDG_WeatherAndTrackConditionsPage_C_WetnessRules) == 0x000050, "Wrong size on WDG_WeatherAndTrackConditionsPage_C_WetnessRules");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_WetnessRules, ByWetness) == 0x000000, "Member 'WDG_WeatherAndTrackConditionsPage_C_WetnessRules::ByWetness' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_WetnessRules, CallFunc_GetValue_ReturnValue) == 0x000004, "Member 'WDG_WeatherAndTrackConditionsPage_C_WetnessRules::CallFunc_GetValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_WetnessRules, CallFunc_Conv_IntToFloat_ReturnValue) == 0x000008, "Member 'WDG_WeatherAndTrackConditionsPage_C_WetnessRules::CallFunc_Conv_IntToFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_WetnessRules, CallFunc_GetValue_ReturnValue_1) == 0x00000C, "Member 'WDG_WeatherAndTrackConditionsPage_C_WetnessRules::CallFunc_GetValue_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_WetnessRules, CallFunc_Conv_IntToFloat_ReturnValue_1) == 0x000010, "Member 'WDG_WeatherAndTrackConditionsPage_C_WetnessRules::CallFunc_Conv_IntToFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_WetnessRules, CallFunc_Less_IntInt_ReturnValue) == 0x000014, "Member 'WDG_WeatherAndTrackConditionsPage_C_WetnessRules::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_WetnessRules, CallFunc_GetValue_ReturnValue_2) == 0x000018, "Member 'WDG_WeatherAndTrackConditionsPage_C_WetnessRules::CallFunc_GetValue_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_WetnessRules, CallFunc_Conv_IntToFloat_ReturnValue_2) == 0x00001C, "Member 'WDG_WeatherAndTrackConditionsPage_C_WetnessRules::CallFunc_Conv_IntToFloat_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_WetnessRules, CallFunc_GetValue_ReturnValue_3) == 0x000020, "Member 'WDG_WeatherAndTrackConditionsPage_C_WetnessRules::CallFunc_GetValue_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_WetnessRules, CallFunc_GetValue_ReturnValue_4) == 0x000024, "Member 'WDG_WeatherAndTrackConditionsPage_C_WetnessRules::CallFunc_GetValue_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_WetnessRules, CallFunc_Conv_IntToFloat_ReturnValue_3) == 0x000028, "Member 'WDG_WeatherAndTrackConditionsPage_C_WetnessRules::CallFunc_Conv_IntToFloat_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_WetnessRules, CallFunc_GreaterEqual_IntInt_ReturnValue) == 0x00002C, "Member 'WDG_WeatherAndTrackConditionsPage_C_WetnessRules::CallFunc_GreaterEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_WetnessRules, CallFunc_Conv_IntToFloat_ReturnValue_4) == 0x000030, "Member 'WDG_WeatherAndTrackConditionsPage_C_WetnessRules::CallFunc_Conv_IntToFloat_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_WetnessRules, CallFunc_ReMapValue_ReturnValue) == 0x000034, "Member 'WDG_WeatherAndTrackConditionsPage_C_WetnessRules::CallFunc_ReMapValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_WetnessRules, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000038, "Member 'WDG_WeatherAndTrackConditionsPage_C_WetnessRules::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_WetnessRules, CallFunc_FTrunc_ReturnValue) == 0x00003C, "Member 'WDG_WeatherAndTrackConditionsPage_C_WetnessRules::CallFunc_FTrunc_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_WetnessRules, CallFunc_Greater_IntInt_ReturnValue) == 0x000040, "Member 'WDG_WeatherAndTrackConditionsPage_C_WetnessRules::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_WetnessRules, CallFunc_ReMapValue_ReturnValue_1) == 0x000044, "Member 'WDG_WeatherAndTrackConditionsPage_C_WetnessRules::CallFunc_ReMapValue_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_WetnessRules, CallFunc_FTrunc_ReturnValue_1) == 0x000048, "Member 'WDG_WeatherAndTrackConditionsPage_C_WetnessRules::CallFunc_FTrunc_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_WetnessRules, CallFunc_Less_IntInt_ReturnValue_1) == 0x00004C, "Member 'WDG_WeatherAndTrackConditionsPage_C_WetnessRules::CallFunc_Less_IntInt_ReturnValue_1' has a wrong offset!");

// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.FocusOnDynamicSlider
// 0x0010 (0x0010 - 0x0000)
struct WDG_WeatherAndTrackConditionsPage_C_FocusOnDynamicSlider final
{
public:
	EUINavigation                                 Navigation_0;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                ReturnValue;                                       // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_WeatherAndTrackConditionsPage_C_FocusOnDynamicSlider) == 0x000008, "Wrong alignment on WDG_WeatherAndTrackConditionsPage_C_FocusOnDynamicSlider");
static_assert(sizeof(WDG_WeatherAndTrackConditionsPage_C_FocusOnDynamicSlider) == 0x000010, "Wrong size on WDG_WeatherAndTrackConditionsPage_C_FocusOnDynamicSlider");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_FocusOnDynamicSlider, Navigation_0) == 0x000000, "Member 'WDG_WeatherAndTrackConditionsPage_C_FocusOnDynamicSlider::Navigation_0' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_FocusOnDynamicSlider, ReturnValue) == 0x000008, "Member 'WDG_WeatherAndTrackConditionsPage_C_FocusOnDynamicSlider::ReturnValue' has a wrong offset!");

// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.OnPreviewKeyDown
// 0x01E8 (0x01E8 - 0x0000)
struct WDG_WeatherAndTrackConditionsPage_C_OnPreviewKeyDown final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FKeyEvent                              InKeyEvent;                                        // 0x0038(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm)
	struct FEventReply                            ReturnValue;                                       // 0x0070(0x00B8)(Parm, OutParm, ReturnParm)
	struct FEventReply                            CallFunc_OnPreviewKeyDown_ReturnValue;             // 0x0128(0x00B8)()
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x01E0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_HasFocusedDescendants_ReturnValue;        // 0x01E1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x01E2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x01E3(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_WeatherAndTrackConditionsPage_C_OnPreviewKeyDown) == 0x000008, "Wrong alignment on WDG_WeatherAndTrackConditionsPage_C_OnPreviewKeyDown");
static_assert(sizeof(WDG_WeatherAndTrackConditionsPage_C_OnPreviewKeyDown) == 0x0001E8, "Wrong size on WDG_WeatherAndTrackConditionsPage_C_OnPreviewKeyDown");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_OnPreviewKeyDown, MyGeometry) == 0x000000, "Member 'WDG_WeatherAndTrackConditionsPage_C_OnPreviewKeyDown::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_OnPreviewKeyDown, InKeyEvent) == 0x000038, "Member 'WDG_WeatherAndTrackConditionsPage_C_OnPreviewKeyDown::InKeyEvent' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_OnPreviewKeyDown, ReturnValue) == 0x000070, "Member 'WDG_WeatherAndTrackConditionsPage_C_OnPreviewKeyDown::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_OnPreviewKeyDown, CallFunc_OnPreviewKeyDown_ReturnValue) == 0x000128, "Member 'WDG_WeatherAndTrackConditionsPage_C_OnPreviewKeyDown::CallFunc_OnPreviewKeyDown_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_OnPreviewKeyDown, CallFunc_IsValid_ReturnValue) == 0x0001E0, "Member 'WDG_WeatherAndTrackConditionsPage_C_OnPreviewKeyDown::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_OnPreviewKeyDown, CallFunc_HasFocusedDescendants_ReturnValue) == 0x0001E1, "Member 'WDG_WeatherAndTrackConditionsPage_C_OnPreviewKeyDown::CallFunc_HasFocusedDescendants_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_OnPreviewKeyDown, CallFunc_Not_PreBool_ReturnValue) == 0x0001E2, "Member 'WDG_WeatherAndTrackConditionsPage_C_OnPreviewKeyDown::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_OnPreviewKeyDown, CallFunc_BooleanAND_ReturnValue) == 0x0001E3, "Member 'WDG_WeatherAndTrackConditionsPage_C_OnPreviewKeyDown::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");

// Function WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C.GetBiasedWetnessPuddles
// 0x0010 (0x0010 - 0x0000)
struct WDG_WeatherAndTrackConditionsPage_C_GetBiasedWetnessPuddles final
{
public:
	float                                         DryWeight;                                         // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CapForDry;                                         // 0x0004(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         MaxWetnessPuddles;                                 // 0x0008(0x0004)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_RandomBoolWithWeight_ReturnValue;         // 0x000C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_WeatherAndTrackConditionsPage_C_GetBiasedWetnessPuddles) == 0x000004, "Wrong alignment on WDG_WeatherAndTrackConditionsPage_C_GetBiasedWetnessPuddles");
static_assert(sizeof(WDG_WeatherAndTrackConditionsPage_C_GetBiasedWetnessPuddles) == 0x000010, "Wrong size on WDG_WeatherAndTrackConditionsPage_C_GetBiasedWetnessPuddles");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_GetBiasedWetnessPuddles, DryWeight) == 0x000000, "Member 'WDG_WeatherAndTrackConditionsPage_C_GetBiasedWetnessPuddles::DryWeight' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_GetBiasedWetnessPuddles, CapForDry) == 0x000004, "Member 'WDG_WeatherAndTrackConditionsPage_C_GetBiasedWetnessPuddles::CapForDry' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_GetBiasedWetnessPuddles, MaxWetnessPuddles) == 0x000008, "Member 'WDG_WeatherAndTrackConditionsPage_C_GetBiasedWetnessPuddles::MaxWetnessPuddles' has a wrong offset!");
static_assert(offsetof(WDG_WeatherAndTrackConditionsPage_C_GetBiasedWetnessPuddles, CallFunc_RandomBoolWithWeight_ReturnValue) == 0x00000C, "Member 'WDG_WeatherAndTrackConditionsPage_C_GetBiasedWetnessPuddles::CallFunc_RandomBoolWithWeight_ReturnValue' has a wrong offset!");

}

