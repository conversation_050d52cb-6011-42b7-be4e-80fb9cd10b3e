﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SessionCountdown

#include "Basic.hpp"

#include "UMG_structs.hpp"
#include "Engine_structs.hpp"
#include "SlateCore_structs.hpp"
#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_SessionCountdown.WDG_SessionCountdown_C.ExecuteUbergraph_WDG_SessionCountdown
// 0x0198 (0x0198 - 0x0000)
struct WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESlateVisibility                              CallFunc_GetVisibility_ReturnValue;                // 0x0004(0x0001)(ZeroConstructor, <PERSON><PERSON><PERSON>OldD<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	ESlateVisibility                              CallFunc_GetVisibility_ReturnValue_1;              // 0x0006(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess_1;                    // 0x0007(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_9[0x3];                                        // 0x0009(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_getCarCount_ReturnValue;                  // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_isFinishAsSpectatorEnabled_ReturnValue;   // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0012(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_13[0x1];                                       // 0x0013(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_getCarCount_ReturnValue_1;                // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_1;             // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_isFinishAsSpectatorEnabled_ReturnValue_1; // 0x0019(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	ESlateVisibility                              CallFunc_GetVisibility_ReturnValue_2;              // 0x001A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess_2;                    // 0x001B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1C[0x4];                                       // 0x001C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateColor                            K2Node_MakeStruct_SlateColor;                      // 0x0020(0x0028)()
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_1;                    // 0x0048(0x0028)(UObjectWrapper)
	bool                                          CallFunc_GetIsEnabled_ReturnValue;                 // 0x0070(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	ESlateVisibility                              CallFunc_GetVisibility_ReturnValue_3;              // 0x0071(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess_3;                    // 0x0072(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_73[0x1];                                       // 0x0073(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate;              // 0x0074(0x0010)(ZeroConstructor, NoDestructor)
	uint8                                         Pad_84[0x4];                                       // 0x0084(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTimerHandle                           CallFunc_K2_SetTimerDelegate_ReturnValue;          // 0x0088(0x0008)(NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_getCurrentSessionCountdown_ReturnValue;   // 0x0090(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_2;             // 0x0094(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x0095(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0096(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x0097(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	class FString                                 CallFunc_ConvertInt32ToFormattedTime_ReturnValue;  // 0x0098(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x00A8(0x0018)()
	bool                                          CallFunc_GreaterEqual_IntInt_ReturnValue;          // 0x00C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	ERaceSessionPhase                             CallFunc_GetCurrentSessionPhaseUI_ReturnValue;     // 0x00C1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C2[0x2];                                       // 0x00C2(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_getCurrentSessionCountdown_ReturnValue_1; // 0x00C4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x00C8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_3;             // 0x00C9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_1;           // 0x00CA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_2;                 // 0x00CB(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_3;                 // 0x00CC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_CD[0x3];                                       // 0x00CD(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_ConvertInt32ToFormattedTime_ReturnValue_1; // 0x00D0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_1;          // 0x00E0(0x0018)()
	bool                                          CallFunc_GreaterEqual_IntInt_ReturnValue_1;        // 0x00F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_1;        // 0x00F9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_2;        // 0x00FA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x00FB(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanOR_ReturnValue_1;                  // 0x00FC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_3;        // 0x00FD(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	ERaceSessionPhase                             CallFunc_GetCurrentSessionPhaseUI_ReturnValue_1;   // 0x00FE(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue_2;                  // 0x00FF(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_4;        // 0x0100(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_4;                 // 0x0101(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_5;        // 0x0102(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanOR_ReturnValue_3;                  // 0x0103(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_6;        // 0x0104(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_7;        // 0x0105(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanOR_ReturnValue_4;                  // 0x0106(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanOR_ReturnValue_5;                  // 0x0107(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_5;                 // 0x0108(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_2;            // 0x0109(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_3;            // 0x010A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_10B[0x5];                                      // 0x010B(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_2;                    // 0x0110(0x0028)()
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_3;                    // 0x0138(0x0028)(UObjectWrapper)
	bool                                          CallFunc_GetIsEnabled_ReturnValue_1;               // 0x0160(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_161[0x7];                                      // 0x0161(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue;              // 0x0168(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance;             // 0x0170(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0178(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_isOnline_ReturnValue;                     // 0x0179(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_17A[0x6];                                      // 0x017A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class AGameModeBase*                          CallFunc_GetGameMode_ReturnValue;                  // 0x0180(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AAcRaceGameMode*                        K2Node_DynamicCast_AsAc_Race_Game_Mode;            // 0x0188(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x0190(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_GetIsEnabled_ReturnValue_2;               // 0x0191(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0192(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_6;                 // 0x0193(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown) == 0x000008, "Wrong alignment on WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown");
static_assert(sizeof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown) == 0x000198, "Wrong size on WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, EntryPoint) == 0x000000, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_GetVisibility_ReturnValue) == 0x000004, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_GetVisibility_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, K2Node_SwitchEnum_CmpSuccess) == 0x000005, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_GetVisibility_ReturnValue_1) == 0x000006, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_GetVisibility_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, K2Node_SwitchEnum_CmpSuccess_1) == 0x000007, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::K2Node_SwitchEnum_CmpSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_MakeLiteralByte_ReturnValue) == 0x000008, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_getCarCount_ReturnValue) == 0x00000C, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_getCarCount_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_Greater_IntInt_ReturnValue) == 0x000010, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_isFinishAsSpectatorEnabled_ReturnValue) == 0x000011, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_isFinishAsSpectatorEnabled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000012, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_getCarCount_ReturnValue_1) == 0x000014, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_getCarCount_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_Greater_IntInt_ReturnValue_1) == 0x000018, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_Greater_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_isFinishAsSpectatorEnabled_ReturnValue_1) == 0x000019, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_isFinishAsSpectatorEnabled_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_GetVisibility_ReturnValue_2) == 0x00001A, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_GetVisibility_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, K2Node_SwitchEnum_CmpSuccess_2) == 0x00001B, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::K2Node_SwitchEnum_CmpSuccess_2' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, K2Node_MakeStruct_SlateColor) == 0x000020, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::K2Node_MakeStruct_SlateColor' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, K2Node_MakeStruct_SlateColor_1) == 0x000048, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::K2Node_MakeStruct_SlateColor_1' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_GetIsEnabled_ReturnValue) == 0x000070, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_GetIsEnabled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_GetVisibility_ReturnValue_3) == 0x000071, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_GetVisibility_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, K2Node_SwitchEnum_CmpSuccess_3) == 0x000072, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::K2Node_SwitchEnum_CmpSuccess_3' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, K2Node_CreateDelegate_OutputDelegate) == 0x000074, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_K2_SetTimerDelegate_ReturnValue) == 0x000088, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_K2_SetTimerDelegate_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_getCurrentSessionCountdown_ReturnValue) == 0x000090, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_getCurrentSessionCountdown_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_Greater_IntInt_ReturnValue_2) == 0x000094, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_Greater_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_LessEqual_IntInt_ReturnValue) == 0x000095, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_BooleanAND_ReturnValue) == 0x000096, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_BooleanAND_ReturnValue_1) == 0x000097, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_ConvertInt32ToFormattedTime_ReturnValue) == 0x000098, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_ConvertInt32ToFormattedTime_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_Conv_StringToText_ReturnValue) == 0x0000A8, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_GreaterEqual_IntInt_ReturnValue) == 0x0000C0, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_GreaterEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_GetCurrentSessionPhaseUI_ReturnValue) == 0x0000C1, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_GetCurrentSessionPhaseUI_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_getCurrentSessionCountdown_ReturnValue_1) == 0x0000C4, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_getCurrentSessionCountdown_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x0000C8, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_Greater_IntInt_ReturnValue_3) == 0x0000C9, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_Greater_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_LessEqual_IntInt_ReturnValue_1) == 0x0000CA, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_LessEqual_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_BooleanAND_ReturnValue_2) == 0x0000CB, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_BooleanAND_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_BooleanAND_ReturnValue_3) == 0x0000CC, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_BooleanAND_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_ConvertInt32ToFormattedTime_ReturnValue_1) == 0x0000D0, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_ConvertInt32ToFormattedTime_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_Conv_StringToText_ReturnValue_1) == 0x0000E0, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_Conv_StringToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_GreaterEqual_IntInt_ReturnValue_1) == 0x0000F8, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_GreaterEqual_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_EqualEqual_ByteByte_ReturnValue_1) == 0x0000F9, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_EqualEqual_ByteByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_EqualEqual_ByteByte_ReturnValue_2) == 0x0000FA, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_EqualEqual_ByteByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_BooleanOR_ReturnValue) == 0x0000FB, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_BooleanOR_ReturnValue_1) == 0x0000FC, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_BooleanOR_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_EqualEqual_ByteByte_ReturnValue_3) == 0x0000FD, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_EqualEqual_ByteByte_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_GetCurrentSessionPhaseUI_ReturnValue_1) == 0x0000FE, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_GetCurrentSessionPhaseUI_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_BooleanOR_ReturnValue_2) == 0x0000FF, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_BooleanOR_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_EqualEqual_ByteByte_ReturnValue_4) == 0x000100, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_EqualEqual_ByteByte_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_BooleanAND_ReturnValue_4) == 0x000101, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_BooleanAND_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_EqualEqual_ByteByte_ReturnValue_5) == 0x000102, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_EqualEqual_ByteByte_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_BooleanOR_ReturnValue_3) == 0x000103, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_BooleanOR_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_EqualEqual_ByteByte_ReturnValue_6) == 0x000104, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_EqualEqual_ByteByte_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_EqualEqual_ByteByte_ReturnValue_7) == 0x000105, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_EqualEqual_ByteByte_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_BooleanOR_ReturnValue_4) == 0x000106, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_BooleanOR_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_BooleanOR_ReturnValue_5) == 0x000107, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_BooleanOR_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_BooleanAND_ReturnValue_5) == 0x000108, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_BooleanAND_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_MakeLiteralByte_ReturnValue_2) == 0x000109, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_MakeLiteralByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_MakeLiteralByte_ReturnValue_3) == 0x00010A, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_MakeLiteralByte_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, K2Node_MakeStruct_SlateColor_2) == 0x000110, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::K2Node_MakeStruct_SlateColor_2' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, K2Node_MakeStruct_SlateColor_3) == 0x000138, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::K2Node_MakeStruct_SlateColor_3' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_GetIsEnabled_ReturnValue_1) == 0x000160, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_GetIsEnabled_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_GetGameInstance_ReturnValue) == 0x000168, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_GetGameInstance_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, K2Node_DynamicCast_AsAc_Game_Instance) == 0x000170, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::K2Node_DynamicCast_AsAc_Game_Instance' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, K2Node_DynamicCast_bSuccess) == 0x000178, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_isOnline_ReturnValue) == 0x000179, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_isOnline_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_GetGameMode_ReturnValue) == 0x000180, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_GetGameMode_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, K2Node_DynamicCast_AsAc_Race_Game_Mode) == 0x000188, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::K2Node_DynamicCast_AsAc_Race_Game_Mode' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, K2Node_DynamicCast_bSuccess_1) == 0x000190, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_GetIsEnabled_ReturnValue_2) == 0x000191, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_GetIsEnabled_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, K2Node_Event_IsDesignTime) == 0x000192, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown, CallFunc_BooleanAND_ReturnValue_6) == 0x000193, "Member 'WDG_SessionCountdown_C_ExecuteUbergraph_WDG_SessionCountdown::CallFunc_BooleanAND_ReturnValue_6' has a wrong offset!");

// Function WDG_SessionCountdown.WDG_SessionCountdown_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_SessionCountdown_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SessionCountdown_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_SessionCountdown_C_PreConstruct");
static_assert(sizeof(WDG_SessionCountdown_C_PreConstruct) == 0x000001, "Wrong size on WDG_SessionCountdown_C_PreConstruct");
static_assert(offsetof(WDG_SessionCountdown_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_SessionCountdown_C_PreConstruct::IsDesignTime' has a wrong offset!");

}

