﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SpecialEventsPanel

#include "Basic.hpp"

#include "WDG_SpecialEventsPanel_classes.hpp"
#include "WDG_SpecialEventsPanel_parameters.hpp"


namespace SDK
{

// Function WDG_SpecialEventsPanel.WDG_SpecialEventsPanel_C.ExecuteUbergraph_WDG_SpecialEventsPanel
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SpecialEventsPanel_C::ExecuteUbergraph_WDG_SpecialEventsPanel(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventsPanel_C", "ExecuteUbergraph_WDG_SpecialEventsPanel");

	Params::WDG_SpecialEventsPanel_C_ExecuteUbergraph_WDG_SpecialEventsPanel Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEventsPanel.WDG_SpecialEventsPanel_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SpecialEventsPanel_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventsPanel_C", "PreConstruct");

	Params::WDG_SpecialEventsPanel_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEventsPanel.WDG_SpecialEventsPanel_C.BP_MouseLeave
// (Event, Public, BlueprintEvent)

void UWDG_SpecialEventsPanel_C::BP_MouseLeave()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventsPanel_C", "BP_MouseLeave");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEventsPanel.WDG_SpecialEventsPanel_C.BP_MouseOver
// (Event, Public, BlueprintEvent)

void UWDG_SpecialEventsPanel_C::BP_MouseOver()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventsPanel_C", "BP_MouseOver");

	UObject::ProcessEvent(Func, nullptr);
}

}

