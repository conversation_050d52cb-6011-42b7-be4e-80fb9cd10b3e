﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RatingSummaryItem

#include "Basic.hpp"


namespace SDK::Params
{

// Function WDG_RatingSummaryItem.WDG_RatingSummaryItem_C.ExecuteUbergraph_WDG_RatingSummaryItem
// 0x0008 (0x0008 - 0x0000)
struct WDG_RatingSummaryItem_C_ExecuteUbergraph_WDG_RatingSummaryItem final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_RatingSummaryItem_C_ExecuteUbergraph_WDG_RatingSummaryItem) == 0x000004, "Wrong alignment on WDG_RatingSummaryItem_C_ExecuteUbergraph_WDG_RatingSummaryItem");
static_assert(sizeof(WDG_RatingSummaryItem_C_ExecuteUbergraph_WDG_RatingSummaryItem) == 0x000008, "Wrong size on WDG_RatingSummaryItem_C_ExecuteUbergraph_WDG_RatingSummaryItem");
static_assert(offsetof(WDG_RatingSummaryItem_C_ExecuteUbergraph_WDG_RatingSummaryItem, EntryPoint) == 0x000000, "Member 'WDG_RatingSummaryItem_C_ExecuteUbergraph_WDG_RatingSummaryItem::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_RatingSummaryItem_C_ExecuteUbergraph_WDG_RatingSummaryItem, K2Node_Event_IsDesignTime) == 0x000004, "Member 'WDG_RatingSummaryItem_C_ExecuteUbergraph_WDG_RatingSummaryItem::K2Node_Event_IsDesignTime' has a wrong offset!");

// Function WDG_RatingSummaryItem.WDG_RatingSummaryItem_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_RatingSummaryItem_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_RatingSummaryItem_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_RatingSummaryItem_C_PreConstruct");
static_assert(sizeof(WDG_RatingSummaryItem_C_PreConstruct) == 0x000001, "Wrong size on WDG_RatingSummaryItem_C_PreConstruct");
static_assert(offsetof(WDG_RatingSummaryItem_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_RatingSummaryItem_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WDG_RatingSummaryItem.WDG_RatingSummaryItem_C.SetValueText
// 0x0038 (0x0038 - 0x0000)
struct WDG_RatingSummaryItem_C_SetValueText final
{
public:
	class FText                                   ReturnValue;                                       // 0x0000(0x0018)(Parm, OutParm, ReturnParm)
	class FText                                   CallFunc_Conv_IntToText_ReturnValue;               // 0x0018(0x0018)()
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_RatingSummaryItem_C_SetValueText) == 0x000008, "Wrong alignment on WDG_RatingSummaryItem_C_SetValueText");
static_assert(sizeof(WDG_RatingSummaryItem_C_SetValueText) == 0x000038, "Wrong size on WDG_RatingSummaryItem_C_SetValueText");
static_assert(offsetof(WDG_RatingSummaryItem_C_SetValueText, ReturnValue) == 0x000000, "Member 'WDG_RatingSummaryItem_C_SetValueText::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RatingSummaryItem_C_SetValueText, CallFunc_Conv_IntToText_ReturnValue) == 0x000018, "Member 'WDG_RatingSummaryItem_C_SetValueText::CallFunc_Conv_IntToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RatingSummaryItem_C_SetValueText, CallFunc_Less_IntInt_ReturnValue) == 0x000030, "Member 'WDG_RatingSummaryItem_C_SetValueText::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");

// Function WDG_RatingSummaryItem.WDG_RatingSummaryItem_C.GetValueAsPercent
// 0x000C (0x000C - 0x0000)
struct WDG_RatingSummaryItem_C_GetValueAsPercent final
{
public:
	float                                         ReturnValue;                                       // 0x0000(0x0004)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Conv_IntToFloat_ReturnValue;              // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Divide_FloatFloat_ReturnValue;            // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_RatingSummaryItem_C_GetValueAsPercent) == 0x000004, "Wrong alignment on WDG_RatingSummaryItem_C_GetValueAsPercent");
static_assert(sizeof(WDG_RatingSummaryItem_C_GetValueAsPercent) == 0x00000C, "Wrong size on WDG_RatingSummaryItem_C_GetValueAsPercent");
static_assert(offsetof(WDG_RatingSummaryItem_C_GetValueAsPercent, ReturnValue) == 0x000000, "Member 'WDG_RatingSummaryItem_C_GetValueAsPercent::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RatingSummaryItem_C_GetValueAsPercent, CallFunc_Conv_IntToFloat_ReturnValue) == 0x000004, "Member 'WDG_RatingSummaryItem_C_GetValueAsPercent::CallFunc_Conv_IntToFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_RatingSummaryItem_C_GetValueAsPercent, CallFunc_Divide_FloatFloat_ReturnValue) == 0x000008, "Member 'WDG_RatingSummaryItem_C_GetValueAsPercent::CallFunc_Divide_FloatFloat_ReturnValue' has a wrong offset!");

}

