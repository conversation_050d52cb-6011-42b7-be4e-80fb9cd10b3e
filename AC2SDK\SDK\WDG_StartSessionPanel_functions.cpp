﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_StartSessionPanel

#include "Basic.hpp"

#include "WDG_StartSessionPanel_classes.hpp"
#include "WDG_StartSessionPanel_parameters.hpp"


namespace SDK
{

// Function WDG_StartSessionPanel.WDG_StartSessionPanel_C.ExecuteUbergraph_WDG_StartSessionPanel
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_StartSessionPanel_C::ExecuteUbergraph_WDG_StartSessionPanel(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_StartSessionPanel_C", "ExecuteUbergraph_WDG_StartSessionPanel");

	Params::WDG_StartSessionPanel_C_ExecuteUbergraph_WDG_StartSessionPanel Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_StartSessionPanel.WDG_StartSessionPanel_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_StartSessionPanel_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_StartSessionPanel_C", "PreConstruct");

	Params::WDG_StartSessionPanel_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}

}

