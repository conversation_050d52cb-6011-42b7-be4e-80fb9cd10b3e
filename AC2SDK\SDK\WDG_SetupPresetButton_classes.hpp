﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SetupPresetButton

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SetupPresetButton.WDG_SetupPresetButton_C
// 0x0038 (0x0618 - 0x05E0)
class UWDG_SetupPresetButton_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       OpacityAnim;                                       // 0x05E8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 imgBottom;                                         // 0x05F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgLeft;                                           // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgRight;                                          // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgTop;                                            // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ScalableButton_C*                  SetupElement_0;                                    // 0x0610(0x0008)(Edit, BlueprintVisible, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_SetupPresetButton(int32 EntryPoint);
	void Construct();
	void BP_MouseOver();
	void BP_MouseLeave();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SetupPresetButton_C">();
	}
	static class UWDG_SetupPresetButton_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SetupPresetButton_C>();
	}
};
static_assert(alignof(UWDG_SetupPresetButton_C) == 0x000008, "Wrong alignment on UWDG_SetupPresetButton_C");
static_assert(sizeof(UWDG_SetupPresetButton_C) == 0x000618, "Wrong size on UWDG_SetupPresetButton_C");
static_assert(offsetof(UWDG_SetupPresetButton_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_SetupPresetButton_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPresetButton_C, OpacityAnim) == 0x0005E8, "Member 'UWDG_SetupPresetButton_C::OpacityAnim' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPresetButton_C, imgBottom) == 0x0005F0, "Member 'UWDG_SetupPresetButton_C::imgBottom' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPresetButton_C, imgLeft) == 0x0005F8, "Member 'UWDG_SetupPresetButton_C::imgLeft' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPresetButton_C, imgRight) == 0x000600, "Member 'UWDG_SetupPresetButton_C::imgRight' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPresetButton_C, imgTop) == 0x000608, "Member 'UWDG_SetupPresetButton_C::imgTop' has a wrong offset!");
static_assert(offsetof(UWDG_SetupPresetButton_C, SetupElement_0) == 0x000610, "Member 'UWDG_SetupPresetButton_C::SetupElement_0' has a wrong offset!");

}

