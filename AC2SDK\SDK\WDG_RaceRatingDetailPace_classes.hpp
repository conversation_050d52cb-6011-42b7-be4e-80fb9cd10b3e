﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RaceRatingDetailPace

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RaceRatingDetailPace.WDG_RaceRatingDetailPace_C
// 0x0000 (0x0280 - 0x0280)
class UWDG_RaceRatingDetailPace_C final : public URatingDetailPace
{
public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RaceRatingDetailPace_C">();
	}
	static class UWDG_RaceRatingDetailPace_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RaceRatingDetailPace_C>();
	}
};
static_assert(alignof(UWDG_RaceRatingDetailPace_C) == 0x000008, "Wrong alignment on UWDG_RaceRatingDetailPace_C");
static_assert(sizeof(UWDG_RaceRatingDetailPace_C) == 0x000280, "Wrong size on UWDG_RaceRatingDetailPace_C");

}

