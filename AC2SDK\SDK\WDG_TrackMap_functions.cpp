﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TrackMap

#include "Basic.hpp"

#include "WDG_TrackMap_classes.hpp"
#include "WDG_TrackMap_parameters.hpp"


namespace SDK
{

// Function WDG_TrackMap.WDG_TrackMap_C.ExecuteUbergraph_WDG_TrackMap
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_TrackMap_C::ExecuteUbergraph_WDG_TrackMap(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TrackMap_C", "ExecuteUbergraph_WDG_TrackMap");

	Params::WDG_TrackMap_C_ExecuteUbergraph_WDG_TrackMap Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_TrackMap.WDG_TrackMap_C.MapModeChanged
// (Event, Protected, BlueprintEvent)

void UWDG_TrackMap_C::MapModeChanged()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TrackMap_C", "MapModeChanged");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_TrackMap.WDG_TrackMap_C.IsWidgetDefinitionEnabled
// (Event, Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UAcGameInstance*                  GameInstance                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FHUDOptions&               HUDOptions                                             (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor)

bool UWDG_TrackMap_C::IsWidgetDefinitionEnabled(class UAcGameInstance* GameInstance, const struct FHUDOptions& HUDOptions)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TrackMap_C", "IsWidgetDefinitionEnabled");

	Params::WDG_TrackMap_C_IsWidgetDefinitionEnabled Parms{};

	Parms.GameInstance = GameInstance;
	Parms.HUDOptions = std::move(HUDOptions);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}

}

