﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ServerInfoSessionItem

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ServerInfoSessionItem.WDG_ServerInfoSessionItem_C
// 0x0050 (0x02B0 - 0x0260)
class UWDG_ServerInfoSessionItem_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0260(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UBorder*                                background;                                        // 0x0268(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtRemaining;                                      // 0x0270(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtTime;                                           // 0x0278(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtType;                                           // 0x0280(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class FText                                   sessionType;                                       // 0x0288(0x0018)(Edit, BlueprintVisible)
	int32                                         SessionRemaining;                                  // 0x02A0(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         SessionDuration;                                   // 0x02A4(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          isCurrent;                                         // 0x02A8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)

public:
	void ExecuteUbergraph_WDG_ServerInfoSessionItem(int32 EntryPoint);
	void Construct();
	void PreConstruct(bool IsDesignTime);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ServerInfoSessionItem_C">();
	}
	static class UWDG_ServerInfoSessionItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ServerInfoSessionItem_C>();
	}
};
static_assert(alignof(UWDG_ServerInfoSessionItem_C) == 0x000008, "Wrong alignment on UWDG_ServerInfoSessionItem_C");
static_assert(sizeof(UWDG_ServerInfoSessionItem_C) == 0x0002B0, "Wrong size on UWDG_ServerInfoSessionItem_C");
static_assert(offsetof(UWDG_ServerInfoSessionItem_C, UberGraphFrame) == 0x000260, "Member 'UWDG_ServerInfoSessionItem_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_ServerInfoSessionItem_C, background) == 0x000268, "Member 'UWDG_ServerInfoSessionItem_C::background' has a wrong offset!");
static_assert(offsetof(UWDG_ServerInfoSessionItem_C, txtRemaining) == 0x000270, "Member 'UWDG_ServerInfoSessionItem_C::txtRemaining' has a wrong offset!");
static_assert(offsetof(UWDG_ServerInfoSessionItem_C, txtTime) == 0x000278, "Member 'UWDG_ServerInfoSessionItem_C::txtTime' has a wrong offset!");
static_assert(offsetof(UWDG_ServerInfoSessionItem_C, txtType) == 0x000280, "Member 'UWDG_ServerInfoSessionItem_C::txtType' has a wrong offset!");
static_assert(offsetof(UWDG_ServerInfoSessionItem_C, sessionType) == 0x000288, "Member 'UWDG_ServerInfoSessionItem_C::sessionType' has a wrong offset!");
static_assert(offsetof(UWDG_ServerInfoSessionItem_C, SessionRemaining) == 0x0002A0, "Member 'UWDG_ServerInfoSessionItem_C::SessionRemaining' has a wrong offset!");
static_assert(offsetof(UWDG_ServerInfoSessionItem_C, SessionDuration) == 0x0002A4, "Member 'UWDG_ServerInfoSessionItem_C::SessionDuration' has a wrong offset!");
static_assert(offsetof(UWDG_ServerInfoSessionItem_C, isCurrent) == 0x0002A8, "Member 'UWDG_ServerInfoSessionItem_C::isCurrent' has a wrong offset!");

}

