﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WPB_RaceTransitionPage

#include "Basic.hpp"

#include "WPB_RaceTransitionPage_classes.hpp"
#include "WPB_RaceTransitionPage_parameters.hpp"


namespace SDK
{

// Function WPB_RaceTransitionPage.WPB_RaceTransitionPage_C.ExecuteUbergraph_WPB_RaceTransitionPage
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWPB_RaceTransitionPage_C::ExecuteUbergraph_WPB_RaceTransitionPage(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WPB_RaceTransitionPage_C", "ExecuteUbergraph_WPB_RaceTransitionPage");

	Params::WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WPB_RaceTransitionPage.WPB_RaceTransitionPage_C.BP_StartPage
// (Event, Public, BlueprintEvent)

void UWPB_RaceTransitionPage_C::BP_StartPage()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WPB_RaceTransitionPage_C", "BP_StartPage");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WPB_RaceTransitionPage.WPB_RaceTransitionPage_C.Tick
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// float                                   InDeltaTime                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWPB_RaceTransitionPage_C::Tick(const struct FGeometry& MyGeometry, float InDeltaTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WPB_RaceTransitionPage_C", "Tick");

	Params::WPB_RaceTransitionPage_C_Tick Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InDeltaTime = InDeltaTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WPB_RaceTransitionPage.WPB_RaceTransitionPage_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWPB_RaceTransitionPage_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WPB_RaceTransitionPage_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WPB_RaceTransitionPage.WPB_RaceTransitionPage_C.StartFade
// (Event, Protected, BlueprintEvent)
// Parameters:
// float                                   timeMult                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWPB_RaceTransitionPage_C::StartFade(float timeMult)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WPB_RaceTransitionPage_C", "StartFade");

	Params::WPB_RaceTransitionPage_C_StartFade Parms{};

	Parms.timeMult = timeMult;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WPB_RaceTransitionPage.WPB_RaceTransitionPage_C.EndFade
// (Event, Protected, BlueprintEvent)
// Parameters:
// float                                   timeMult                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWPB_RaceTransitionPage_C::EndFade(float timeMult)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WPB_RaceTransitionPage_C", "EndFade");

	Params::WPB_RaceTransitionPage_C_EndFade Parms{};

	Parms.timeMult = timeMult;

	UObject::ProcessEvent(Func, &Parms);
}

}

