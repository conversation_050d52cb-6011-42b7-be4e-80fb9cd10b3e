﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SaveGameITem

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_SaveGameITem.WDG_SaveGameItem_C.ExecuteUbergraph_WDG_SaveGameItem
// 0x0068 (0x0068 - 0x0000)
struct WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          Temp_bool_Variable;                                // 0x0006(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_7[0x1];                                        // 0x0007(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPanelBase*                           K2Node_DynamicCast_AsAc_Panel_Base;                // 0x0008(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_12[0x2];                                       // 0x0012(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_GetChildrenCount_ReturnValue;             // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_HasFocusedDescendants_ReturnValue;        // 0x001C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1D[0x3];                                       // 0x001D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FFocusEvent                            K2Node_Event_InFocusEvent_1;                       // 0x0020(0x0008)(NoDestructor)
	struct FFocusEvent                            K2Node_Event_InFocusEvent;                         // 0x0028(0x0008)(NoDestructor)
	bool                                          K2Node_Event_highlighted;                          // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_31[0x3];                                       // 0x0031(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable;                                 // 0x0034(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWidget*                                CallFunc_GetChildAt_ReturnValue;                   // 0x0038(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GetIsEnabled_ReturnValue;                 // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsVisible_ReturnValue;                    // 0x0041(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_42[0x2];                                       // 0x0042(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0044(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0048(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x0049(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x004A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x004B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4C[0x4];                                       // 0x004C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class AGameModeBase*                          CallFunc_GetGameMode_ReturnValue;                  // 0x0050(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AAcMenuGameMode*                        K2Node_DynamicCast_AsAc_Menu_Game_Mode;            // 0x0058(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x0060(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
};
static_assert(alignof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem) == 0x000008, "Wrong alignment on WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem");
static_assert(sizeof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem) == 0x000068, "Wrong size on WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, EntryPoint) == 0x000000, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, CallFunc_MakeLiteralByte_ReturnValue) == 0x000004, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, K2Node_SwitchEnum_CmpSuccess) == 0x000005, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, Temp_bool_Variable) == 0x000006, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, K2Node_DynamicCast_AsAc_Panel_Base) == 0x000008, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::K2Node_DynamicCast_AsAc_Panel_Base' has a wrong offset!");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, K2Node_DynamicCast_bSuccess) == 0x000010, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, CallFunc_Not_PreBool_ReturnValue) == 0x000011, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, CallFunc_GetChildrenCount_ReturnValue) == 0x000014, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::CallFunc_GetChildrenCount_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, CallFunc_Subtract_IntInt_ReturnValue) == 0x000018, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, CallFunc_HasFocusedDescendants_ReturnValue) == 0x00001C, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::CallFunc_HasFocusedDescendants_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, K2Node_Event_InFocusEvent_1) == 0x000020, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::K2Node_Event_InFocusEvent_1' has a wrong offset!");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, K2Node_Event_InFocusEvent) == 0x000028, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::K2Node_Event_InFocusEvent' has a wrong offset!");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, K2Node_Event_highlighted) == 0x000030, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::K2Node_Event_highlighted' has a wrong offset!");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, Temp_int_Variable) == 0x000034, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, CallFunc_GetChildAt_ReturnValue) == 0x000038, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::CallFunc_GetChildAt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, CallFunc_GetIsEnabled_ReturnValue) == 0x000040, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::CallFunc_GetIsEnabled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, CallFunc_IsVisible_ReturnValue) == 0x000041, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::CallFunc_IsVisible_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, CallFunc_Add_IntInt_ReturnValue) == 0x000044, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, CallFunc_BooleanAND_ReturnValue) == 0x000048, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, CallFunc_LessEqual_IntInt_ReturnValue) == 0x000049, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, CallFunc_BooleanAND_ReturnValue_1) == 0x00004A, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x00004B, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, CallFunc_GetGameMode_ReturnValue) == 0x000050, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::CallFunc_GetGameMode_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, K2Node_DynamicCast_AsAc_Menu_Game_Mode) == 0x000058, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::K2Node_DynamicCast_AsAc_Menu_Game_Mode' has a wrong offset!");
static_assert(offsetof(WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem, K2Node_DynamicCast_bSuccess_1) == 0x000060, "Member 'WDG_SaveGameItem_C_ExecuteUbergraph_WDG_SaveGameItem::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");

// Function WDG_SaveGameITem.WDG_SaveGameItem_C.BP_SetHighlight
// 0x0001 (0x0001 - 0x0000)
struct WDG_SaveGameItem_C_BP_SetHighlight final
{
public:
	bool                                          highlighted;                                       // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SaveGameItem_C_BP_SetHighlight) == 0x000001, "Wrong alignment on WDG_SaveGameItem_C_BP_SetHighlight");
static_assert(sizeof(WDG_SaveGameItem_C_BP_SetHighlight) == 0x000001, "Wrong size on WDG_SaveGameItem_C_BP_SetHighlight");
static_assert(offsetof(WDG_SaveGameItem_C_BP_SetHighlight, highlighted) == 0x000000, "Member 'WDG_SaveGameItem_C_BP_SetHighlight::highlighted' has a wrong offset!");

// Function WDG_SaveGameITem.WDG_SaveGameItem_C.OnAddedToFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_SaveGameItem_C_OnAddedToFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_SaveGameItem_C_OnAddedToFocusPath) == 0x000004, "Wrong alignment on WDG_SaveGameItem_C_OnAddedToFocusPath");
static_assert(sizeof(WDG_SaveGameItem_C_OnAddedToFocusPath) == 0x000008, "Wrong size on WDG_SaveGameItem_C_OnAddedToFocusPath");
static_assert(offsetof(WDG_SaveGameItem_C_OnAddedToFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_SaveGameItem_C_OnAddedToFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_SaveGameITem.WDG_SaveGameItem_C.OnRemovedFromFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_SaveGameItem_C_OnRemovedFromFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_SaveGameItem_C_OnRemovedFromFocusPath) == 0x000004, "Wrong alignment on WDG_SaveGameItem_C_OnRemovedFromFocusPath");
static_assert(sizeof(WDG_SaveGameItem_C_OnRemovedFromFocusPath) == 0x000008, "Wrong size on WDG_SaveGameItem_C_OnRemovedFromFocusPath");
static_assert(offsetof(WDG_SaveGameItem_C_OnRemovedFromFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_SaveGameItem_C_OnRemovedFromFocusPath::InFocusEvent' has a wrong offset!");

}

