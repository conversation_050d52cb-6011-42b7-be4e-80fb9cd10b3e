﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PresetsItem

#include "Basic.hpp"

#include "WDG_PresetsItem_classes.hpp"
#include "WDG_PresetsItem_parameters.hpp"


namespace SDK
{

// Function WDG_PresetsItem.WDG_PresetsItem_C.ExecuteUbergraph_WDG_PresetsItem
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_PresetsItem_C::ExecuteUbergraph_WDG_PresetsItem(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PresetsItem_C", "ExecuteUbergraph_WDG_PresetsItem");

	Params::WDG_PresetsItem_C_ExecuteUbergraph_WDG_PresetsItem Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PresetsItem.WDG_PresetsItem_C.BP_MouseOver
// (Event, Public, BlueprintEvent)

void UWDG_PresetsItem_C::BP_MouseOver()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PresetsItem_C", "BP_MouseOver");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_PresetsItem.WDG_PresetsItem_C.BP_MouseLeave
// (Event, Public, BlueprintEvent)

void UWDG_PresetsItem_C::BP_MouseLeave()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PresetsItem_C", "BP_MouseLeave");

	UObject::ProcessEvent(Func, nullptr);
}

}

