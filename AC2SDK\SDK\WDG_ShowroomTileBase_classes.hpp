﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomTileBase

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "AC2_classes.hpp"
#include "Engine_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "ShowroomTileType_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ShowroomTileBase.WDG_ShowroomTileBase_C
// 0x04D0 (0x0AB0 - 0x05E0)
class UWDG_ShowroomTileBase_C : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	TMulticastInlineDelegate<void(class UWDG_ShowroomTileBase_C* Sender, class FName Key, int32 KeyInt)> OnItemForward; // 0x05E8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	struct FModelInfo                             ModelInfo;                                         // 0x05F8(0x01A8)(Edit, BlueprintVisible, ExposeOnSpawn)
	struct FLinearColor                           OverColor;                                         // 0x07A0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           NormalColor;                                       // 0x07B0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           OverTextColor;                                     // 0x07C0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           NormalTextColor;                                   // 0x07D0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TMulticastInlineDelegate<void(class UWDG_ShowroomTileBase_C* Sender)> OnItemFocused;             // 0x07E0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	EShowroomTileType                             TileType;                                          // 0x07F0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	uint8                                         Pad_7F1[0x7];                                      // 0x07F1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTeamInfo                              TeamInfo;                                          // 0x07F8(0x0038)(Edit, BlueprintVisible, ExposeOnSpawn)
	struct FCarInfo                               CarInfo;                                           // 0x0830(0x00E0)(Edit, BlueprintVisible, ExposeOnSpawn)
	bool                                          IsAnimatable;                                      // 0x0910(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_911[0x7];                                      // 0x0911(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FDriverInfo                            DriverInfo;                                        // 0x0918(0x00F0)(Edit, BlueprintVisible, ExposeOnSpawn)
	class FName                                   ItemKey;                                           // 0x0A08(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	int32                                         ItemKeyInt;                                        // 0x0A10(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	bool                                          IsSelected;                                        // 0x0A14(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_A15[0x3];                                      // 0x0A15(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UTextBlock*                             txtName;                                           // 0x0A18(0x0008)(Edit, BlueprintVisible, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	class UWDG_CarBrandWidget_C*                  Brand;                                             // 0x0A20(0x0008)(Edit, BlueprintVisible, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	class UBorder*                                Indicator;                                         // 0x0A28(0x0008)(Edit, BlueprintVisible, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	class UTextBlock*                             txtModelYear;                                      // 0x0A30(0x0008)(Edit, BlueprintVisible, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_GenericMappedLabel_C*              CarNumber;                                         // 0x0A38(0x0008)(Edit, BlueprintVisible, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 imgTeam;                                           // 0x0A40(0x0008)(Edit, BlueprintVisible, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	class UImage*                                 imgDriver;                                         // 0x0A48(0x0008)(Edit, BlueprintVisible, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, AdvancedDisplay, HasGetValueTypeHash)
	class UWDG_Flags_C*                           flag;                                              // 0x0A50(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWidgetSwitcher*                        switcherType;                                      // 0x0A58(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_GenericMappedLabel_C*              DriverLicense;                                     // 0x0A60(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Large;                                             // 0x0A68(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_A69[0x7];                                      // 0x0A69(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 textCache;                                         // 0x0A70(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         hbTeamYears;                                       // 0x0A80(0x0008)(Edit, BlueprintVisible, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 imgSeasonDeco;                                     // 0x0A88(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UBorder*                                borderYear;                                        // 0x0A90(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          addedGTWC;                                         // 0x0A98(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	uint8                                         Pad_A99[0x3];                                      // 0x0A99(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         numSeasons;                                        // 0x0A9C(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           seasonLabelBgr;                                    // 0x0AA0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_ShowroomTileBase(int32 EntryPoint);
	void UpdateDriver(const struct FDriverInfo& DriverInfo_0, class FName DriverKey, bool isCustom);
	void UpdateCar(const struct FCarInfo& CarInfo_0, class FName CarKey);
	void UpdateTeam(const struct FTeamInfo& TeamInfo_0);
	void OnMouseEnter(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent);
	void OnMouseLeave(const struct FPointerEvent& MouseEvent);
	void UpdateModel(const struct FModelInfo& ModelInfo_0);
	void OnItemForwardEvent();
	void Construct();
	void OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent);
	void OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent);
	void PreConstruct(bool IsDesignTime);
	struct FEventReply OnMouseButtonUp(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent);
	struct FEventReply OnMouseButtonDoubleClick(const struct FGeometry& InMyGeometry, const struct FPointerEvent& InMouseEvent);
	struct FEventReply OnMouseButtonDown(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent);
	void SetSelected(bool IsSelected_0);
	void GetNormalColor(struct FLinearColor* NormalTextColor_0);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ShowroomTileBase_C">();
	}
	static class UWDG_ShowroomTileBase_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ShowroomTileBase_C>();
	}
};
static_assert(alignof(UWDG_ShowroomTileBase_C) == 0x000008, "Wrong alignment on UWDG_ShowroomTileBase_C");
static_assert(sizeof(UWDG_ShowroomTileBase_C) == 0x000AB0, "Wrong size on UWDG_ShowroomTileBase_C");
static_assert(offsetof(UWDG_ShowroomTileBase_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_ShowroomTileBase_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, OnItemForward) == 0x0005E8, "Member 'UWDG_ShowroomTileBase_C::OnItemForward' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, ModelInfo) == 0x0005F8, "Member 'UWDG_ShowroomTileBase_C::ModelInfo' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, OverColor) == 0x0007A0, "Member 'UWDG_ShowroomTileBase_C::OverColor' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, NormalColor) == 0x0007B0, "Member 'UWDG_ShowroomTileBase_C::NormalColor' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, OverTextColor) == 0x0007C0, "Member 'UWDG_ShowroomTileBase_C::OverTextColor' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, NormalTextColor) == 0x0007D0, "Member 'UWDG_ShowroomTileBase_C::NormalTextColor' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, OnItemFocused) == 0x0007E0, "Member 'UWDG_ShowroomTileBase_C::OnItemFocused' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, TileType) == 0x0007F0, "Member 'UWDG_ShowroomTileBase_C::TileType' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, TeamInfo) == 0x0007F8, "Member 'UWDG_ShowroomTileBase_C::TeamInfo' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, CarInfo) == 0x000830, "Member 'UWDG_ShowroomTileBase_C::CarInfo' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, IsAnimatable) == 0x000910, "Member 'UWDG_ShowroomTileBase_C::IsAnimatable' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, DriverInfo) == 0x000918, "Member 'UWDG_ShowroomTileBase_C::DriverInfo' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, ItemKey) == 0x000A08, "Member 'UWDG_ShowroomTileBase_C::ItemKey' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, ItemKeyInt) == 0x000A10, "Member 'UWDG_ShowroomTileBase_C::ItemKeyInt' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, IsSelected) == 0x000A14, "Member 'UWDG_ShowroomTileBase_C::IsSelected' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, txtName) == 0x000A18, "Member 'UWDG_ShowroomTileBase_C::txtName' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, Brand) == 0x000A20, "Member 'UWDG_ShowroomTileBase_C::Brand' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, Indicator) == 0x000A28, "Member 'UWDG_ShowroomTileBase_C::Indicator' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, txtModelYear) == 0x000A30, "Member 'UWDG_ShowroomTileBase_C::txtModelYear' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, CarNumber) == 0x000A38, "Member 'UWDG_ShowroomTileBase_C::CarNumber' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, imgTeam) == 0x000A40, "Member 'UWDG_ShowroomTileBase_C::imgTeam' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, imgDriver) == 0x000A48, "Member 'UWDG_ShowroomTileBase_C::imgDriver' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, flag) == 0x000A50, "Member 'UWDG_ShowroomTileBase_C::flag' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, switcherType) == 0x000A58, "Member 'UWDG_ShowroomTileBase_C::switcherType' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, DriverLicense) == 0x000A60, "Member 'UWDG_ShowroomTileBase_C::DriverLicense' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, Large) == 0x000A68, "Member 'UWDG_ShowroomTileBase_C::Large' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, textCache) == 0x000A70, "Member 'UWDG_ShowroomTileBase_C::textCache' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, hbTeamYears) == 0x000A80, "Member 'UWDG_ShowroomTileBase_C::hbTeamYears' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, imgSeasonDeco) == 0x000A88, "Member 'UWDG_ShowroomTileBase_C::imgSeasonDeco' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, borderYear) == 0x000A90, "Member 'UWDG_ShowroomTileBase_C::borderYear' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, addedGTWC) == 0x000A98, "Member 'UWDG_ShowroomTileBase_C::addedGTWC' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, numSeasons) == 0x000A9C, "Member 'UWDG_ShowroomTileBase_C::numSeasons' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomTileBase_C, seasonLabelBgr) == 0x000AA0, "Member 'UWDG_ShowroomTileBase_C::seasonLabelBgr' has a wrong offset!");

}

