﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PlayerDataPage

#include "Basic.hpp"

#include "WDG_PlayerDataPage_classes.hpp"
#include "WDG_PlayerDataPage_parameters.hpp"


namespace SDK
{

// Function WDG_PlayerDataPage.WDG_PlayerDataPage_C.ExecuteUbergraph_WDG_PlayerDataPage
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_PlayerDataPage_C::ExecuteUbergraph_WDG_PlayerDataPage(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerDataPage_C", "ExecuteUbergraph_WDG_PlayerDataPage");

	Params::WDG_PlayerDataPage_C_ExecuteUbergraph_WDG_PlayerDataPage Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PlayerDataPage.WDG_PlayerDataPage_C.BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_5_OnEditableTextCommittedEvent__DelegateSignature
// (HasOutParams, BlueprintEvent)
// Parameters:
// const class FText&                      text                                                   (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// ETextCommit                             CommitMethod                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_PlayerDataPage_C::BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_5_OnEditableTextCommittedEvent__DelegateSignature(const class FText& text, ETextCommit CommitMethod)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerDataPage_C", "BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_5_OnEditableTextCommittedEvent__DelegateSignature");

	Params::WDG_PlayerDataPage_C_BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_5_OnEditableTextCommittedEvent__DelegateSignature Parms{};

	Parms.text = std::move(text);
	Parms.CommitMethod = CommitMethod;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PlayerDataPage.WDG_PlayerDataPage_C.BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_4_OnEditableTextCommittedEvent__DelegateSignature
// (HasOutParams, BlueprintEvent)
// Parameters:
// const class FText&                      text                                                   (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// ETextCommit                             CommitMethod                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_PlayerDataPage_C::BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_4_OnEditableTextCommittedEvent__DelegateSignature(const class FText& text, ETextCommit CommitMethod)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerDataPage_C", "BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_4_OnEditableTextCommittedEvent__DelegateSignature");

	Params::WDG_PlayerDataPage_C_BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_4_OnEditableTextCommittedEvent__DelegateSignature Parms{};

	Parms.text = std::move(text);
	Parms.CommitMethod = CommitMethod;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PlayerDataPage.WDG_PlayerDataPage_C.BndEvt__txtNameValue_K2Node_ComponentBoundEvent_3_OnEditableTextCommittedEvent__DelegateSignature
// (HasOutParams, BlueprintEvent)
// Parameters:
// const class FText&                      text                                                   (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// ETextCommit                             CommitMethod                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_PlayerDataPage_C::BndEvt__txtNameValue_K2Node_ComponentBoundEvent_3_OnEditableTextCommittedEvent__DelegateSignature(const class FText& text, ETextCommit CommitMethod)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerDataPage_C", "BndEvt__txtNameValue_K2Node_ComponentBoundEvent_3_OnEditableTextCommittedEvent__DelegateSignature");

	Params::WDG_PlayerDataPage_C_BndEvt__txtNameValue_K2Node_ComponentBoundEvent_3_OnEditableTextCommittedEvent__DelegateSignature Parms{};

	Parms.text = std::move(text);
	Parms.CommitMethod = CommitMethod;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PlayerDataPage.WDG_PlayerDataPage_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_PlayerDataPage_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerDataPage_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_PlayerDataPage.WDG_PlayerDataPage_C.BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_2_OnEditableTextChangedEvent__DelegateSignature
// (HasOutParams, BlueprintEvent)
// Parameters:
// const class FText&                      text                                                   (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_PlayerDataPage_C::BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_2_OnEditableTextChangedEvent__DelegateSignature(const class FText& text)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerDataPage_C", "BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_2_OnEditableTextChangedEvent__DelegateSignature");

	Params::WDG_PlayerDataPage_C_BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_2_OnEditableTextChangedEvent__DelegateSignature Parms{};

	Parms.text = std::move(text);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PlayerDataPage.WDG_PlayerDataPage_C.BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_1_OnEditableTextChangedEvent__DelegateSignature
// (HasOutParams, BlueprintEvent)
// Parameters:
// const class FText&                      text                                                   (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_PlayerDataPage_C::BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_1_OnEditableTextChangedEvent__DelegateSignature(const class FText& text)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerDataPage_C", "BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_1_OnEditableTextChangedEvent__DelegateSignature");

	Params::WDG_PlayerDataPage_C_BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_1_OnEditableTextChangedEvent__DelegateSignature Parms{};

	Parms.text = std::move(text);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PlayerDataPage.WDG_PlayerDataPage_C.BndEvt__txtNameValue_K2Node_ComponentBoundEvent_0_OnEditableTextChangedEvent__DelegateSignature
// (HasOutParams, BlueprintEvent)
// Parameters:
// const class FText&                      text                                                   (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_PlayerDataPage_C::BndEvt__txtNameValue_K2Node_ComponentBoundEvent_0_OnEditableTextChangedEvent__DelegateSignature(const class FText& text)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerDataPage_C", "BndEvt__txtNameValue_K2Node_ComponentBoundEvent_0_OnEditableTextChangedEvent__DelegateSignature");

	Params::WDG_PlayerDataPage_C_BndEvt__txtNameValue_K2Node_ComponentBoundEvent_0_OnEditableTextChangedEvent__DelegateSignature Parms{};

	Parms.text = std::move(text);

	UObject::ProcessEvent(Func, &Parms);
}

}

