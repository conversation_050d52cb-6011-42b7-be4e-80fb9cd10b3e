﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SinglePlayerPanel

#include "Basic.hpp"


namespace SDK::Params
{

// Function WDG_SinglePlayerPanel.WDG_SinglePlayerPanel_C.ExecuteUbergraph_WDG_SinglePlayerPanel
// 0x0030 (0x0030 - 0x0000)
struct WDG_SinglePlayerPanel_C_ExecuteUbergraph_WDG_SinglePlayerPanel final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0008(0x0008)(ZeroConstructor, IsPlainOldD<PERSON>, NoD<PERSON>ructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_1;              // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_2;              // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_3;              // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0028(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0029(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SinglePlayerPanel_C_ExecuteUbergraph_WDG_SinglePlayerPanel) == 0x000008, "Wrong alignment on WDG_SinglePlayerPanel_C_ExecuteUbergraph_WDG_SinglePlayerPanel");
static_assert(sizeof(WDG_SinglePlayerPanel_C_ExecuteUbergraph_WDG_SinglePlayerPanel) == 0x000030, "Wrong size on WDG_SinglePlayerPanel_C_ExecuteUbergraph_WDG_SinglePlayerPanel");
static_assert(offsetof(WDG_SinglePlayerPanel_C_ExecuteUbergraph_WDG_SinglePlayerPanel, EntryPoint) == 0x000000, "Member 'WDG_SinglePlayerPanel_C_ExecuteUbergraph_WDG_SinglePlayerPanel::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPanel_C_ExecuteUbergraph_WDG_SinglePlayerPanel, CallFunc_PlayAnimation_ReturnValue) == 0x000008, "Member 'WDG_SinglePlayerPanel_C_ExecuteUbergraph_WDG_SinglePlayerPanel::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPanel_C_ExecuteUbergraph_WDG_SinglePlayerPanel, CallFunc_PlayAnimation_ReturnValue_1) == 0x000010, "Member 'WDG_SinglePlayerPanel_C_ExecuteUbergraph_WDG_SinglePlayerPanel::CallFunc_PlayAnimation_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPanel_C_ExecuteUbergraph_WDG_SinglePlayerPanel, CallFunc_PlayAnimation_ReturnValue_2) == 0x000018, "Member 'WDG_SinglePlayerPanel_C_ExecuteUbergraph_WDG_SinglePlayerPanel::CallFunc_PlayAnimation_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPanel_C_ExecuteUbergraph_WDG_SinglePlayerPanel, CallFunc_PlayAnimation_ReturnValue_3) == 0x000020, "Member 'WDG_SinglePlayerPanel_C_ExecuteUbergraph_WDG_SinglePlayerPanel::CallFunc_PlayAnimation_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPanel_C_ExecuteUbergraph_WDG_SinglePlayerPanel, K2Node_Event_IsDesignTime) == 0x000028, "Member 'WDG_SinglePlayerPanel_C_ExecuteUbergraph_WDG_SinglePlayerPanel::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPanel_C_ExecuteUbergraph_WDG_SinglePlayerPanel, CallFunc_IsValid_ReturnValue) == 0x000029, "Member 'WDG_SinglePlayerPanel_C_ExecuteUbergraph_WDG_SinglePlayerPanel::CallFunc_IsValid_ReturnValue' has a wrong offset!");

// Function WDG_SinglePlayerPanel.WDG_SinglePlayerPanel_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_SinglePlayerPanel_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SinglePlayerPanel_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_SinglePlayerPanel_C_PreConstruct");
static_assert(sizeof(WDG_SinglePlayerPanel_C_PreConstruct) == 0x000001, "Wrong size on WDG_SinglePlayerPanel_C_PreConstruct");
static_assert(offsetof(WDG_SinglePlayerPanel_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_SinglePlayerPanel_C_PreConstruct::IsDesignTime' has a wrong offset!");

}

