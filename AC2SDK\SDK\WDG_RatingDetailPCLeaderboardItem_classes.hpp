﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RatingDetailPCLeaderboardItem

#include "Basic.hpp"

#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RatingDetailPCLeaderboardItem.WDG_RatingDetailPCLeaderboardItem_C
// 0x0020 (0x0280 - 0x0260)
class UWDG_RatingDetailPCLeaderboardItem_C final : public UUserWidget
{
public:
	class UTextBlock*                             txtBlockEntries;                                   // 0x0260(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtBlockPace;                                      // 0x0268(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtBlockRank;                                      // 0x0270(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtBlockSeasonNo;                                  // 0x0278(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void SetData(const struct FOnlineServicesLeaderboardRank& Rank);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RatingDetailPCLeaderboardItem_C">();
	}
	static class UWDG_RatingDetailPCLeaderboardItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RatingDetailPCLeaderboardItem_C>();
	}
};
static_assert(alignof(UWDG_RatingDetailPCLeaderboardItem_C) == 0x000008, "Wrong alignment on UWDG_RatingDetailPCLeaderboardItem_C");
static_assert(sizeof(UWDG_RatingDetailPCLeaderboardItem_C) == 0x000280, "Wrong size on UWDG_RatingDetailPCLeaderboardItem_C");
static_assert(offsetof(UWDG_RatingDetailPCLeaderboardItem_C, txtBlockEntries) == 0x000260, "Member 'UWDG_RatingDetailPCLeaderboardItem_C::txtBlockEntries' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailPCLeaderboardItem_C, txtBlockPace) == 0x000268, "Member 'UWDG_RatingDetailPCLeaderboardItem_C::txtBlockPace' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailPCLeaderboardItem_C, txtBlockRank) == 0x000270, "Member 'UWDG_RatingDetailPCLeaderboardItem_C::txtBlockRank' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailPCLeaderboardItem_C, txtBlockSeasonNo) == 0x000278, "Member 'UWDG_RatingDetailPCLeaderboardItem_C::txtBlockSeasonNo' has a wrong offset!");

}

