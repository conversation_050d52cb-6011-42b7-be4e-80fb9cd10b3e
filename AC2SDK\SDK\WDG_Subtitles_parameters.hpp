﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_Subtitles

#include "Basic.hpp"

#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_Subtitles.WDG_Subtitles_C.ExecuteUbergraph_WDG_Subtitles
// 0x0058 (0x0058 - 0x0000)
struct WDG_Subtitles_C_ExecuteUbergraph_WDG_Subtitles final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue;              // 0x0008(0x0008)(ZeroConstructor, IsPlainOldD<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	class UBP_GameInstance_C*                     K2Node_DynamicCast_AsBP_Game_Instance;             // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSubtitleRow                           CallFunc_GetSubRow_Destination;                    // 0x0020(0x0030)()
	bool                                          CallFunc_GetSubRow_ReturnValue;                    // 0x0050(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_Subtitles_C_ExecuteUbergraph_WDG_Subtitles) == 0x000008, "Wrong alignment on WDG_Subtitles_C_ExecuteUbergraph_WDG_Subtitles");
static_assert(sizeof(WDG_Subtitles_C_ExecuteUbergraph_WDG_Subtitles) == 0x000058, "Wrong size on WDG_Subtitles_C_ExecuteUbergraph_WDG_Subtitles");
static_assert(offsetof(WDG_Subtitles_C_ExecuteUbergraph_WDG_Subtitles, EntryPoint) == 0x000000, "Member 'WDG_Subtitles_C_ExecuteUbergraph_WDG_Subtitles::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_Subtitles_C_ExecuteUbergraph_WDG_Subtitles, CallFunc_GetGameInstance_ReturnValue) == 0x000008, "Member 'WDG_Subtitles_C_ExecuteUbergraph_WDG_Subtitles::CallFunc_GetGameInstance_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_Subtitles_C_ExecuteUbergraph_WDG_Subtitles, K2Node_DynamicCast_AsBP_Game_Instance) == 0x000010, "Member 'WDG_Subtitles_C_ExecuteUbergraph_WDG_Subtitles::K2Node_DynamicCast_AsBP_Game_Instance' has a wrong offset!");
static_assert(offsetof(WDG_Subtitles_C_ExecuteUbergraph_WDG_Subtitles, K2Node_DynamicCast_bSuccess) == 0x000018, "Member 'WDG_Subtitles_C_ExecuteUbergraph_WDG_Subtitles::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_Subtitles_C_ExecuteUbergraph_WDG_Subtitles, CallFunc_GetSubRow_Destination) == 0x000020, "Member 'WDG_Subtitles_C_ExecuteUbergraph_WDG_Subtitles::CallFunc_GetSubRow_Destination' has a wrong offset!");
static_assert(offsetof(WDG_Subtitles_C_ExecuteUbergraph_WDG_Subtitles, CallFunc_GetSubRow_ReturnValue) == 0x000050, "Member 'WDG_Subtitles_C_ExecuteUbergraph_WDG_Subtitles::CallFunc_GetSubRow_ReturnValue' has a wrong offset!");

// Function WDG_Subtitles.WDG_Subtitles_C.UpdateSubText
// 0x0060 (0x0060 - 0x0000)
struct WDG_Subtitles_C_UpdateSubText final
{
public:
	int32                                         playTimeMS;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         playTime;                                          // 0x0004(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSubText                               CallFunc_Array_Get_Item;                           // 0x0010(0x0020)()
	bool                                          CallFunc_Array_IsValidIndex_ReturnValue;           // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0031(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_GreaterEqual_IntInt_ReturnValue;          // 0x0032(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0033(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_34[0x4];                                       // 0x0034(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSubText                               CallFunc_Array_Get_Item_1;                         // 0x0038(0x0020)()
	bool                                          CallFunc_Less_IntInt_ReturnValue_1;                // 0x0058(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_Subtitles_C_UpdateSubText) == 0x000008, "Wrong alignment on WDG_Subtitles_C_UpdateSubText");
static_assert(sizeof(WDG_Subtitles_C_UpdateSubText) == 0x000060, "Wrong size on WDG_Subtitles_C_UpdateSubText");
static_assert(offsetof(WDG_Subtitles_C_UpdateSubText, playTimeMS) == 0x000000, "Member 'WDG_Subtitles_C_UpdateSubText::playTimeMS' has a wrong offset!");
static_assert(offsetof(WDG_Subtitles_C_UpdateSubText, playTime) == 0x000004, "Member 'WDG_Subtitles_C_UpdateSubText::playTime' has a wrong offset!");
static_assert(offsetof(WDG_Subtitles_C_UpdateSubText, CallFunc_Add_IntInt_ReturnValue) == 0x000008, "Member 'WDG_Subtitles_C_UpdateSubText::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_Subtitles_C_UpdateSubText, CallFunc_Add_IntInt_ReturnValue_1) == 0x00000C, "Member 'WDG_Subtitles_C_UpdateSubText::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_Subtitles_C_UpdateSubText, CallFunc_Array_Get_Item) == 0x000010, "Member 'WDG_Subtitles_C_UpdateSubText::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_Subtitles_C_UpdateSubText, CallFunc_Array_IsValidIndex_ReturnValue) == 0x000030, "Member 'WDG_Subtitles_C_UpdateSubText::CallFunc_Array_IsValidIndex_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_Subtitles_C_UpdateSubText, CallFunc_Less_IntInt_ReturnValue) == 0x000031, "Member 'WDG_Subtitles_C_UpdateSubText::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_Subtitles_C_UpdateSubText, CallFunc_GreaterEqual_IntInt_ReturnValue) == 0x000032, "Member 'WDG_Subtitles_C_UpdateSubText::CallFunc_GreaterEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_Subtitles_C_UpdateSubText, CallFunc_BooleanAND_ReturnValue) == 0x000033, "Member 'WDG_Subtitles_C_UpdateSubText::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_Subtitles_C_UpdateSubText, CallFunc_Array_Get_Item_1) == 0x000038, "Member 'WDG_Subtitles_C_UpdateSubText::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(WDG_Subtitles_C_UpdateSubText, CallFunc_Less_IntInt_ReturnValue_1) == 0x000058, "Member 'WDG_Subtitles_C_UpdateSubText::CallFunc_Less_IntInt_ReturnValue_1' has a wrong offset!");

}

