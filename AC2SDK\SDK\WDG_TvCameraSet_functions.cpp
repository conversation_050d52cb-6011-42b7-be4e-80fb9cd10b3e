﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TvCameraSet

#include "Basic.hpp"

#include "WDG_TvCameraSet_classes.hpp"
#include "WDG_TvCameraSet_parameters.hpp"


namespace SDK
{

// Function WDG_TvCameraSet.WDG_TvCameraSet_C.ExecuteUbergraph_WDG_TvCameraSet
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_TvCameraSet_C::ExecuteUbergraph_WDG_TvCameraSet(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TvCameraSet_C", "ExecuteUbergraph_WDG_TvCameraSet");

	Params::WDG_TvCameraSet_C_ExecuteUbergraph_WDG_TvCameraSet Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_TvCameraSet.WDG_TvCameraSet_C.OnHudTick
// (Event, Protected, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FRaceHUDState&             State                                                  (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_TvCameraSet_C::OnHudTick(const struct FRaceHUDState& State)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TvCameraSet_C", "OnHudTick");

	Params::WDG_TvCameraSet_C_OnHudTick Parms{};

	Parms.State = std::move(State);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_TvCameraSet.WDG_TvCameraSet_C.OnStartWidget
// (Event, Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UAcGameInstance*                  GameInstance                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class AAcRaceGameMode*                  raceGameMode                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class ACarAvatar*                       CarAvatar                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FHUDOptions&               HUDOptions                                             (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)

void UWDG_TvCameraSet_C::OnStartWidget(class UAcGameInstance* GameInstance, class AAcRaceGameMode* raceGameMode, class ACarAvatar* CarAvatar, const struct FHUDOptions& HUDOptions)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TvCameraSet_C", "OnStartWidget");

	Params::WDG_TvCameraSet_C_OnStartWidget Parms{};

	Parms.GameInstance = GameInstance;
	Parms.raceGameMode = raceGameMode;
	Parms.CarAvatar = CarAvatar;
	Parms.HUDOptions = std::move(HUDOptions);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_TvCameraSet.WDG_TvCameraSet_C.IsWidgetDefinitionEnabled
// (Event, Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UAcGameInstance*                  GameInstance                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FHUDOptions&               HUDOptions                                             (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor)

bool UWDG_TvCameraSet_C::IsWidgetDefinitionEnabled(class UAcGameInstance* GameInstance, const struct FHUDOptions& HUDOptions)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TvCameraSet_C", "IsWidgetDefinitionEnabled");

	Params::WDG_TvCameraSet_C_IsWidgetDefinitionEnabled Parms{};

	Parms.GameInstance = GameInstance;
	Parms.HUDOptions = std::move(HUDOptions);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}

}

