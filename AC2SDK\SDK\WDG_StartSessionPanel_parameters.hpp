﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_StartSessionPanel

#include "Basic.hpp"


namespace SDK::Params
{

// Function WDG_StartSessionPanel.WDG_StartSessionPanel_C.ExecuteUbergraph_WDG_StartSessionPanel
// 0x0008 (0x0008 - 0x0000)
struct WDG_StartSessionPanel_C_ExecuteUbergraph_WDG_StartSessionPanel final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_StartSessionPanel_C_ExecuteUbergraph_WDG_StartSessionPanel) == 0x000004, "Wrong alignment on WDG_StartSessionPanel_C_ExecuteUbergraph_WDG_StartSessionPanel");
static_assert(sizeof(WDG_StartSessionPanel_C_ExecuteUbergraph_WDG_StartSessionPanel) == 0x000008, "Wrong size on WDG_StartSessionPanel_C_ExecuteUbergraph_WDG_StartSessionPanel");
static_assert(offsetof(WDG_StartSessionPanel_C_ExecuteUbergraph_WDG_StartSessionPanel, EntryPoint) == 0x000000, "Member 'WDG_StartSessionPanel_C_ExecuteUbergraph_WDG_StartSessionPanel::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_StartSessionPanel_C_ExecuteUbergraph_WDG_StartSessionPanel, K2Node_Event_IsDesignTime) == 0x000004, "Member 'WDG_StartSessionPanel_C_ExecuteUbergraph_WDG_StartSessionPanel::K2Node_Event_IsDesignTime' has a wrong offset!");

// Function WDG_StartSessionPanel.WDG_StartSessionPanel_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_StartSessionPanel_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_StartSessionPanel_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_StartSessionPanel_C_PreConstruct");
static_assert(sizeof(WDG_StartSessionPanel_C_PreConstruct) == 0x000001, "Wrong size on WDG_StartSessionPanel_C_PreConstruct");
static_assert(offsetof(WDG_StartSessionPanel_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_StartSessionPanel_C_PreConstruct::IsDesignTime' has a wrong offset!");

}

