﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TextInput

#include "Basic.hpp"

#include "Slate_structs.hpp"
#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_TextInput.WDG_TextInput_C
// 0x0028 (0x0750 - 0x0728)
class UWDG_TextInput_C final : public UAcTextInput
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0728(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       Pulse;                                             // 0x0730(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class USizeBox*                               sizeOverride;                                      // 0x0738(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	bool                                          IsPassword;                                        // 0x0740(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn)
	ETextJustify                                  TextAlignment;                                     // 0x0741(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_742[0x2];                                      // 0x0742(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         FixedWidth;                                        // 0x0744(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	float                                         MinDesiredTextInputWidth;                          // 0x0748(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_TextInput(int32 EntryPoint);
	void OnMouseLeave(const struct FPointerEvent& MouseEvent);
	void OnMouseEnter(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent);
	void PreConstruct(bool IsDesignTime);
	void PlayPulseAnimation(int32 NumLoopsToPlay, float PlaybackSpeed);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_TextInput_C">();
	}
	static class UWDG_TextInput_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_TextInput_C>();
	}
};
static_assert(alignof(UWDG_TextInput_C) == 0x000008, "Wrong alignment on UWDG_TextInput_C");
static_assert(sizeof(UWDG_TextInput_C) == 0x000750, "Wrong size on UWDG_TextInput_C");
static_assert(offsetof(UWDG_TextInput_C, UberGraphFrame) == 0x000728, "Member 'UWDG_TextInput_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_TextInput_C, Pulse) == 0x000730, "Member 'UWDG_TextInput_C::Pulse' has a wrong offset!");
static_assert(offsetof(UWDG_TextInput_C, sizeOverride) == 0x000738, "Member 'UWDG_TextInput_C::sizeOverride' has a wrong offset!");
static_assert(offsetof(UWDG_TextInput_C, IsPassword) == 0x000740, "Member 'UWDG_TextInput_C::IsPassword' has a wrong offset!");
static_assert(offsetof(UWDG_TextInput_C, TextAlignment) == 0x000741, "Member 'UWDG_TextInput_C::TextAlignment' has a wrong offset!");
static_assert(offsetof(UWDG_TextInput_C, FixedWidth) == 0x000744, "Member 'UWDG_TextInput_C::FixedWidth' has a wrong offset!");
static_assert(offsetof(UWDG_TextInput_C, MinDesiredTextInputWidth) == 0x000748, "Member 'UWDG_TextInput_C::MinDesiredTextInputWidth' has a wrong offset!");

}

