﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RedInfobox

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RedInfobox.WDG_RedInfobox_C
// 0x0040 (0x02A0 - 0x0260)
class UWDG_RedInfobox_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0260(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UNamedSlot*                             Body;                                              // 0x0268(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgDarkBackground;                                 // 0x0270(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgTopBackground;                                  // 0x0278(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             NamedSlot_0;                                       // 0x0280(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             Title;                                             // 0x0288(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 topcorner;                                         // 0x0290(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	bool                                          UseWhiteHeader;                                    // 0x0298(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)

public:
	void ExecuteUbergraph_WDG_RedInfobox(int32 EntryPoint);
	void Construct();
	void PreConstruct(bool IsDesignTime);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RedInfobox_C">();
	}
	static class UWDG_RedInfobox_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RedInfobox_C>();
	}
};
static_assert(alignof(UWDG_RedInfobox_C) == 0x000008, "Wrong alignment on UWDG_RedInfobox_C");
static_assert(sizeof(UWDG_RedInfobox_C) == 0x0002A0, "Wrong size on UWDG_RedInfobox_C");
static_assert(offsetof(UWDG_RedInfobox_C, UberGraphFrame) == 0x000260, "Member 'UWDG_RedInfobox_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_RedInfobox_C, Body) == 0x000268, "Member 'UWDG_RedInfobox_C::Body' has a wrong offset!");
static_assert(offsetof(UWDG_RedInfobox_C, imgDarkBackground) == 0x000270, "Member 'UWDG_RedInfobox_C::imgDarkBackground' has a wrong offset!");
static_assert(offsetof(UWDG_RedInfobox_C, imgTopBackground) == 0x000278, "Member 'UWDG_RedInfobox_C::imgTopBackground' has a wrong offset!");
static_assert(offsetof(UWDG_RedInfobox_C, NamedSlot_0) == 0x000280, "Member 'UWDG_RedInfobox_C::NamedSlot_0' has a wrong offset!");
static_assert(offsetof(UWDG_RedInfobox_C, Title) == 0x000288, "Member 'UWDG_RedInfobox_C::Title' has a wrong offset!");
static_assert(offsetof(UWDG_RedInfobox_C, topcorner) == 0x000290, "Member 'UWDG_RedInfobox_C::topcorner' has a wrong offset!");
static_assert(offsetof(UWDG_RedInfobox_C, UseWhiteHeader) == 0x000298, "Member 'UWDG_RedInfobox_C::UseWhiteHeader' has a wrong offset!");

}

