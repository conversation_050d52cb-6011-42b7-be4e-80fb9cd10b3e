﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SpecialEventsLeaderboardLine

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "AC2_classes.hpp"
#include "Engine_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SpecialEventsLeaderboardLine.WDG_SpecialEventsLeaderboardLine_C
// 0x00D0 (0x0398 - 0x02C8)
class UWDG_SpecialEventsLeaderboardLine_C final : public USpecialEventLeaderboardItems
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x02C8(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UImage*                                 imgFlag;                                           // 0x02D0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoD<PERSON><PERSON><PERSON>, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgLine;                                           // 0x02D8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_KunosCategories_C*                 wdgKunosCategories;                                // 0x02E0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	struct FOnlineServicesHotlapEntry             Entry;                                             // 0x02E8(0x0088)(Edit, BlueprintVisible, ExposeOnSpawn)
	int32                                         Position;                                          // 0x0370(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	uint8                                         Pad_374[0x4];                                      // 0x0374(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   Gap;                                               // 0x0378(0x0018)(Edit, BlueprintVisible, ExposeOnSpawn)
	bool                                          Spawn;                                             // 0x0390(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn)
	bool                                          IsHotstint;                                        // 0x0391(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn)

public:
	void ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine(int32 EntryPoint);
	void Construct();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SpecialEventsLeaderboardLine_C">();
	}
	static class UWDG_SpecialEventsLeaderboardLine_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SpecialEventsLeaderboardLine_C>();
	}
};
static_assert(alignof(UWDG_SpecialEventsLeaderboardLine_C) == 0x000008, "Wrong alignment on UWDG_SpecialEventsLeaderboardLine_C");
static_assert(sizeof(UWDG_SpecialEventsLeaderboardLine_C) == 0x000398, "Wrong size on UWDG_SpecialEventsLeaderboardLine_C");
static_assert(offsetof(UWDG_SpecialEventsLeaderboardLine_C, UberGraphFrame) == 0x0002C8, "Member 'UWDG_SpecialEventsLeaderboardLine_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsLeaderboardLine_C, imgFlag) == 0x0002D0, "Member 'UWDG_SpecialEventsLeaderboardLine_C::imgFlag' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsLeaderboardLine_C, imgLine) == 0x0002D8, "Member 'UWDG_SpecialEventsLeaderboardLine_C::imgLine' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsLeaderboardLine_C, wdgKunosCategories) == 0x0002E0, "Member 'UWDG_SpecialEventsLeaderboardLine_C::wdgKunosCategories' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsLeaderboardLine_C, Entry) == 0x0002E8, "Member 'UWDG_SpecialEventsLeaderboardLine_C::Entry' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsLeaderboardLine_C, Position) == 0x000370, "Member 'UWDG_SpecialEventsLeaderboardLine_C::Position' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsLeaderboardLine_C, Gap) == 0x000378, "Member 'UWDG_SpecialEventsLeaderboardLine_C::Gap' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsLeaderboardLine_C, Spawn) == 0x000390, "Member 'UWDG_SpecialEventsLeaderboardLine_C::Spawn' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsLeaderboardLine_C, IsHotstint) == 0x000391, "Member 'UWDG_SpecialEventsLeaderboardLine_C::IsHotstint' has a wrong offset!");

}

