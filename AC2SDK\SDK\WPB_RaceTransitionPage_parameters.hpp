﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WPB_RaceTransitionPage

#include "Basic.hpp"

#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WPB_RaceTransitionPage.WPB_RaceTransitionPage_C.ExecuteUbergraph_WPB_RaceTransitionPage
// 0x0100 (0x0100 - 0x0000)
struct WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue;              // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance;             // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_IsHMDEnabled_ReturnValue;                 // 0x0019(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1A[0x2];                                       // 0x001A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         K2Node_Event_timeMult_1;                           // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_Event_timeMult;                             // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_24[0x4];                                       // 0x0024(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_1;              // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_GetCurrentGameVersion_ReturnValue;        // 0x0038(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_TextToString_ReturnValue;            // 0x0048(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x0058(0x0018)()
	class FString                                 CallFunc_Concat_StrStr_ReturnValue;                // 0x0070(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_TextToString_ReturnValue_1;          // 0x0080(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	struct FGeometry                              K2Node_Event_MyGeometry;                           // 0x0090(0x0038)(IsPlainOldData, NoDestructor)
	float                                         K2Node_Event_InDeltaTime;                          // 0x00C8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_CC[0x4];                                       // 0x00CC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_1;              // 0x00D0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_1;          // 0x00E0(0x0018)()
	bool                                          CallFunc_isReplaySaving_ReturnValue;               // 0x00F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage) == 0x000008, "Wrong alignment on WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage");
static_assert(sizeof(WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage) == 0x000100, "Wrong size on WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage");
static_assert(offsetof(WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage, EntryPoint) == 0x000000, "Member 'WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage::EntryPoint' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage, CallFunc_GetGameInstance_ReturnValue) == 0x000008, "Member 'WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage::CallFunc_GetGameInstance_ReturnValue' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage, K2Node_DynamicCast_AsAc_Game_Instance) == 0x000010, "Member 'WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage::K2Node_DynamicCast_AsAc_Game_Instance' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage, K2Node_DynamicCast_bSuccess) == 0x000018, "Member 'WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage, CallFunc_IsHMDEnabled_ReturnValue) == 0x000019, "Member 'WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage::CallFunc_IsHMDEnabled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage, K2Node_Event_timeMult_1) == 0x00001C, "Member 'WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage::K2Node_Event_timeMult_1' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage, K2Node_Event_timeMult) == 0x000020, "Member 'WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage::K2Node_Event_timeMult' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage, CallFunc_PlayAnimation_ReturnValue) == 0x000028, "Member 'WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage, CallFunc_PlayAnimation_ReturnValue_1) == 0x000030, "Member 'WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage::CallFunc_PlayAnimation_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage, CallFunc_GetCurrentGameVersion_ReturnValue) == 0x000038, "Member 'WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage::CallFunc_GetCurrentGameVersion_ReturnValue' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage, CallFunc_Conv_TextToString_ReturnValue) == 0x000048, "Member 'WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage::CallFunc_Conv_TextToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage, CallFunc_Conv_StringToText_ReturnValue) == 0x000058, "Member 'WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage, CallFunc_Concat_StrStr_ReturnValue) == 0x000070, "Member 'WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage::CallFunc_Concat_StrStr_ReturnValue' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage, CallFunc_Conv_TextToString_ReturnValue_1) == 0x000080, "Member 'WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage::CallFunc_Conv_TextToString_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage, K2Node_Event_MyGeometry) == 0x000090, "Member 'WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage::K2Node_Event_MyGeometry' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage, K2Node_Event_InDeltaTime) == 0x0000C8, "Member 'WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage::K2Node_Event_InDeltaTime' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage, CallFunc_Concat_StrStr_ReturnValue_1) == 0x0000D0, "Member 'WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage::CallFunc_Concat_StrStr_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage, CallFunc_Conv_StringToText_ReturnValue_1) == 0x0000E0, "Member 'WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage::CallFunc_Conv_StringToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage, CallFunc_isReplaySaving_ReturnValue) == 0x0000F8, "Member 'WPB_RaceTransitionPage_C_ExecuteUbergraph_WPB_RaceTransitionPage::CallFunc_isReplaySaving_ReturnValue' has a wrong offset!");

// Function WPB_RaceTransitionPage.WPB_RaceTransitionPage_C.Tick
// 0x003C (0x003C - 0x0000)
struct WPB_RaceTransitionPage_C_Tick final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	float                                         InDeltaTime;                                       // 0x0038(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WPB_RaceTransitionPage_C_Tick) == 0x000004, "Wrong alignment on WPB_RaceTransitionPage_C_Tick");
static_assert(sizeof(WPB_RaceTransitionPage_C_Tick) == 0x00003C, "Wrong size on WPB_RaceTransitionPage_C_Tick");
static_assert(offsetof(WPB_RaceTransitionPage_C_Tick, MyGeometry) == 0x000000, "Member 'WPB_RaceTransitionPage_C_Tick::MyGeometry' has a wrong offset!");
static_assert(offsetof(WPB_RaceTransitionPage_C_Tick, InDeltaTime) == 0x000038, "Member 'WPB_RaceTransitionPage_C_Tick::InDeltaTime' has a wrong offset!");

// Function WPB_RaceTransitionPage.WPB_RaceTransitionPage_C.StartFade
// 0x0004 (0x0004 - 0x0000)
struct WPB_RaceTransitionPage_C_StartFade final
{
public:
	float                                         timeMult;                                          // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WPB_RaceTransitionPage_C_StartFade) == 0x000004, "Wrong alignment on WPB_RaceTransitionPage_C_StartFade");
static_assert(sizeof(WPB_RaceTransitionPage_C_StartFade) == 0x000004, "Wrong size on WPB_RaceTransitionPage_C_StartFade");
static_assert(offsetof(WPB_RaceTransitionPage_C_StartFade, timeMult) == 0x000000, "Member 'WPB_RaceTransitionPage_C_StartFade::timeMult' has a wrong offset!");

// Function WPB_RaceTransitionPage.WPB_RaceTransitionPage_C.EndFade
// 0x0004 (0x0004 - 0x0000)
struct WPB_RaceTransitionPage_C_EndFade final
{
public:
	float                                         timeMult;                                          // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WPB_RaceTransitionPage_C_EndFade) == 0x000004, "Wrong alignment on WPB_RaceTransitionPage_C_EndFade");
static_assert(sizeof(WPB_RaceTransitionPage_C_EndFade) == 0x000004, "Wrong size on WPB_RaceTransitionPage_C_EndFade");
static_assert(offsetof(WPB_RaceTransitionPage_C_EndFade, timeMult) == 0x000000, "Member 'WPB_RaceTransitionPage_C_EndFade::timeMult' has a wrong offset!");

}

