﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomPage

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "SlateCore_structs.hpp"
#include "ShowroomTileType_structs.hpp"
#include "InputCore_structs.hpp"
#include "UMG_structs.hpp"
#include "AC2_structs.hpp"
#include "CoreUObject_structs.hpp"


namespace SDK::Params
{

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.ExecuteUbergraph_WDG_ShowroomPage
// 0x1940 (0x1940 - 0x0000)
struct WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6[0x2];                                        // 0x0006(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_1;                   // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_14[0x4];                                       // 0x0014(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_IntToText_ReturnValue;               // 0x0018(0x0018)()
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_2;            // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_3;            // 0x0031(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_4;            // 0x0032(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_33[0x1];                                       // 0x0033(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0034(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x0038(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_2;                   // 0x003C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_1;                  // 0x0040(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_2;                 // 0x0044(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_5;            // 0x0048(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_6;            // 0x0049(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x004A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x004B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_1;                // 0x004C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_7;            // 0x004D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4E[0x2];                                       // 0x004E(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue;              // 0x0050(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_2;                  // 0x0058(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_5C[0x4];                                       // 0x005C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance;             // 0x0060(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0068(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_69[0x7];                                       // 0x0069(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UGamePlatformServices*                  CallFunc_GetGamePlatformServices_ReturnValue;      // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_3;                 // 0x0078(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_7C[0x4];                                       // 0x007C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FGamePlatformUserAccountData           CallFunc_GetLocalUserAccount_ReturnValue;          // 0x0080(0x00C0)(ConstParm)
	struct FDriverInfo                            K2Node_MakeStruct_DriverInfo;                      // 0x0140(0x00F0)()
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_8;            // 0x0230(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable_1;                              // 0x0231(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_2;                // 0x0232(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_9;            // 0x0233(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void(class FName Key, class UWDG_ShowroomTileItemHorizontal_C* Sender)> K2Node_CreateDelegate_OutputDelegate; // 0x0234(0x0010)(ZeroConstructor, NoDestructor)
	TDelegate<void(class FName Key, class UWDG_ShowroomTileItemHorizontal_C* Sender)> K2Node_CreateDelegate_OutputDelegate_1; // 0x0244(0x0010)(ZeroConstructor, NoDestructor)
	TDelegate<void(class UWDG_ShowroomTileBase_C* Sender, class FName Key, int32 KeyInt)> K2Node_CreateDelegate_OutputDelegate_2; // 0x0254(0x0010)(ZeroConstructor, NoDestructor)
	uint8                                         Pad_264[0x4];                                      // 0x0264(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UGenericSelectorItem*                   K2Node_ComponentBoundEvent_source;                 // 0x0268(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_current_index;          // 0x0270(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_current_value;          // 0x0274(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcCarSystems*                          CallFunc_GetCarSystems_carSystems;                 // 0x0278(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsDirectionLightLeftOn_ReturnValue;       // 0x0280(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsRainLightOn_ReturnValue;                // 0x0281(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_282[0x6];                                      // 0x0282(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcCarSystems*                          CallFunc_GetCarSystems_carSystems_1;               // 0x0288(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsDoorOpen_ReturnValue;                   // 0x0290(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsDoorOpen_ReturnValue_1;                 // 0x0291(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_292[0x6];                                      // 0x0292(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcCarAnimations*                       CallFunc_GetCarAnimations_CarAnimations;           // 0x0298(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcCarSystems*                          CallFunc_GetCarSystems_carSystems_2;               // 0x02A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetValue_ReturnValue;                     // 0x02A8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetValue_ReturnValue_1;                   // 0x02AC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class ULevelSequencePlayer*                   CallFunc_GetSequencePlayer_ReturnValue;            // 0x02B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class ULevelSequencePlayer*                   CallFunc_GetSequencePlayer_ReturnValue_1;          // 0x02B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsShowroomPlaying_ReturnValue;            // 0x02C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2C1[0x7];                                      // 0x02C1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	const class UWidgetAnimation*                 K2Node_Event_Animation;                            // 0x02C8(0x0008)(ConstParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsAnimationPlayingForward_ReturnValue;    // 0x02D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ObjectObject_ReturnValue;      // 0x02D1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2D2[0x6];                                      // 0x02D2(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x02D8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_1;              // 0x02E0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_2;              // 0x02E8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsAnimationPlaying_ReturnValue;           // 0x02F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_3;                // 0x02F1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x02F2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2F3[0x5];                                      // 0x02F3(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_GenericBarItem_C*                  K2Node_ComponentBoundEvent_Sender_13;              // 0x02F8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_SaveCustomCar_ReturnValue;                // 0x0300(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          K2Node_ComponentBoundEvent_HasConfirmation;        // 0x0301(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_302[0x6];                                      // 0x0302(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_GenericBarItem_C*                  K2Node_ComponentBoundEvent_Sender_12;              // 0x0308(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_DeleteCustomCar_ReturnValue;              // 0x0310(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_311[0x7];                                      // 0x0311(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_GenericBarItem_C*                  K2Node_ComponentBoundEvent_Sender_11;              // 0x0318(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_SaveCustomCar_ReturnValue_1;              // 0x0320(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsNewCustomCar_ReturnValue;               // 0x0321(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_322[0x6];                                      // 0x0322(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_GenericBarItem_C*                  K2Node_ComponentBoundEvent_Sender_10;              // 0x0328(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  K2Node_ComponentBoundEvent_Sender_9;               // 0x0330(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FModelInfo                             K2Node_Event_ModelInfo_1;                          // 0x0338(0x01A8)()
	class UWDG_ShowroomTileBase_C*                K2Node_ComponentBoundEvent_Sender_8;               // 0x04E0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   K2Node_ComponentBoundEvent_Key_7;                  // 0x04E8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_KeyInt_3;               // 0x04F0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4F4[0x4];                                      // 0x04F4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<struct FModelInfo>                     CallFunc_GetAllModels_ReturnValue;                 // 0x04F8(0x0010)(ReferenceParm)
	class FText                                   K2Node_ComponentBoundEvent_Text_1;                 // 0x0508(0x0018)(ConstParm)
	class FString                                 CallFunc_Conv_TextToString_ReturnValue;            // 0x0520(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_TextToString_ReturnValue_1;          // 0x0530(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetZeroPaddingFromRaceNumber_ReturnValue; // 0x0540(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Conv_StringToInt_ReturnValue;             // 0x0544(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_Conv_IntToByte_ReturnValue;               // 0x0548(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_549[0x7];                                      // 0x0549(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FCarInfo                               K2Node_ComponentBoundEvent_CarInfo;                // 0x0550(0x00E0)()
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x0630(0x0018)()
	int32                                         CallFunc_Conv_ByteToInt_ReturnValue;               // 0x0648(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_64C[0x4];                                      // 0x064C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_PadNumberWithZeroesAlways_ReturnValue;    // 0x0650(0x0018)()
	class FText                                   K2Node_ComponentBoundEvent_Text;                   // 0x0668(0x0018)(ConstParm)
	class FString                                 CallFunc_Conv_TextToString_ReturnValue_2;          // 0x0680(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	TArray<struct FModelInfo>                     CallFunc_GetAllModels_ReturnValue_1;               // 0x0690(0x0010)(ReferenceParm)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x06A0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6A4[0x4];                                      // 0x06A4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FDriverInfo                            K2Node_Event_driverInfo;                           // 0x06A8(0x00F0)()
	class FName                                   K2Node_Event_DriverKey;                            // 0x0798(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x07A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_7A1[0x3];                                      // 0x07A1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_GetChildrenCount_ReturnValue;             // 0x07A4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileBase_C*                K2Node_ComponentBoundEvent_Sender_7;               // 0x07A8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   K2Node_ComponentBoundEvent_Key_6;                  // 0x07B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_KeyInt_2;               // 0x07B8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x07BC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_TextIsEmpty_ReturnValue;                  // 0x07C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsCurrentCarCustom_isCustom;              // 0x07C1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_4;                // 0x07C2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsCurrentCarCustom_isCustom_1;            // 0x07C3(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_7C4[0x4];                                      // 0x07C4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FCarInfo                               K2Node_Event_carInfo;                              // 0x07C8(0x00E0)()
	class FName                                   K2Node_Event_CarKey;                               // 0x08A8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetChildrenCount_ReturnValue_1;           // 0x08B0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_1;            // 0x08B4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<class FName>                           CallFunc_Map_Keys_Keys;                            // 0x08B8(0x0010)(ReferenceParm)
	TArray<struct FDriverInfo>                    CallFunc_Map_Values_Values;                        // 0x08C8(0x0010)(ReferenceParm)
	class FName                                   CallFunc_Array_Get_Item;                           // 0x08D8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FDriverInfo                            CallFunc_Array_Get_Item_1;                         // 0x08E0(0x00F0)()
	class FString                                 CallFunc_Conv_NameToString_ReturnValue;            // 0x09D0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x09E0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x09E4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_9E5[0x3];                                      // 0x09E5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomTileBase_C*                K2Node_CustomEvent_Sender_6;                       // 0x09E8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   K2Node_CustomEvent_Key_5;                          // 0x09F0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_CustomEvent_KeyInt_1;                       // 0x09F8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_9FC[0x4];                                      // 0x09FC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue;              // 0x0A00(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NotEqual_NameName_ReturnValue;            // 0x0A08(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_A09[0x7];                                      // 0x0A09(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomTileItemHorizontal_C*      CallFunc_Create_ReturnValue;                       // 0x0A10(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue;                     // 0x0A18(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void(class FName Key, class UWDG_ShowroomTileItemHorizontal_C* Sender)> K2Node_CreateDelegate_OutputDelegate_3; // 0x0A20(0x0010)(ZeroConstructor, NoDestructor)
	TDelegate<void(class FName Key, class UWDG_ShowroomTileItemHorizontal_C* Sender)> K2Node_CreateDelegate_OutputDelegate_4; // 0x0A30(0x0010)(ZeroConstructor, NoDestructor)
	bool                                          CallFunc_EqualEqual_NameName_ReturnValue;          // 0x0A40(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_A41[0x3];                                      // 0x0A41(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void(class UWDG_ShowroomTileItemHorizontal_C* Sender, int32 ColorCode, const struct FLinearColor& Color)> K2Node_CreateDelegate_OutputDelegate_5; // 0x0A44(0x0010)(ZeroConstructor, NoDestructor)
	int32                                         K2Node_Event_variant_key;                          // 0x0A54(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSkinTemplate                          CallFunc_Array_Get_Item_2;                         // 0x0A58(0x0048)()
	int32                                         CallFunc_Array_Length_ReturnValue_2;               // 0x0AA0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_1;                // 0x0AA4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsVariantSelected_ReturnValue;            // 0x0AA5(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_AA6[0x2];                                      // 0x0AA6(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable;                                 // 0x0AA8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_4;                 // 0x0AAC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_10;           // 0x0AB0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_AB1[0x3];                                      // 0x0AB1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue_3;               // 0x0AB4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_1;             // 0x0AB8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_AB9[0x3];                                      // 0x0AB9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateChildSize                        K2Node_MakeStruct_SlateChildSize;                  // 0x0ABC(0x0008)(NoDestructor)
	struct FSlateChildSize                        K2Node_MakeStruct_SlateChildSize_1;                // 0x0AC4(0x0008)(NoDestructor, UObjectWrapper)
	uint8                                         Pad_ACC[0x4];                                      // 0x0ACC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UHorizontalBoxSlot*                     CallFunc_SlotAsHorizontalBoxSlot_ReturnValue;      // 0x0AD0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      K2Node_CustomEvent_Sender_5;                       // 0x0AD8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_CustomEvent_ColorCode;                      // 0x0AE0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_CustomEvent_Color;                          // 0x0AE4(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_AF4[0x4];                                      // 0x0AF4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                CallFunc_GetChildAt_ReturnValue;                   // 0x0AF8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetChildrenCount_ReturnValue_2;           // 0x0B00(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_B04[0x4];                                      // 0x0B04(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomTileItemHorizontal_C*      K2Node_DynamicCast_AsWDG_Showroom_Tile_Item_Horizontal; // 0x0B08(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x0B10(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_B11[0x3];                                      // 0x0B11(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Subtract_IntInt_ReturnValue_2;            // 0x0B14(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x0B18(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x0B19(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x0B1A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          Temp_bool_Variable_2;                              // 0x0B1B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	class FName                                   K2Node_CustomEvent_Key_4;                          // 0x0B1C(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_B24[0x4];                                      // 0x0B24(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomTileItemHorizontal_C*      K2Node_CustomEvent_Sender_4;                       // 0x0B28(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_5;                // 0x0B30(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_B31[0x3];                                      // 0x0B31(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue_4;               // 0x0B34(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_5;               // 0x0B38(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_2;             // 0x0B3C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_3;             // 0x0B3D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_B3E[0x2];                                      // 0x0B3E(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Find_ReturnValue;                   // 0x0B40(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_B44[0x4];                                      // 0x0B44(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomTileItemHorizontal_C*      CallFunc_FindSelectedVariant_AsWDG_Showroom_Tile_Item_Horizontal; // 0x0B48(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_5;                 // 0x0B50(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Percent_IntInt_ReturnValue;               // 0x0B54(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_4;             // 0x0B58(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_B59[0x3];                                      // 0x0B59(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_6;                 // 0x0B5C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue_2;                 // 0x0B60(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_B61[0x3];                                      // 0x0B61(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Percent_IntInt_ReturnValue_1;             // 0x0B64(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Get_Item_3;                         // 0x0B68(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_B6C[0x4];                                      // 0x0B6C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomTileItemHorizontal_C*      CallFunc_FindSelectedVariant_AsWDG_Showroom_Tile_Item_Horizontal_1; // 0x0B70(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   K2Node_CustomEvent_Key_3;                          // 0x0B78(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      K2Node_CustomEvent_Sender_3;                       // 0x0B80(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Find_ReturnValue_1;                 // 0x0B88(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_7;                 // 0x0B8C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Percent_IntInt_ReturnValue_2;             // 0x0B90(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_5;             // 0x0B94(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_B95[0x3];                                      // 0x0B95(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_8;                 // 0x0B98(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue_3;                 // 0x0B9C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_B9D[0x3];                                      // 0x0B9D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Percent_IntInt_ReturnValue_3;             // 0x0BA0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Get_Item_4;                         // 0x0BA4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsCurrentCarCustom_isCustom_2;            // 0x0BA8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_6;                // 0x0BA9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_4;                 // 0x0BAA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_BAB[0x5];                                      // 0x0BAB(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTeamInfo                              K2Node_Event_TeamInfo;                             // 0x0BB0(0x0038)()
	struct FTeamInfo                              K2Node_CustomEvent_TeamInfo;                       // 0x0BE8(0x0038)()
	TArray<struct FCarInfo>                       CallFunc_Map_Values_Values_1;                      // 0x0C20(0x0010)(ReferenceParm)
	struct FCarInfo                               CallFunc_Array_Get_Item_5;                         // 0x0C30(0x00E0)()
	int32                                         CallFunc_Array_Length_ReturnValue_6;               // 0x0D10(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D14[0x4];                                      // 0x0D14(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue_1;            // 0x0D18(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_2;                // 0x0D20(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_D21[0x7];                                      // 0x0D21(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomTileItemHorizontal_C*      CallFunc_Create_ReturnValue_1;                     // 0x0D28(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue_1;                   // 0x0D30(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<class FName>                           CallFunc_Map_Keys_Keys_1;                          // 0x0D38(0x0010)(ReferenceParm)
	class FName                                   CallFunc_Array_Get_Item_6;                         // 0x0D48(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileBase_C*                K2Node_CustomEvent_Sender_2;                       // 0x0D50(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   K2Node_CustomEvent_Key_2;                          // 0x0D58(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_CustomEvent_KeyInt;                         // 0x0D60(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D64[0x4];                                      // 0x0D64(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomTileItemHorizontal_C*      CallFunc_Create_ReturnValue_2;                     // 0x0D68(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_NameName_ReturnValue_1;        // 0x0D70(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_D71[0x7];                                      // 0x0D71(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue_2;                   // 0x0D78(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileBase_C*                K2Node_ComponentBoundEvent_Sender_6;               // 0x0D80(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileBase_C*                K2Node_ComponentBoundEvent_Sender_5;               // 0x0D88(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   K2Node_ComponentBoundEvent_Key_5;                  // 0x0D90(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_KeyInt_1;               // 0x0D98(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D9C[0x4];                                      // 0x0D9C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTeamInfo                              K2Node_ComponentBoundEvent_TeamInfo;               // 0x0DA0(0x0038)()
	int32                                         CallFunc_Array_Length_ReturnValue_7;               // 0x0DD8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_6;             // 0x0DDC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_DDD[0x3];                                      // 0x0DDD(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class FName                                   K2Node_CustomEvent_Key_1;                          // 0x0DE0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      K2Node_CustomEvent_Sender_1;                       // 0x0DE8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   K2Node_CustomEvent_Key;                            // 0x0DF0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      K2Node_CustomEvent_Sender;                         // 0x0DF8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      CallFunc_FindSelectedEntry_AsWDG_Showroom_Tile_Item_Horizontal; // 0x0E00(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_8;               // 0x0E08(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_7;             // 0x0E0C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_E0D[0x3];                                      // 0x0E0D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Find_ReturnValue_2;                 // 0x0E10(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_9;               // 0x0E14(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_9;                 // 0x0E18(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_8;             // 0x0E1C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_E1D[0x3];                                      // 0x0E1D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Percent_IntInt_ReturnValue_4;             // 0x0E20(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_9;             // 0x0E24(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_E25[0x3];                                      // 0x0E25(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_10;                // 0x0E28(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue_5;                 // 0x0E2C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_E2D[0x3];                                      // 0x0E2D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Percent_IntInt_ReturnValue_5;             // 0x0E30(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   CallFunc_Array_Get_Item_7;                         // 0x0E34(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Find_ReturnValue_3;                 // 0x0E3C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_11;                // 0x0E40(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_10;            // 0x0E44(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_E45[0x3];                                      // 0x0E45(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Percent_IntInt_ReturnValue_6;             // 0x0E48(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue_6;                 // 0x0E4C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_E4D[0x3];                                      // 0x0E4D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_12;                // 0x0E50(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Percent_IntInt_ReturnValue_7;             // 0x0E54(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      CallFunc_FindSelectedEntry_AsWDG_Showroom_Tile_Item_Horizontal_1; // 0x0E58(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   CallFunc_Array_Get_Item_8;                         // 0x0E60(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FModelInfo                             K2Node_Event_ModelInfo;                            // 0x0E68(0x01A8)()
	struct FModelInfo                             K2Node_ComponentBoundEvent_ModelInfo;              // 0x1010(0x01A8)()
	struct FModelInfo                             K2Node_CustomEvent_ModelInfo;                      // 0x11B8(0x01A8)()
	class UWDG_ShowroomTileBase_C*                K2Node_ComponentBoundEvent_Sender_4;               // 0x1360(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   K2Node_ComponentBoundEvent_Key_4;                  // 0x1368(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_KeyInt;                 // 0x1370(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1374[0x4];                                     // 0x1374(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Conv_NameToString_ReturnValue_1;          // 0x1378(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	TArray<struct FModelInfo>                     CallFunc_GetModels_ReturnValue;                    // 0x1388(0x0010)(ReferenceParm)
	int32                                         CallFunc_Array_Length_ReturnValue_10;              // 0x1398(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_11;            // 0x139C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_139D[0x3];                                     // 0x139D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPanelBase*                           K2Node_ComponentBoundEvent_CallingPanel_2;         // 0x13A0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_ComponentBoundEvent_Cancelled;              // 0x13A8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x13A9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_13AA[0x6];                                     // 0x13AA(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPanelBase*                           K2Node_ComponentBoundEvent_CallingPanel_1;         // 0x13B0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void(class UWDG_ShowroomTileBase_C* Sender, class FName Key, int32 KeyInt)> K2Node_CreateDelegate_OutputDelegate_6; // 0x13B8(0x0010)(ZeroConstructor, NoDestructor)
	class UAcPanelBase*                           K2Node_ComponentBoundEvent_CallingPanel;           // 0x13C8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ObjectObject_ReturnValue_1;    // 0x13D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x13D1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_7;                 // 0x13D2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_7;                // 0x13D3(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_8;                 // 0x13D4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_11;           // 0x13D5(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsVisible_ReturnValue;                    // 0x13D6(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	EShowroomCarFilterType                        K2Node_ComponentBoundEvent_ActiveFilter_2;         // 0x13D7(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EShowroomCarFilterType                        K2Node_Event_filter;                               // 0x13D8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_13D9[0x7];                                     // 0x13D9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_GetEnumeratorUserFriendlyName_ReturnValue; // 0x13E0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_12;           // 0x13F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESeasonType                                   K2Node_Event_season;                               // 0x13F1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_13F2[0x2];                                     // 0x13F2(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class FName                                   K2Node_ComponentBoundEvent_Key_3;                  // 0x13F4(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_13FC[0x4];                                     // 0x13FC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomTileItemHorizontal_C*      K2Node_ComponentBoundEvent_Sender_3;               // 0x1400(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   K2Node_ComponentBoundEvent_Key_2;                  // 0x1408(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      K2Node_ComponentBoundEvent_Sender_2;               // 0x1410(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable_1;                               // 0x1418(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_141C[0x4];                                     // 0x141C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                CallFunc_GetChildAt_ReturnValue_1;                 // 0x1420(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_11;              // 0x1428(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_142C[0x4];                                     // 0x142C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomTileItemHorizontal_C*      K2Node_DynamicCast_AsWDG_Showroom_Tile_Item_Horizontal_1; // 0x1430(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_2;                     // 0x1438(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_12;            // 0x1439(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_143A[0x2];                                     // 0x143A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue_12;              // 0x143C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_NameName_ReturnValue_2;        // 0x1440(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_13;            // 0x1441(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1442[0x2];                                     // 0x1442(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_13;                // 0x1444(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_1;           // 0x1448(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_9;                 // 0x1449(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_144A[0x2];                                     // 0x144A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Find_ReturnValue_4;                 // 0x144C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Find_ReturnValue_5;                 // 0x1450(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_14;                // 0x1454(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_15;                // 0x1458(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Percent_IntInt_ReturnValue_8;             // 0x145C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Percent_IntInt_ReturnValue_9;             // 0x1460(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_16;                // 0x1464(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_17;                // 0x1468(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Percent_IntInt_ReturnValue_10;            // 0x146C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Percent_IntInt_ReturnValue_11;            // 0x1470(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1474[0x4];                                     // 0x1474(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FModelInfo                             CallFunc_Array_Get_Item_9;                         // 0x1478(0x01A8)()
	struct FModelInfo                             CallFunc_Array_Get_Item_10;                        // 0x1620(0x01A8)()
	bool                                          CallFunc_Greater_IntInt_ReturnValue_14;            // 0x17C8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_15;            // 0x17C9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_10;                // 0x17CA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_11;                // 0x17CB(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	class FName                                   K2Node_ComponentBoundEvent_Key_1;                  // 0x17CC(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_17D4[0x4];                                     // 0x17D4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomTileItemHorizontal_C*      K2Node_ComponentBoundEvent_Sender_1;               // 0x17D8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   K2Node_ComponentBoundEvent_Key;                    // 0x17E0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      K2Node_ComponentBoundEvent_Sender;                 // 0x17E8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_13;              // 0x17F0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Find_ReturnValue_6;                 // 0x17F4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_16;            // 0x17F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_17F9[0x3];                                     // 0x17F9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_18;                // 0x17FC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_17;            // 0x1800(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1801[0x3];                                     // 0x1801(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Percent_IntInt_ReturnValue_12;            // 0x1804(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue_12;                // 0x1808(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1809[0x3];                                     // 0x1809(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_19;                // 0x180C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Percent_IntInt_ReturnValue_13;            // 0x1810(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Find_ReturnValue_7;                 // 0x1814(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FTeamInfo                              CallFunc_Array_Get_Item_11;                        // 0x1818(0x0038)()
	int32                                         CallFunc_Add_IntInt_ReturnValue_20;                // 0x1850(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_18;            // 0x1854(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1855[0x3];                                     // 0x1855(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue_14;              // 0x1858(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_19;            // 0x185C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_13;                // 0x185D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_185E[0x2];                                     // 0x185E(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Percent_IntInt_ReturnValue_14;            // 0x1860(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_21;                // 0x1864(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Percent_IntInt_ReturnValue_15;            // 0x1868(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_186C[0x4];                                     // 0x186C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTeamInfo                              CallFunc_Array_Get_Item_12;                        // 0x1870(0x0038)()
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue_2;            // 0x18A8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class ABP_MenuController_C*                   K2Node_DynamicCast_AsBP_Menu_Controller;           // 0x18B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_3;                     // 0x18B8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          Temp_bool_Variable_3;                              // 0x18B9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_8;                // 0x18BA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_18BB[0x5];                                     // 0x18BB(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue;               // 0x18C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ECarGroup                                     K2Node_ComponentBoundEvent_ActiveFilter_1;         // 0x18C8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_18C9[0x7];                                     // 0x18C9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_CarGroupToTextFull_Output;                // 0x18D0(0x0018)()
	class FString                                 CallFunc_Conv_TextToString_ReturnValue_3;          // 0x18E8(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_9;                // 0x18F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_14;                // 0x18F9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_13;           // 0x18FA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESeasonType                                   K2Node_ComponentBoundEvent_ActiveFilter;           // 0x18FB(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_15;              // 0x18FC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_14;           // 0x1900(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_20;            // 0x1901(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_15;                // 0x1902(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_15;           // 0x1903(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_16;           // 0x1904(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x1905(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1906[0x2];                                     // 0x1906(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable_2;                               // 0x1908(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_190C[0x4];                                     // 0x190C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                CallFunc_GetChildAt_ReturnValue_2;                 // 0x1910(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      K2Node_DynamicCast_AsWDG_Showroom_Tile_Item_Horizontal_2; // 0x1918(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_4;                     // 0x1920(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_1921[0x3];                                     // 0x1921(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_22;                // 0x1924(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_NameName_ReturnValue_3;        // 0x1928(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_2;           // 0x1929(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_16;                // 0x192A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_10;               // 0x192B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_11;               // 0x192C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_12;               // 0x192D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_17;                // 0x192E(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_192F[0x1];                                     // 0x192F(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue_1;             // 0x1930(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsOfficialGameMode_ReturnValue;           // 0x1938(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_18;                // 0x1939(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_19;                // 0x193A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanOR_ReturnValue_1;                  // 0x193B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage");
static_assert(sizeof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage) == 0x001940, "Wrong size on WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, EntryPoint) == 0x000000, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_MakeLiteralByte_ReturnValue) == 0x000004, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000005, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, Temp_int_Array_Index_Variable) == 0x000008, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, Temp_int_Array_Index_Variable_1) == 0x00000C, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::Temp_int_Array_Index_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Add_IntInt_ReturnValue) == 0x000010, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Conv_IntToText_ReturnValue) == 0x000018, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Conv_IntToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_MakeLiteralByte_ReturnValue_2) == 0x000030, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_MakeLiteralByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_MakeLiteralByte_ReturnValue_3) == 0x000031, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_MakeLiteralByte_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_MakeLiteralByte_ReturnValue_4) == 0x000032, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_MakeLiteralByte_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, Temp_int_Loop_Counter_Variable) == 0x000034, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Add_IntInt_ReturnValue_1) == 0x000038, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, Temp_int_Array_Index_Variable_2) == 0x00003C, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::Temp_int_Array_Index_Variable_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, Temp_int_Loop_Counter_Variable_1) == 0x000040, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::Temp_int_Loop_Counter_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Add_IntInt_ReturnValue_2) == 0x000044, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Add_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_MakeLiteralByte_ReturnValue_5) == 0x000048, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_MakeLiteralByte_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_MakeLiteralByte_ReturnValue_6) == 0x000049, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_MakeLiteralByte_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, Temp_bool_Variable) == 0x00004A, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Not_PreBool_ReturnValue) == 0x00004B, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Not_PreBool_ReturnValue_1) == 0x00004C, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Not_PreBool_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_MakeLiteralByte_ReturnValue_7) == 0x00004D, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_MakeLiteralByte_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetGameInstance_ReturnValue) == 0x000050, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetGameInstance_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, Temp_int_Loop_Counter_Variable_2) == 0x000058, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::Temp_int_Loop_Counter_Variable_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_DynamicCast_AsAc_Game_Instance) == 0x000060, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_DynamicCast_AsAc_Game_Instance' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_DynamicCast_bSuccess) == 0x000068, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetGamePlatformServices_ReturnValue) == 0x000070, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetGamePlatformServices_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Add_IntInt_ReturnValue_3) == 0x000078, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Add_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetLocalUserAccount_ReturnValue) == 0x000080, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetLocalUserAccount_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_MakeStruct_DriverInfo) == 0x000140, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_MakeStruct_DriverInfo' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_MakeLiteralByte_ReturnValue_8) == 0x000230, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_MakeLiteralByte_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, Temp_bool_Variable_1) == 0x000231, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::Temp_bool_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Not_PreBool_ReturnValue_2) == 0x000232, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Not_PreBool_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_MakeLiteralByte_ReturnValue_9) == 0x000233, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_MakeLiteralByte_ReturnValue_9' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CreateDelegate_OutputDelegate) == 0x000234, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CreateDelegate_OutputDelegate_1) == 0x000244, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CreateDelegate_OutputDelegate_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CreateDelegate_OutputDelegate_2) == 0x000254, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CreateDelegate_OutputDelegate_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_source) == 0x000268, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_source' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_current_index) == 0x000270, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_current_index' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_current_value) == 0x000274, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_current_value' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetCarSystems_carSystems) == 0x000278, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetCarSystems_carSystems' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_IsDirectionLightLeftOn_ReturnValue) == 0x000280, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_IsDirectionLightLeftOn_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_IsRainLightOn_ReturnValue) == 0x000281, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_IsRainLightOn_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetCarSystems_carSystems_1) == 0x000288, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetCarSystems_carSystems_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_IsDoorOpen_ReturnValue) == 0x000290, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_IsDoorOpen_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_IsDoorOpen_ReturnValue_1) == 0x000291, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_IsDoorOpen_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetCarAnimations_CarAnimations) == 0x000298, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetCarAnimations_CarAnimations' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetCarSystems_carSystems_2) == 0x0002A0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetCarSystems_carSystems_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetValue_ReturnValue) == 0x0002A8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetValue_ReturnValue_1) == 0x0002AC, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetValue_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetSequencePlayer_ReturnValue) == 0x0002B0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetSequencePlayer_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetSequencePlayer_ReturnValue_1) == 0x0002B8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetSequencePlayer_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_IsShowroomPlaying_ReturnValue) == 0x0002C0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_IsShowroomPlaying_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_Event_Animation) == 0x0002C8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_Event_Animation' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_IsAnimationPlayingForward_ReturnValue) == 0x0002D0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_IsAnimationPlayingForward_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_EqualEqual_ObjectObject_ReturnValue) == 0x0002D1, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_EqualEqual_ObjectObject_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_PlayAnimation_ReturnValue) == 0x0002D8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_PlayAnimation_ReturnValue_1) == 0x0002E0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_PlayAnimation_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_PlayAnimation_ReturnValue_2) == 0x0002E8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_PlayAnimation_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_IsAnimationPlaying_ReturnValue) == 0x0002F0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_IsAnimationPlaying_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Not_PreBool_ReturnValue_3) == 0x0002F1, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Not_PreBool_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_BooleanAND_ReturnValue) == 0x0002F2, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Sender_13) == 0x0002F8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Sender_13' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_SaveCustomCar_ReturnValue) == 0x000300, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_SaveCustomCar_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_HasConfirmation) == 0x000301, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_HasConfirmation' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Sender_12) == 0x000308, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Sender_12' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_DeleteCustomCar_ReturnValue) == 0x000310, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_DeleteCustomCar_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Sender_11) == 0x000318, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Sender_11' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_SaveCustomCar_ReturnValue_1) == 0x000320, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_SaveCustomCar_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_IsNewCustomCar_ReturnValue) == 0x000321, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_IsNewCustomCar_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Sender_10) == 0x000328, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Sender_10' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Sender_9) == 0x000330, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Sender_9' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_Event_ModelInfo_1) == 0x000338, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_Event_ModelInfo_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Sender_8) == 0x0004E0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Sender_8' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Key_7) == 0x0004E8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Key_7' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_KeyInt_3) == 0x0004F0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_KeyInt_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetAllModels_ReturnValue) == 0x0004F8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetAllModels_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Text_1) == 0x000508, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Text_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Conv_TextToString_ReturnValue) == 0x000520, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Conv_TextToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Conv_TextToString_ReturnValue_1) == 0x000530, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Conv_TextToString_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetZeroPaddingFromRaceNumber_ReturnValue) == 0x000540, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetZeroPaddingFromRaceNumber_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Conv_StringToInt_ReturnValue) == 0x000544, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Conv_StringToInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Conv_IntToByte_ReturnValue) == 0x000548, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Conv_IntToByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_CarInfo) == 0x000550, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_CarInfo' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Conv_StringToText_ReturnValue) == 0x000630, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Conv_ByteToInt_ReturnValue) == 0x000648, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Conv_ByteToInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_PadNumberWithZeroesAlways_ReturnValue) == 0x000650, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_PadNumberWithZeroesAlways_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Text) == 0x000668, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Text' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Conv_TextToString_ReturnValue_2) == 0x000680, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Conv_TextToString_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetAllModels_ReturnValue_1) == 0x000690, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetAllModels_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Length_ReturnValue) == 0x0006A0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_Event_driverInfo) == 0x0006A8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_Event_driverInfo' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_Event_DriverKey) == 0x000798, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_Event_DriverKey' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Greater_IntInt_ReturnValue) == 0x0007A0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetChildrenCount_ReturnValue) == 0x0007A4, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetChildrenCount_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Sender_7) == 0x0007A8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Sender_7' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Key_6) == 0x0007B0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Key_6' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_KeyInt_2) == 0x0007B8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_KeyInt_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Subtract_IntInt_ReturnValue) == 0x0007BC, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_TextIsEmpty_ReturnValue) == 0x0007C0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_TextIsEmpty_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_IsCurrentCarCustom_isCustom) == 0x0007C1, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_IsCurrentCarCustom_isCustom' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Not_PreBool_ReturnValue_4) == 0x0007C2, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Not_PreBool_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_IsCurrentCarCustom_isCustom_1) == 0x0007C3, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_IsCurrentCarCustom_isCustom_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_Event_carInfo) == 0x0007C8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_Event_carInfo' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_Event_CarKey) == 0x0008A8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_Event_CarKey' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetChildrenCount_ReturnValue_1) == 0x0008B0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetChildrenCount_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Subtract_IntInt_ReturnValue_1) == 0x0008B4, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Subtract_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Map_Keys_Keys) == 0x0008B8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Map_Keys_Keys' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Map_Values_Values) == 0x0008C8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Map_Values_Values' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Get_Item) == 0x0008D8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Get_Item_1) == 0x0008E0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Conv_NameToString_ReturnValue) == 0x0009D0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Conv_NameToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Length_ReturnValue_1) == 0x0009E0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Less_IntInt_ReturnValue) == 0x0009E4, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CustomEvent_Sender_6) == 0x0009E8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CustomEvent_Sender_6' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CustomEvent_Key_5) == 0x0009F0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CustomEvent_Key_5' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CustomEvent_KeyInt_1) == 0x0009F8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CustomEvent_KeyInt_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetOwningPlayer_ReturnValue) == 0x000A00, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetOwningPlayer_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_NotEqual_NameName_ReturnValue) == 0x000A08, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_NotEqual_NameName_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Create_ReturnValue) == 0x000A10, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_AddChild_ReturnValue) == 0x000A18, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_AddChild_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CreateDelegate_OutputDelegate_3) == 0x000A20, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CreateDelegate_OutputDelegate_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CreateDelegate_OutputDelegate_4) == 0x000A30, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CreateDelegate_OutputDelegate_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_EqualEqual_NameName_ReturnValue) == 0x000A40, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_EqualEqual_NameName_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CreateDelegate_OutputDelegate_5) == 0x000A44, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CreateDelegate_OutputDelegate_5' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_Event_variant_key) == 0x000A54, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_Event_variant_key' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Get_Item_2) == 0x000A58, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Get_Item_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Length_ReturnValue_2) == 0x000AA0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Length_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Less_IntInt_ReturnValue_1) == 0x000AA4, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Less_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_IsVariantSelected_ReturnValue) == 0x000AA5, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_IsVariantSelected_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, Temp_int_Variable) == 0x000AA8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Add_IntInt_ReturnValue_4) == 0x000AAC, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Add_IntInt_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_MakeLiteralByte_ReturnValue_10) == 0x000AB0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_MakeLiteralByte_ReturnValue_10' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Length_ReturnValue_3) == 0x000AB4, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Length_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Greater_IntInt_ReturnValue_1) == 0x000AB8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Greater_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_MakeStruct_SlateChildSize) == 0x000ABC, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_MakeStruct_SlateChildSize' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_MakeStruct_SlateChildSize_1) == 0x000AC4, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_MakeStruct_SlateChildSize_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_SlotAsHorizontalBoxSlot_ReturnValue) == 0x000AD0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_SlotAsHorizontalBoxSlot_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CustomEvent_Sender_5) == 0x000AD8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CustomEvent_Sender_5' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CustomEvent_ColorCode) == 0x000AE0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CustomEvent_ColorCode' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CustomEvent_Color) == 0x000AE4, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CustomEvent_Color' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetChildAt_ReturnValue) == 0x000AF8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetChildAt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetChildrenCount_ReturnValue_2) == 0x000B00, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetChildrenCount_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_DynamicCast_AsWDG_Showroom_Tile_Item_Horizontal) == 0x000B08, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_DynamicCast_AsWDG_Showroom_Tile_Item_Horizontal' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_DynamicCast_bSuccess_1) == 0x000B10, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Subtract_IntInt_ReturnValue_2) == 0x000B14, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Subtract_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_LessEqual_IntInt_ReturnValue) == 0x000B18, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x000B19, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_BooleanAND_ReturnValue_1) == 0x000B1A, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, Temp_bool_Variable_2) == 0x000B1B, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::Temp_bool_Variable_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CustomEvent_Key_4) == 0x000B1C, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CustomEvent_Key_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CustomEvent_Sender_4) == 0x000B28, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CustomEvent_Sender_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Not_PreBool_ReturnValue_5) == 0x000B30, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Not_PreBool_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Length_ReturnValue_4) == 0x000B34, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Length_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Length_ReturnValue_5) == 0x000B38, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Length_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Greater_IntInt_ReturnValue_2) == 0x000B3C, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Greater_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Greater_IntInt_ReturnValue_3) == 0x000B3D, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Greater_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Find_ReturnValue) == 0x000B40, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Find_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_FindSelectedVariant_AsWDG_Showroom_Tile_Item_Horizontal) == 0x000B48, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_FindSelectedVariant_AsWDG_Showroom_Tile_Item_Horizontal' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Add_IntInt_ReturnValue_5) == 0x000B50, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Add_IntInt_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Percent_IntInt_ReturnValue) == 0x000B54, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Percent_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Greater_IntInt_ReturnValue_4) == 0x000B58, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Greater_IntInt_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Add_IntInt_ReturnValue_6) == 0x000B5C, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Add_IntInt_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_BooleanAND_ReturnValue_2) == 0x000B60, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_BooleanAND_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Percent_IntInt_ReturnValue_1) == 0x000B64, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Percent_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Get_Item_3) == 0x000B68, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Get_Item_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_FindSelectedVariant_AsWDG_Showroom_Tile_Item_Horizontal_1) == 0x000B70, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_FindSelectedVariant_AsWDG_Showroom_Tile_Item_Horizontal_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CustomEvent_Key_3) == 0x000B78, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CustomEvent_Key_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CustomEvent_Sender_3) == 0x000B80, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CustomEvent_Sender_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Find_ReturnValue_1) == 0x000B88, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Find_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Add_IntInt_ReturnValue_7) == 0x000B8C, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Add_IntInt_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Percent_IntInt_ReturnValue_2) == 0x000B90, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Percent_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Greater_IntInt_ReturnValue_5) == 0x000B94, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Greater_IntInt_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Add_IntInt_ReturnValue_8) == 0x000B98, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Add_IntInt_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_BooleanAND_ReturnValue_3) == 0x000B9C, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_BooleanAND_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Percent_IntInt_ReturnValue_3) == 0x000BA0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Percent_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Get_Item_4) == 0x000BA4, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Get_Item_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_IsCurrentCarCustom_isCustom_2) == 0x000BA8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_IsCurrentCarCustom_isCustom_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Not_PreBool_ReturnValue_6) == 0x000BA9, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Not_PreBool_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_BooleanAND_ReturnValue_4) == 0x000BAA, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_BooleanAND_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_Event_TeamInfo) == 0x000BB0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_Event_TeamInfo' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CustomEvent_TeamInfo) == 0x000BE8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CustomEvent_TeamInfo' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Map_Values_Values_1) == 0x000C20, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Map_Values_Values_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Get_Item_5) == 0x000C30, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Get_Item_5' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Length_ReturnValue_6) == 0x000D10, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Length_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetOwningPlayer_ReturnValue_1) == 0x000D18, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetOwningPlayer_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Less_IntInt_ReturnValue_2) == 0x000D20, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Less_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Create_ReturnValue_1) == 0x000D28, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Create_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_AddChild_ReturnValue_1) == 0x000D30, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_AddChild_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Map_Keys_Keys_1) == 0x000D38, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Map_Keys_Keys_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Get_Item_6) == 0x000D48, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Get_Item_6' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CustomEvent_Sender_2) == 0x000D50, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CustomEvent_Sender_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CustomEvent_Key_2) == 0x000D58, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CustomEvent_Key_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CustomEvent_KeyInt) == 0x000D60, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CustomEvent_KeyInt' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Create_ReturnValue_2) == 0x000D68, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Create_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_EqualEqual_NameName_ReturnValue_1) == 0x000D70, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_EqualEqual_NameName_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_AddChild_ReturnValue_2) == 0x000D78, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_AddChild_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Sender_6) == 0x000D80, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Sender_6' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Sender_5) == 0x000D88, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Sender_5' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Key_5) == 0x000D90, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Key_5' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_KeyInt_1) == 0x000D98, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_KeyInt_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_TeamInfo) == 0x000DA0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_TeamInfo' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Length_ReturnValue_7) == 0x000DD8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Length_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Greater_IntInt_ReturnValue_6) == 0x000DDC, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Greater_IntInt_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CustomEvent_Key_1) == 0x000DE0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CustomEvent_Key_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CustomEvent_Sender_1) == 0x000DE8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CustomEvent_Sender_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CustomEvent_Key) == 0x000DF0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CustomEvent_Key' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CustomEvent_Sender) == 0x000DF8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CustomEvent_Sender' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_FindSelectedEntry_AsWDG_Showroom_Tile_Item_Horizontal) == 0x000E00, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_FindSelectedEntry_AsWDG_Showroom_Tile_Item_Horizontal' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Length_ReturnValue_8) == 0x000E08, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Length_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Greater_IntInt_ReturnValue_7) == 0x000E0C, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Greater_IntInt_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Find_ReturnValue_2) == 0x000E10, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Find_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Length_ReturnValue_9) == 0x000E14, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Length_ReturnValue_9' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Add_IntInt_ReturnValue_9) == 0x000E18, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Add_IntInt_ReturnValue_9' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Greater_IntInt_ReturnValue_8) == 0x000E1C, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Greater_IntInt_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Percent_IntInt_ReturnValue_4) == 0x000E20, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Percent_IntInt_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Greater_IntInt_ReturnValue_9) == 0x000E24, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Greater_IntInt_ReturnValue_9' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Add_IntInt_ReturnValue_10) == 0x000E28, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Add_IntInt_ReturnValue_10' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_BooleanAND_ReturnValue_5) == 0x000E2C, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_BooleanAND_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Percent_IntInt_ReturnValue_5) == 0x000E30, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Percent_IntInt_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Get_Item_7) == 0x000E34, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Get_Item_7' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Find_ReturnValue_3) == 0x000E3C, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Find_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Add_IntInt_ReturnValue_11) == 0x000E40, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Add_IntInt_ReturnValue_11' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Greater_IntInt_ReturnValue_10) == 0x000E44, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Greater_IntInt_ReturnValue_10' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Percent_IntInt_ReturnValue_6) == 0x000E48, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Percent_IntInt_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_BooleanAND_ReturnValue_6) == 0x000E4C, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_BooleanAND_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Add_IntInt_ReturnValue_12) == 0x000E50, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Add_IntInt_ReturnValue_12' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Percent_IntInt_ReturnValue_7) == 0x000E54, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Percent_IntInt_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_FindSelectedEntry_AsWDG_Showroom_Tile_Item_Horizontal_1) == 0x000E58, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_FindSelectedEntry_AsWDG_Showroom_Tile_Item_Horizontal_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Get_Item_8) == 0x000E60, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Get_Item_8' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_Event_ModelInfo) == 0x000E68, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_Event_ModelInfo' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_ModelInfo) == 0x001010, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_ModelInfo' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CustomEvent_ModelInfo) == 0x0011B8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CustomEvent_ModelInfo' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Sender_4) == 0x001360, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Sender_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Key_4) == 0x001368, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Key_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_KeyInt) == 0x001370, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_KeyInt' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Conv_NameToString_ReturnValue_1) == 0x001378, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Conv_NameToString_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetModels_ReturnValue) == 0x001388, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetModels_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Length_ReturnValue_10) == 0x001398, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Length_ReturnValue_10' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Greater_IntInt_ReturnValue_11) == 0x00139C, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Greater_IntInt_ReturnValue_11' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_CallingPanel_2) == 0x0013A0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_CallingPanel_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Cancelled) == 0x0013A8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Cancelled' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_IsValid_ReturnValue) == 0x0013A9, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_CallingPanel_1) == 0x0013B0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_CallingPanel_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_CreateDelegate_OutputDelegate_6) == 0x0013B8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_CreateDelegate_OutputDelegate_6' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_CallingPanel) == 0x0013C8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_CallingPanel' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_EqualEqual_ObjectObject_ReturnValue_1) == 0x0013D0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_EqualEqual_ObjectObject_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_IsValid_ReturnValue_1) == 0x0013D1, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_BooleanAND_ReturnValue_7) == 0x0013D2, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_BooleanAND_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Not_PreBool_ReturnValue_7) == 0x0013D3, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Not_PreBool_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_BooleanAND_ReturnValue_8) == 0x0013D4, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_BooleanAND_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_MakeLiteralByte_ReturnValue_11) == 0x0013D5, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_MakeLiteralByte_ReturnValue_11' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_IsVisible_ReturnValue) == 0x0013D6, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_IsVisible_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_ActiveFilter_2) == 0x0013D7, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_ActiveFilter_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_Event_filter) == 0x0013D8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_Event_filter' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetEnumeratorUserFriendlyName_ReturnValue) == 0x0013E0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetEnumeratorUserFriendlyName_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_MakeLiteralByte_ReturnValue_12) == 0x0013F0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_MakeLiteralByte_ReturnValue_12' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_Event_season) == 0x0013F1, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_Event_season' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Key_3) == 0x0013F4, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Key_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Sender_3) == 0x001400, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Sender_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Key_2) == 0x001408, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Key_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Sender_2) == 0x001410, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Sender_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, Temp_int_Variable_1) == 0x001418, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetChildAt_ReturnValue_1) == 0x001420, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetChildAt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Length_ReturnValue_11) == 0x001428, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Length_ReturnValue_11' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_DynamicCast_AsWDG_Showroom_Tile_Item_Horizontal_1) == 0x001430, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_DynamicCast_AsWDG_Showroom_Tile_Item_Horizontal_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_DynamicCast_bSuccess_2) == 0x001438, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_DynamicCast_bSuccess_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Greater_IntInt_ReturnValue_12) == 0x001439, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Greater_IntInt_ReturnValue_12' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Length_ReturnValue_12) == 0x00143C, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Length_ReturnValue_12' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_EqualEqual_NameName_ReturnValue_2) == 0x001440, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_EqualEqual_NameName_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Greater_IntInt_ReturnValue_13) == 0x001441, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Greater_IntInt_ReturnValue_13' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Add_IntInt_ReturnValue_13) == 0x001444, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Add_IntInt_ReturnValue_13' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_LessEqual_IntInt_ReturnValue_1) == 0x001448, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_LessEqual_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_BooleanAND_ReturnValue_9) == 0x001449, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_BooleanAND_ReturnValue_9' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Find_ReturnValue_4) == 0x00144C, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Find_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Find_ReturnValue_5) == 0x001450, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Find_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Add_IntInt_ReturnValue_14) == 0x001454, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Add_IntInt_ReturnValue_14' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Add_IntInt_ReturnValue_15) == 0x001458, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Add_IntInt_ReturnValue_15' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Percent_IntInt_ReturnValue_8) == 0x00145C, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Percent_IntInt_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Percent_IntInt_ReturnValue_9) == 0x001460, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Percent_IntInt_ReturnValue_9' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Add_IntInt_ReturnValue_16) == 0x001464, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Add_IntInt_ReturnValue_16' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Add_IntInt_ReturnValue_17) == 0x001468, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Add_IntInt_ReturnValue_17' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Percent_IntInt_ReturnValue_10) == 0x00146C, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Percent_IntInt_ReturnValue_10' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Percent_IntInt_ReturnValue_11) == 0x001470, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Percent_IntInt_ReturnValue_11' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Get_Item_9) == 0x001478, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Get_Item_9' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Get_Item_10) == 0x001620, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Get_Item_10' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Greater_IntInt_ReturnValue_14) == 0x0017C8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Greater_IntInt_ReturnValue_14' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Greater_IntInt_ReturnValue_15) == 0x0017C9, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Greater_IntInt_ReturnValue_15' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_BooleanAND_ReturnValue_10) == 0x0017CA, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_BooleanAND_ReturnValue_10' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_BooleanAND_ReturnValue_11) == 0x0017CB, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_BooleanAND_ReturnValue_11' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Key_1) == 0x0017CC, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Key_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Sender_1) == 0x0017D8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Sender_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Key) == 0x0017E0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Key' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_Sender) == 0x0017E8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_Sender' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Length_ReturnValue_13) == 0x0017F0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Length_ReturnValue_13' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Find_ReturnValue_6) == 0x0017F4, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Find_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Greater_IntInt_ReturnValue_16) == 0x0017F8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Greater_IntInt_ReturnValue_16' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Add_IntInt_ReturnValue_18) == 0x0017FC, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Add_IntInt_ReturnValue_18' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Greater_IntInt_ReturnValue_17) == 0x001800, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Greater_IntInt_ReturnValue_17' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Percent_IntInt_ReturnValue_12) == 0x001804, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Percent_IntInt_ReturnValue_12' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_BooleanAND_ReturnValue_12) == 0x001808, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_BooleanAND_ReturnValue_12' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Add_IntInt_ReturnValue_19) == 0x00180C, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Add_IntInt_ReturnValue_19' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Percent_IntInt_ReturnValue_13) == 0x001810, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Percent_IntInt_ReturnValue_13' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Find_ReturnValue_7) == 0x001814, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Find_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Get_Item_11) == 0x001818, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Get_Item_11' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Add_IntInt_ReturnValue_20) == 0x001850, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Add_IntInt_ReturnValue_20' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Greater_IntInt_ReturnValue_18) == 0x001854, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Greater_IntInt_ReturnValue_18' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Length_ReturnValue_14) == 0x001858, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Length_ReturnValue_14' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Greater_IntInt_ReturnValue_19) == 0x00185C, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Greater_IntInt_ReturnValue_19' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_BooleanAND_ReturnValue_13) == 0x00185D, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_BooleanAND_ReturnValue_13' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Percent_IntInt_ReturnValue_14) == 0x001860, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Percent_IntInt_ReturnValue_14' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Add_IntInt_ReturnValue_21) == 0x001864, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Add_IntInt_ReturnValue_21' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Percent_IntInt_ReturnValue_15) == 0x001868, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Percent_IntInt_ReturnValue_15' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Get_Item_12) == 0x001870, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Get_Item_12' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetOwningPlayer_ReturnValue_2) == 0x0018A8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetOwningPlayer_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_DynamicCast_AsBP_Menu_Controller) == 0x0018B0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_DynamicCast_AsBP_Menu_Controller' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_DynamicCast_bSuccess_3) == 0x0018B8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_DynamicCast_bSuccess_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, Temp_bool_Variable_3) == 0x0018B9, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::Temp_bool_Variable_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Not_PreBool_ReturnValue_8) == 0x0018BA, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Not_PreBool_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetMenuManager_ReturnValue) == 0x0018C0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetMenuManager_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_ActiveFilter_1) == 0x0018C8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_ActiveFilter_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_CarGroupToTextFull_Output) == 0x0018D0, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_CarGroupToTextFull_Output' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Conv_TextToString_ReturnValue_3) == 0x0018E8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Conv_TextToString_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Not_PreBool_ReturnValue_9) == 0x0018F8, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Not_PreBool_ReturnValue_9' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_BooleanAND_ReturnValue_14) == 0x0018F9, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_BooleanAND_ReturnValue_14' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_MakeLiteralByte_ReturnValue_13) == 0x0018FA, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_MakeLiteralByte_ReturnValue_13' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_ComponentBoundEvent_ActiveFilter) == 0x0018FB, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_ComponentBoundEvent_ActiveFilter' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Array_Length_ReturnValue_15) == 0x0018FC, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Array_Length_ReturnValue_15' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_MakeLiteralByte_ReturnValue_14) == 0x001900, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_MakeLiteralByte_ReturnValue_14' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Greater_IntInt_ReturnValue_20) == 0x001901, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Greater_IntInt_ReturnValue_20' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_BooleanAND_ReturnValue_15) == 0x001902, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_BooleanAND_ReturnValue_15' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_MakeLiteralByte_ReturnValue_15) == 0x001903, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_MakeLiteralByte_ReturnValue_15' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_MakeLiteralByte_ReturnValue_16) == 0x001904, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_MakeLiteralByte_ReturnValue_16' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_BooleanOR_ReturnValue) == 0x001905, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, Temp_int_Variable_2) == 0x001908, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::Temp_int_Variable_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetChildAt_ReturnValue_2) == 0x001910, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetChildAt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_DynamicCast_AsWDG_Showroom_Tile_Item_Horizontal_2) == 0x001918, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_DynamicCast_AsWDG_Showroom_Tile_Item_Horizontal_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, K2Node_DynamicCast_bSuccess_4) == 0x001920, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::K2Node_DynamicCast_bSuccess_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Add_IntInt_ReturnValue_22) == 0x001924, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Add_IntInt_ReturnValue_22' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_EqualEqual_NameName_ReturnValue_3) == 0x001928, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_EqualEqual_NameName_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_LessEqual_IntInt_ReturnValue_2) == 0x001929, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_LessEqual_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_BooleanAND_ReturnValue_16) == 0x00192A, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_BooleanAND_ReturnValue_16' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Not_PreBool_ReturnValue_10) == 0x00192B, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Not_PreBool_ReturnValue_10' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Not_PreBool_ReturnValue_11) == 0x00192C, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Not_PreBool_ReturnValue_11' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_Not_PreBool_ReturnValue_12) == 0x00192D, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_Not_PreBool_ReturnValue_12' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_BooleanAND_ReturnValue_17) == 0x00192E, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_BooleanAND_ReturnValue_17' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_GetMenuManager_ReturnValue_1) == 0x001930, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_GetMenuManager_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_IsOfficialGameMode_ReturnValue) == 0x001938, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_IsOfficialGameMode_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_BooleanAND_ReturnValue_18) == 0x001939, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_BooleanAND_ReturnValue_18' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_BooleanAND_ReturnValue_19) == 0x00193A, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_BooleanAND_ReturnValue_19' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage, CallFunc_BooleanOR_ReturnValue_1) == 0x00193B, "Member 'WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage::CallFunc_BooleanOR_ReturnValue_1' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__WDG_ShowroomPage_SeasonTypeFilter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__WDG_ShowroomPage_SeasonTypeFilter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature final
{
public:
	ESeasonType                                   activeFilter_0;                                    // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__WDG_ShowroomPage_SeasonTypeFilter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature) == 0x000001, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__WDG_ShowroomPage_SeasonTypeFilter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__WDG_ShowroomPage_SeasonTypeFilter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature) == 0x000001, "Wrong size on WDG_ShowroomPage_C_BndEvt__WDG_ShowroomPage_SeasonTypeFilter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__WDG_ShowroomPage_SeasonTypeFilter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature, activeFilter_0) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__WDG_ShowroomPage_SeasonTypeFilter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature::activeFilter_0' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__WDG_ShowroomPage_CarGroupFilter_K2Node_ComponentBoundEvent_9_OnFilterSelected__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__WDG_ShowroomPage_CarGroupFilter_K2Node_ComponentBoundEvent_9_OnFilterSelected__DelegateSignature final
{
public:
	ECarGroup                                     activeFilter_0;                                    // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__WDG_ShowroomPage_CarGroupFilter_K2Node_ComponentBoundEvent_9_OnFilterSelected__DelegateSignature) == 0x000001, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__WDG_ShowroomPage_CarGroupFilter_K2Node_ComponentBoundEvent_9_OnFilterSelected__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__WDG_ShowroomPage_CarGroupFilter_K2Node_ComponentBoundEvent_9_OnFilterSelected__DelegateSignature) == 0x000001, "Wrong size on WDG_ShowroomPage_C_BndEvt__WDG_ShowroomPage_CarGroupFilter_K2Node_ComponentBoundEvent_9_OnFilterSelected__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__WDG_ShowroomPage_CarGroupFilter_K2Node_ComponentBoundEvent_9_OnFilterSelected__DelegateSignature, activeFilter_0) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__WDG_ShowroomPage_CarGroupFilter_K2Node_ComponentBoundEvent_9_OnFilterSelected__DelegateSignature::activeFilter_0' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_7_OnItemPrevious__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_7_OnItemPrevious__DelegateSignature final
{
public:
	class FName                                   Key;                                               // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      Sender;                                            // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_7_OnItemPrevious__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_7_OnItemPrevious__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_7_OnItemPrevious__DelegateSignature) == 0x000010, "Wrong size on WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_7_OnItemPrevious__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_7_OnItemPrevious__DelegateSignature, Key) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_7_OnItemPrevious__DelegateSignature::Key' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_7_OnItemPrevious__DelegateSignature, Sender) == 0x000008, "Member 'WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_7_OnItemPrevious__DelegateSignature::Sender' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_5_OnItemNext__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_5_OnItemNext__DelegateSignature final
{
public:
	class FName                                   Key;                                               // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      Sender;                                            // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_5_OnItemNext__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_5_OnItemNext__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_5_OnItemNext__DelegateSignature) == 0x000010, "Wrong size on WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_5_OnItemNext__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_5_OnItemNext__DelegateSignature, Key) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_5_OnItemNext__DelegateSignature::Key' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_5_OnItemNext__DelegateSignature, Sender) == 0x000008, "Member 'WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_5_OnItemNext__DelegateSignature::Sender' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__CurrentModel_K2Node_ComponentBoundEvent_3_OnItemPrevious__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__CurrentModel_K2Node_ComponentBoundEvent_3_OnItemPrevious__DelegateSignature final
{
public:
	class FName                                   Key;                                               // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      Sender;                                            // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__CurrentModel_K2Node_ComponentBoundEvent_3_OnItemPrevious__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__CurrentModel_K2Node_ComponentBoundEvent_3_OnItemPrevious__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__CurrentModel_K2Node_ComponentBoundEvent_3_OnItemPrevious__DelegateSignature) == 0x000010, "Wrong size on WDG_ShowroomPage_C_BndEvt__CurrentModel_K2Node_ComponentBoundEvent_3_OnItemPrevious__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__CurrentModel_K2Node_ComponentBoundEvent_3_OnItemPrevious__DelegateSignature, Key) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__CurrentModel_K2Node_ComponentBoundEvent_3_OnItemPrevious__DelegateSignature::Key' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__CurrentModel_K2Node_ComponentBoundEvent_3_OnItemPrevious__DelegateSignature, Sender) == 0x000008, "Member 'WDG_ShowroomPage_C_BndEvt__CurrentModel_K2Node_ComponentBoundEvent_3_OnItemPrevious__DelegateSignature::Sender' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__CurrentModel_K2Node_ComponentBoundEvent_1_OnItemNext__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__CurrentModel_K2Node_ComponentBoundEvent_1_OnItemNext__DelegateSignature final
{
public:
	class FName                                   Key;                                               // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      Sender;                                            // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__CurrentModel_K2Node_ComponentBoundEvent_1_OnItemNext__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__CurrentModel_K2Node_ComponentBoundEvent_1_OnItemNext__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__CurrentModel_K2Node_ComponentBoundEvent_1_OnItemNext__DelegateSignature) == 0x000010, "Wrong size on WDG_ShowroomPage_C_BndEvt__CurrentModel_K2Node_ComponentBoundEvent_1_OnItemNext__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__CurrentModel_K2Node_ComponentBoundEvent_1_OnItemNext__DelegateSignature, Key) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__CurrentModel_K2Node_ComponentBoundEvent_1_OnItemNext__DelegateSignature::Key' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__CurrentModel_K2Node_ComponentBoundEvent_1_OnItemNext__DelegateSignature, Sender) == 0x000008, "Member 'WDG_ShowroomPage_C_BndEvt__CurrentModel_K2Node_ComponentBoundEvent_1_OnItemNext__DelegateSignature::Sender' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnEntryListSeason
// 0x0001 (0x0001 - 0x0000)
struct WDG_ShowroomPage_C_OnEntryListSeason final
{
public:
	ESeasonType                                   Season;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_OnEntryListSeason) == 0x000001, "Wrong alignment on WDG_ShowroomPage_C_OnEntryListSeason");
static_assert(sizeof(WDG_ShowroomPage_C_OnEntryListSeason) == 0x000001, "Wrong size on WDG_ShowroomPage_C_OnEntryListSeason");
static_assert(offsetof(WDG_ShowroomPage_C_OnEntryListSeason, Season) == 0x000000, "Member 'WDG_ShowroomPage_C_OnEntryListSeason::Season' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnFilterApplied
// 0x0001 (0x0001 - 0x0000)
struct WDG_ShowroomPage_C_OnFilterApplied final
{
public:
	EShowroomCarFilterType                        Filter_0;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_OnFilterApplied) == 0x000001, "Wrong alignment on WDG_ShowroomPage_C_OnFilterApplied");
static_assert(sizeof(WDG_ShowroomPage_C_OnFilterApplied) == 0x000001, "Wrong size on WDG_ShowroomPage_C_OnFilterApplied");
static_assert(offsetof(WDG_ShowroomPage_C_OnFilterApplied, Filter_0) == 0x000000, "Member 'WDG_ShowroomPage_C_OnFilterApplied::Filter_0' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__Filter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__Filter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature final
{
public:
	EShowroomCarFilterType                        activeFilter_0;                                    // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__Filter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature) == 0x000001, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__Filter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__Filter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature) == 0x000001, "Wrong size on WDG_ShowroomPage_C_BndEvt__Filter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__Filter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature, activeFilter_0) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__Filter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature::activeFilter_0' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnCancel__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnCancel__DelegateSignature final
{
public:
	class UAcPanelBase*                           CallingPanel;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnCancel__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnCancel__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnCancel__DelegateSignature) == 0x000008, "Wrong size on WDG_ShowroomPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnCancel__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnCancel__DelegateSignature, CallingPanel) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnCancel__DelegateSignature::CallingPanel' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__modalOverlay_K2Node_ComponentBoundEvent_1_OnShow__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__modalOverlay_K2Node_ComponentBoundEvent_1_OnShow__DelegateSignature final
{
public:
	class UAcPanelBase*                           CallingPanel;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__modalOverlay_K2Node_ComponentBoundEvent_1_OnShow__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__modalOverlay_K2Node_ComponentBoundEvent_1_OnShow__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__modalOverlay_K2Node_ComponentBoundEvent_1_OnShow__DelegateSignature) == 0x000008, "Wrong size on WDG_ShowroomPage_C_BndEvt__modalOverlay_K2Node_ComponentBoundEvent_1_OnShow__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__modalOverlay_K2Node_ComponentBoundEvent_1_OnShow__DelegateSignature, CallingPanel) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__modalOverlay_K2Node_ComponentBoundEvent_1_OnShow__DelegateSignature::CallingPanel' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__modalOverlay_K2Node_ComponentBoundEvent_0_OnHide__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__modalOverlay_K2Node_ComponentBoundEvent_0_OnHide__DelegateSignature final
{
public:
	class UAcPanelBase*                           CallingPanel;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Cancelled;                                         // 0x0008(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__modalOverlay_K2Node_ComponentBoundEvent_0_OnHide__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__modalOverlay_K2Node_ComponentBoundEvent_0_OnHide__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__modalOverlay_K2Node_ComponentBoundEvent_0_OnHide__DelegateSignature) == 0x000010, "Wrong size on WDG_ShowroomPage_C_BndEvt__modalOverlay_K2Node_ComponentBoundEvent_0_OnHide__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__modalOverlay_K2Node_ComponentBoundEvent_0_OnHide__DelegateSignature, CallingPanel) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__modalOverlay_K2Node_ComponentBoundEvent_0_OnHide__DelegateSignature::CallingPanel' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__modalOverlay_K2Node_ComponentBoundEvent_0_OnHide__DelegateSignature, Cancelled) == 0x000008, "Member 'WDG_ShowroomPage_C_BndEvt__modalOverlay_K2Node_ComponentBoundEvent_0_OnHide__DelegateSignature::Cancelled' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__WDG_ModelSelectorItemHorizontal_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature
// 0x0018 (0x0018 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__WDG_ModelSelectorItemHorizontal_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature final
{
public:
	class UWDG_ShowroomTileBase_C*                Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   Key;                                               // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         KeyInt;                                            // 0x0010(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__WDG_ModelSelectorItemHorizontal_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__WDG_ModelSelectorItemHorizontal_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__WDG_ModelSelectorItemHorizontal_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature) == 0x000018, "Wrong size on WDG_ShowroomPage_C_BndEvt__WDG_ModelSelectorItemHorizontal_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__WDG_ModelSelectorItemHorizontal_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature, Sender) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__WDG_ModelSelectorItemHorizontal_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature::Sender' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__WDG_ModelSelectorItemHorizontal_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature, Key) == 0x000008, "Member 'WDG_ShowroomPage_C_BndEvt__WDG_ModelSelectorItemHorizontal_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature::Key' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__WDG_ModelSelectorItemHorizontal_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature, KeyInt) == 0x000010, "Member 'WDG_ShowroomPage_C_BndEvt__WDG_ModelSelectorItemHorizontal_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature::KeyInt' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.Update Model
// 0x01A8 (0x01A8 - 0x0000)
struct WDG_ShowroomPage_C_Update_Model final
{
public:
	struct FModelInfo                             ModelInfo;                                         // 0x0000(0x01A8)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_ShowroomPage_C_Update_Model) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_Update_Model");
static_assert(sizeof(WDG_ShowroomPage_C_Update_Model) == 0x0001A8, "Wrong size on WDG_ShowroomPage_C_Update_Model");
static_assert(offsetof(WDG_ShowroomPage_C_Update_Model, ModelInfo) == 0x000000, "Member 'WDG_ShowroomPage_C_Update_Model::ModelInfo' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__SelectorModel_K2Node_ComponentBoundEvent_1_OnModelSelected__DelegateSignature
// 0x01A8 (0x01A8 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__SelectorModel_K2Node_ComponentBoundEvent_1_OnModelSelected__DelegateSignature final
{
public:
	struct FModelInfo                             ModelInfo;                                         // 0x0000(0x01A8)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__SelectorModel_K2Node_ComponentBoundEvent_1_OnModelSelected__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__SelectorModel_K2Node_ComponentBoundEvent_1_OnModelSelected__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__SelectorModel_K2Node_ComponentBoundEvent_1_OnModelSelected__DelegateSignature) == 0x0001A8, "Wrong size on WDG_ShowroomPage_C_BndEvt__SelectorModel_K2Node_ComponentBoundEvent_1_OnModelSelected__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__SelectorModel_K2Node_ComponentBoundEvent_1_OnModelSelected__DelegateSignature, ModelInfo) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__SelectorModel_K2Node_ComponentBoundEvent_1_OnModelSelected__DelegateSignature::ModelInfo' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnModelUpdate
// 0x01A8 (0x01A8 - 0x0000)
struct WDG_ShowroomPage_C_OnModelUpdate final
{
public:
	struct FModelInfo                             ModelInfo;                                         // 0x0000(0x01A8)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_ShowroomPage_C_OnModelUpdate) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_OnModelUpdate");
static_assert(sizeof(WDG_ShowroomPage_C_OnModelUpdate) == 0x0001A8, "Wrong size on WDG_ShowroomPage_C_OnModelUpdate");
static_assert(offsetof(WDG_ShowroomPage_C_OnModelUpdate, ModelInfo) == 0x000000, "Member 'WDG_ShowroomPage_C_OnModelUpdate::ModelInfo' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnPreviousCarEntry
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomPage_C_OnPreviousCarEntry final
{
public:
	class FName                                   Key;                                               // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      Sender;                                            // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_OnPreviousCarEntry) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_OnPreviousCarEntry");
static_assert(sizeof(WDG_ShowroomPage_C_OnPreviousCarEntry) == 0x000010, "Wrong size on WDG_ShowroomPage_C_OnPreviousCarEntry");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviousCarEntry, Key) == 0x000000, "Member 'WDG_ShowroomPage_C_OnPreviousCarEntry::Key' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviousCarEntry, Sender) == 0x000008, "Member 'WDG_ShowroomPage_C_OnPreviousCarEntry::Sender' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnNextCarEntry
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomPage_C_OnNextCarEntry final
{
public:
	class FName                                   Key;                                               // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      Sender;                                            // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_OnNextCarEntry) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_OnNextCarEntry");
static_assert(sizeof(WDG_ShowroomPage_C_OnNextCarEntry) == 0x000010, "Wrong size on WDG_ShowroomPage_C_OnNextCarEntry");
static_assert(offsetof(WDG_ShowroomPage_C_OnNextCarEntry, Key) == 0x000000, "Member 'WDG_ShowroomPage_C_OnNextCarEntry::Key' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnNextCarEntry, Sender) == 0x000008, "Member 'WDG_ShowroomPage_C_OnNextCarEntry::Sender' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__SelectorTeam_K2Node_ComponentBoundEvent_2_OnTeamSelected__DelegateSignature
// 0x0038 (0x0038 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__SelectorTeam_K2Node_ComponentBoundEvent_2_OnTeamSelected__DelegateSignature final
{
public:
	struct FTeamInfo                              TeamInfo;                                          // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__SelectorTeam_K2Node_ComponentBoundEvent_2_OnTeamSelected__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__SelectorTeam_K2Node_ComponentBoundEvent_2_OnTeamSelected__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__SelectorTeam_K2Node_ComponentBoundEvent_2_OnTeamSelected__DelegateSignature) == 0x000038, "Wrong size on WDG_ShowroomPage_C_BndEvt__SelectorTeam_K2Node_ComponentBoundEvent_2_OnTeamSelected__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__SelectorTeam_K2Node_ComponentBoundEvent_2_OnTeamSelected__DelegateSignature, TeamInfo) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__SelectorTeam_K2Node_ComponentBoundEvent_2_OnTeamSelected__DelegateSignature::TeamInfo' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature
// 0x0018 (0x0018 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature final
{
public:
	class UWDG_ShowroomTileBase_C*                Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   Key;                                               // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         KeyInt;                                            // 0x0010(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature) == 0x000018, "Wrong size on WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature, Sender) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature::Sender' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature, Key) == 0x000008, "Member 'WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature::Key' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature, KeyInt) == 0x000010, "Member 'WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature::KeyInt' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_0_OnItemFocused__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_0_OnItemFocused__DelegateSignature final
{
public:
	class UWDG_ShowroomTileBase_C*                Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_0_OnItemFocused__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_0_OnItemFocused__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_0_OnItemFocused__DelegateSignature) == 0x000008, "Wrong size on WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_0_OnItemFocused__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_0_OnItemFocused__DelegateSignature, Sender) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_0_OnItemFocused__DelegateSignature::Sender' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnCarItemSelected
// 0x0018 (0x0018 - 0x0000)
struct WDG_ShowroomPage_C_OnCarItemSelected final
{
public:
	class UWDG_ShowroomTileBase_C*                Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   Key;                                               // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         KeyInt;                                            // 0x0010(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_OnCarItemSelected) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_OnCarItemSelected");
static_assert(sizeof(WDG_ShowroomPage_C_OnCarItemSelected) == 0x000018, "Wrong size on WDG_ShowroomPage_C_OnCarItemSelected");
static_assert(offsetof(WDG_ShowroomPage_C_OnCarItemSelected, Sender) == 0x000000, "Member 'WDG_ShowroomPage_C_OnCarItemSelected::Sender' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnCarItemSelected, Key) == 0x000008, "Member 'WDG_ShowroomPage_C_OnCarItemSelected::Key' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnCarItemSelected, KeyInt) == 0x000010, "Member 'WDG_ShowroomPage_C_OnCarItemSelected::KeyInt' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.Update Team
// 0x0038 (0x0038 - 0x0000)
struct WDG_ShowroomPage_C_Update_Team final
{
public:
	struct FTeamInfo                              TeamInfo;                                          // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_ShowroomPage_C_Update_Team) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_Update_Team");
static_assert(sizeof(WDG_ShowroomPage_C_Update_Team) == 0x000038, "Wrong size on WDG_ShowroomPage_C_Update_Team");
static_assert(offsetof(WDG_ShowroomPage_C_Update_Team, TeamInfo) == 0x000000, "Member 'WDG_ShowroomPage_C_Update_Team::TeamInfo' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnTeamUpdate
// 0x0038 (0x0038 - 0x0000)
struct WDG_ShowroomPage_C_OnTeamUpdate final
{
public:
	struct FTeamInfo                              TeamInfo;                                          // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_ShowroomPage_C_OnTeamUpdate) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_OnTeamUpdate");
static_assert(sizeof(WDG_ShowroomPage_C_OnTeamUpdate) == 0x000038, "Wrong size on WDG_ShowroomPage_C_OnTeamUpdate");
static_assert(offsetof(WDG_ShowroomPage_C_OnTeamUpdate, TeamInfo) == 0x000000, "Member 'WDG_ShowroomPage_C_OnTeamUpdate::TeamInfo' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.PreviousVariant
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomPage_C_PreviousVariant final
{
public:
	class FName                                   Key;                                               // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      Sender;                                            // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_PreviousVariant) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_PreviousVariant");
static_assert(sizeof(WDG_ShowroomPage_C_PreviousVariant) == 0x000010, "Wrong size on WDG_ShowroomPage_C_PreviousVariant");
static_assert(offsetof(WDG_ShowroomPage_C_PreviousVariant, Key) == 0x000000, "Member 'WDG_ShowroomPage_C_PreviousVariant::Key' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_PreviousVariant, Sender) == 0x000008, "Member 'WDG_ShowroomPage_C_PreviousVariant::Sender' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.NextVariant
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomPage_C_NextVariant final
{
public:
	class FName                                   Key;                                               // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      Sender;                                            // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_NextVariant) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_NextVariant");
static_assert(sizeof(WDG_ShowroomPage_C_NextVariant) == 0x000010, "Wrong size on WDG_ShowroomPage_C_NextVariant");
static_assert(offsetof(WDG_ShowroomPage_C_NextVariant, Key) == 0x000000, "Member 'WDG_ShowroomPage_C_NextVariant::Key' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_NextVariant, Sender) == 0x000008, "Member 'WDG_ShowroomPage_C_NextVariant::Sender' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.VariantSelected
// 0x0020 (0x0020 - 0x0000)
struct WDG_ShowroomPage_C_VariantSelected final
{
public:
	class UWDG_ShowroomTileItemHorizontal_C*      Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         ColorCode;                                         // 0x0008(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Color;                                             // 0x000C(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_VariantSelected) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_VariantSelected");
static_assert(sizeof(WDG_ShowroomPage_C_VariantSelected) == 0x000020, "Wrong size on WDG_ShowroomPage_C_VariantSelected");
static_assert(offsetof(WDG_ShowroomPage_C_VariantSelected, Sender) == 0x000000, "Member 'WDG_ShowroomPage_C_VariantSelected::Sender' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_VariantSelected, ColorCode) == 0x000008, "Member 'WDG_ShowroomPage_C_VariantSelected::ColorCode' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_VariantSelected, Color) == 0x00000C, "Member 'WDG_ShowroomPage_C_VariantSelected::Color' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnVariantUpdate
// 0x0004 (0x0004 - 0x0000)
struct WDG_ShowroomPage_C_OnVariantUpdate final
{
public:
	int32                                         variant_key;                                       // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_OnVariantUpdate) == 0x000004, "Wrong alignment on WDG_ShowroomPage_C_OnVariantUpdate");
static_assert(sizeof(WDG_ShowroomPage_C_OnVariantUpdate) == 0x000004, "Wrong size on WDG_ShowroomPage_C_OnVariantUpdate");
static_assert(offsetof(WDG_ShowroomPage_C_OnVariantUpdate, variant_key) == 0x000000, "Member 'WDG_ShowroomPage_C_OnVariantUpdate::variant_key' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnDriverItemSelected
// 0x0018 (0x0018 - 0x0000)
struct WDG_ShowroomPage_C_OnDriverItemSelected final
{
public:
	class UWDG_ShowroomTileBase_C*                Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   Key;                                               // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         KeyInt;                                            // 0x0010(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_OnDriverItemSelected) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_OnDriverItemSelected");
static_assert(sizeof(WDG_ShowroomPage_C_OnDriverItemSelected) == 0x000018, "Wrong size on WDG_ShowroomPage_C_OnDriverItemSelected");
static_assert(offsetof(WDG_ShowroomPage_C_OnDriverItemSelected, Sender) == 0x000000, "Member 'WDG_ShowroomPage_C_OnDriverItemSelected::Sender' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnDriverItemSelected, Key) == 0x000008, "Member 'WDG_ShowroomPage_C_OnDriverItemSelected::Key' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnDriverItemSelected, KeyInt) == 0x000010, "Member 'WDG_ShowroomPage_C_OnDriverItemSelected::KeyInt' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnCarUpdate
// 0x00E8 (0x00E8 - 0x0000)
struct WDG_ShowroomPage_C_OnCarUpdate final
{
public:
	struct FCarInfo                               CarInfo;                                           // 0x0000(0x00E0)(BlueprintVisible, BlueprintReadOnly, Parm)
	class FName                                   CarKey;                                            // 0x00E0(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_OnCarUpdate) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_OnCarUpdate");
static_assert(sizeof(WDG_ShowroomPage_C_OnCarUpdate) == 0x0000E8, "Wrong size on WDG_ShowroomPage_C_OnCarUpdate");
static_assert(offsetof(WDG_ShowroomPage_C_OnCarUpdate, CarInfo) == 0x000000, "Member 'WDG_ShowroomPage_C_OnCarUpdate::CarInfo' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnCarUpdate, CarKey) == 0x0000E0, "Member 'WDG_ShowroomPage_C_OnCarUpdate::CarKey' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__CurrentDriver_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature
// 0x0018 (0x0018 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__CurrentDriver_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature final
{
public:
	class UWDG_ShowroomTileBase_C*                Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   Key;                                               // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         KeyInt;                                            // 0x0010(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__CurrentDriver_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__CurrentDriver_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__CurrentDriver_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature) == 0x000018, "Wrong size on WDG_ShowroomPage_C_BndEvt__CurrentDriver_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__CurrentDriver_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature, Sender) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__CurrentDriver_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature::Sender' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__CurrentDriver_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature, Key) == 0x000008, "Member 'WDG_ShowroomPage_C_BndEvt__CurrentDriver_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature::Key' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__CurrentDriver_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature, KeyInt) == 0x000010, "Member 'WDG_ShowroomPage_C_BndEvt__CurrentDriver_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature::KeyInt' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnDriverUpdate
// 0x00F8 (0x00F8 - 0x0000)
struct WDG_ShowroomPage_C_OnDriverUpdate final
{
public:
	struct FDriverInfo                            DriverInfo;                                        // 0x0000(0x00F0)(BlueprintVisible, BlueprintReadOnly, Parm)
	class FName                                   DriverKey;                                         // 0x00F0(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_OnDriverUpdate) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_OnDriverUpdate");
static_assert(sizeof(WDG_ShowroomPage_C_OnDriverUpdate) == 0x0000F8, "Wrong size on WDG_ShowroomPage_C_OnDriverUpdate");
static_assert(offsetof(WDG_ShowroomPage_C_OnDriverUpdate, DriverInfo) == 0x000000, "Member 'WDG_ShowroomPage_C_OnDriverUpdate::DriverInfo' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnDriverUpdate, DriverKey) == 0x0000F0, "Member 'WDG_ShowroomPage_C_OnDriverUpdate::DriverKey' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__inputTeamName_K2Node_ComponentBoundEvent_1_AcTextInputChanged__DelegateSignature
// 0x0018 (0x0018 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__inputTeamName_K2Node_ComponentBoundEvent_1_AcTextInputChanged__DelegateSignature final
{
public:
	class FText                                   text;                                              // 0x0000(0x0018)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__inputTeamName_K2Node_ComponentBoundEvent_1_AcTextInputChanged__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__inputTeamName_K2Node_ComponentBoundEvent_1_AcTextInputChanged__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__inputTeamName_K2Node_ComponentBoundEvent_1_AcTextInputChanged__DelegateSignature) == 0x000018, "Wrong size on WDG_ShowroomPage_C_BndEvt__inputTeamName_K2Node_ComponentBoundEvent_1_AcTextInputChanged__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__inputTeamName_K2Node_ComponentBoundEvent_1_AcTextInputChanged__DelegateSignature, text) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__inputTeamName_K2Node_ComponentBoundEvent_1_AcTextInputChanged__DelegateSignature::text' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__customCarOptions_K2Node_ComponentBoundEvent_8_OnValuesUpdated__DelegateSignature
// 0x00E0 (0x00E0 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__customCarOptions_K2Node_ComponentBoundEvent_8_OnValuesUpdated__DelegateSignature final
{
public:
	struct FCarInfo                               CarInfo;                                           // 0x0000(0x00E0)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__customCarOptions_K2Node_ComponentBoundEvent_8_OnValuesUpdated__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__customCarOptions_K2Node_ComponentBoundEvent_8_OnValuesUpdated__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__customCarOptions_K2Node_ComponentBoundEvent_8_OnValuesUpdated__DelegateSignature) == 0x0000E0, "Wrong size on WDG_ShowroomPage_C_BndEvt__customCarOptions_K2Node_ComponentBoundEvent_8_OnValuesUpdated__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__customCarOptions_K2Node_ComponentBoundEvent_8_OnValuesUpdated__DelegateSignature, CarInfo) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__customCarOptions_K2Node_ComponentBoundEvent_8_OnValuesUpdated__DelegateSignature::CarInfo' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__inputRaceNumber_K2Node_ComponentBoundEvent_3_AcTextInputChanged__DelegateSignature
// 0x0018 (0x0018 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__inputRaceNumber_K2Node_ComponentBoundEvent_3_AcTextInputChanged__DelegateSignature final
{
public:
	class FText                                   text;                                              // 0x0000(0x0018)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__inputRaceNumber_K2Node_ComponentBoundEvent_3_AcTextInputChanged__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__inputRaceNumber_K2Node_ComponentBoundEvent_3_AcTextInputChanged__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__inputRaceNumber_K2Node_ComponentBoundEvent_3_AcTextInputChanged__DelegateSignature) == 0x000018, "Wrong size on WDG_ShowroomPage_C_BndEvt__inputRaceNumber_K2Node_ComponentBoundEvent_3_AcTextInputChanged__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__inputRaceNumber_K2Node_ComponentBoundEvent_3_AcTextInputChanged__DelegateSignature, text) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__inputRaceNumber_K2Node_ComponentBoundEvent_3_AcTextInputChanged__DelegateSignature::text' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__CustomCarModel_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature
// 0x0018 (0x0018 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__CustomCarModel_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature final
{
public:
	class UWDG_ShowroomTileBase_C*                Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   Key;                                               // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         KeyInt;                                            // 0x0010(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__CustomCarModel_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__CustomCarModel_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__CustomCarModel_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature) == 0x000018, "Wrong size on WDG_ShowroomPage_C_BndEvt__CustomCarModel_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__CustomCarModel_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature, Sender) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__CustomCarModel_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature::Sender' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__CustomCarModel_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature, Key) == 0x000008, "Member 'WDG_ShowroomPage_C_BndEvt__CustomCarModel_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature::Key' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__CustomCarModel_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature, KeyInt) == 0x000010, "Member 'WDG_ShowroomPage_C_BndEvt__CustomCarModel_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature::KeyInt' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnCustomModelUpdate
// 0x01A8 (0x01A8 - 0x0000)
struct WDG_ShowroomPage_C_OnCustomModelUpdate final
{
public:
	struct FModelInfo                             ModelInfo;                                         // 0x0000(0x01A8)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_ShowroomPage_C_OnCustomModelUpdate) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_OnCustomModelUpdate");
static_assert(sizeof(WDG_ShowroomPage_C_OnCustomModelUpdate) == 0x0001A8, "Wrong size on WDG_ShowroomPage_C_OnCustomModelUpdate");
static_assert(offsetof(WDG_ShowroomPage_C_OnCustomModelUpdate, ModelInfo) == 0x000000, "Member 'WDG_ShowroomPage_C_OnCustomModelUpdate::ModelInfo' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__btnEditCustom_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__btnEditCustom_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature final
{
public:
	class UWDG_GenericBarItem_C*                  Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__btnEditCustom_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__btnEditCustom_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__btnEditCustom_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature) == 0x000008, "Wrong size on WDG_ShowroomPage_C_BndEvt__btnEditCustom_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__btnEditCustom_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature, Sender) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__btnEditCustom_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature::Sender' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__btnAddCustom_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__btnAddCustom_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature final
{
public:
	class UWDG_GenericBarItem_C*                  Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__btnAddCustom_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__btnAddCustom_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__btnAddCustom_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature) == 0x000008, "Wrong size on WDG_ShowroomPage_C_BndEvt__btnAddCustom_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__btnAddCustom_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature, Sender) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__btnAddCustom_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature::Sender' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__btnCustomSaveAsNew_K2Node_ComponentBoundEvent_3_OnItemForward__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__btnCustomSaveAsNew_K2Node_ComponentBoundEvent_3_OnItemForward__DelegateSignature final
{
public:
	class UWDG_GenericBarItem_C*                  Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__btnCustomSaveAsNew_K2Node_ComponentBoundEvent_3_OnItemForward__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__btnCustomSaveAsNew_K2Node_ComponentBoundEvent_3_OnItemForward__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__btnCustomSaveAsNew_K2Node_ComponentBoundEvent_3_OnItemForward__DelegateSignature) == 0x000008, "Wrong size on WDG_ShowroomPage_C_BndEvt__btnCustomSaveAsNew_K2Node_ComponentBoundEvent_3_OnItemForward__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__btnCustomSaveAsNew_K2Node_ComponentBoundEvent_3_OnItemForward__DelegateSignature, Sender) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__btnCustomSaveAsNew_K2Node_ComponentBoundEvent_3_OnItemForward__DelegateSignature::Sender' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__btnCustomDelete_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__btnCustomDelete_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature final
{
public:
	class UWDG_GenericBarItem_C*                  Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__btnCustomDelete_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__btnCustomDelete_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__btnCustomDelete_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature) == 0x000008, "Wrong size on WDG_ShowroomPage_C_BndEvt__btnCustomDelete_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__btnCustomDelete_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature, Sender) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__btnCustomDelete_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature::Sender' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__popupCarSaved_K2Node_ComponentBoundEvent_4_OnShow__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__popupCarSaved_K2Node_ComponentBoundEvent_4_OnShow__DelegateSignature final
{
public:
	bool                                          HasConfirmation;                                   // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__popupCarSaved_K2Node_ComponentBoundEvent_4_OnShow__DelegateSignature) == 0x000001, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__popupCarSaved_K2Node_ComponentBoundEvent_4_OnShow__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__popupCarSaved_K2Node_ComponentBoundEvent_4_OnShow__DelegateSignature) == 0x000001, "Wrong size on WDG_ShowroomPage_C_BndEvt__popupCarSaved_K2Node_ComponentBoundEvent_4_OnShow__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__popupCarSaved_K2Node_ComponentBoundEvent_4_OnShow__DelegateSignature, HasConfirmation) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__popupCarSaved_K2Node_ComponentBoundEvent_4_OnShow__DelegateSignature::HasConfirmation' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__btnCustomSave_K2Node_ComponentBoundEvent_7_OnItemForward__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__btnCustomSave_K2Node_ComponentBoundEvent_7_OnItemForward__DelegateSignature final
{
public:
	class UWDG_GenericBarItem_C*                  Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__btnCustomSave_K2Node_ComponentBoundEvent_7_OnItemForward__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__btnCustomSave_K2Node_ComponentBoundEvent_7_OnItemForward__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__btnCustomSave_K2Node_ComponentBoundEvent_7_OnItemForward__DelegateSignature) == 0x000008, "Wrong size on WDG_ShowroomPage_C_BndEvt__btnCustomSave_K2Node_ComponentBoundEvent_7_OnItemForward__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__btnCustomSave_K2Node_ComponentBoundEvent_7_OnItemForward__DelegateSignature, Sender) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__btnCustomSave_K2Node_ComponentBoundEvent_7_OnItemForward__DelegateSignature::Sender' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnAnimationFinished
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomPage_C_OnAnimationFinished final
{
public:
	const class UWidgetAnimation*                 Animation;                                         // 0x0000(0x0008)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_OnAnimationFinished) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_OnAnimationFinished");
static_assert(sizeof(WDG_ShowroomPage_C_OnAnimationFinished) == 0x000008, "Wrong size on WDG_ShowroomPage_C_OnAnimationFinished");
static_assert(offsetof(WDG_ShowroomPage_C_OnAnimationFinished, Animation) == 0x000000, "Member 'WDG_ShowroomPage_C_OnAnimationFinished::Animation' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__WDG_HorizontalSlider_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomPage_C_BndEvt__WDG_HorizontalSlider_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature final
{
public:
	class UGenericSelectorItem*                   Source;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_index;                                     // 0x0008(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         current_value;                                     // 0x000C(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_BndEvt__WDG_HorizontalSlider_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_BndEvt__WDG_HorizontalSlider_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPage_C_BndEvt__WDG_HorizontalSlider_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature) == 0x000010, "Wrong size on WDG_ShowroomPage_C_BndEvt__WDG_HorizontalSlider_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__WDG_HorizontalSlider_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature, Source) == 0x000000, "Member 'WDG_ShowroomPage_C_BndEvt__WDG_HorizontalSlider_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature::Source' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__WDG_HorizontalSlider_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature, current_index) == 0x000008, "Member 'WDG_ShowroomPage_C_BndEvt__WDG_HorizontalSlider_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature::current_index' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_BndEvt__WDG_HorizontalSlider_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature, current_value) == 0x00000C, "Member 'WDG_ShowroomPage_C_BndEvt__WDG_HorizontalSlider_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature::current_value' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.ResetView
// 0x00C8 (0x00C8 - 0x0000)
struct WDG_ShowroomPage_C_ResetView final
{
public:
	bool                                          ResetOnlyOffset;                                   // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2[0x6];                                        // 0x0002(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class AShowRoomCameraActor_C*                 K2Node_DynamicCast_AsShow_Room_Camera_Actor;       // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_11[0x3];                                       // 0x0011(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector                                CallFunc_K2_GetActorLocation_ReturnValue;          // 0x0014(0x000C)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakVector_X;                            // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakVector_Y;                            // 0x0024(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakVector_Z;                            // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector                                CallFunc_MakeVector_ReturnValue;                   // 0x002C(0x000C)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FHitResult                             CallFunc_K2_SetActorLocation_SweepHitResult;       // 0x0038(0x008C)(IsPlainOldData, NoDestructor, ContainsInstancedReference)
	bool                                          CallFunc_K2_SetActorLocation_ReturnValue;          // 0x00C4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_ResetView) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_ResetView");
static_assert(sizeof(WDG_ShowroomPage_C_ResetView) == 0x0000C8, "Wrong size on WDG_ShowroomPage_C_ResetView");
static_assert(offsetof(WDG_ShowroomPage_C_ResetView, ResetOnlyOffset) == 0x000000, "Member 'WDG_ShowroomPage_C_ResetView::ResetOnlyOffset' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ResetView, CallFunc_IsValid_ReturnValue) == 0x000001, "Member 'WDG_ShowroomPage_C_ResetView::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ResetView, K2Node_DynamicCast_AsShow_Room_Camera_Actor) == 0x000008, "Member 'WDG_ShowroomPage_C_ResetView::K2Node_DynamicCast_AsShow_Room_Camera_Actor' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ResetView, K2Node_DynamicCast_bSuccess) == 0x000010, "Member 'WDG_ShowroomPage_C_ResetView::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ResetView, CallFunc_K2_GetActorLocation_ReturnValue) == 0x000014, "Member 'WDG_ShowroomPage_C_ResetView::CallFunc_K2_GetActorLocation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ResetView, CallFunc_BreakVector_X) == 0x000020, "Member 'WDG_ShowroomPage_C_ResetView::CallFunc_BreakVector_X' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ResetView, CallFunc_BreakVector_Y) == 0x000024, "Member 'WDG_ShowroomPage_C_ResetView::CallFunc_BreakVector_Y' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ResetView, CallFunc_BreakVector_Z) == 0x000028, "Member 'WDG_ShowroomPage_C_ResetView::CallFunc_BreakVector_Z' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ResetView, CallFunc_MakeVector_ReturnValue) == 0x00002C, "Member 'WDG_ShowroomPage_C_ResetView::CallFunc_MakeVector_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ResetView, CallFunc_K2_SetActorLocation_SweepHitResult) == 0x000038, "Member 'WDG_ShowroomPage_C_ResetView::CallFunc_K2_SetActorLocation_SweepHitResult' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ResetView, CallFunc_K2_SetActorLocation_ReturnValue) == 0x0000C4, "Member 'WDG_ShowroomPage_C_ResetView::CallFunc_K2_SetActorLocation_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnMouseButtonUp
// 0x02F8 (0x02F8 - 0x0000)
struct WDG_ShowroomPage_C_OnMouseButtonUp final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FPointerEvent                          MouseEvent;                                        // 0x0038(0x0070)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	struct FEventReply                            ReturnValue;                                       // 0x00A8(0x00B8)(Parm, OutParm, ReturnParm)
	struct FEventReply                            CallFunc_OnMouseButtonUp_ReturnValue;              // 0x0160(0x00B8)()
	bool                                          CallFunc_IsVisible_ReturnValue;                    // 0x0218(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_219[0x7];                                      // 0x0219(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FKey                                   CallFunc_PointerEvent_GetEffectingButton_ReturnValue; // 0x0220(0x0018)(HasGetValueTypeHash)
	struct FEventReply                            CallFunc_Unhandled_ReturnValue;                    // 0x0238(0x00B8)()
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue;            // 0x02F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_OnMouseButtonUp) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_OnMouseButtonUp");
static_assert(sizeof(WDG_ShowroomPage_C_OnMouseButtonUp) == 0x0002F8, "Wrong size on WDG_ShowroomPage_C_OnMouseButtonUp");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseButtonUp, MyGeometry) == 0x000000, "Member 'WDG_ShowroomPage_C_OnMouseButtonUp::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseButtonUp, MouseEvent) == 0x000038, "Member 'WDG_ShowroomPage_C_OnMouseButtonUp::MouseEvent' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseButtonUp, ReturnValue) == 0x0000A8, "Member 'WDG_ShowroomPage_C_OnMouseButtonUp::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseButtonUp, CallFunc_OnMouseButtonUp_ReturnValue) == 0x000160, "Member 'WDG_ShowroomPage_C_OnMouseButtonUp::CallFunc_OnMouseButtonUp_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseButtonUp, CallFunc_IsVisible_ReturnValue) == 0x000218, "Member 'WDG_ShowroomPage_C_OnMouseButtonUp::CallFunc_IsVisible_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseButtonUp, CallFunc_PointerEvent_GetEffectingButton_ReturnValue) == 0x000220, "Member 'WDG_ShowroomPage_C_OnMouseButtonUp::CallFunc_PointerEvent_GetEffectingButton_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseButtonUp, CallFunc_Unhandled_ReturnValue) == 0x000238, "Member 'WDG_ShowroomPage_C_OnMouseButtonUp::CallFunc_Unhandled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseButtonUp, CallFunc_EqualEqual_KeyKey_ReturnValue) == 0x0002F0, "Member 'WDG_ShowroomPage_C_OnMouseButtonUp::CallFunc_EqualEqual_KeyKey_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnMouseMove
// 0x07F8 (0x07F8 - 0x0000)
struct WDG_ShowroomPage_C_OnMouseMove final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FPointerEvent                          MouseEvent;                                        // 0x0038(0x0070)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	struct FEventReply                            ReturnValue;                                       // 0x00A8(0x00B8)(Parm, OutParm, ReturnParm)
	bool                                          IsRightDown;                                       // 0x0160(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          IsLeftDown;                                        // 0x0161(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_162[0x2];                                      // 0x0162(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         HeightMultiplier;                                  // 0x0164(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         RotationMultiplier;                                // 0x0168(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CursorDelta;                                       // 0x016C(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_174[0x4];                                      // 0x0174(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_OnMouseMove_ReturnValue;                  // 0x0178(0x00B8)()
	struct FVector2D                              CallFunc_PointerEvent_GetCursorDelta_ReturnValue;  // 0x0230(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_PointerEvent_IsMouseButtonDown_ReturnValue; // 0x0238(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_PointerEvent_IsMouseButtonDown_ReturnValue_1; // 0x0239(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_23A[0x6];                                      // 0x023A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_Unhandled_ReturnValue;                    // 0x0240(0x00B8)()
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x02F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2F9[0x7];                                      // 0x02F9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_Unhandled_ReturnValue_1;                  // 0x0300(0x00B8)()
	float                                         CallFunc_BreakVector2D_X;                          // 0x03B8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakVector2D_Y;                          // 0x03BC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue;          // 0x03C0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue_1;        // 0x03C4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FRotator                               CallFunc_MakeRotator_ReturnValue;                  // 0x03C8(0x000C)(ZeroConstructor, IsPlainOldData, NoDestructor)
	struct FRotator                               CallFunc_Multiply_RotatorFloat_ReturnValue;        // 0x03D4(0x000C)(ZeroConstructor, IsPlainOldData, NoDestructor)
	float                                         CallFunc_BreakRotator_Roll;                        // 0x03E0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakRotator_Pitch;                       // 0x03E4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakRotator_Yaw;                         // 0x03E8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FHitResult                             CallFunc_K2_AddRelativeRotation_SweepHitResult;    // 0x03EC(0x008C)(IsPlainOldData, NoDestructor, ContainsInstancedReference)
	float                                         CallFunc_BreakRotator_Roll_1;                      // 0x0478(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakRotator_Pitch_1;                     // 0x047C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakRotator_Yaw_1;                       // 0x0480(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0484(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_485[0x3];                                      // 0x0485(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Add_FloatFloat_ReturnValue;               // 0x0488(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsBetweenVectorValues_ReturnValue;        // 0x048C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x048D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x048E(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_HasFocusedDescendants_ReturnValue;        // 0x048F(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x0490(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_491[0x7];                                      // 0x0491(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_Unhandled_ReturnValue_2;                  // 0x0498(0x00B8)()
	float                                         CallFunc_BreakVector2D_X_1;                        // 0x0550(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakVector2D_Y_1;                        // 0x0554(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FEventReply                            CallFunc_Unhandled_ReturnValue_3;                  // 0x0558(0x00B8)()
	struct FRotator                               CallFunc_MakeRotator_ReturnValue_1;                // 0x0610(0x000C)(ZeroConstructor, IsPlainOldData, NoDestructor)
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue_2;        // 0x061C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FRotator                               CallFunc_Multiply_RotatorFloat_ReturnValue_1;      // 0x0620(0x000C)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_62C[0x4];                                      // 0x062C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_Unhandled_ReturnValue_4;                  // 0x0630(0x00B8)()
	struct FPointerEvent                          CallFunc_getLastVRMousePointerEvent_ReturnValue;   // 0x06E8(0x0070)()
	struct FVector2D                              CallFunc_PointerEvent_GetCursorDelta_ReturnValue_1; // 0x0758(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsHMDEnabled_ReturnValue;                 // 0x0760(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0761(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_2;                 // 0x0762(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsValid_ReturnValue_2;                    // 0x0763(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	struct FHitResult                             CallFunc_K2_AddActorLocalRotation_SweepHitResult;  // 0x0764(0x008C)(IsPlainOldData, NoDestructor, ContainsInstancedReference)
	bool                                          CallFunc_BooleanOR_ReturnValue_1;                  // 0x07F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_OnMouseMove) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_OnMouseMove");
static_assert(sizeof(WDG_ShowroomPage_C_OnMouseMove) == 0x0007F8, "Wrong size on WDG_ShowroomPage_C_OnMouseMove");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, MyGeometry) == 0x000000, "Member 'WDG_ShowroomPage_C_OnMouseMove::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, MouseEvent) == 0x000038, "Member 'WDG_ShowroomPage_C_OnMouseMove::MouseEvent' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, ReturnValue) == 0x0000A8, "Member 'WDG_ShowroomPage_C_OnMouseMove::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, IsRightDown) == 0x000160, "Member 'WDG_ShowroomPage_C_OnMouseMove::IsRightDown' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, IsLeftDown) == 0x000161, "Member 'WDG_ShowroomPage_C_OnMouseMove::IsLeftDown' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, HeightMultiplier) == 0x000164, "Member 'WDG_ShowroomPage_C_OnMouseMove::HeightMultiplier' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, RotationMultiplier) == 0x000168, "Member 'WDG_ShowroomPage_C_OnMouseMove::RotationMultiplier' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CursorDelta) == 0x00016C, "Member 'WDG_ShowroomPage_C_OnMouseMove::CursorDelta' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_OnMouseMove_ReturnValue) == 0x000178, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_OnMouseMove_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_PointerEvent_GetCursorDelta_ReturnValue) == 0x000230, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_PointerEvent_GetCursorDelta_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_PointerEvent_IsMouseButtonDown_ReturnValue) == 0x000238, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_PointerEvent_IsMouseButtonDown_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_PointerEvent_IsMouseButtonDown_ReturnValue_1) == 0x000239, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_PointerEvent_IsMouseButtonDown_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_Unhandled_ReturnValue) == 0x000240, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_Unhandled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_BooleanOR_ReturnValue) == 0x0002F8, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_Unhandled_ReturnValue_1) == 0x000300, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_Unhandled_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_BreakVector2D_X) == 0x0003B8, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_BreakVector2D_Y) == 0x0003BC, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_Multiply_FloatFloat_ReturnValue) == 0x0003C0, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_Multiply_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_Multiply_FloatFloat_ReturnValue_1) == 0x0003C4, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_Multiply_FloatFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_MakeRotator_ReturnValue) == 0x0003C8, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_MakeRotator_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_Multiply_RotatorFloat_ReturnValue) == 0x0003D4, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_Multiply_RotatorFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_BreakRotator_Roll) == 0x0003E0, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_BreakRotator_Roll' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_BreakRotator_Pitch) == 0x0003E4, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_BreakRotator_Pitch' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_BreakRotator_Yaw) == 0x0003E8, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_BreakRotator_Yaw' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_K2_AddRelativeRotation_SweepHitResult) == 0x0003EC, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_K2_AddRelativeRotation_SweepHitResult' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_BreakRotator_Roll_1) == 0x000478, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_BreakRotator_Roll_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_BreakRotator_Pitch_1) == 0x00047C, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_BreakRotator_Pitch_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_BreakRotator_Yaw_1) == 0x000480, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_BreakRotator_Yaw_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_IsValid_ReturnValue) == 0x000484, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_Add_FloatFloat_ReturnValue) == 0x000488, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_Add_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_IsBetweenVectorValues_ReturnValue) == 0x00048C, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_IsBetweenVectorValues_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_BooleanAND_ReturnValue) == 0x00048D, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_BooleanAND_ReturnValue_1) == 0x00048E, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_HasFocusedDescendants_ReturnValue) == 0x00048F, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_HasFocusedDescendants_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_IsValid_ReturnValue_1) == 0x000490, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_Unhandled_ReturnValue_2) == 0x000498, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_Unhandled_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_BreakVector2D_X_1) == 0x000550, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_BreakVector2D_X_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_BreakVector2D_Y_1) == 0x000554, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_BreakVector2D_Y_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_Unhandled_ReturnValue_3) == 0x000558, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_Unhandled_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_MakeRotator_ReturnValue_1) == 0x000610, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_MakeRotator_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_Multiply_FloatFloat_ReturnValue_2) == 0x00061C, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_Multiply_FloatFloat_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_Multiply_RotatorFloat_ReturnValue_1) == 0x000620, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_Multiply_RotatorFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_Unhandled_ReturnValue_4) == 0x000630, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_Unhandled_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_getLastVRMousePointerEvent_ReturnValue) == 0x0006E8, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_getLastVRMousePointerEvent_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_PointerEvent_GetCursorDelta_ReturnValue_1) == 0x000758, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_PointerEvent_GetCursorDelta_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_IsHMDEnabled_ReturnValue) == 0x000760, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_IsHMDEnabled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_Not_PreBool_ReturnValue) == 0x000761, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_BooleanAND_ReturnValue_2) == 0x000762, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_BooleanAND_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_IsValid_ReturnValue_2) == 0x000763, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_IsValid_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_K2_AddActorLocalRotation_SweepHitResult) == 0x000764, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_K2_AddActorLocalRotation_SweepHitResult' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseMove, CallFunc_BooleanOR_ReturnValue_1) == 0x0007F0, "Member 'WDG_ShowroomPage_C_OnMouseMove::CallFunc_BooleanOR_ReturnValue_1' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.SetShowroomCamera
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomPage_C_SetShowroomCamera final
{
public:
	class AShowRoomCameraActor_C*                 K2Node_DynamicCast_AsShow_Room_Camera_Actor;       // 0x0000(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0009(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_SetShowroomCamera) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_SetShowroomCamera");
static_assert(sizeof(WDG_ShowroomPage_C_SetShowroomCamera) == 0x000010, "Wrong size on WDG_ShowroomPage_C_SetShowroomCamera");
static_assert(offsetof(WDG_ShowroomPage_C_SetShowroomCamera, K2Node_DynamicCast_AsShow_Room_Camera_Actor) == 0x000000, "Member 'WDG_ShowroomPage_C_SetShowroomCamera::K2Node_DynamicCast_AsShow_Room_Camera_Actor' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_SetShowroomCamera, K2Node_DynamicCast_bSuccess) == 0x000008, "Member 'WDG_ShowroomPage_C_SetShowroomCamera::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_SetShowroomCamera, CallFunc_IsValid_ReturnValue) == 0x000009, "Member 'WDG_ShowroomPage_C_SetShowroomCamera::CallFunc_IsValid_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnMouseWheel
// 0x0238 (0x0238 - 0x0000)
struct WDG_ShowroomPage_C_OnMouseWheel final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FPointerEvent                          MouseEvent;                                        // 0x0038(0x0070)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	struct FEventReply                            ReturnValue;                                       // 0x00A8(0x00B8)(Parm, OutParm, ReturnParm)
	float                                         ZoomMultipier;                                     // 0x0160(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_PointerEvent_GetWheelDelta_ReturnValue;   // 0x0164(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0168(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0169(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsHMDEnabled_ReturnValue;                 // 0x016A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x016B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_16C[0x4];                                      // 0x016C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_Unhandled_ReturnValue;                    // 0x0170(0x00B8)()
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x0228(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x0229(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_22A[0x2];                                      // 0x022A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue;          // 0x022C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Add_FloatFloat_ReturnValue;               // 0x0230(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_FClamp_ReturnValue;                       // 0x0234(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_OnMouseWheel) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_OnMouseWheel");
static_assert(sizeof(WDG_ShowroomPage_C_OnMouseWheel) == 0x000238, "Wrong size on WDG_ShowroomPage_C_OnMouseWheel");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseWheel, MyGeometry) == 0x000000, "Member 'WDG_ShowroomPage_C_OnMouseWheel::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseWheel, MouseEvent) == 0x000038, "Member 'WDG_ShowroomPage_C_OnMouseWheel::MouseEvent' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseWheel, ReturnValue) == 0x0000A8, "Member 'WDG_ShowroomPage_C_OnMouseWheel::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseWheel, ZoomMultipier) == 0x000160, "Member 'WDG_ShowroomPage_C_OnMouseWheel::ZoomMultipier' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseWheel, CallFunc_PointerEvent_GetWheelDelta_ReturnValue) == 0x000164, "Member 'WDG_ShowroomPage_C_OnMouseWheel::CallFunc_PointerEvent_GetWheelDelta_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseWheel, CallFunc_IsValid_ReturnValue) == 0x000168, "Member 'WDG_ShowroomPage_C_OnMouseWheel::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseWheel, CallFunc_BooleanAND_ReturnValue) == 0x000169, "Member 'WDG_ShowroomPage_C_OnMouseWheel::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseWheel, CallFunc_IsHMDEnabled_ReturnValue) == 0x00016A, "Member 'WDG_ShowroomPage_C_OnMouseWheel::CallFunc_IsHMDEnabled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseWheel, CallFunc_Not_PreBool_ReturnValue) == 0x00016B, "Member 'WDG_ShowroomPage_C_OnMouseWheel::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseWheel, CallFunc_Unhandled_ReturnValue) == 0x000170, "Member 'WDG_ShowroomPage_C_OnMouseWheel::CallFunc_Unhandled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseWheel, CallFunc_IsValid_ReturnValue_1) == 0x000228, "Member 'WDG_ShowroomPage_C_OnMouseWheel::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseWheel, CallFunc_BooleanAND_ReturnValue_1) == 0x000229, "Member 'WDG_ShowroomPage_C_OnMouseWheel::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseWheel, CallFunc_Multiply_FloatFloat_ReturnValue) == 0x00022C, "Member 'WDG_ShowroomPage_C_OnMouseWheel::CallFunc_Multiply_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseWheel, CallFunc_Add_FloatFloat_ReturnValue) == 0x000230, "Member 'WDG_ShowroomPage_C_OnMouseWheel::CallFunc_Add_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseWheel, CallFunc_FClamp_ReturnValue) == 0x000234, "Member 'WDG_ShowroomPage_C_OnMouseWheel::CallFunc_FClamp_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnMouseButtonDoubleClick
// 0x03B8 (0x03B8 - 0x0000)
struct WDG_ShowroomPage_C_OnMouseButtonDoubleClick final
{
public:
	struct FGeometry                              InMyGeometry;                                      // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FPointerEvent                          InMouseEvent;                                      // 0x0038(0x0070)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	struct FEventReply                            ReturnValue;                                       // 0x00A8(0x00B8)(Parm, OutParm, ReturnParm)
	struct FKey                                   CallFunc_PointerEvent_GetEffectingButton_ReturnValue; // 0x0160(0x0018)(HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue;            // 0x0178(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue_1;          // 0x0179(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_17A[0x6];                                      // 0x017A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_Handled_ReturnValue;                      // 0x0180(0x00B8)()
	bool                                          CallFunc_HasFocusedDescendants_ReturnValue;        // 0x0238(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_239[0x7];                                      // 0x0239(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_Unhandled_ReturnValue;                    // 0x0240(0x00B8)()
	bool                                          CallFunc_IsHMDEnabled_ReturnValue;                 // 0x02F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x02F9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2FA[0x6];                                      // 0x02FA(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_Handled_ReturnValue_1;                    // 0x0300(0x00B8)()
};
static_assert(alignof(WDG_ShowroomPage_C_OnMouseButtonDoubleClick) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_OnMouseButtonDoubleClick");
static_assert(sizeof(WDG_ShowroomPage_C_OnMouseButtonDoubleClick) == 0x0003B8, "Wrong size on WDG_ShowroomPage_C_OnMouseButtonDoubleClick");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseButtonDoubleClick, InMyGeometry) == 0x000000, "Member 'WDG_ShowroomPage_C_OnMouseButtonDoubleClick::InMyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseButtonDoubleClick, InMouseEvent) == 0x000038, "Member 'WDG_ShowroomPage_C_OnMouseButtonDoubleClick::InMouseEvent' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseButtonDoubleClick, ReturnValue) == 0x0000A8, "Member 'WDG_ShowroomPage_C_OnMouseButtonDoubleClick::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseButtonDoubleClick, CallFunc_PointerEvent_GetEffectingButton_ReturnValue) == 0x000160, "Member 'WDG_ShowroomPage_C_OnMouseButtonDoubleClick::CallFunc_PointerEvent_GetEffectingButton_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseButtonDoubleClick, CallFunc_EqualEqual_KeyKey_ReturnValue) == 0x000178, "Member 'WDG_ShowroomPage_C_OnMouseButtonDoubleClick::CallFunc_EqualEqual_KeyKey_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseButtonDoubleClick, CallFunc_EqualEqual_KeyKey_ReturnValue_1) == 0x000179, "Member 'WDG_ShowroomPage_C_OnMouseButtonDoubleClick::CallFunc_EqualEqual_KeyKey_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseButtonDoubleClick, CallFunc_Handled_ReturnValue) == 0x000180, "Member 'WDG_ShowroomPage_C_OnMouseButtonDoubleClick::CallFunc_Handled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseButtonDoubleClick, CallFunc_HasFocusedDescendants_ReturnValue) == 0x000238, "Member 'WDG_ShowroomPage_C_OnMouseButtonDoubleClick::CallFunc_HasFocusedDescendants_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseButtonDoubleClick, CallFunc_Unhandled_ReturnValue) == 0x000240, "Member 'WDG_ShowroomPage_C_OnMouseButtonDoubleClick::CallFunc_Unhandled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseButtonDoubleClick, CallFunc_IsHMDEnabled_ReturnValue) == 0x0002F8, "Member 'WDG_ShowroomPage_C_OnMouseButtonDoubleClick::CallFunc_IsHMDEnabled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseButtonDoubleClick, CallFunc_BooleanOR_ReturnValue) == 0x0002F9, "Member 'WDG_ShowroomPage_C_OnMouseButtonDoubleClick::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnMouseButtonDoubleClick, CallFunc_Handled_ReturnValue_1) == 0x000300, "Member 'WDG_ShowroomPage_C_OnMouseButtonDoubleClick::CallFunc_Handled_ReturnValue_1' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.ShowModalOverlay
// 0x0018 (0x0018 - 0x0000)
struct WDG_ShowroomPage_C_ShowModalOverlay final
{
public:
	EShowroomTileType                             Type;                                              // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPanelBase*                           CallingPanel;                                      // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_ShowModalOverlay) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_ShowModalOverlay");
static_assert(sizeof(WDG_ShowroomPage_C_ShowModalOverlay) == 0x000018, "Wrong size on WDG_ShowroomPage_C_ShowModalOverlay");
static_assert(offsetof(WDG_ShowroomPage_C_ShowModalOverlay, Type) == 0x000000, "Member 'WDG_ShowroomPage_C_ShowModalOverlay::Type' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ShowModalOverlay, CallingPanel) == 0x000008, "Member 'WDG_ShowroomPage_C_ShowModalOverlay::CallingPanel' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ShowModalOverlay, K2Node_SwitchEnum_CmpSuccess) == 0x000010, "Member 'WDG_ShowroomPage_C_ShowModalOverlay::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.HideModalOverlay
// 0x0002 (0x0002 - 0x0000)
struct WDG_ShowroomPage_C_HideModalOverlay final
{
public:
	bool                                          Cancelled;                                         // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_HideModalOverlay) == 0x000001, "Wrong alignment on WDG_ShowroomPage_C_HideModalOverlay");
static_assert(sizeof(WDG_ShowroomPage_C_HideModalOverlay) == 0x000002, "Wrong size on WDG_ShowroomPage_C_HideModalOverlay");
static_assert(offsetof(WDG_ShowroomPage_C_HideModalOverlay, Cancelled) == 0x000000, "Member 'WDG_ShowroomPage_C_HideModalOverlay::Cancelled' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_HideModalOverlay, CallFunc_BooleanAND_ReturnValue) == 0x000001, "Member 'WDG_ShowroomPage_C_HideModalOverlay::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnPreviewKeyDown
// 0x0508 (0x0508 - 0x0000)
struct WDG_ShowroomPage_C_OnPreviewKeyDown final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FKeyEvent                              InKeyEvent;                                        // 0x0038(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm)
	struct FEventReply                            ReturnValue;                                       // 0x0070(0x00B8)(Parm, OutParm, ReturnParm)
	bool                                          CallFunc_Show_UI_If_Hidden_WasHidden;              // 0x0128(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_129[0x7];                                      // 0x0129(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FKey                                   CallFunc_GetKey_ReturnValue;                       // 0x0130(0x0018)(HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue;            // 0x0148(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue_1;          // 0x0149(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_14A[0x6];                                      // 0x014A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_OnPreviewKeyDown_ReturnValue;             // 0x0150(0x00B8)()
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x0208(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsInCustomEditor_ReturnValue;             // 0x0209(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_20A[0x6];                                      // 0x020A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_Handled_ReturnValue;                      // 0x0210(0x00B8)()
	class ULevelSequencePlayer*                   CallFunc_GetSequencePlayer_ReturnValue;            // 0x02C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsPlaying_ReturnValue;                    // 0x02D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x02D1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2D2[0x6];                                      // 0x02D2(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_Handled_ReturnValue_1;                    // 0x02D8(0x00B8)()
	struct FEventReply                            CallFunc_Handled_ReturnValue_2;                    // 0x0390(0x00B8)()
	struct FEventReply                            CallFunc_Unhandled_ReturnValue;                    // 0x0448(0x00B8)()
	bool                                          CallFunc_HasFocusedDescendants_ReturnValue;        // 0x0500(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanOR_ReturnValue_1;                  // 0x0501(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_OnPreviewKeyDown) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_OnPreviewKeyDown");
static_assert(sizeof(WDG_ShowroomPage_C_OnPreviewKeyDown) == 0x000508, "Wrong size on WDG_ShowroomPage_C_OnPreviewKeyDown");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewKeyDown, MyGeometry) == 0x000000, "Member 'WDG_ShowroomPage_C_OnPreviewKeyDown::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewKeyDown, InKeyEvent) == 0x000038, "Member 'WDG_ShowroomPage_C_OnPreviewKeyDown::InKeyEvent' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewKeyDown, ReturnValue) == 0x000070, "Member 'WDG_ShowroomPage_C_OnPreviewKeyDown::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewKeyDown, CallFunc_Show_UI_If_Hidden_WasHidden) == 0x000128, "Member 'WDG_ShowroomPage_C_OnPreviewKeyDown::CallFunc_Show_UI_If_Hidden_WasHidden' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewKeyDown, CallFunc_GetKey_ReturnValue) == 0x000130, "Member 'WDG_ShowroomPage_C_OnPreviewKeyDown::CallFunc_GetKey_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewKeyDown, CallFunc_EqualEqual_KeyKey_ReturnValue) == 0x000148, "Member 'WDG_ShowroomPage_C_OnPreviewKeyDown::CallFunc_EqualEqual_KeyKey_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewKeyDown, CallFunc_EqualEqual_KeyKey_ReturnValue_1) == 0x000149, "Member 'WDG_ShowroomPage_C_OnPreviewKeyDown::CallFunc_EqualEqual_KeyKey_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewKeyDown, CallFunc_OnPreviewKeyDown_ReturnValue) == 0x000150, "Member 'WDG_ShowroomPage_C_OnPreviewKeyDown::CallFunc_OnPreviewKeyDown_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewKeyDown, CallFunc_BooleanOR_ReturnValue) == 0x000208, "Member 'WDG_ShowroomPage_C_OnPreviewKeyDown::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewKeyDown, CallFunc_IsInCustomEditor_ReturnValue) == 0x000209, "Member 'WDG_ShowroomPage_C_OnPreviewKeyDown::CallFunc_IsInCustomEditor_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewKeyDown, CallFunc_Handled_ReturnValue) == 0x000210, "Member 'WDG_ShowroomPage_C_OnPreviewKeyDown::CallFunc_Handled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewKeyDown, CallFunc_GetSequencePlayer_ReturnValue) == 0x0002C8, "Member 'WDG_ShowroomPage_C_OnPreviewKeyDown::CallFunc_GetSequencePlayer_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewKeyDown, CallFunc_IsPlaying_ReturnValue) == 0x0002D0, "Member 'WDG_ShowroomPage_C_OnPreviewKeyDown::CallFunc_IsPlaying_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewKeyDown, CallFunc_BooleanAND_ReturnValue) == 0x0002D1, "Member 'WDG_ShowroomPage_C_OnPreviewKeyDown::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewKeyDown, CallFunc_Handled_ReturnValue_1) == 0x0002D8, "Member 'WDG_ShowroomPage_C_OnPreviewKeyDown::CallFunc_Handled_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewKeyDown, CallFunc_Handled_ReturnValue_2) == 0x000390, "Member 'WDG_ShowroomPage_C_OnPreviewKeyDown::CallFunc_Handled_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewKeyDown, CallFunc_Unhandled_ReturnValue) == 0x000448, "Member 'WDG_ShowroomPage_C_OnPreviewKeyDown::CallFunc_Unhandled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewKeyDown, CallFunc_HasFocusedDescendants_ReturnValue) == 0x000500, "Member 'WDG_ShowroomPage_C_OnPreviewKeyDown::CallFunc_HasFocusedDescendants_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewKeyDown, CallFunc_BooleanOR_ReturnValue_1) == 0x000501, "Member 'WDG_ShowroomPage_C_OnPreviewKeyDown::CallFunc_BooleanOR_ReturnValue_1' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.UpdateModelTiles
// 0x0028 (0x0028 - 0x0000)
struct WDG_ShowroomPage_C_UpdateModelTiles final
{
public:
	bool                                          All_Models;                                        // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<struct FModelInfo>                     CallFunc_GetAllModels_ReturnValue;                 // 0x0008(0x0010)(ReferenceParm)
	TArray<struct FModelInfo>                     CallFunc_GetModels_ReturnValue;                    // 0x0018(0x0010)(ReferenceParm)
};
static_assert(alignof(WDG_ShowroomPage_C_UpdateModelTiles) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_UpdateModelTiles");
static_assert(sizeof(WDG_ShowroomPage_C_UpdateModelTiles) == 0x000028, "Wrong size on WDG_ShowroomPage_C_UpdateModelTiles");
static_assert(offsetof(WDG_ShowroomPage_C_UpdateModelTiles, All_Models) == 0x000000, "Member 'WDG_ShowroomPage_C_UpdateModelTiles::All_Models' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_UpdateModelTiles, CallFunc_GetAllModels_ReturnValue) == 0x000008, "Member 'WDG_ShowroomPage_C_UpdateModelTiles::CallFunc_GetAllModels_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_UpdateModelTiles, CallFunc_GetModels_ReturnValue) == 0x000018, "Member 'WDG_ShowroomPage_C_UpdateModelTiles::CallFunc_GetModels_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.UpdateTeamTiles
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomPage_C_UpdateTeamTiles final
{
public:
	TArray<struct FTeamInfo>                      CallFunc_GetTeams_ReturnValue;                     // 0x0000(0x0010)(ReferenceParm)
};
static_assert(alignof(WDG_ShowroomPage_C_UpdateTeamTiles) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_UpdateTeamTiles");
static_assert(sizeof(WDG_ShowroomPage_C_UpdateTeamTiles) == 0x000010, "Wrong size on WDG_ShowroomPage_C_UpdateTeamTiles");
static_assert(offsetof(WDG_ShowroomPage_C_UpdateTeamTiles, CallFunc_GetTeams_ReturnValue) == 0x000000, "Member 'WDG_ShowroomPage_C_UpdateTeamTiles::CallFunc_GetTeams_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.FocusToSelectedCarEntry
// 0x0030 (0x0030 - 0x0000)
struct WDG_ShowroomPage_C_FocusToSelectedCarEntry final
{
public:
	int32                                         Temp_int_Variable;                                 // 0x0000(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_6[0x2];                                        // 0x0006(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetChildrenCount_ReturnValue;             // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWidget*                                CallFunc_GetChildAt_ReturnValue;                   // 0x0010(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1C[0x4];                                       // 0x001C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomTileItemHorizontal_C*      K2Node_DynamicCast_AsWDG_Showroom_Tile_Item_Horizontal; // 0x0020(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0028(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x0029(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x002A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_FocusToSelectedCarEntry) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_FocusToSelectedCarEntry");
static_assert(sizeof(WDG_ShowroomPage_C_FocusToSelectedCarEntry) == 0x000030, "Wrong size on WDG_ShowroomPage_C_FocusToSelectedCarEntry");
static_assert(offsetof(WDG_ShowroomPage_C_FocusToSelectedCarEntry, Temp_int_Variable) == 0x000000, "Member 'WDG_ShowroomPage_C_FocusToSelectedCarEntry::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FocusToSelectedCarEntry, Temp_bool_Variable) == 0x000004, "Member 'WDG_ShowroomPage_C_FocusToSelectedCarEntry::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FocusToSelectedCarEntry, CallFunc_Not_PreBool_ReturnValue) == 0x000005, "Member 'WDG_ShowroomPage_C_FocusToSelectedCarEntry::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FocusToSelectedCarEntry, CallFunc_Add_IntInt_ReturnValue) == 0x000008, "Member 'WDG_ShowroomPage_C_FocusToSelectedCarEntry::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FocusToSelectedCarEntry, CallFunc_GetChildrenCount_ReturnValue) == 0x00000C, "Member 'WDG_ShowroomPage_C_FocusToSelectedCarEntry::CallFunc_GetChildrenCount_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FocusToSelectedCarEntry, CallFunc_GetChildAt_ReturnValue) == 0x000010, "Member 'WDG_ShowroomPage_C_FocusToSelectedCarEntry::CallFunc_GetChildAt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FocusToSelectedCarEntry, CallFunc_Subtract_IntInt_ReturnValue) == 0x000018, "Member 'WDG_ShowroomPage_C_FocusToSelectedCarEntry::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FocusToSelectedCarEntry, K2Node_DynamicCast_AsWDG_Showroom_Tile_Item_Horizontal) == 0x000020, "Member 'WDG_ShowroomPage_C_FocusToSelectedCarEntry::K2Node_DynamicCast_AsWDG_Showroom_Tile_Item_Horizontal' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FocusToSelectedCarEntry, K2Node_DynamicCast_bSuccess) == 0x000028, "Member 'WDG_ShowroomPage_C_FocusToSelectedCarEntry::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FocusToSelectedCarEntry, CallFunc_LessEqual_IntInt_ReturnValue) == 0x000029, "Member 'WDG_ShowroomPage_C_FocusToSelectedCarEntry::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FocusToSelectedCarEntry, CallFunc_BooleanAND_ReturnValue) == 0x00002A, "Member 'WDG_ShowroomPage_C_FocusToSelectedCarEntry::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.GoToCurrentDriver
// 0x0018 (0x0018 - 0x0000)
struct WDG_ShowroomPage_C_GoToCurrentDriver final
{
public:
	EUINavigation                                 Navigation_0;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                ReturnValue;                                       // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsVisible_ReturnValue;                    // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsVisible_ReturnValue_1;                  // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0012(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_GoToCurrentDriver) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_GoToCurrentDriver");
static_assert(sizeof(WDG_ShowroomPage_C_GoToCurrentDriver) == 0x000018, "Wrong size on WDG_ShowroomPage_C_GoToCurrentDriver");
static_assert(offsetof(WDG_ShowroomPage_C_GoToCurrentDriver, Navigation_0) == 0x000000, "Member 'WDG_ShowroomPage_C_GoToCurrentDriver::Navigation_0' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_GoToCurrentDriver, ReturnValue) == 0x000008, "Member 'WDG_ShowroomPage_C_GoToCurrentDriver::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_GoToCurrentDriver, CallFunc_IsVisible_ReturnValue) == 0x000010, "Member 'WDG_ShowroomPage_C_GoToCurrentDriver::CallFunc_IsVisible_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_GoToCurrentDriver, CallFunc_IsVisible_ReturnValue_1) == 0x000011, "Member 'WDG_ShowroomPage_C_GoToCurrentDriver::CallFunc_IsVisible_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_GoToCurrentDriver, CallFunc_BooleanAND_ReturnValue) == 0x000012, "Member 'WDG_ShowroomPage_C_GoToCurrentDriver::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.PrepareCustomEditor
// 0x00E8 (0x00E8 - 0x0000)
struct WDG_ShowroomPage_C_PrepareCustomEditor final
{
public:
	struct FCarInfo                               CarInfo;                                           // 0x0000(0x00E0)(BlueprintVisible, BlueprintReadOnly, Parm)
	ECarModelType                                 CarModel;                                          // 0x00E0(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          WasDirty;                                          // 0x00E1(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_PrepareCustomEditor) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_PrepareCustomEditor");
static_assert(sizeof(WDG_ShowroomPage_C_PrepareCustomEditor) == 0x0000E8, "Wrong size on WDG_ShowroomPage_C_PrepareCustomEditor");
static_assert(offsetof(WDG_ShowroomPage_C_PrepareCustomEditor, CarInfo) == 0x000000, "Member 'WDG_ShowroomPage_C_PrepareCustomEditor::CarInfo' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_PrepareCustomEditor, CarModel) == 0x0000E0, "Member 'WDG_ShowroomPage_C_PrepareCustomEditor::CarModel' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_PrepareCustomEditor, WasDirty) == 0x0000E1, "Member 'WDG_ShowroomPage_C_PrepareCustomEditor::WasDirty' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.ReturnToCallingPage
// 0x0028 (0x0028 - 0x0000)
struct WDG_ShowroomPage_C_ReturnToCallingPage final
{
public:
	bool                                          CallFunc_deleteChampionshipResults_ReturnValue;    // 0x0000(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class ULevelSequencePlayer*                   CallFunc_GetSequencePlayer_ReturnValue;            // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue;               // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcPageBase*                            CallFunc_GoToPage_ReturnValue;                     // 0x0018(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GoToCurrentRootPage_ReturnValue;          // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_ReturnToCallingPage) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_ReturnToCallingPage");
static_assert(sizeof(WDG_ShowroomPage_C_ReturnToCallingPage) == 0x000028, "Wrong size on WDG_ShowroomPage_C_ReturnToCallingPage");
static_assert(offsetof(WDG_ShowroomPage_C_ReturnToCallingPage, CallFunc_deleteChampionshipResults_ReturnValue) == 0x000000, "Member 'WDG_ShowroomPage_C_ReturnToCallingPage::CallFunc_deleteChampionshipResults_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ReturnToCallingPage, CallFunc_GetSequencePlayer_ReturnValue) == 0x000008, "Member 'WDG_ShowroomPage_C_ReturnToCallingPage::CallFunc_GetSequencePlayer_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ReturnToCallingPage, CallFunc_GetMenuManager_ReturnValue) == 0x000010, "Member 'WDG_ShowroomPage_C_ReturnToCallingPage::CallFunc_GetMenuManager_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ReturnToCallingPage, CallFunc_GoToPage_ReturnValue) == 0x000018, "Member 'WDG_ShowroomPage_C_ReturnToCallingPage::CallFunc_GoToPage_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ReturnToCallingPage, CallFunc_GoToCurrentRootPage_ReturnValue) == 0x000020, "Member 'WDG_ShowroomPage_C_ReturnToCallingPage::CallFunc_GoToCurrentRootPage_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.CheckEnableHideFileOpButtons
// 0x0004 (0x0004 - 0x0000)
struct WDG_ShowroomPage_C_CheckEnableHideFileOpButtons final
{
public:
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0000(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsNewCustomCar_ReturnValue;               // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0003(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_CheckEnableHideFileOpButtons) == 0x000001, "Wrong alignment on WDG_ShowroomPage_C_CheckEnableHideFileOpButtons");
static_assert(sizeof(WDG_ShowroomPage_C_CheckEnableHideFileOpButtons) == 0x000004, "Wrong size on WDG_ShowroomPage_C_CheckEnableHideFileOpButtons");
static_assert(offsetof(WDG_ShowroomPage_C_CheckEnableHideFileOpButtons, CallFunc_MakeLiteralByte_ReturnValue) == 0x000000, "Member 'WDG_ShowroomPage_C_CheckEnableHideFileOpButtons::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_CheckEnableHideFileOpButtons, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000001, "Member 'WDG_ShowroomPage_C_CheckEnableHideFileOpButtons::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_CheckEnableHideFileOpButtons, CallFunc_IsNewCustomCar_ReturnValue) == 0x000002, "Member 'WDG_ShowroomPage_C_CheckEnableHideFileOpButtons::CallFunc_IsNewCustomCar_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_CheckEnableHideFileOpButtons, CallFunc_Not_PreBool_ReturnValue) == 0x000003, "Member 'WDG_ShowroomPage_C_CheckEnableHideFileOpButtons::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.FindSelectedEntry
// 0x0040 (0x0040 - 0x0000)
struct WDG_ShowroomPage_C_FindSelectedEntry final
{
public:
	bool                                          ScrollToOnFind;                                    // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomTileItemHorizontal_C*      AsWDG_Showroom_Tile_Item_Horizontal;               // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_12[0x2];                                       // 0x0012(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable;                                 // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetChildrenCount_ReturnValue;             // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWidget*                                CallFunc_GetChildAt_ReturnValue;                   // 0x0020(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2C[0x4];                                       // 0x002C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomTileItemHorizontal_C*      K2Node_DynamicCast_AsWDG_Showroom_Tile_Item_Horizontal; // 0x0030(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x0039(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x003A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_FindSelectedEntry) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_FindSelectedEntry");
static_assert(sizeof(WDG_ShowroomPage_C_FindSelectedEntry) == 0x000040, "Wrong size on WDG_ShowroomPage_C_FindSelectedEntry");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedEntry, ScrollToOnFind) == 0x000000, "Member 'WDG_ShowroomPage_C_FindSelectedEntry::ScrollToOnFind' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedEntry, AsWDG_Showroom_Tile_Item_Horizontal) == 0x000008, "Member 'WDG_ShowroomPage_C_FindSelectedEntry::AsWDG_Showroom_Tile_Item_Horizontal' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedEntry, Temp_bool_Variable) == 0x000010, "Member 'WDG_ShowroomPage_C_FindSelectedEntry::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedEntry, CallFunc_Not_PreBool_ReturnValue) == 0x000011, "Member 'WDG_ShowroomPage_C_FindSelectedEntry::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedEntry, Temp_int_Variable) == 0x000014, "Member 'WDG_ShowroomPage_C_FindSelectedEntry::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedEntry, CallFunc_Add_IntInt_ReturnValue) == 0x000018, "Member 'WDG_ShowroomPage_C_FindSelectedEntry::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedEntry, CallFunc_GetChildrenCount_ReturnValue) == 0x00001C, "Member 'WDG_ShowroomPage_C_FindSelectedEntry::CallFunc_GetChildrenCount_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedEntry, CallFunc_GetChildAt_ReturnValue) == 0x000020, "Member 'WDG_ShowroomPage_C_FindSelectedEntry::CallFunc_GetChildAt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedEntry, CallFunc_Subtract_IntInt_ReturnValue) == 0x000028, "Member 'WDG_ShowroomPage_C_FindSelectedEntry::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedEntry, K2Node_DynamicCast_AsWDG_Showroom_Tile_Item_Horizontal) == 0x000030, "Member 'WDG_ShowroomPage_C_FindSelectedEntry::K2Node_DynamicCast_AsWDG_Showroom_Tile_Item_Horizontal' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedEntry, K2Node_DynamicCast_bSuccess) == 0x000038, "Member 'WDG_ShowroomPage_C_FindSelectedEntry::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedEntry, CallFunc_LessEqual_IntInt_ReturnValue) == 0x000039, "Member 'WDG_ShowroomPage_C_FindSelectedEntry::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedEntry, CallFunc_BooleanAND_ReturnValue) == 0x00003A, "Member 'WDG_ShowroomPage_C_FindSelectedEntry::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.IsShowroomPlaying
// 0x0018 (0x0018 - 0x0000)
struct WDG_ShowroomPage_C_IsShowroomPlaying final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class ULevelSequencePlayer*                   CallFunc_GetSequencePlayer_ReturnValue;            // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsPlaying_ReturnValue;                    // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_IsShowroomPlaying) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_IsShowroomPlaying");
static_assert(sizeof(WDG_ShowroomPage_C_IsShowroomPlaying) == 0x000018, "Wrong size on WDG_ShowroomPage_C_IsShowroomPlaying");
static_assert(offsetof(WDG_ShowroomPage_C_IsShowroomPlaying, ReturnValue) == 0x000000, "Member 'WDG_ShowroomPage_C_IsShowroomPlaying::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_IsShowroomPlaying, CallFunc_GetSequencePlayer_ReturnValue) == 0x000008, "Member 'WDG_ShowroomPage_C_IsShowroomPlaying::CallFunc_GetSequencePlayer_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_IsShowroomPlaying, CallFunc_IsPlaying_ReturnValue) == 0x000010, "Member 'WDG_ShowroomPage_C_IsShowroomPlaying::CallFunc_IsPlaying_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnPreviewMouseButtonDown
// 0x0300 (0x0300 - 0x0000)
struct WDG_ShowroomPage_C_OnPreviewMouseButtonDown final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FPointerEvent                          MouseEvent;                                        // 0x0038(0x0070)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	struct FEventReply                            ReturnValue;                                       // 0x00A8(0x00B8)(Parm, OutParm, ReturnParm)
	struct FKey                                   CallFunc_PointerEvent_GetEffectingButton_ReturnValue; // 0x0160(0x0018)(HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue;            // 0x0178(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_CanPlay_Result;                           // 0x0179(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_17A[0x6];                                      // 0x017A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_OnPreviewMouseButtonDown_ReturnValue;     // 0x0180(0x00B8)()
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0238(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_239[0x7];                                      // 0x0239(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_Unhandled_ReturnValue;                    // 0x0240(0x00B8)()
	bool                                          CallFunc_IsVisible_ReturnValue;                    // 0x02F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_OnPreviewMouseButtonDown) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_OnPreviewMouseButtonDown");
static_assert(sizeof(WDG_ShowroomPage_C_OnPreviewMouseButtonDown) == 0x000300, "Wrong size on WDG_ShowroomPage_C_OnPreviewMouseButtonDown");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewMouseButtonDown, MyGeometry) == 0x000000, "Member 'WDG_ShowroomPage_C_OnPreviewMouseButtonDown::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewMouseButtonDown, MouseEvent) == 0x000038, "Member 'WDG_ShowroomPage_C_OnPreviewMouseButtonDown::MouseEvent' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewMouseButtonDown, ReturnValue) == 0x0000A8, "Member 'WDG_ShowroomPage_C_OnPreviewMouseButtonDown::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewMouseButtonDown, CallFunc_PointerEvent_GetEffectingButton_ReturnValue) == 0x000160, "Member 'WDG_ShowroomPage_C_OnPreviewMouseButtonDown::CallFunc_PointerEvent_GetEffectingButton_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewMouseButtonDown, CallFunc_EqualEqual_KeyKey_ReturnValue) == 0x000178, "Member 'WDG_ShowroomPage_C_OnPreviewMouseButtonDown::CallFunc_EqualEqual_KeyKey_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewMouseButtonDown, CallFunc_CanPlay_Result) == 0x000179, "Member 'WDG_ShowroomPage_C_OnPreviewMouseButtonDown::CallFunc_CanPlay_Result' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewMouseButtonDown, CallFunc_OnPreviewMouseButtonDown_ReturnValue) == 0x000180, "Member 'WDG_ShowroomPage_C_OnPreviewMouseButtonDown::CallFunc_OnPreviewMouseButtonDown_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewMouseButtonDown, CallFunc_BooleanAND_ReturnValue) == 0x000238, "Member 'WDG_ShowroomPage_C_OnPreviewMouseButtonDown::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewMouseButtonDown, CallFunc_Unhandled_ReturnValue) == 0x000240, "Member 'WDG_ShowroomPage_C_OnPreviewMouseButtonDown::CallFunc_Unhandled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnPreviewMouseButtonDown, CallFunc_IsVisible_ReturnValue) == 0x0002F8, "Member 'WDG_ShowroomPage_C_OnPreviewMouseButtonDown::CallFunc_IsVisible_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.GetCarSystems
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomPage_C_GetCarSystems final
{
public:
	class UAcCarSystems*                          carSystems;                                        // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_GetCarSystems) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_GetCarSystems");
static_assert(sizeof(WDG_ShowroomPage_C_GetCarSystems) == 0x000008, "Wrong size on WDG_ShowroomPage_C_GetCarSystems");
static_assert(offsetof(WDG_ShowroomPage_C_GetCarSystems, carSystems) == 0x000000, "Member 'WDG_ShowroomPage_C_GetCarSystems::carSystems' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.SetCarLights
// 0x0020 (0x0020 - 0x0000)
struct WDG_ShowroomPage_C_SetCarLights final
{
public:
	int32                                         Stage;                                             // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         lightStage;                                        // 0x0004(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchInteger_CmpSuccess;                   // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x0009(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_Conv_IntToByte_ReturnValue;               // 0x000A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_GetValidValue_ReturnValue;                // 0x000B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C[0x4];                                        // 0x000C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcCarSystems*                          CallFunc_GetCarSystems_carSystems;                 // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0019(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_SetCarLights) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_SetCarLights");
static_assert(sizeof(WDG_ShowroomPage_C_SetCarLights) == 0x000020, "Wrong size on WDG_ShowroomPage_C_SetCarLights");
static_assert(offsetof(WDG_ShowroomPage_C_SetCarLights, Stage) == 0x000000, "Member 'WDG_ShowroomPage_C_SetCarLights::Stage' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_SetCarLights, lightStage) == 0x000004, "Member 'WDG_ShowroomPage_C_SetCarLights::lightStage' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_SetCarLights, K2Node_SwitchInteger_CmpSuccess) == 0x000008, "Member 'WDG_ShowroomPage_C_SetCarLights::K2Node_SwitchInteger_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_SetCarLights, CallFunc_Greater_IntInt_ReturnValue) == 0x000009, "Member 'WDG_ShowroomPage_C_SetCarLights::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_SetCarLights, CallFunc_Conv_IntToByte_ReturnValue) == 0x00000A, "Member 'WDG_ShowroomPage_C_SetCarLights::CallFunc_Conv_IntToByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_SetCarLights, CallFunc_GetValidValue_ReturnValue) == 0x00000B, "Member 'WDG_ShowroomPage_C_SetCarLights::CallFunc_GetValidValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_SetCarLights, CallFunc_GetCarSystems_carSystems) == 0x000010, "Member 'WDG_ShowroomPage_C_SetCarLights::CallFunc_GetCarSystems_carSystems' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_SetCarLights, CallFunc_Not_PreBool_ReturnValue) == 0x000018, "Member 'WDG_ShowroomPage_C_SetCarLights::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_SetCarLights, CallFunc_BooleanAND_ReturnValue) == 0x000019, "Member 'WDG_ShowroomPage_C_SetCarLights::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.GetCarAnimations
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomPage_C_GetCarAnimations final
{
public:
	class UAcCarAnimations*                       CarAnimations;                                     // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_GetCarAnimations) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_GetCarAnimations");
static_assert(sizeof(WDG_ShowroomPage_C_GetCarAnimations) == 0x000008, "Wrong size on WDG_ShowroomPage_C_GetCarAnimations");
static_assert(offsetof(WDG_ShowroomPage_C_GetCarAnimations, CarAnimations) == 0x000000, "Member 'WDG_ShowroomPage_C_GetCarAnimations::CarAnimations' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.ToggleUIOpacity
// 0x000C (0x000C - 0x0000)
struct WDG_ShowroomPage_C_ToggleUIOpacity final
{
public:
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0000(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2[0x2];                                        // 0x0002(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_GetRenderOpacity_ReturnValue;             // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_FloatFloat_ReturnValue;        // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_ToggleUIOpacity) == 0x000004, "Wrong alignment on WDG_ShowroomPage_C_ToggleUIOpacity");
static_assert(sizeof(WDG_ShowroomPage_C_ToggleUIOpacity) == 0x00000C, "Wrong size on WDG_ShowroomPage_C_ToggleUIOpacity");
static_assert(offsetof(WDG_ShowroomPage_C_ToggleUIOpacity, CallFunc_MakeLiteralByte_ReturnValue) == 0x000000, "Member 'WDG_ShowroomPage_C_ToggleUIOpacity::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ToggleUIOpacity, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000001, "Member 'WDG_ShowroomPage_C_ToggleUIOpacity::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ToggleUIOpacity, CallFunc_GetRenderOpacity_ReturnValue) == 0x000004, "Member 'WDG_ShowroomPage_C_ToggleUIOpacity::CallFunc_GetRenderOpacity_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ToggleUIOpacity, CallFunc_EqualEqual_FloatFloat_ReturnValue) == 0x000008, "Member 'WDG_ShowroomPage_C_ToggleUIOpacity::CallFunc_EqualEqual_FloatFloat_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnKeyUp
// 0x0210 (0x0210 - 0x0000)
struct WDG_ShowroomPage_C_OnKeyUp final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FKeyEvent                              InKeyEvent;                                        // 0x0038(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm)
	struct FEventReply                            ReturnValue;                                       // 0x0070(0x00B8)(Parm, OutParm, ReturnParm)
	bool                                          CallFunc_IsShowroomPlaying_ReturnValue;            // 0x0128(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_129[0x7];                                      // 0x0129(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FKey                                   CallFunc_GetKey_ReturnValue;                       // 0x0130(0x0018)(HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0148(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue;            // 0x0149(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_14A[0x6];                                      // 0x014A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_Unhandled_ReturnValue;                    // 0x0150(0x00B8)()
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0208(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_OnKeyUp) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_OnKeyUp");
static_assert(sizeof(WDG_ShowroomPage_C_OnKeyUp) == 0x000210, "Wrong size on WDG_ShowroomPage_C_OnKeyUp");
static_assert(offsetof(WDG_ShowroomPage_C_OnKeyUp, MyGeometry) == 0x000000, "Member 'WDG_ShowroomPage_C_OnKeyUp::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnKeyUp, InKeyEvent) == 0x000038, "Member 'WDG_ShowroomPage_C_OnKeyUp::InKeyEvent' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnKeyUp, ReturnValue) == 0x000070, "Member 'WDG_ShowroomPage_C_OnKeyUp::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnKeyUp, CallFunc_IsShowroomPlaying_ReturnValue) == 0x000128, "Member 'WDG_ShowroomPage_C_OnKeyUp::CallFunc_IsShowroomPlaying_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnKeyUp, CallFunc_GetKey_ReturnValue) == 0x000130, "Member 'WDG_ShowroomPage_C_OnKeyUp::CallFunc_GetKey_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnKeyUp, CallFunc_Not_PreBool_ReturnValue) == 0x000148, "Member 'WDG_ShowroomPage_C_OnKeyUp::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnKeyUp, CallFunc_EqualEqual_KeyKey_ReturnValue) == 0x000149, "Member 'WDG_ShowroomPage_C_OnKeyUp::CallFunc_EqualEqual_KeyKey_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnKeyUp, CallFunc_Unhandled_ReturnValue) == 0x000150, "Member 'WDG_ShowroomPage_C_OnKeyUp::CallFunc_Unhandled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnKeyUp, CallFunc_BooleanAND_ReturnValue) == 0x000208, "Member 'WDG_ShowroomPage_C_OnKeyUp::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.FindSelectedVariant
// 0x0040 (0x0040 - 0x0000)
struct WDG_ShowroomPage_C_FindSelectedVariant final
{
public:
	bool                                          ScrollToOnFind;                                    // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomTileItemHorizontal_C*      AsWDG_Showroom_Tile_Item_Horizontal;               // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_12[0x2];                                       // 0x0012(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable;                                 // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetChildrenCount_ReturnValue;             // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWidget*                                CallFunc_GetChildAt_ReturnValue;                   // 0x0020(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2C[0x4];                                       // 0x002C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomTileItemHorizontal_C*      K2Node_DynamicCast_AsWDG_Showroom_Tile_Item_Horizontal; // 0x0030(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x0039(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x003A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_FindSelectedVariant) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_FindSelectedVariant");
static_assert(sizeof(WDG_ShowroomPage_C_FindSelectedVariant) == 0x000040, "Wrong size on WDG_ShowroomPage_C_FindSelectedVariant");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedVariant, ScrollToOnFind) == 0x000000, "Member 'WDG_ShowroomPage_C_FindSelectedVariant::ScrollToOnFind' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedVariant, AsWDG_Showroom_Tile_Item_Horizontal) == 0x000008, "Member 'WDG_ShowroomPage_C_FindSelectedVariant::AsWDG_Showroom_Tile_Item_Horizontal' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedVariant, Temp_bool_Variable) == 0x000010, "Member 'WDG_ShowroomPage_C_FindSelectedVariant::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedVariant, CallFunc_Not_PreBool_ReturnValue) == 0x000011, "Member 'WDG_ShowroomPage_C_FindSelectedVariant::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedVariant, Temp_int_Variable) == 0x000014, "Member 'WDG_ShowroomPage_C_FindSelectedVariant::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedVariant, CallFunc_Add_IntInt_ReturnValue) == 0x000018, "Member 'WDG_ShowroomPage_C_FindSelectedVariant::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedVariant, CallFunc_GetChildrenCount_ReturnValue) == 0x00001C, "Member 'WDG_ShowroomPage_C_FindSelectedVariant::CallFunc_GetChildrenCount_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedVariant, CallFunc_GetChildAt_ReturnValue) == 0x000020, "Member 'WDG_ShowroomPage_C_FindSelectedVariant::CallFunc_GetChildAt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedVariant, CallFunc_Subtract_IntInt_ReturnValue) == 0x000028, "Member 'WDG_ShowroomPage_C_FindSelectedVariant::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedVariant, K2Node_DynamicCast_AsWDG_Showroom_Tile_Item_Horizontal) == 0x000030, "Member 'WDG_ShowroomPage_C_FindSelectedVariant::K2Node_DynamicCast_AsWDG_Showroom_Tile_Item_Horizontal' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedVariant, K2Node_DynamicCast_bSuccess) == 0x000038, "Member 'WDG_ShowroomPage_C_FindSelectedVariant::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedVariant, CallFunc_LessEqual_IntInt_ReturnValue) == 0x000039, "Member 'WDG_ShowroomPage_C_FindSelectedVariant::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_FindSelectedVariant, CallFunc_BooleanAND_ReturnValue) == 0x00003A, "Member 'WDG_ShowroomPage_C_FindSelectedVariant::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.IsCurrentCarCustom
// 0x0001 (0x0001 - 0x0000)
struct WDG_ShowroomPage_C_IsCurrentCarCustom final
{
public:
	bool                                          isCustom;                                          // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_IsCurrentCarCustom) == 0x000001, "Wrong alignment on WDG_ShowroomPage_C_IsCurrentCarCustom");
static_assert(sizeof(WDG_ShowroomPage_C_IsCurrentCarCustom) == 0x000001, "Wrong size on WDG_ShowroomPage_C_IsCurrentCarCustom");
static_assert(offsetof(WDG_ShowroomPage_C_IsCurrentCarCustom, isCustom) == 0x000000, "Member 'WDG_ShowroomPage_C_IsCurrentCarCustom::isCustom' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.UpdateShowroomLabels
// 0x0028 (0x0028 - 0x0000)
struct WDG_ShowroomPage_C_UpdateShowroomLabels final
{
public:
	class FName                                   ModelName;                                         // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EBrandType                                    Brand;                                             // 0x0008(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_9[0x7];                                        // 0x0009(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_NameToText_ReturnValue;              // 0x0010(0x0018)()
};
static_assert(alignof(WDG_ShowroomPage_C_UpdateShowroomLabels) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_UpdateShowroomLabels");
static_assert(sizeof(WDG_ShowroomPage_C_UpdateShowroomLabels) == 0x000028, "Wrong size on WDG_ShowroomPage_C_UpdateShowroomLabels");
static_assert(offsetof(WDG_ShowroomPage_C_UpdateShowroomLabels, ModelName) == 0x000000, "Member 'WDG_ShowroomPage_C_UpdateShowroomLabels::ModelName' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_UpdateShowroomLabels, Brand) == 0x000008, "Member 'WDG_ShowroomPage_C_UpdateShowroomLabels::Brand' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_UpdateShowroomLabels, CallFunc_Conv_NameToText_ReturnValue) == 0x000010, "Member 'WDG_ShowroomPage_C_UpdateShowroomLabels::CallFunc_Conv_NameToText_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.ProceedToNextPage
// 0x0078 (0x0078 - 0x0000)
struct WDG_ShowroomPage_C_ProceedToNextPage final
{
public:
	class FName                                   Temp_name_Variable;                                // 0x0000(0x0008)(ConstParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue;              // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance;             // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Conv_NameToString_ReturnValue;            // 0x0020(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue;                // 0x0030(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_41[0x7];                                       // 0x0041(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UClass*                                 CallFunc_Map_Find_Value;                           // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Map_Find_ReturnValue;                     // 0x0050(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_isChamp_ReturnValue;                      // 0x0051(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_52[0x6];                                       // 0x0052(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPageBase*                            CallFunc_GoToPage_ReturnValue;                     // 0x0058(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class ULevelSequencePlayer*                   CallFunc_GetSequencePlayer_ReturnValue;            // 0x0060(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue;               // 0x0068(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GoToCurrentRootPage_ReturnValue;          // 0x0070(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_ProceedToNextPage) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_ProceedToNextPage");
static_assert(sizeof(WDG_ShowroomPage_C_ProceedToNextPage) == 0x000078, "Wrong size on WDG_ShowroomPage_C_ProceedToNextPage");
static_assert(offsetof(WDG_ShowroomPage_C_ProceedToNextPage, Temp_name_Variable) == 0x000000, "Member 'WDG_ShowroomPage_C_ProceedToNextPage::Temp_name_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ProceedToNextPage, CallFunc_GetGameInstance_ReturnValue) == 0x000008, "Member 'WDG_ShowroomPage_C_ProceedToNextPage::CallFunc_GetGameInstance_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ProceedToNextPage, K2Node_DynamicCast_AsAc_Game_Instance) == 0x000010, "Member 'WDG_ShowroomPage_C_ProceedToNextPage::K2Node_DynamicCast_AsAc_Game_Instance' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ProceedToNextPage, K2Node_DynamicCast_bSuccess) == 0x000018, "Member 'WDG_ShowroomPage_C_ProceedToNextPage::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ProceedToNextPage, CallFunc_Conv_NameToString_ReturnValue) == 0x000020, "Member 'WDG_ShowroomPage_C_ProceedToNextPage::CallFunc_Conv_NameToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ProceedToNextPage, CallFunc_Concat_StrStr_ReturnValue) == 0x000030, "Member 'WDG_ShowroomPage_C_ProceedToNextPage::CallFunc_Concat_StrStr_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ProceedToNextPage, CallFunc_IsValid_ReturnValue) == 0x000040, "Member 'WDG_ShowroomPage_C_ProceedToNextPage::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ProceedToNextPage, CallFunc_Map_Find_Value) == 0x000048, "Member 'WDG_ShowroomPage_C_ProceedToNextPage::CallFunc_Map_Find_Value' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ProceedToNextPage, CallFunc_Map_Find_ReturnValue) == 0x000050, "Member 'WDG_ShowroomPage_C_ProceedToNextPage::CallFunc_Map_Find_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ProceedToNextPage, CallFunc_isChamp_ReturnValue) == 0x000051, "Member 'WDG_ShowroomPage_C_ProceedToNextPage::CallFunc_isChamp_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ProceedToNextPage, CallFunc_GoToPage_ReturnValue) == 0x000058, "Member 'WDG_ShowroomPage_C_ProceedToNextPage::CallFunc_GoToPage_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ProceedToNextPage, CallFunc_GetSequencePlayer_ReturnValue) == 0x000060, "Member 'WDG_ShowroomPage_C_ProceedToNextPage::CallFunc_GetSequencePlayer_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ProceedToNextPage, CallFunc_GetMenuManager_ReturnValue) == 0x000068, "Member 'WDG_ShowroomPage_C_ProceedToNextPage::CallFunc_GetMenuManager_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ProceedToNextPage, CallFunc_GoToCurrentRootPage_ReturnValue) == 0x000070, "Member 'WDG_ShowroomPage_C_ProceedToNextPage::CallFunc_GoToCurrentRootPage_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.ToggleLockIcon
// 0x00E8 (0x00E8 - 0x0000)
struct WDG_ShowroomPage_C_ToggleLockIcon final
{
public:
	struct FCarInfo                               CarInfo;                                           // 0x0000(0x00E0)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	bool                                          CallFunc_CanPlay_Result;                           // 0x00E0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x00E1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x00E2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x00E3(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_ToggleLockIcon) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_ToggleLockIcon");
static_assert(sizeof(WDG_ShowroomPage_C_ToggleLockIcon) == 0x0000E8, "Wrong size on WDG_ShowroomPage_C_ToggleLockIcon");
static_assert(offsetof(WDG_ShowroomPage_C_ToggleLockIcon, CarInfo) == 0x000000, "Member 'WDG_ShowroomPage_C_ToggleLockIcon::CarInfo' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ToggleLockIcon, CallFunc_CanPlay_Result) == 0x0000E0, "Member 'WDG_ShowroomPage_C_ToggleLockIcon::CallFunc_CanPlay_Result' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ToggleLockIcon, CallFunc_MakeLiteralByte_ReturnValue) == 0x0000E1, "Member 'WDG_ShowroomPage_C_ToggleLockIcon::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ToggleLockIcon, CallFunc_Not_PreBool_ReturnValue) == 0x0000E2, "Member 'WDG_ShowroomPage_C_ToggleLockIcon::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_ToggleLockIcon, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x0000E3, "Member 'WDG_ShowroomPage_C_ToggleLockIcon::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.ShowCustomEditor
// 0x0001 (0x0001 - 0x0000)
struct WDG_ShowroomPage_C_ShowCustomEditor final
{
public:
	bool                                          isCustom;                                          // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_ShowCustomEditor) == 0x000001, "Wrong alignment on WDG_ShowroomPage_C_ShowCustomEditor");
static_assert(sizeof(WDG_ShowroomPage_C_ShowCustomEditor) == 0x000001, "Wrong size on WDG_ShowroomPage_C_ShowCustomEditor");
static_assert(offsetof(WDG_ShowroomPage_C_ShowCustomEditor, isCustom) == 0x000000, "Member 'WDG_ShowroomPage_C_ShowCustomEditor::isCustom' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.IsInCustomEditor
// 0x0018 (0x0018 - 0x0000)
struct WDG_ShowroomPage_C_IsInCustomEditor final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                CallFunc_GetActiveWidget_ReturnValue;              // 0x0008(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ObjectObject_ReturnValue;      // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_IsInCustomEditor) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_IsInCustomEditor");
static_assert(sizeof(WDG_ShowroomPage_C_IsInCustomEditor) == 0x000018, "Wrong size on WDG_ShowroomPage_C_IsInCustomEditor");
static_assert(offsetof(WDG_ShowroomPage_C_IsInCustomEditor, ReturnValue) == 0x000000, "Member 'WDG_ShowroomPage_C_IsInCustomEditor::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_IsInCustomEditor, CallFunc_GetActiveWidget_ReturnValue) == 0x000008, "Member 'WDG_ShowroomPage_C_IsInCustomEditor::CallFunc_GetActiveWidget_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_IsInCustomEditor, CallFunc_EqualEqual_ObjectObject_ReturnValue) == 0x000010, "Member 'WDG_ShowroomPage_C_IsInCustomEditor::CallFunc_EqualEqual_ObjectObject_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.getCarAvatar
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomPage_C_getCarAvatar final
{
public:
	class ACarAvatar*                             currentShowroomCar;                                // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_getCarAvatar) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_getCarAvatar");
static_assert(sizeof(WDG_ShowroomPage_C_getCarAvatar) == 0x000008, "Wrong size on WDG_ShowroomPage_C_getCarAvatar");
static_assert(offsetof(WDG_ShowroomPage_C_getCarAvatar, currentShowroomCar) == 0x000000, "Member 'WDG_ShowroomPage_C_getCarAvatar::currentShowroomCar' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.GoToDriverCustomization
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomPage_C_GoToDriverCustomization final
{
public:
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue;               // 0x0000(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcPageBase*                            CallFunc_GoToPage_ReturnValue;                     // 0x0008(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPage_C_GoToDriverCustomization) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_GoToDriverCustomization");
static_assert(sizeof(WDG_ShowroomPage_C_GoToDriverCustomization) == 0x000010, "Wrong size on WDG_ShowroomPage_C_GoToDriverCustomization");
static_assert(offsetof(WDG_ShowroomPage_C_GoToDriverCustomization, CallFunc_GetMenuManager_ReturnValue) == 0x000000, "Member 'WDG_ShowroomPage_C_GoToDriverCustomization::CallFunc_GetMenuManager_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_GoToDriverCustomization, CallFunc_GoToPage_ReturnValue) == 0x000008, "Member 'WDG_ShowroomPage_C_GoToDriverCustomization::CallFunc_GoToPage_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.Show UI If Hidden
// 0x000C (0x000C - 0x0000)
struct WDG_ShowroomPage_C_Show_UI_If_Hidden final
{
public:
	bool                                          WasHidden;                                         // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1[0x3];                                        // 0x0001(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_GetRenderOpacity_ReturnValue;             // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_FloatFloat_ReturnValue;        // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsVisible_ReturnValue;                    // 0x0009(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x000A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_Show_UI_If_Hidden) == 0x000004, "Wrong alignment on WDG_ShowroomPage_C_Show_UI_If_Hidden");
static_assert(sizeof(WDG_ShowroomPage_C_Show_UI_If_Hidden) == 0x00000C, "Wrong size on WDG_ShowroomPage_C_Show_UI_If_Hidden");
static_assert(offsetof(WDG_ShowroomPage_C_Show_UI_If_Hidden, WasHidden) == 0x000000, "Member 'WDG_ShowroomPage_C_Show_UI_If_Hidden::WasHidden' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_Show_UI_If_Hidden, CallFunc_GetRenderOpacity_ReturnValue) == 0x000004, "Member 'WDG_ShowroomPage_C_Show_UI_If_Hidden::CallFunc_GetRenderOpacity_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_Show_UI_If_Hidden, CallFunc_EqualEqual_FloatFloat_ReturnValue) == 0x000008, "Member 'WDG_ShowroomPage_C_Show_UI_If_Hidden::CallFunc_EqualEqual_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_Show_UI_If_Hidden, CallFunc_IsVisible_ReturnValue) == 0x000009, "Member 'WDG_ShowroomPage_C_Show_UI_If_Hidden::CallFunc_IsVisible_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_Show_UI_If_Hidden, CallFunc_BooleanAND_ReturnValue) == 0x00000A, "Member 'WDG_ShowroomPage_C_Show_UI_If_Hidden::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnKeyDown
// 0x02B8 (0x02B8 - 0x0000)
struct WDG_ShowroomPage_C_OnKeyDown final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FKeyEvent                              InKeyEvent;                                        // 0x0038(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm)
	struct FEventReply                            ReturnValue;                                       // 0x0070(0x00B8)(Parm, OutParm, ReturnParm)
	struct FKey                                   CallFunc_GetKey_ReturnValue;                       // 0x0128(0x0018)(HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue;            // 0x0140(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue_1;          // 0x0141(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue_2;          // 0x0142(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_143[0x5];                                      // 0x0143(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_Unhandled_ReturnValue;                    // 0x0148(0x00B8)()
	struct FEventReply                            CallFunc_Handled_ReturnValue;                      // 0x0200(0x00B8)()
};
static_assert(alignof(WDG_ShowroomPage_C_OnKeyDown) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_OnKeyDown");
static_assert(sizeof(WDG_ShowroomPage_C_OnKeyDown) == 0x0002B8, "Wrong size on WDG_ShowroomPage_C_OnKeyDown");
static_assert(offsetof(WDG_ShowroomPage_C_OnKeyDown, MyGeometry) == 0x000000, "Member 'WDG_ShowroomPage_C_OnKeyDown::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnKeyDown, InKeyEvent) == 0x000038, "Member 'WDG_ShowroomPage_C_OnKeyDown::InKeyEvent' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnKeyDown, ReturnValue) == 0x000070, "Member 'WDG_ShowroomPage_C_OnKeyDown::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnKeyDown, CallFunc_GetKey_ReturnValue) == 0x000128, "Member 'WDG_ShowroomPage_C_OnKeyDown::CallFunc_GetKey_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnKeyDown, CallFunc_EqualEqual_KeyKey_ReturnValue) == 0x000140, "Member 'WDG_ShowroomPage_C_OnKeyDown::CallFunc_EqualEqual_KeyKey_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnKeyDown, CallFunc_EqualEqual_KeyKey_ReturnValue_1) == 0x000141, "Member 'WDG_ShowroomPage_C_OnKeyDown::CallFunc_EqualEqual_KeyKey_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnKeyDown, CallFunc_EqualEqual_KeyKey_ReturnValue_2) == 0x000142, "Member 'WDG_ShowroomPage_C_OnKeyDown::CallFunc_EqualEqual_KeyKey_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnKeyDown, CallFunc_Unhandled_ReturnValue) == 0x000148, "Member 'WDG_ShowroomPage_C_OnKeyDown::CallFunc_Unhandled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_OnKeyDown, CallFunc_Handled_ReturnValue) == 0x000200, "Member 'WDG_ShowroomPage_C_OnKeyDown::CallFunc_Handled_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.GoUpFromCustomEdit
// 0x0018 (0x0018 - 0x0000)
struct WDG_ShowroomPage_C_GoUpFromCustomEdit final
{
public:
	EUINavigation                                 Navigation_0;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                ReturnValue;                                       // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsVisible_ReturnValue;                    // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPage_C_GoUpFromCustomEdit) == 0x000008, "Wrong alignment on WDG_ShowroomPage_C_GoUpFromCustomEdit");
static_assert(sizeof(WDG_ShowroomPage_C_GoUpFromCustomEdit) == 0x000018, "Wrong size on WDG_ShowroomPage_C_GoUpFromCustomEdit");
static_assert(offsetof(WDG_ShowroomPage_C_GoUpFromCustomEdit, Navigation_0) == 0x000000, "Member 'WDG_ShowroomPage_C_GoUpFromCustomEdit::Navigation_0' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_GoUpFromCustomEdit, ReturnValue) == 0x000008, "Member 'WDG_ShowroomPage_C_GoUpFromCustomEdit::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPage_C_GoUpFromCustomEdit, CallFunc_IsVisible_ReturnValue) == 0x000010, "Member 'WDG_ShowroomPage_C_GoUpFromCustomEdit::CallFunc_IsVisible_ReturnValue' has a wrong offset!");

}

