﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_MPQuickjoinSlotCPInviteSlot

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "Engine_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_MPQuickjoinSlotCPInviteSlot.WDG_MPQuickjoinSlotCPInviteSlot_C.ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot
// 0x00C8 (0x00C8 - 0x0000)
struct WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, <PERSON>rm, ZeroConstructor, IsPlainOldData, NoD<PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	bool                                          Temp_bool_Has_Been_Initd_Variable;                 // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          Temp_bool_IsClosed_Variable;                       // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_6[0x2];                                        // 0x0006(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FGeometry                              K2Node_Event_MyGeometry;                           // 0x0008(0x0038)(IsPlainOldData, NoDestructor)
	float                                         K2Node_Event_InDeltaTime;                          // 0x0040(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_44[0x4];                                       // 0x0044(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationForward_ReturnValue;         // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationReverse_ReturnValue;         // 0x0050(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationForward_ReturnValue_1;       // 0x0058(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationReverse_ReturnValue_1;       // 0x0060(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSlateColor                            K2Node_MakeStruct_SlateColor;                      // 0x0068(0x0028)(UObjectWrapper)
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_1;                    // 0x0090(0x0028)(UObjectWrapper)
	bool                                          CallFunc_UpdateTimer_TimeUp;                       // 0x00B8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_B9[0x7];                                       // 0x00B9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x00C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot) == 0x000008, "Wrong alignment on WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot");
static_assert(sizeof(WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot) == 0x0000C8, "Wrong size on WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot, EntryPoint) == 0x000000, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot, Temp_bool_Has_Been_Initd_Variable) == 0x000004, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot::Temp_bool_Has_Been_Initd_Variable' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot, Temp_bool_IsClosed_Variable) == 0x000005, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot::Temp_bool_IsClosed_Variable' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot, K2Node_Event_MyGeometry) == 0x000008, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot::K2Node_Event_MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot, K2Node_Event_InDeltaTime) == 0x000040, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot::K2Node_Event_InDeltaTime' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot, CallFunc_PlayAnimationForward_ReturnValue) == 0x000048, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot::CallFunc_PlayAnimationForward_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot, CallFunc_PlayAnimationReverse_ReturnValue) == 0x000050, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot::CallFunc_PlayAnimationReverse_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot, CallFunc_PlayAnimationForward_ReturnValue_1) == 0x000058, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot::CallFunc_PlayAnimationForward_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot, CallFunc_PlayAnimationReverse_ReturnValue_1) == 0x000060, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot::CallFunc_PlayAnimationReverse_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot, K2Node_MakeStruct_SlateColor) == 0x000068, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot::K2Node_MakeStruct_SlateColor' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot, K2Node_MakeStruct_SlateColor_1) == 0x000090, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot::K2Node_MakeStruct_SlateColor_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot, CallFunc_UpdateTimer_TimeUp) == 0x0000B8, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot::CallFunc_UpdateTimer_TimeUp' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot, CallFunc_PlayAnimation_ReturnValue) == 0x0000C0, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");

// Function WDG_MPQuickjoinSlotCPInviteSlot.WDG_MPQuickjoinSlotCPInviteSlot_C.Tick
// 0x003C (0x003C - 0x0000)
struct WDG_MPQuickjoinSlotCPInviteSlot_C_Tick final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	float                                         InDeltaTime;                                       // 0x0038(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_MPQuickjoinSlotCPInviteSlot_C_Tick) == 0x000004, "Wrong alignment on WDG_MPQuickjoinSlotCPInviteSlot_C_Tick");
static_assert(sizeof(WDG_MPQuickjoinSlotCPInviteSlot_C_Tick) == 0x00003C, "Wrong size on WDG_MPQuickjoinSlotCPInviteSlot_C_Tick");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_Tick, MyGeometry) == 0x000000, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_Tick::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_Tick, InDeltaTime) == 0x000038, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_Tick::InDeltaTime' has a wrong offset!");

// Function WDG_MPQuickjoinSlotCPInviteSlot.WDG_MPQuickjoinSlotCPInviteSlot_C.SetInviteState
// 0x0200 (0x0200 - 0x0000)
struct WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState final
{
public:
	struct FOnlineServicesCPInvitationState       inviteState;                                       // 0x0000(0x0098)(BlueprintVisible, BlueprintReadOnly, Parm)
	class FText                                   waitingIndicatorText;                              // 0x0098(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm)
	struct FLinearColor                           ErrorForegroundColor;                              // 0x00B0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           ErrorBackgroundColor;                              // 0x00C0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_TextIsEmpty_ReturnValue;                  // 0x00D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x00D1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_D2[0x6];                                       // 0x00D2(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_IntToText_ReturnValue;               // 0x00D8(0x0018)()
	class FText                                   CallFunc_FormatBanMessage_text;                    // 0x00F0(0x0018)()
	class FText                                   CallFunc_FormatBanMessage_text_1;                  // 0x0108(0x0018)()
	int32                                         CallFunc_Conv_ByteToInt_ReturnValue;               // 0x0120(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_124[0x4];                                      // 0x0124(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_1;             // 0x0128(0x0018)()
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_2;             // 0x0140(0x0018)()
	bool                                          CallFunc_GreaterEqual_IntInt_ReturnValue;          // 0x0158(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0159(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_15A[0x6];                                      // 0x015A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_3;             // 0x0160(0x0018)()
	class FText                                   CallFunc_AppendToFText_ReturnValue;                // 0x0178(0x0018)()
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_4;             // 0x0190(0x0018)()
	class FText                                   CallFunc_AppendToFText_ReturnValue_1;              // 0x01A8(0x0018)()
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x01C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1C1[0x7];                                      // 0x01C1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_5;             // 0x01C8(0x0018)()
	class FText                                   CallFunc_AppendToFText_ReturnValue_2;              // 0x01E0(0x0018)()
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x01F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState) == 0x000008, "Wrong alignment on WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState");
static_assert(sizeof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState) == 0x000200, "Wrong size on WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState, inviteState) == 0x000000, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState::inviteState' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState, waitingIndicatorText) == 0x000098, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState::waitingIndicatorText' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState, ErrorForegroundColor) == 0x0000B0, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState::ErrorForegroundColor' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState, ErrorBackgroundColor) == 0x0000C0, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState::ErrorBackgroundColor' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState, CallFunc_TextIsEmpty_ReturnValue) == 0x0000D0, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState::CallFunc_TextIsEmpty_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState, CallFunc_Not_PreBool_ReturnValue) == 0x0000D1, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState, CallFunc_Conv_IntToText_ReturnValue) == 0x0000D8, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState::CallFunc_Conv_IntToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState, CallFunc_FormatBanMessage_text) == 0x0000F0, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState::CallFunc_FormatBanMessage_text' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState, CallFunc_FormatBanMessage_text_1) == 0x000108, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState::CallFunc_FormatBanMessage_text_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState, CallFunc_Conv_ByteToInt_ReturnValue) == 0x000120, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState::CallFunc_Conv_ByteToInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState, CallFunc_Conv_IntToText_ReturnValue_1) == 0x000128, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState::CallFunc_Conv_IntToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState, CallFunc_Conv_IntToText_ReturnValue_2) == 0x000140, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState::CallFunc_Conv_IntToText_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState, CallFunc_GreaterEqual_IntInt_ReturnValue) == 0x000158, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState::CallFunc_GreaterEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState, K2Node_SwitchEnum_CmpSuccess) == 0x000159, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState, CallFunc_Conv_IntToText_ReturnValue_3) == 0x000160, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState::CallFunc_Conv_IntToText_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState, CallFunc_AppendToFText_ReturnValue) == 0x000178, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState::CallFunc_AppendToFText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState, CallFunc_Conv_IntToText_ReturnValue_4) == 0x000190, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState::CallFunc_Conv_IntToText_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState, CallFunc_AppendToFText_ReturnValue_1) == 0x0001A8, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState::CallFunc_AppendToFText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState, CallFunc_MakeLiteralByte_ReturnValue) == 0x0001C0, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState, CallFunc_Conv_IntToText_ReturnValue_5) == 0x0001C8, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState::CallFunc_Conv_IntToText_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState, CallFunc_AppendToFText_ReturnValue_2) == 0x0001E0, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState::CallFunc_AppendToFText_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x0001F8, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");

// Function WDG_MPQuickjoinSlotCPInviteSlot.WDG_MPQuickjoinSlotCPInviteSlot_C.GetMinutesRemainingText
// 0x00A0 (0x00A0 - 0x0000)
struct WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText final
{
public:
	struct FDateTime                              SessionEndUtc;                                     // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class FText                                   SessionEndText;                                    // 0x0008(0x0018)(Parm, OutParm)
	struct FDateTime                              CallFunc_DateTimeMinValue_ReturnValue;             // 0x0020(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FDateTime                              CallFunc_UtcNow_ReturnValue;                       // 0x0028(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FTimespan                              CallFunc_Subtract_DateTimeDateTime_ReturnValue;    // 0x0030(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_DateTimeDateTime_ReturnValue;  // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_39[0x3];                                       // 0x0039(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_BreakTimespan_Days;                       // 0x003C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakTimespan_Hours;                      // 0x0040(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakTimespan_Minutes;                    // 0x0044(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakTimespan_Seconds;                    // 0x0048(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakTimespan_Milliseconds;               // 0x004C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_IntToString_ReturnValue;             // 0x0050(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DateTimeDateTime_ReturnValue;     // 0x0060(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_61[0x7];                                       // 0x0061(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Concat_StrStr_ReturnValue;                // 0x0068(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_1;              // 0x0078(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x0088(0x0018)()
};
static_assert(alignof(WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText) == 0x000008, "Wrong alignment on WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText");
static_assert(sizeof(WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText) == 0x0000A0, "Wrong size on WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText, SessionEndUtc) == 0x000000, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText::SessionEndUtc' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText, SessionEndText) == 0x000008, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText::SessionEndText' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText, CallFunc_DateTimeMinValue_ReturnValue) == 0x000020, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText::CallFunc_DateTimeMinValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText, CallFunc_UtcNow_ReturnValue) == 0x000028, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText::CallFunc_UtcNow_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText, CallFunc_Subtract_DateTimeDateTime_ReturnValue) == 0x000030, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText::CallFunc_Subtract_DateTimeDateTime_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText, CallFunc_EqualEqual_DateTimeDateTime_ReturnValue) == 0x000038, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText::CallFunc_EqualEqual_DateTimeDateTime_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText, CallFunc_BreakTimespan_Days) == 0x00003C, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText::CallFunc_BreakTimespan_Days' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText, CallFunc_BreakTimespan_Hours) == 0x000040, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText::CallFunc_BreakTimespan_Hours' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText, CallFunc_BreakTimespan_Minutes) == 0x000044, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText::CallFunc_BreakTimespan_Minutes' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText, CallFunc_BreakTimespan_Seconds) == 0x000048, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText::CallFunc_BreakTimespan_Seconds' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText, CallFunc_BreakTimespan_Milliseconds) == 0x00004C, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText::CallFunc_BreakTimespan_Milliseconds' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText, CallFunc_Conv_IntToString_ReturnValue) == 0x000050, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText::CallFunc_Conv_IntToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText, CallFunc_Greater_DateTimeDateTime_ReturnValue) == 0x000060, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText::CallFunc_Greater_DateTimeDateTime_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText, CallFunc_Concat_StrStr_ReturnValue) == 0x000068, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText::CallFunc_Concat_StrStr_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText, CallFunc_Concat_StrStr_ReturnValue_1) == 0x000078, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText::CallFunc_Concat_StrStr_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText, CallFunc_Conv_StringToText_ReturnValue) == 0x000088, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");

// Function WDG_MPQuickjoinSlotCPInviteSlot.WDG_MPQuickjoinSlotCPInviteSlot_C.UpdateTimer
// 0x0040 (0x0040 - 0x0000)
struct WDG_MPQuickjoinSlotCPInviteSlot_C_UpdateTimer final
{
public:
	bool                                          TimeUp;                                            // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FDateTime                              CallFunc_UtcNow_ReturnValue;                       // 0x0008(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FDateTime                              CallFunc_UtcNow_ReturnValue_1;                     // 0x0010(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DateTimeDateTime_ReturnValue;     // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTimespan                              CallFunc_Subtract_DateTimeDateTime_ReturnValue;    // 0x0020(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class FText                                   CallFunc_AsTimespan_Timespan_ReturnValue;          // 0x0028(0x0018)()
};
static_assert(alignof(WDG_MPQuickjoinSlotCPInviteSlot_C_UpdateTimer) == 0x000008, "Wrong alignment on WDG_MPQuickjoinSlotCPInviteSlot_C_UpdateTimer");
static_assert(sizeof(WDG_MPQuickjoinSlotCPInviteSlot_C_UpdateTimer) == 0x000040, "Wrong size on WDG_MPQuickjoinSlotCPInviteSlot_C_UpdateTimer");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_UpdateTimer, TimeUp) == 0x000000, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_UpdateTimer::TimeUp' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_UpdateTimer, CallFunc_UtcNow_ReturnValue) == 0x000008, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_UpdateTimer::CallFunc_UtcNow_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_UpdateTimer, CallFunc_UtcNow_ReturnValue_1) == 0x000010, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_UpdateTimer::CallFunc_UtcNow_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_UpdateTimer, CallFunc_Greater_DateTimeDateTime_ReturnValue) == 0x000018, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_UpdateTimer::CallFunc_Greater_DateTimeDateTime_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_UpdateTimer, CallFunc_Subtract_DateTimeDateTime_ReturnValue) == 0x000020, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_UpdateTimer::CallFunc_Subtract_DateTimeDateTime_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_UpdateTimer, CallFunc_AsTimespan_Timespan_ReturnValue) == 0x000028, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_UpdateTimer::CallFunc_AsTimespan_Timespan_ReturnValue' has a wrong offset!");

// Function WDG_MPQuickjoinSlotCPInviteSlot.WDG_MPQuickjoinSlotCPInviteSlot_C.SetCountdownOrErrorMessage
// 0x0098 (0x0098 - 0x0000)
struct WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage final
{
public:
	bool                                          isError;                                           // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   text;                                              // 0x0008(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm)
	struct FLinearColor                           BackgroundColor;                                   // 0x0020(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           ForegroundColor_0;                                 // 0x0030(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          UsePulseAnimation;                                 // 0x0040(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0041(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_42[0x6];                                       // 0x0042(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateColor                            K2Node_MakeStruct_SlateColor;                      // 0x0048(0x0028)(UObjectWrapper)
	class FText                                   CallFunc_TextToUpper_ReturnValue;                  // 0x0070(0x0018)()
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0088(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsAnimationPlaying_ReturnValue;           // 0x0089(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_8A[0x6];                                       // 0x008A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0090(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage) == 0x000008, "Wrong alignment on WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage");
static_assert(sizeof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage) == 0x000098, "Wrong size on WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage, isError) == 0x000000, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage::isError' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage, text) == 0x000008, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage::text' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage, BackgroundColor) == 0x000020, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage::BackgroundColor' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage, ForegroundColor_0) == 0x000030, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage::ForegroundColor_0' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage, UsePulseAnimation) == 0x000040, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage::UsePulseAnimation' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage, CallFunc_MakeLiteralByte_ReturnValue) == 0x000041, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage, K2Node_MakeStruct_SlateColor) == 0x000048, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage::K2Node_MakeStruct_SlateColor' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage, CallFunc_TextToUpper_ReturnValue) == 0x000070, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage::CallFunc_TextToUpper_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000088, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage, CallFunc_IsAnimationPlaying_ReturnValue) == 0x000089, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage::CallFunc_IsAnimationPlaying_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage, CallFunc_PlayAnimation_ReturnValue) == 0x000090, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");

// Function WDG_MPQuickjoinSlotCPInviteSlot.WDG_MPQuickjoinSlotCPInviteSlot_C.FormatBanMessage
// 0x0088 (0x0088 - 0x0000)
struct WDG_MPQuickjoinSlotCPInviteSlot_C_FormatBanMessage final
{
public:
	int32                                         Days;                                              // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   text;                                              // 0x0008(0x0018)(Parm, OutParm)
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData;              // 0x0020(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	TArray<struct FFormatArgumentData>            K2Node_MakeArray_Array;                            // 0x0060(0x0010)(ReferenceParm)
	class FText                                   CallFunc_Format_ReturnValue;                       // 0x0070(0x0018)()
};
static_assert(alignof(WDG_MPQuickjoinSlotCPInviteSlot_C_FormatBanMessage) == 0x000008, "Wrong alignment on WDG_MPQuickjoinSlotCPInviteSlot_C_FormatBanMessage");
static_assert(sizeof(WDG_MPQuickjoinSlotCPInviteSlot_C_FormatBanMessage) == 0x000088, "Wrong size on WDG_MPQuickjoinSlotCPInviteSlot_C_FormatBanMessage");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_FormatBanMessage, Days) == 0x000000, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_FormatBanMessage::Days' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_FormatBanMessage, text) == 0x000008, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_FormatBanMessage::text' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_FormatBanMessage, K2Node_MakeStruct_FormatArgumentData) == 0x000020, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_FormatBanMessage::K2Node_MakeStruct_FormatArgumentData' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_FormatBanMessage, K2Node_MakeArray_Array) == 0x000060, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_FormatBanMessage::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlotCPInviteSlot_C_FormatBanMessage, CallFunc_Format_ReturnValue) == 0x000070, "Member 'WDG_MPQuickjoinSlotCPInviteSlot_C_FormatBanMessage::CallFunc_Format_ReturnValue' has a wrong offset!");

}

