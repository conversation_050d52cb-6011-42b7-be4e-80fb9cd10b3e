﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ReplayItem

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "SlateCore_structs.hpp"
#include "AC2_structs.hpp"
#include "CoreUObject_structs.hpp"


namespace SDK::Params
{

// Function WDG_ReplayItem.WDG_ReplayItem_C.ExecuteUbergraph_WDG_ReplayItem
// 0x06B8 (0x06B8 - 0x0000)
struct WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0004(0x0001)(ZeroConstructor, IsPlainOldD<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_6[0x2];                                        // 0x0006(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x0008(0x0018)()
	bool                                          CallFunc_TextIsEmpty_ReturnValue;                  // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0021(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_1;        // 0x0022(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_23[0x5];                                       // 0x0023(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_AsDateTime_DateTime_ReturnValue;          // 0x0028(0x0018)()
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_GreaterEqual_IntInt_ReturnValue;          // 0x0041(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_Conv_IntToByte_ReturnValue;               // 0x0042(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_43[0x5];                                       // 0x0043(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTimespan                              CallFunc_FromSeconds_ReturnValue;                  // 0x0048(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_GetValidValue_ReturnValue;                // 0x0050(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_51[0x3];                                       // 0x0051(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_BreakTimespan_Days;                       // 0x0054(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakTimespan_Hours;                      // 0x0058(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakTimespan_Minutes;                    // 0x005C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakTimespan_Seconds;                    // 0x0060(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakTimespan_Milliseconds;               // 0x0064(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_IntToText_ReturnValue;               // 0x0068(0x0018)()
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_1;             // 0x0080(0x0018)()
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData;              // 0x0098(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData_1;            // 0x00D8(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_2;             // 0x0118(0x0018)()
	bool                                          K2Node_SwitchEnum_CmpSuccess_1;                    // 0x0130(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_131[0x7];                                      // 0x0131(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData_2;            // 0x0138(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	TArray<struct FFormatArgumentData>            K2Node_MakeArray_Array;                            // 0x0178(0x0010)(ReferenceParm)
	class FText                                   CallFunc_Format_ReturnValue;                       // 0x0188(0x0018)()
	bool                                          Temp_bool_Variable;                                // 0x01A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x01A1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1A2[0x6];                                      // 0x01A2(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPanelBase*                           K2Node_DynamicCast_AsAc_Panel_Base;                // 0x01A8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x01B0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_1B1[0x7];                                      // 0x01B1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue;              // 0x01B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance;             // 0x01C0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x01C8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_1C9[0x3];                                      // 0x01C9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_GetChildrenCount_ReturnValue;             // 0x01CC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   CallFunc_GetCommonSessionTypeText_ReturnValue;     // 0x01D0(0x0018)()
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x01E8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_HasFocusedDescendants_ReturnValue;        // 0x01EC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1ED[0x3];                                      // 0x01ED(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FFocusEvent                            K2Node_Event_InFocusEvent_1;                       // 0x01F0(0x0008)(NoDestructor)
	struct FFocusEvent                            K2Node_Event_InFocusEvent;                         // 0x01F8(0x0008)(NoDestructor)
	bool                                          K2Node_Event_highlighted;                          // 0x0200(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0201(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_2;            // 0x0202(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_203[0x5];                                      // 0x0203(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	class AGameModeBase*                          CallFunc_GetGameMode_ReturnValue;                  // 0x0208(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AAcMenuGameMode*                        K2Node_DynamicCast_AsAc_Menu_Game_Mode;            // 0x0210(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_2;                     // 0x0218(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_219[0x7];                                      // 0x0219(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FModelInfo                             CallFunc_GetModelInfoByType_ReturnValue;           // 0x0220(0x01A8)()
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_3;            // 0x03C8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3C9[0x7];                                      // 0x03C9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_NameToText_ReturnValue;              // 0x03D0(0x0018)()
	struct FSlateColor                            K2Node_MakeStruct_SlateColor;                      // 0x03E8(0x0028)()
	struct FCircuitInfo                           CallFunc_GetCircuitInfoById_ReturnValue;           // 0x0410(0x01F0)()
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_1;          // 0x0600(0x0018)()
	int32                                         Temp_int_Variable;                                 // 0x0618(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_61C[0x4];                                      // 0x061C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_1;                    // 0x0620(0x0028)(UObjectWrapper)
	class UWidget*                                CallFunc_GetChildAt_ReturnValue;                   // 0x0648(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GetIsEnabled_ReturnValue;                 // 0x0650(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsVisible_ReturnValue;                    // 0x0651(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_652[0x2];                                      // 0x0652(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0654(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x0658(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x0659(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_65A[0x6];                                      // 0x065A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_2;                    // 0x0660(0x0028)()
	bool                                          CallFunc_BooleanAND_ReturnValue_2;                 // 0x0688(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_689[0x7];                                      // 0x0689(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_3;                    // 0x0690(0x0028)()
};
static_assert(alignof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem) == 0x000008, "Wrong alignment on WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem");
static_assert(sizeof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem) == 0x0006B8, "Wrong size on WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, EntryPoint) == 0x000000, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_MakeLiteralByte_ReturnValue) == 0x000004, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000005, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_Conv_StringToText_ReturnValue) == 0x000008, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_TextIsEmpty_ReturnValue) == 0x000020, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_TextIsEmpty_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, K2Node_SwitchEnum_CmpSuccess) == 0x000021, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_EqualEqual_ByteByte_ReturnValue_1) == 0x000022, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_EqualEqual_ByteByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_AsDateTime_DateTime_ReturnValue) == 0x000028, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_AsDateTime_DateTime_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_BooleanAND_ReturnValue) == 0x000040, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_GreaterEqual_IntInt_ReturnValue) == 0x000041, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_GreaterEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_Conv_IntToByte_ReturnValue) == 0x000042, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_Conv_IntToByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_FromSeconds_ReturnValue) == 0x000048, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_FromSeconds_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_GetValidValue_ReturnValue) == 0x000050, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_GetValidValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_BreakTimespan_Days) == 0x000054, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_BreakTimespan_Days' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_BreakTimespan_Hours) == 0x000058, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_BreakTimespan_Hours' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_BreakTimespan_Minutes) == 0x00005C, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_BreakTimespan_Minutes' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_BreakTimespan_Seconds) == 0x000060, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_BreakTimespan_Seconds' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_BreakTimespan_Milliseconds) == 0x000064, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_BreakTimespan_Milliseconds' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_Conv_IntToText_ReturnValue) == 0x000068, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_Conv_IntToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_Conv_IntToText_ReturnValue_1) == 0x000080, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_Conv_IntToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, K2Node_MakeStruct_FormatArgumentData) == 0x000098, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::K2Node_MakeStruct_FormatArgumentData' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, K2Node_MakeStruct_FormatArgumentData_1) == 0x0000D8, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::K2Node_MakeStruct_FormatArgumentData_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_Conv_IntToText_ReturnValue_2) == 0x000118, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_Conv_IntToText_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, K2Node_SwitchEnum_CmpSuccess_1) == 0x000130, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::K2Node_SwitchEnum_CmpSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, K2Node_MakeStruct_FormatArgumentData_2) == 0x000138, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::K2Node_MakeStruct_FormatArgumentData_2' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, K2Node_MakeArray_Array) == 0x000178, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_Format_ReturnValue) == 0x000188, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_Format_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, Temp_bool_Variable) == 0x0001A0, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_Not_PreBool_ReturnValue) == 0x0001A1, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, K2Node_DynamicCast_AsAc_Panel_Base) == 0x0001A8, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::K2Node_DynamicCast_AsAc_Panel_Base' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, K2Node_DynamicCast_bSuccess) == 0x0001B0, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_GetGameInstance_ReturnValue) == 0x0001B8, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_GetGameInstance_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, K2Node_DynamicCast_AsAc_Game_Instance) == 0x0001C0, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::K2Node_DynamicCast_AsAc_Game_Instance' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, K2Node_DynamicCast_bSuccess_1) == 0x0001C8, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_GetChildrenCount_ReturnValue) == 0x0001CC, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_GetChildrenCount_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_GetCommonSessionTypeText_ReturnValue) == 0x0001D0, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_GetCommonSessionTypeText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_Subtract_IntInt_ReturnValue) == 0x0001E8, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_HasFocusedDescendants_ReturnValue) == 0x0001EC, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_HasFocusedDescendants_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, K2Node_Event_InFocusEvent_1) == 0x0001F0, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::K2Node_Event_InFocusEvent_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, K2Node_Event_InFocusEvent) == 0x0001F8, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::K2Node_Event_InFocusEvent' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, K2Node_Event_highlighted) == 0x000200, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::K2Node_Event_highlighted' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000201, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_MakeLiteralByte_ReturnValue_2) == 0x000202, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_MakeLiteralByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_GetGameMode_ReturnValue) == 0x000208, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_GetGameMode_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, K2Node_DynamicCast_AsAc_Menu_Game_Mode) == 0x000210, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::K2Node_DynamicCast_AsAc_Menu_Game_Mode' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, K2Node_DynamicCast_bSuccess_2) == 0x000218, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::K2Node_DynamicCast_bSuccess_2' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_GetModelInfoByType_ReturnValue) == 0x000220, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_GetModelInfoByType_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_MakeLiteralByte_ReturnValue_3) == 0x0003C8, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_MakeLiteralByte_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_Conv_NameToText_ReturnValue) == 0x0003D0, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_Conv_NameToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, K2Node_MakeStruct_SlateColor) == 0x0003E8, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::K2Node_MakeStruct_SlateColor' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_GetCircuitInfoById_ReturnValue) == 0x000410, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_GetCircuitInfoById_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_Conv_StringToText_ReturnValue_1) == 0x000600, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_Conv_StringToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, Temp_int_Variable) == 0x000618, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, K2Node_MakeStruct_SlateColor_1) == 0x000620, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::K2Node_MakeStruct_SlateColor_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_GetChildAt_ReturnValue) == 0x000648, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_GetChildAt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_GetIsEnabled_ReturnValue) == 0x000650, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_GetIsEnabled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_IsVisible_ReturnValue) == 0x000651, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_IsVisible_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_Add_IntInt_ReturnValue) == 0x000654, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_BooleanAND_ReturnValue_1) == 0x000658, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_LessEqual_IntInt_ReturnValue) == 0x000659, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, K2Node_MakeStruct_SlateColor_2) == 0x000660, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::K2Node_MakeStruct_SlateColor_2' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, CallFunc_BooleanAND_ReturnValue_2) == 0x000688, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::CallFunc_BooleanAND_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem, K2Node_MakeStruct_SlateColor_3) == 0x000690, "Member 'WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem::K2Node_MakeStruct_SlateColor_3' has a wrong offset!");

// Function WDG_ReplayItem.WDG_ReplayItem_C.BP_SetHighlight
// 0x0001 (0x0001 - 0x0000)
struct WDG_ReplayItem_C_BP_SetHighlight final
{
public:
	bool                                          highlighted;                                       // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ReplayItem_C_BP_SetHighlight) == 0x000001, "Wrong alignment on WDG_ReplayItem_C_BP_SetHighlight");
static_assert(sizeof(WDG_ReplayItem_C_BP_SetHighlight) == 0x000001, "Wrong size on WDG_ReplayItem_C_BP_SetHighlight");
static_assert(offsetof(WDG_ReplayItem_C_BP_SetHighlight, highlighted) == 0x000000, "Member 'WDG_ReplayItem_C_BP_SetHighlight::highlighted' has a wrong offset!");

// Function WDG_ReplayItem.WDG_ReplayItem_C.OnAddedToFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_ReplayItem_C_OnAddedToFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_ReplayItem_C_OnAddedToFocusPath) == 0x000004, "Wrong alignment on WDG_ReplayItem_C_OnAddedToFocusPath");
static_assert(sizeof(WDG_ReplayItem_C_OnAddedToFocusPath) == 0x000008, "Wrong size on WDG_ReplayItem_C_OnAddedToFocusPath");
static_assert(offsetof(WDG_ReplayItem_C_OnAddedToFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_ReplayItem_C_OnAddedToFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_ReplayItem.WDG_ReplayItem_C.OnRemovedFromFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_ReplayItem_C_OnRemovedFromFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_ReplayItem_C_OnRemovedFromFocusPath) == 0x000004, "Wrong alignment on WDG_ReplayItem_C_OnRemovedFromFocusPath");
static_assert(sizeof(WDG_ReplayItem_C_OnRemovedFromFocusPath) == 0x000008, "Wrong size on WDG_ReplayItem_C_OnRemovedFromFocusPath");
static_assert(offsetof(WDG_ReplayItem_C_OnRemovedFromFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_ReplayItem_C_OnRemovedFromFocusPath::InFocusEvent' has a wrong offset!");

}

