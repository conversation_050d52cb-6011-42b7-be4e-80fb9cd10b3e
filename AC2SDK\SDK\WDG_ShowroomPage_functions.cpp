﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomPage

#include "Basic.hpp"

#include "WDG_ShowroomPage_classes.hpp"
#include "WDG_ShowroomPage_parameters.hpp"


namespace SDK
{

// Function WDG_ShowroomPage.WDG_ShowroomPage_C.ExecuteUbergraph_WDG_ShowroomPage
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::ExecuteUbergraph_WDG_ShowroomPage(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "ExecuteUbergraph_WDG_ShowroomPage");

	Params::WDG_ShowroomPage_C_ExecuteUbergraph_WDG_ShowroomPage Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__WDG_ShowroomPage_SeasonTypeFilter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature
// (BlueprintEvent)
// Parameters:
// ESeasonType                             activeFilter_0                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::BndEvt__WDG_ShowroomPage_SeasonTypeFilter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature(ESeasonType activeFilter_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__WDG_ShowroomPage_SeasonTypeFilter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__WDG_ShowroomPage_SeasonTypeFilter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature Parms{};

	Parms.activeFilter_0 = activeFilter_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__WDG_ShowroomPage_CarGroupFilter_K2Node_ComponentBoundEvent_9_OnFilterSelected__DelegateSignature
// (BlueprintEvent)
// Parameters:
// ECarGroup                               activeFilter_0                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::BndEvt__WDG_ShowroomPage_CarGroupFilter_K2Node_ComponentBoundEvent_9_OnFilterSelected__DelegateSignature(ECarGroup activeFilter_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__WDG_ShowroomPage_CarGroupFilter_K2Node_ComponentBoundEvent_9_OnFilterSelected__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__WDG_ShowroomPage_CarGroupFilter_K2Node_ComponentBoundEvent_9_OnFilterSelected__DelegateSignature Parms{};

	Parms.activeFilter_0 = activeFilter_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_7_OnItemPrevious__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class FName                             Key                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWDG_ShowroomTileItemHorizontal_C*Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_7_OnItemPrevious__DelegateSignature(class FName Key, class UWDG_ShowroomTileItemHorizontal_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_7_OnItemPrevious__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_7_OnItemPrevious__DelegateSignature Parms{};

	Parms.Key = Key;
	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_5_OnItemNext__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class FName                             Key                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWDG_ShowroomTileItemHorizontal_C*Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_5_OnItemNext__DelegateSignature(class FName Key, class UWDG_ShowroomTileItemHorizontal_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_5_OnItemNext__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_5_OnItemNext__DelegateSignature Parms{};

	Parms.Key = Key;
	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__CurrentModel_K2Node_ComponentBoundEvent_3_OnItemPrevious__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class FName                             Key                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWDG_ShowroomTileItemHorizontal_C*Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::BndEvt__CurrentModel_K2Node_ComponentBoundEvent_3_OnItemPrevious__DelegateSignature(class FName Key, class UWDG_ShowroomTileItemHorizontal_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__CurrentModel_K2Node_ComponentBoundEvent_3_OnItemPrevious__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__CurrentModel_K2Node_ComponentBoundEvent_3_OnItemPrevious__DelegateSignature Parms{};

	Parms.Key = Key;
	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__CurrentModel_K2Node_ComponentBoundEvent_1_OnItemNext__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class FName                             Key                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWDG_ShowroomTileItemHorizontal_C*Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::BndEvt__CurrentModel_K2Node_ComponentBoundEvent_1_OnItemNext__DelegateSignature(class FName Key, class UWDG_ShowroomTileItemHorizontal_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__CurrentModel_K2Node_ComponentBoundEvent_1_OnItemNext__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__CurrentModel_K2Node_ComponentBoundEvent_1_OnItemNext__DelegateSignature Parms{};

	Parms.Key = Key;
	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnEntryListSeason
// (Event, Protected, BlueprintEvent)
// Parameters:
// ESeasonType                             Season                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::OnEntryListSeason(ESeasonType Season)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnEntryListSeason");

	Params::WDG_ShowroomPage_C_OnEntryListSeason Parms{};

	Parms.Season = Season;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__btnSelect_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_ShowroomPage_C::BndEvt__btnSelect_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__btnSelect_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__Back_K2Node_ComponentBoundEvent_0_OnClicked__DelegateSignature
// (BlueprintEvent)

void UWDG_ShowroomPage_C::BndEvt__Back_K2Node_ComponentBoundEvent_0_OnClicked__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__Back_K2Node_ComponentBoundEvent_0_OnClicked__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnFilterApplied
// (Event, Protected, BlueprintEvent)
// Parameters:
// EShowroomCarFilterType                  Filter_0                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::OnFilterApplied(EShowroomCarFilterType Filter_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnFilterApplied");

	Params::WDG_ShowroomPage_C_OnFilterApplied Parms{};

	Parms.Filter_0 = Filter_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__Filter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature
// (BlueprintEvent)
// Parameters:
// EShowroomCarFilterType                  activeFilter_0                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::BndEvt__Filter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature(EShowroomCarFilterType activeFilter_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__Filter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__Filter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature Parms{};

	Parms.activeFilter_0 = activeFilter_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnPageReady
// (Event, Protected, BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomPage_C::OnPageReady()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnPageReady");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BP_OnBackward
// (Event, Public, BlueprintEvent)

void UWDG_ShowroomPage_C::BP_OnBackward()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BP_OnBackward");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnCancel__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UAcPanelBase*                     CallingPanel                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnCancel__DelegateSignature(class UAcPanelBase* CallingPanel)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnCancel__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnCancel__DelegateSignature Parms{};

	Parms.CallingPanel = CallingPanel;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__modalOverlay_K2Node_ComponentBoundEvent_1_OnShow__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UAcPanelBase*                     CallingPanel                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::BndEvt__modalOverlay_K2Node_ComponentBoundEvent_1_OnShow__DelegateSignature(class UAcPanelBase* CallingPanel)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__modalOverlay_K2Node_ComponentBoundEvent_1_OnShow__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__modalOverlay_K2Node_ComponentBoundEvent_1_OnShow__DelegateSignature Parms{};

	Parms.CallingPanel = CallingPanel;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__modalOverlay_K2Node_ComponentBoundEvent_0_OnHide__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UAcPanelBase*                     CallingPanel                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    Cancelled                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ShowroomPage_C::BndEvt__modalOverlay_K2Node_ComponentBoundEvent_0_OnHide__DelegateSignature(class UAcPanelBase* CallingPanel, bool Cancelled)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__modalOverlay_K2Node_ComponentBoundEvent_0_OnHide__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__modalOverlay_K2Node_ComponentBoundEvent_0_OnHide__DelegateSignature Parms{};

	Parms.CallingPanel = CallingPanel;
	Parms.Cancelled = Cancelled;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__WDG_ModelSelectorItemHorizontal_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UWDG_ShowroomTileBase_C*          Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class FName                             Key                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   KeyInt                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::BndEvt__WDG_ModelSelectorItemHorizontal_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature(class UWDG_ShowroomTileBase_C* Sender, class FName Key, int32 KeyInt)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__WDG_ModelSelectorItemHorizontal_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__WDG_ModelSelectorItemHorizontal_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature Parms{};

	Parms.Sender = Sender;
	Parms.Key = Key;
	Parms.KeyInt = KeyInt;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.Update Model
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FModelInfo&                ModelInfo                                              (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_ShowroomPage_C::Update_Model(const struct FModelInfo& ModelInfo)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "Update Model");

	Params::WDG_ShowroomPage_C_Update_Model Parms{};

	Parms.ModelInfo = std::move(ModelInfo);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__SelectorModel_K2Node_ComponentBoundEvent_1_OnModelSelected__DelegateSignature
// (BlueprintEvent)
// Parameters:
// const struct FModelInfo&                ModelInfo                                              (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_ShowroomPage_C::BndEvt__SelectorModel_K2Node_ComponentBoundEvent_1_OnModelSelected__DelegateSignature(const struct FModelInfo& ModelInfo)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__SelectorModel_K2Node_ComponentBoundEvent_1_OnModelSelected__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__SelectorModel_K2Node_ComponentBoundEvent_1_OnModelSelected__DelegateSignature Parms{};

	Parms.ModelInfo = std::move(ModelInfo);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnModelUpdate
// (Event, Protected, BlueprintEvent)
// Parameters:
// const struct FModelInfo&                ModelInfo                                              (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_ShowroomPage_C::OnModelUpdate(const struct FModelInfo& ModelInfo)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnModelUpdate");

	Params::WDG_ShowroomPage_C_OnModelUpdate Parms{};

	Parms.ModelInfo = std::move(ModelInfo);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnPreviousCarEntry
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class FName                             Key                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWDG_ShowroomTileItemHorizontal_C*Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::OnPreviousCarEntry(class FName Key, class UWDG_ShowroomTileItemHorizontal_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnPreviousCarEntry");

	Params::WDG_ShowroomPage_C_OnPreviousCarEntry Parms{};

	Parms.Key = Key;
	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnNextCarEntry
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class FName                             Key                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWDG_ShowroomTileItemHorizontal_C*Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::OnNextCarEntry(class FName Key, class UWDG_ShowroomTileItemHorizontal_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnNextCarEntry");

	Params::WDG_ShowroomPage_C_OnNextCarEntry Parms{};

	Parms.Key = Key;
	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__SelectorTeam_K2Node_ComponentBoundEvent_2_OnTeamSelected__DelegateSignature
// (BlueprintEvent)
// Parameters:
// const struct FTeamInfo&                 TeamInfo                                               (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_ShowroomPage_C::BndEvt__SelectorTeam_K2Node_ComponentBoundEvent_2_OnTeamSelected__DelegateSignature(const struct FTeamInfo& TeamInfo)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__SelectorTeam_K2Node_ComponentBoundEvent_2_OnTeamSelected__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__SelectorTeam_K2Node_ComponentBoundEvent_2_OnTeamSelected__DelegateSignature Parms{};

	Parms.TeamInfo = std::move(TeamInfo);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UWDG_ShowroomTileBase_C*          Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class FName                             Key                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   KeyInt                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature(class UWDG_ShowroomTileBase_C* Sender, class FName Key, int32 KeyInt)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature Parms{};

	Parms.Sender = Sender;
	Parms.Key = Key;
	Parms.KeyInt = KeyInt;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_0_OnItemFocused__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UWDG_ShowroomTileBase_C*          Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_0_OnItemFocused__DelegateSignature(class UWDG_ShowroomTileBase_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_0_OnItemFocused__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_0_OnItemFocused__DelegateSignature Parms{};

	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnCarItemSelected
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UWDG_ShowroomTileBase_C*          Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class FName                             Key                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   KeyInt                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::OnCarItemSelected(class UWDG_ShowroomTileBase_C* Sender, class FName Key, int32 KeyInt)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnCarItemSelected");

	Params::WDG_ShowroomPage_C_OnCarItemSelected Parms{};

	Parms.Sender = Sender;
	Parms.Key = Key;
	Parms.KeyInt = KeyInt;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.Update Team
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FTeamInfo&                 TeamInfo                                               (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_ShowroomPage_C::Update_Team(const struct FTeamInfo& TeamInfo)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "Update Team");

	Params::WDG_ShowroomPage_C_Update_Team Parms{};

	Parms.TeamInfo = std::move(TeamInfo);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnTeamUpdate
// (Event, Protected, BlueprintEvent)
// Parameters:
// const struct FTeamInfo&                 TeamInfo                                               (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_ShowroomPage_C::OnTeamUpdate(const struct FTeamInfo& TeamInfo)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnTeamUpdate");

	Params::WDG_ShowroomPage_C_OnTeamUpdate Parms{};

	Parms.TeamInfo = std::move(TeamInfo);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.PreviousVariant
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class FName                             Key                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWDG_ShowroomTileItemHorizontal_C*Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::PreviousVariant(class FName Key, class UWDG_ShowroomTileItemHorizontal_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "PreviousVariant");

	Params::WDG_ShowroomPage_C_PreviousVariant Parms{};

	Parms.Key = Key;
	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.NextVariant
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class FName                             Key                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWDG_ShowroomTileItemHorizontal_C*Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::NextVariant(class FName Key, class UWDG_ShowroomTileItemHorizontal_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "NextVariant");

	Params::WDG_ShowroomPage_C_NextVariant Parms{};

	Parms.Key = Key;
	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.VariantSelected
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UWDG_ShowroomTileItemHorizontal_C*Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   ColorCode                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FLinearColor&              Color                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::VariantSelected(class UWDG_ShowroomTileItemHorizontal_C* Sender, int32 ColorCode, const struct FLinearColor& Color)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "VariantSelected");

	Params::WDG_ShowroomPage_C_VariantSelected Parms{};

	Parms.Sender = Sender;
	Parms.ColorCode = ColorCode;
	Parms.Color = std::move(Color);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnVariantUpdate
// (Event, Protected, BlueprintEvent)
// Parameters:
// int32                                   variant_key                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::OnVariantUpdate(int32 variant_key)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnVariantUpdate");

	Params::WDG_ShowroomPage_C_OnVariantUpdate Parms{};

	Parms.variant_key = variant_key;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnPopulateVariants
// (BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomPage_C::OnPopulateVariants()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnPopulateVariants");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnDriverItemSelected
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UWDG_ShowroomTileBase_C*          Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class FName                             Key                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   KeyInt                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::OnDriverItemSelected(class UWDG_ShowroomTileBase_C* Sender, class FName Key, int32 KeyInt)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnDriverItemSelected");

	Params::WDG_ShowroomPage_C_OnDriverItemSelected Parms{};

	Parms.Sender = Sender;
	Parms.Key = Key;
	Parms.KeyInt = KeyInt;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnCarUpdate
// (Event, Protected, BlueprintEvent)
// Parameters:
// const struct FCarInfo&                  CarInfo                                                (BlueprintVisible, BlueprintReadOnly, Parm)
// class FName                             CarKey                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::OnCarUpdate(const struct FCarInfo& CarInfo, class FName CarKey)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnCarUpdate");

	Params::WDG_ShowroomPage_C_OnCarUpdate Parms{};

	Parms.CarInfo = std::move(CarInfo);
	Parms.CarKey = CarKey;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__CurrentDriver_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UWDG_ShowroomTileBase_C*          Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class FName                             Key                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   KeyInt                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::BndEvt__CurrentDriver_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature(class UWDG_ShowroomTileBase_C* Sender, class FName Key, int32 KeyInt)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__CurrentDriver_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__CurrentDriver_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature Parms{};

	Parms.Sender = Sender;
	Parms.Key = Key;
	Parms.KeyInt = KeyInt;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnDriverUpdate
// (Event, Protected, BlueprintEvent)
// Parameters:
// const struct FDriverInfo&               DriverInfo                                             (BlueprintVisible, BlueprintReadOnly, Parm)
// class FName                             DriverKey                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::OnDriverUpdate(const struct FDriverInfo& DriverInfo, class FName DriverKey)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnDriverUpdate");

	Params::WDG_ShowroomPage_C_OnDriverUpdate Parms{};

	Parms.DriverInfo = std::move(DriverInfo);
	Parms.DriverKey = DriverKey;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnHideCustomEditor
// (BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomPage_C::OnHideCustomEditor()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnHideCustomEditor");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnShowCustomEditor
// (BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomPage_C::OnShowCustomEditor()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnShowCustomEditor");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__popupChanges_K2Node_ComponentBoundEvent_6_OnNo__DelegateSignature
// (BlueprintEvent)

void UWDG_ShowroomPage_C::BndEvt__popupChanges_K2Node_ComponentBoundEvent_6_OnNo__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__popupChanges_K2Node_ComponentBoundEvent_6_OnNo__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__popupChanges_K2Node_ComponentBoundEvent_1_OnYes__DelegateSignature
// (BlueprintEvent)

void UWDG_ShowroomPage_C::BndEvt__popupChanges_K2Node_ComponentBoundEvent_1_OnYes__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__popupChanges_K2Node_ComponentBoundEvent_1_OnYes__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__inputTeamName_K2Node_ComponentBoundEvent_1_AcTextInputChanged__DelegateSignature
// (HasOutParams, BlueprintEvent)
// Parameters:
// const class FText&                      text                                                   (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_ShowroomPage_C::BndEvt__inputTeamName_K2Node_ComponentBoundEvent_1_AcTextInputChanged__DelegateSignature(const class FText& text)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__inputTeamName_K2Node_ComponentBoundEvent_1_AcTextInputChanged__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__inputTeamName_K2Node_ComponentBoundEvent_1_AcTextInputChanged__DelegateSignature Parms{};

	Parms.text = std::move(text);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__customCarOptions_K2Node_ComponentBoundEvent_8_OnValuesUpdated__DelegateSignature
// (BlueprintEvent)
// Parameters:
// const struct FCarInfo&                  CarInfo                                                (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_ShowroomPage_C::BndEvt__customCarOptions_K2Node_ComponentBoundEvent_8_OnValuesUpdated__DelegateSignature(const struct FCarInfo& CarInfo)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__customCarOptions_K2Node_ComponentBoundEvent_8_OnValuesUpdated__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__customCarOptions_K2Node_ComponentBoundEvent_8_OnValuesUpdated__DelegateSignature Parms{};

	Parms.CarInfo = std::move(CarInfo);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__inputRaceNumber_K2Node_ComponentBoundEvent_3_AcTextInputChanged__DelegateSignature
// (HasOutParams, BlueprintEvent)
// Parameters:
// const class FText&                      text                                                   (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_ShowroomPage_C::BndEvt__inputRaceNumber_K2Node_ComponentBoundEvent_3_AcTextInputChanged__DelegateSignature(const class FText& text)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__inputRaceNumber_K2Node_ComponentBoundEvent_3_AcTextInputChanged__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__inputRaceNumber_K2Node_ComponentBoundEvent_3_AcTextInputChanged__DelegateSignature Parms{};

	Parms.text = std::move(text);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__CustomCarModel_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UWDG_ShowroomTileBase_C*          Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class FName                             Key                                                    (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   KeyInt                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::BndEvt__CustomCarModel_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature(class UWDG_ShowroomTileBase_C* Sender, class FName Key, int32 KeyInt)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__CustomCarModel_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__CustomCarModel_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature Parms{};

	Parms.Sender = Sender;
	Parms.Key = Key;
	Parms.KeyInt = KeyInt;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnCustomModelUpdate
// (Event, Protected, BlueprintEvent)
// Parameters:
// const struct FModelInfo&                ModelInfo                                              (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_ShowroomPage_C::OnCustomModelUpdate(const struct FModelInfo& ModelInfo)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnCustomModelUpdate");

	Params::WDG_ShowroomPage_C_OnCustomModelUpdate Parms{};

	Parms.ModelInfo = std::move(ModelInfo);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__btnEditCustom_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UWDG_GenericBarItem_C*            Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::BndEvt__btnEditCustom_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature(class UWDG_GenericBarItem_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__btnEditCustom_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__btnEditCustom_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature Parms{};

	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__btnAddCustom_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UWDG_GenericBarItem_C*            Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::BndEvt__btnAddCustom_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature(class UWDG_GenericBarItem_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__btnAddCustom_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__btnAddCustom_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature Parms{};

	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__popupOverwrite_K2Node_ComponentBoundEvent_8_OnNo__DelegateSignature
// (BlueprintEvent)

void UWDG_ShowroomPage_C::BndEvt__popupOverwrite_K2Node_ComponentBoundEvent_8_OnNo__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__popupOverwrite_K2Node_ComponentBoundEvent_8_OnNo__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__popupOverwrite_K2Node_ComponentBoundEvent_7_OnYes__DelegateSignature
// (BlueprintEvent)

void UWDG_ShowroomPage_C::BndEvt__popupOverwrite_K2Node_ComponentBoundEvent_7_OnYes__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__popupOverwrite_K2Node_ComponentBoundEvent_7_OnYes__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__btnCustomSaveAsNew_K2Node_ComponentBoundEvent_3_OnItemForward__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UWDG_GenericBarItem_C*            Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::BndEvt__btnCustomSaveAsNew_K2Node_ComponentBoundEvent_3_OnItemForward__DelegateSignature(class UWDG_GenericBarItem_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__btnCustomSaveAsNew_K2Node_ComponentBoundEvent_3_OnItemForward__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__btnCustomSaveAsNew_K2Node_ComponentBoundEvent_3_OnItemForward__DelegateSignature Parms{};

	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__popupDelete_K2Node_ComponentBoundEvent_2_OnNo__DelegateSignature
// (BlueprintEvent)

void UWDG_ShowroomPage_C::BndEvt__popupDelete_K2Node_ComponentBoundEvent_2_OnNo__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__popupDelete_K2Node_ComponentBoundEvent_2_OnNo__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__popupDelete_K2Node_ComponentBoundEvent_0_OnYes__DelegateSignature
// (BlueprintEvent)

void UWDG_ShowroomPage_C::BndEvt__popupDelete_K2Node_ComponentBoundEvent_0_OnYes__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__popupDelete_K2Node_ComponentBoundEvent_0_OnYes__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__btnCustomDelete_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UWDG_GenericBarItem_C*            Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::BndEvt__btnCustomDelete_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature(class UWDG_GenericBarItem_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__btnCustomDelete_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__btnCustomDelete_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature Parms{};

	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__popupCarSaved_K2Node_ComponentBoundEvent_6_OnHide__DelegateSignature
// (BlueprintEvent)

void UWDG_ShowroomPage_C::BndEvt__popupCarSaved_K2Node_ComponentBoundEvent_6_OnHide__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__popupCarSaved_K2Node_ComponentBoundEvent_6_OnHide__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__popupCarSaved_K2Node_ComponentBoundEvent_4_OnShow__DelegateSignature
// (BlueprintEvent)
// Parameters:
// bool                                    HasConfirmation                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ShowroomPage_C::BndEvt__popupCarSaved_K2Node_ComponentBoundEvent_4_OnShow__DelegateSignature(bool HasConfirmation)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__popupCarSaved_K2Node_ComponentBoundEvent_4_OnShow__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__popupCarSaved_K2Node_ComponentBoundEvent_4_OnShow__DelegateSignature Parms{};

	Parms.HasConfirmation = HasConfirmation;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__btnCustomSave_K2Node_ComponentBoundEvent_7_OnItemForward__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UWDG_GenericBarItem_C*            Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::BndEvt__btnCustomSave_K2Node_ComponentBoundEvent_7_OnItemForward__DelegateSignature(class UWDG_GenericBarItem_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__btnCustomSave_K2Node_ComponentBoundEvent_7_OnItemForward__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__btnCustomSave_K2Node_ComponentBoundEvent_7_OnItemForward__DelegateSignature Parms{};

	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnAnimationFinished
// (BlueprintCosmetic, Event, Protected, BlueprintEvent)
// Parameters:
// const class UWidgetAnimation*           Animation                                              (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::OnAnimationFinished(const class UWidgetAnimation* Animation)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnAnimationFinished");

	Params::WDG_ShowroomPage_C_OnAnimationFinished Parms{};

	Parms.Animation = Animation;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.StopSequence
// (BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomPage_C::StopSequence()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "StopSequence");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__btnPlaySequence_K2Node_ComponentBoundEvent_6_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_ShowroomPage_C::BndEvt__btnPlaySequence_K2Node_ComponentBoundEvent_6_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__btnPlaySequence_K2Node_ComponentBoundEvent_6_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.PlaySequence
// (BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomPage_C::PlaySequence()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "PlaySequence");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__btnDoorRight_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_ShowroomPage_C::BndEvt__btnDoorRight_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__btnDoorRight_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__btnDoorLeft_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_ShowroomPage_C::BndEvt__btnDoorLeft_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__btnDoorLeft_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnPostCarUpdate
// (BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomPage_C::OnPostCarUpdate()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnPostCarUpdate");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__WDG_HorizontalSlider_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UGenericSelectorItem*             Source                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_index                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   current_value                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::BndEvt__WDG_HorizontalSlider_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature(class UGenericSelectorItem* Source, int32 current_index, int32 current_value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__WDG_HorizontalSlider_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature");

	Params::WDG_ShowroomPage_C_BndEvt__WDG_HorizontalSlider_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature Parms{};

	Parms.Source = Source;
	Parms.current_index = current_index;
	Parms.current_value = current_value;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__WDG_GenericBarItem_C_1_K2Node_ComponentBoundEvent_2_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_ShowroomPage_C::BndEvt__WDG_GenericBarItem_C_1_K2Node_ComponentBoundEvent_2_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__WDG_GenericBarItem_C_1_K2Node_ComponentBoundEvent_2_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__btnToggleDoors_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_ShowroomPage_C::BndEvt__btnToggleDoors_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__btnToggleDoors_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnCameraToggle
// (BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomPage_C::OnCameraToggle()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnCameraToggle");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.BndEvt__btnCameraToggle_K2Node_ComponentBoundEvent_7_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_ShowroomPage_C::BndEvt__btnCameraToggle_K2Node_ComponentBoundEvent_7_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "BndEvt__btnCameraToggle_K2Node_ComponentBoundEvent_7_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.ResetView
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    ResetOnlyOffset                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ShowroomPage_C::ResetView(bool ResetOnlyOffset)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "ResetView");

	Params::WDG_ShowroomPage_C_ResetView Parms{};

	Parms.ResetOnlyOffset = ResetOnlyOffset;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.SetPreviousPageTitle
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomPage_C::SetPreviousPageTitle()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "SetPreviousPageTitle");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnMouseButtonUp
// (BlueprintCosmetic, Event, Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FPointerEvent&             MouseEvent                                             (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// struct FEventReply                      ReturnValue                                            (Parm, OutParm, ReturnParm)

struct FEventReply UWDG_ShowroomPage_C::OnMouseButtonUp(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnMouseButtonUp");

	Params::WDG_ShowroomPage_C_OnMouseButtonUp Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.MouseEvent = std::move(MouseEvent);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnMouseMove
// (BlueprintCosmetic, Event, Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FPointerEvent&             MouseEvent                                             (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// struct FEventReply                      ReturnValue                                            (Parm, OutParm, ReturnParm)

struct FEventReply UWDG_ShowroomPage_C::OnMouseMove(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnMouseMove");

	Params::WDG_ShowroomPage_C_OnMouseMove Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.MouseEvent = std::move(MouseEvent);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.SetShowroomCamera
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomPage_C::SetShowroomCamera()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "SetShowroomCamera");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnMouseWheel
// (BlueprintCosmetic, Event, Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FPointerEvent&             MouseEvent                                             (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// struct FEventReply                      ReturnValue                                            (Parm, OutParm, ReturnParm)

struct FEventReply UWDG_ShowroomPage_C::OnMouseWheel(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnMouseWheel");

	Params::WDG_ShowroomPage_C_OnMouseWheel Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.MouseEvent = std::move(MouseEvent);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnMouseButtonDoubleClick
// (BlueprintCosmetic, Event, Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 InMyGeometry                                           (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FPointerEvent&             InMouseEvent                                           (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// struct FEventReply                      ReturnValue                                            (Parm, OutParm, ReturnParm)

struct FEventReply UWDG_ShowroomPage_C::OnMouseButtonDoubleClick(const struct FGeometry& InMyGeometry, const struct FPointerEvent& InMouseEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnMouseButtonDoubleClick");

	Params::WDG_ShowroomPage_C_OnMouseButtonDoubleClick Parms{};

	Parms.InMyGeometry = std::move(InMyGeometry);
	Parms.InMouseEvent = std::move(InMouseEvent);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.ShowModalOverlay
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// EShowroomTileType                       Type                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UAcPanelBase*                     CallingPanel                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::ShowModalOverlay(EShowroomTileType Type, class UAcPanelBase* CallingPanel)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "ShowModalOverlay");

	Params::WDG_ShowroomPage_C_ShowModalOverlay Parms{};

	Parms.Type = Type;
	Parms.CallingPanel = CallingPanel;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.HideModalOverlay
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    Cancelled                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ShowroomPage_C::HideModalOverlay(bool Cancelled)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "HideModalOverlay");

	Params::WDG_ShowroomPage_C_HideModalOverlay Parms{};

	Parms.Cancelled = Cancelled;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnPreviewKeyDown
// (Event, Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FKeyEvent&                 InKeyEvent                                             (BlueprintVisible, BlueprintReadOnly, Parm)
// struct FEventReply                      ReturnValue                                            (Parm, OutParm, ReturnParm)

struct FEventReply UWDG_ShowroomPage_C::OnPreviewKeyDown(const struct FGeometry& MyGeometry, const struct FKeyEvent& InKeyEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnPreviewKeyDown");

	Params::WDG_ShowroomPage_C_OnPreviewKeyDown Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InKeyEvent = std::move(InKeyEvent);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.UpdateModelTiles
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    All_Models                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ShowroomPage_C::UpdateModelTiles(bool All_Models)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "UpdateModelTiles");

	Params::WDG_ShowroomPage_C_UpdateModelTiles Parms{};

	Parms.All_Models = All_Models;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.UpdateTeamTiles
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomPage_C::UpdateTeamTiles()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "UpdateTeamTiles");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.FocusToSelectedCarEntry
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomPage_C::FocusToSelectedCarEntry()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "FocusToSelectedCarEntry");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.GoToCurrentDriver
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// EUINavigation                           Navigation_0                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWidget*                          ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

class UWidget* UWDG_ShowroomPage_C::GoToCurrentDriver(EUINavigation Navigation_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "GoToCurrentDriver");

	Params::WDG_ShowroomPage_C_GoToCurrentDriver Parms{};

	Parms.Navigation_0 = Navigation_0;

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.PrepareCustomEditor
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FCarInfo&                  CarInfo                                                (BlueprintVisible, BlueprintReadOnly, Parm)
// ECarModelType                           CarModel                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::PrepareCustomEditor(const struct FCarInfo& CarInfo, ECarModelType CarModel)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "PrepareCustomEditor");

	Params::WDG_ShowroomPage_C_PrepareCustomEditor Parms{};

	Parms.CarInfo = std::move(CarInfo);
	Parms.CarModel = CarModel;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.ReturnToCallingPage
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomPage_C::ReturnToCallingPage()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "ReturnToCallingPage");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.CheckEnableHideFileOpButtons
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomPage_C::CheckEnableHideFileOpButtons()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "CheckEnableHideFileOpButtons");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.ReturnFromCustomEditor
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomPage_C::ReturnFromCustomEditor()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "ReturnFromCustomEditor");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.FindSelectedEntry
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    ScrollToOnFind                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
// class UWDG_ShowroomTileItemHorizontal_C**AsWDG_Showroom_Tile_Item_Horizontal                    (Parm, OutParm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::FindSelectedEntry(bool ScrollToOnFind, class UWDG_ShowroomTileItemHorizontal_C** AsWDG_Showroom_Tile_Item_Horizontal)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "FindSelectedEntry");

	Params::WDG_ShowroomPage_C_FindSelectedEntry Parms{};

	Parms.ScrollToOnFind = ScrollToOnFind;

	UObject::ProcessEvent(Func, &Parms);

	if (AsWDG_Showroom_Tile_Item_Horizontal != nullptr)
		*AsWDG_Showroom_Tile_Item_Horizontal = Parms.AsWDG_Showroom_Tile_Item_Horizontal;
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.IsShowroomPlaying
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor)

bool UWDG_ShowroomPage_C::IsShowroomPlaying()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "IsShowroomPlaying");

	Params::WDG_ShowroomPage_C_IsShowroomPlaying Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnPreviewMouseButtonDown
// (BlueprintCosmetic, Event, Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FPointerEvent&             MouseEvent                                             (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// struct FEventReply                      ReturnValue                                            (Parm, OutParm, ReturnParm)

struct FEventReply UWDG_ShowroomPage_C::OnPreviewMouseButtonDown(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnPreviewMouseButtonDown");

	Params::WDG_ShowroomPage_C_OnPreviewMouseButtonDown Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.MouseEvent = std::move(MouseEvent);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.GetCarSystems
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UAcCarSystems**                   carSystems                                             (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::GetCarSystems(class UAcCarSystems** carSystems)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "GetCarSystems");

	Params::WDG_ShowroomPage_C_GetCarSystems Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (carSystems != nullptr)
		*carSystems = Parms.carSystems;
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.SetCarLights
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   Stage                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::SetCarLights(int32 Stage)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "SetCarLights");

	Params::WDG_ShowroomPage_C_SetCarLights Parms{};

	Parms.Stage = Stage;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.GetCarAnimations
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class UAcCarAnimations**                CarAnimations                                          (Parm, OutParm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::GetCarAnimations(class UAcCarAnimations** CarAnimations)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "GetCarAnimations");

	Params::WDG_ShowroomPage_C_GetCarAnimations Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (CarAnimations != nullptr)
		*CarAnimations = Parms.CarAnimations;
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.ToggleUIOpacity
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomPage_C::ToggleUIOpacity()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "ToggleUIOpacity");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnKeyUp
// (BlueprintCosmetic, Event, Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FKeyEvent&                 InKeyEvent                                             (BlueprintVisible, BlueprintReadOnly, Parm)
// struct FEventReply                      ReturnValue                                            (Parm, OutParm, ReturnParm)

struct FEventReply UWDG_ShowroomPage_C::OnKeyUp(const struct FGeometry& MyGeometry, const struct FKeyEvent& InKeyEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnKeyUp");

	Params::WDG_ShowroomPage_C_OnKeyUp Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InKeyEvent = std::move(InKeyEvent);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.FindSelectedVariant
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    ScrollToOnFind                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
// class UWDG_ShowroomTileItemHorizontal_C**AsWDG_Showroom_Tile_Item_Horizontal                    (Parm, OutParm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::FindSelectedVariant(bool ScrollToOnFind, class UWDG_ShowroomTileItemHorizontal_C** AsWDG_Showroom_Tile_Item_Horizontal)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "FindSelectedVariant");

	Params::WDG_ShowroomPage_C_FindSelectedVariant Parms{};

	Parms.ScrollToOnFind = ScrollToOnFind;

	UObject::ProcessEvent(Func, &Parms);

	if (AsWDG_Showroom_Tile_Item_Horizontal != nullptr)
		*AsWDG_Showroom_Tile_Item_Horizontal = Parms.AsWDG_Showroom_Tile_Item_Horizontal;
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.IsCurrentCarCustom
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool*                                   isCustom                                               (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ShowroomPage_C::IsCurrentCarCustom(bool* isCustom)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "IsCurrentCarCustom");

	Params::WDG_ShowroomPage_C_IsCurrentCarCustom Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (isCustom != nullptr)
		*isCustom = Parms.isCustom;
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.UpdateShowroomLabels
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// class FName                             ModelName                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// EBrandType                              Brand                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::UpdateShowroomLabels(class FName ModelName, EBrandType Brand)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "UpdateShowroomLabels");

	Params::WDG_ShowroomPage_C_UpdateShowroomLabels Parms{};

	Parms.ModelName = ModelName;
	Parms.Brand = Brand;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.ProceedToNextPage
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomPage_C::ProceedToNextPage()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "ProceedToNextPage");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.ToggleLockIcon
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FCarInfo&                  CarInfo                                                (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_ShowroomPage_C::ToggleLockIcon(const struct FCarInfo& CarInfo)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "ToggleLockIcon");

	Params::WDG_ShowroomPage_C_ToggleLockIcon Parms{};

	Parms.CarInfo = std::move(CarInfo);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.ShowCustomEditor
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    isCustom                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ShowroomPage_C::ShowCustomEditor(bool isCustom)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "ShowCustomEditor");

	Params::WDG_ShowroomPage_C_ShowCustomEditor Parms{};

	Parms.isCustom = isCustom;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.IsInCustomEditor
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor)

bool UWDG_ShowroomPage_C::IsInCustomEditor()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "IsInCustomEditor");

	Params::WDG_ShowroomPage_C_IsInCustomEditor Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.getCarAvatar
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class ACarAvatar**                      currentShowroomCar                                     (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomPage_C::getCarAvatar(class ACarAvatar** currentShowroomCar)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "getCarAvatar");

	Params::WDG_ShowroomPage_C_getCarAvatar Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (currentShowroomCar != nullptr)
		*currentShowroomCar = Parms.currentShowroomCar;
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.GoToDriverCustomization
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_ShowroomPage_C::GoToDriverCustomization()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "GoToDriverCustomization");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.Show UI If Hidden
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool*                                   WasHidden                                              (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ShowroomPage_C::Show_UI_If_Hidden(bool* WasHidden)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "Show UI If Hidden");

	Params::WDG_ShowroomPage_C_Show_UI_If_Hidden Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (WasHidden != nullptr)
		*WasHidden = Parms.WasHidden;
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.OnKeyDown
// (BlueprintCosmetic, Event, Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FKeyEvent&                 InKeyEvent                                             (BlueprintVisible, BlueprintReadOnly, Parm)
// struct FEventReply                      ReturnValue                                            (Parm, OutParm, ReturnParm)

struct FEventReply UWDG_ShowroomPage_C::OnKeyDown(const struct FGeometry& MyGeometry, const struct FKeyEvent& InKeyEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "OnKeyDown");

	Params::WDG_ShowroomPage_C_OnKeyDown Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InKeyEvent = std::move(InKeyEvent);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ShowroomPage.WDG_ShowroomPage_C.GoUpFromCustomEdit
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// EUINavigation                           Navigation_0                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class UWidget*                          ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

class UWidget* UWDG_ShowroomPage_C::GoUpFromCustomEdit(EUINavigation Navigation_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomPage_C", "GoUpFromCustomEdit");

	Params::WDG_ShowroomPage_C_GoUpFromCustomEdit Parms{};

	Parms.Navigation_0 = Navigation_0;

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}

}

