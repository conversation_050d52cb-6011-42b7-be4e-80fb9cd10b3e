﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ViewOptionsPage

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "UMG_structs.hpp"
#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_ViewOptionsPage.WDG_ViewOptionsPage_C.ExecuteUbergraph_WDG_ViewOptionsPage
// 0x0010 (0x0010 - 0x0000)
struct WDG_ViewOptionsPage_C_ExecuteUbergraph_WDG_ViewOptionsPage final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_ComponentBoundEvent_New_Value;              // 0x0004(0x0004)(ZeroConstructor, IsPlainOldD<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	EControllerActionType                         K2Node_Event_input;                                // 0x0008(0x0001)(ConstParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_isReleased;                           // 0x0009(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x000A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue_1;        // 0x000B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          K2Node_Event_SameAsOldSettings;                    // 0x000C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x000D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ViewOptionsPage_C_ExecuteUbergraph_WDG_ViewOptionsPage) == 0x000004, "Wrong alignment on WDG_ViewOptionsPage_C_ExecuteUbergraph_WDG_ViewOptionsPage");
static_assert(sizeof(WDG_ViewOptionsPage_C_ExecuteUbergraph_WDG_ViewOptionsPage) == 0x000010, "Wrong size on WDG_ViewOptionsPage_C_ExecuteUbergraph_WDG_ViewOptionsPage");
static_assert(offsetof(WDG_ViewOptionsPage_C_ExecuteUbergraph_WDG_ViewOptionsPage, EntryPoint) == 0x000000, "Member 'WDG_ViewOptionsPage_C_ExecuteUbergraph_WDG_ViewOptionsPage::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_ViewOptionsPage_C_ExecuteUbergraph_WDG_ViewOptionsPage, K2Node_ComponentBoundEvent_New_Value) == 0x000004, "Member 'WDG_ViewOptionsPage_C_ExecuteUbergraph_WDG_ViewOptionsPage::K2Node_ComponentBoundEvent_New_Value' has a wrong offset!");
static_assert(offsetof(WDG_ViewOptionsPage_C_ExecuteUbergraph_WDG_ViewOptionsPage, K2Node_Event_input) == 0x000008, "Member 'WDG_ViewOptionsPage_C_ExecuteUbergraph_WDG_ViewOptionsPage::K2Node_Event_input' has a wrong offset!");
static_assert(offsetof(WDG_ViewOptionsPage_C_ExecuteUbergraph_WDG_ViewOptionsPage, K2Node_Event_isReleased) == 0x000009, "Member 'WDG_ViewOptionsPage_C_ExecuteUbergraph_WDG_ViewOptionsPage::K2Node_Event_isReleased' has a wrong offset!");
static_assert(offsetof(WDG_ViewOptionsPage_C_ExecuteUbergraph_WDG_ViewOptionsPage, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x00000A, "Member 'WDG_ViewOptionsPage_C_ExecuteUbergraph_WDG_ViewOptionsPage::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ViewOptionsPage_C_ExecuteUbergraph_WDG_ViewOptionsPage, CallFunc_EqualEqual_ByteByte_ReturnValue_1) == 0x00000B, "Member 'WDG_ViewOptionsPage_C_ExecuteUbergraph_WDG_ViewOptionsPage::CallFunc_EqualEqual_ByteByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ViewOptionsPage_C_ExecuteUbergraph_WDG_ViewOptionsPage, K2Node_Event_SameAsOldSettings) == 0x00000C, "Member 'WDG_ViewOptionsPage_C_ExecuteUbergraph_WDG_ViewOptionsPage::K2Node_Event_SameAsOldSettings' has a wrong offset!");
static_assert(offsetof(WDG_ViewOptionsPage_C_ExecuteUbergraph_WDG_ViewOptionsPage, CallFunc_Not_PreBool_ReturnValue) == 0x00000D, "Member 'WDG_ViewOptionsPage_C_ExecuteUbergraph_WDG_ViewOptionsPage::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");

// Function WDG_ViewOptionsPage.WDG_ViewOptionsPage_C.OnSettingsChanged
// 0x0001 (0x0001 - 0x0000)
struct WDG_ViewOptionsPage_C_OnSettingsChanged final
{
public:
	bool                                          SameAsOldSettings;                                 // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ViewOptionsPage_C_OnSettingsChanged) == 0x000001, "Wrong alignment on WDG_ViewOptionsPage_C_OnSettingsChanged");
static_assert(sizeof(WDG_ViewOptionsPage_C_OnSettingsChanged) == 0x000001, "Wrong size on WDG_ViewOptionsPage_C_OnSettingsChanged");
static_assert(offsetof(WDG_ViewOptionsPage_C_OnSettingsChanged, SameAsOldSettings) == 0x000000, "Member 'WDG_ViewOptionsPage_C_OnSettingsChanged::SameAsOldSettings' has a wrong offset!");

// Function WDG_ViewOptionsPage.WDG_ViewOptionsPage_C.BP_OnMenuNavigationPreview
// 0x0002 (0x0002 - 0x0000)
struct WDG_ViewOptionsPage_C_BP_OnMenuNavigationPreview final
{
public:
	EControllerActionType                         Input;                                             // 0x0000(0x0001)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          isReleased;                                        // 0x0001(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ViewOptionsPage_C_BP_OnMenuNavigationPreview) == 0x000001, "Wrong alignment on WDG_ViewOptionsPage_C_BP_OnMenuNavigationPreview");
static_assert(sizeof(WDG_ViewOptionsPage_C_BP_OnMenuNavigationPreview) == 0x000002, "Wrong size on WDG_ViewOptionsPage_C_BP_OnMenuNavigationPreview");
static_assert(offsetof(WDG_ViewOptionsPage_C_BP_OnMenuNavigationPreview, Input) == 0x000000, "Member 'WDG_ViewOptionsPage_C_BP_OnMenuNavigationPreview::Input' has a wrong offset!");
static_assert(offsetof(WDG_ViewOptionsPage_C_BP_OnMenuNavigationPreview, isReleased) == 0x000001, "Member 'WDG_ViewOptionsPage_C_BP_OnMenuNavigationPreview::isReleased' has a wrong offset!");

// Function WDG_ViewOptionsPage.WDG_ViewOptionsPage_C.BndEvt__MainSelector_K2Node_ComponentBoundEvent_2_OnChange__DelegateSignature
// 0x0004 (0x0004 - 0x0000)
struct WDG_ViewOptionsPage_C_BndEvt__MainSelector_K2Node_ComponentBoundEvent_2_OnChange__DelegateSignature final
{
public:
	int32                                         New_Value;                                         // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ViewOptionsPage_C_BndEvt__MainSelector_K2Node_ComponentBoundEvent_2_OnChange__DelegateSignature) == 0x000004, "Wrong alignment on WDG_ViewOptionsPage_C_BndEvt__MainSelector_K2Node_ComponentBoundEvent_2_OnChange__DelegateSignature");
static_assert(sizeof(WDG_ViewOptionsPage_C_BndEvt__MainSelector_K2Node_ComponentBoundEvent_2_OnChange__DelegateSignature) == 0x000004, "Wrong size on WDG_ViewOptionsPage_C_BndEvt__MainSelector_K2Node_ComponentBoundEvent_2_OnChange__DelegateSignature");
static_assert(offsetof(WDG_ViewOptionsPage_C_BndEvt__MainSelector_K2Node_ComponentBoundEvent_2_OnChange__DelegateSignature, New_Value) == 0x000000, "Member 'WDG_ViewOptionsPage_C_BndEvt__MainSelector_K2Node_ComponentBoundEvent_2_OnChange__DelegateSignature::New_Value' has a wrong offset!");

// Function WDG_ViewOptionsPage.WDG_ViewOptionsPage_C.OnPreviewKeyDown
// 0x01F0 (0x01F0 - 0x0000)
struct WDG_ViewOptionsPage_C_OnPreviewKeyDown final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FKeyEvent                              InKeyEvent;                                        // 0x0038(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm)
	struct FEventReply                            ReturnValue;                                       // 0x0070(0x00B8)(Parm, OutParm, ReturnParm)
	bool                                          CallFunc_IsKeyBoundToAction_IsBound;               // 0x0128(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_129[0x7];                                      // 0x0129(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_Unhandled_ReturnValue;                    // 0x0130(0x00B8)()
	bool                                          CallFunc_HasFocusedDescendants_ReturnValue;        // 0x01E8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x01E9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ViewOptionsPage_C_OnPreviewKeyDown) == 0x000008, "Wrong alignment on WDG_ViewOptionsPage_C_OnPreviewKeyDown");
static_assert(sizeof(WDG_ViewOptionsPage_C_OnPreviewKeyDown) == 0x0001F0, "Wrong size on WDG_ViewOptionsPage_C_OnPreviewKeyDown");
static_assert(offsetof(WDG_ViewOptionsPage_C_OnPreviewKeyDown, MyGeometry) == 0x000000, "Member 'WDG_ViewOptionsPage_C_OnPreviewKeyDown::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_ViewOptionsPage_C_OnPreviewKeyDown, InKeyEvent) == 0x000038, "Member 'WDG_ViewOptionsPage_C_OnPreviewKeyDown::InKeyEvent' has a wrong offset!");
static_assert(offsetof(WDG_ViewOptionsPage_C_OnPreviewKeyDown, ReturnValue) == 0x000070, "Member 'WDG_ViewOptionsPage_C_OnPreviewKeyDown::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ViewOptionsPage_C_OnPreviewKeyDown, CallFunc_IsKeyBoundToAction_IsBound) == 0x000128, "Member 'WDG_ViewOptionsPage_C_OnPreviewKeyDown::CallFunc_IsKeyBoundToAction_IsBound' has a wrong offset!");
static_assert(offsetof(WDG_ViewOptionsPage_C_OnPreviewKeyDown, CallFunc_Unhandled_ReturnValue) == 0x000130, "Member 'WDG_ViewOptionsPage_C_OnPreviewKeyDown::CallFunc_Unhandled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ViewOptionsPage_C_OnPreviewKeyDown, CallFunc_HasFocusedDescendants_ReturnValue) == 0x0001E8, "Member 'WDG_ViewOptionsPage_C_OnPreviewKeyDown::CallFunc_HasFocusedDescendants_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ViewOptionsPage_C_OnPreviewKeyDown, CallFunc_BooleanOR_ReturnValue) == 0x0001E9, "Member 'WDG_ViewOptionsPage_C_OnPreviewKeyDown::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");

}

