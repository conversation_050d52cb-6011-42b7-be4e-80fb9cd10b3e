﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SinglePlayerLowerPanel

#include "Basic.hpp"

#include "WDG_SinglePlayerLowerPanel_classes.hpp"
#include "WDG_SinglePlayerLowerPanel_parameters.hpp"


namespace SDK
{

// Function WDG_SinglePlayerLowerPanel.WDG_SinglePlayerLowerPanel_C.ExecuteUbergraph_WDG_SinglePlayerLowerPanel
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SinglePlayerLowerPanel_C::ExecuteUbergraph_WDG_SinglePlayerLowerPanel(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerLowerPanel_C", "ExecuteUbergraph_WDG_SinglePlayerLowerPanel");

	Params::WDG_SinglePlayerLowerPanel_C_ExecuteUbergraph_WDG_SinglePlayerLowerPanel Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SinglePlayerLowerPanel.WDG_SinglePlayerLowerPanel_C.BP_MouseLeave
// (Event, Public, BlueprintEvent)

void UWDG_SinglePlayerLowerPanel_C::BP_MouseLeave()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerLowerPanel_C", "BP_MouseLeave");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SinglePlayerLowerPanel.WDG_SinglePlayerLowerPanel_C.BP_MouseOver
// (Event, Public, BlueprintEvent)

void UWDG_SinglePlayerLowerPanel_C::BP_MouseOver()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerLowerPanel_C", "BP_MouseOver");

	UObject::ProcessEvent(Func, nullptr);
}

}

