﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ServerStats

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"
#include "AC2_structs.hpp"
#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_ServerStats.WDG_ServerStats_C.ExecuteUbergraph_WDG_ServerStats
// 0x01B8 (0x01B8 - 0x0000)
struct WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UPanelWidget*                           CallFunc_GetParent_ReturnValue;                    // 0x0008(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_Event_gameInstance;                         // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AAcRaceGameMode*                        K2Node_Event_raceGameMode;                         // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class ACarAvatar*                             K2Node_Event_carAvatar;                            // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FHUDOptions                            K2Node_Event_hudOptions;                           // 0x0028(0x00C0)(ConstParm, NoDestructor)
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue;             // 0x00E8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x00F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_GreaterEqual_IntInt_ReturnValue;          // 0x00F1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_GreaterEqual_IntInt_ReturnValue_1;        // 0x00F2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_GreaterEqual_IntInt_ReturnValue_2;        // 0x00F3(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_GreaterEqual_IntInt_ReturnValue_3;        // 0x00F4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_F5[0x3];                                       // 0x00F5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              CallFunc_GetPosition_ReturnValue;                  // 0x00F8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FMultiplayerServerStats                K2Node_CustomEvent_server_stats;                   // 0x0100(0x0050)(ConstParm)
	class UPanelWidget*                           CallFunc_GetParent_ReturnValue_1;                  // 0x0150(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void(const struct FMultiplayerServerStats& server_stats)> K2Node_CreateDelegate_OutputDelegate; // 0x0158(0x0010)(ZeroConstructor, NoDestructor)
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue_1;           // 0x0168(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x0170(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_171[0x3];                                      // 0x0171(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              CallFunc_GetPosition_ReturnValue_1;                // 0x0174(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_17C[0x4];                                      // 0x017C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UPanelWidget*                           CallFunc_GetParent_ReturnValue_2;                  // 0x0180(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UPanelWidget*                           CallFunc_GetParent_ReturnValue_3;                  // 0x0188(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue_2;           // 0x0190(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue_3;           // 0x0198(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_2;                    // 0x01A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsValid_ReturnValue_3;                    // 0x01A1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1A2[0x2];                                      // 0x01A2(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              CallFunc_GetPosition_ReturnValue_2;                // 0x01A4(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_GetPosition_ReturnValue_3;                // 0x01AC(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x01B4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats) == 0x000008, "Wrong alignment on WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats");
static_assert(sizeof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats) == 0x0001B8, "Wrong size on WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, EntryPoint) == 0x000000, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, CallFunc_GetParent_ReturnValue) == 0x000008, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::CallFunc_GetParent_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, K2Node_Event_gameInstance) == 0x000010, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::K2Node_Event_gameInstance' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, K2Node_Event_raceGameMode) == 0x000018, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::K2Node_Event_raceGameMode' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, K2Node_Event_carAvatar) == 0x000020, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::K2Node_Event_carAvatar' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, K2Node_Event_hudOptions) == 0x000028, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::K2Node_Event_hudOptions' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, CallFunc_SlotAsCanvasSlot_ReturnValue) == 0x0000E8, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::CallFunc_SlotAsCanvasSlot_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, CallFunc_IsValid_ReturnValue) == 0x0000F0, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, CallFunc_GreaterEqual_IntInt_ReturnValue) == 0x0000F1, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::CallFunc_GreaterEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, CallFunc_GreaterEqual_IntInt_ReturnValue_1) == 0x0000F2, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::CallFunc_GreaterEqual_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, CallFunc_GreaterEqual_IntInt_ReturnValue_2) == 0x0000F3, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::CallFunc_GreaterEqual_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, CallFunc_GreaterEqual_IntInt_ReturnValue_3) == 0x0000F4, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::CallFunc_GreaterEqual_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, CallFunc_GetPosition_ReturnValue) == 0x0000F8, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::CallFunc_GetPosition_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, K2Node_CustomEvent_server_stats) == 0x000100, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::K2Node_CustomEvent_server_stats' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, CallFunc_GetParent_ReturnValue_1) == 0x000150, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::CallFunc_GetParent_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, K2Node_CreateDelegate_OutputDelegate) == 0x000158, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, CallFunc_SlotAsCanvasSlot_ReturnValue_1) == 0x000168, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::CallFunc_SlotAsCanvasSlot_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, CallFunc_IsValid_ReturnValue_1) == 0x000170, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, CallFunc_GetPosition_ReturnValue_1) == 0x000174, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::CallFunc_GetPosition_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, CallFunc_GetParent_ReturnValue_2) == 0x000180, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::CallFunc_GetParent_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, CallFunc_GetParent_ReturnValue_3) == 0x000188, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::CallFunc_GetParent_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, CallFunc_SlotAsCanvasSlot_ReturnValue_2) == 0x000190, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::CallFunc_SlotAsCanvasSlot_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, CallFunc_SlotAsCanvasSlot_ReturnValue_3) == 0x000198, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::CallFunc_SlotAsCanvasSlot_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, CallFunc_IsValid_ReturnValue_2) == 0x0001A0, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::CallFunc_IsValid_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, CallFunc_IsValid_ReturnValue_3) == 0x0001A1, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::CallFunc_IsValid_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, CallFunc_GetPosition_ReturnValue_2) == 0x0001A4, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::CallFunc_GetPosition_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, CallFunc_GetPosition_ReturnValue_3) == 0x0001AC, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::CallFunc_GetPosition_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats, K2Node_Event_IsDesignTime) == 0x0001B4, "Member 'WDG_ServerStats_C_ExecuteUbergraph_WDG_ServerStats::K2Node_Event_IsDesignTime' has a wrong offset!");

// Function WDG_ServerStats.WDG_ServerStats_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_ServerStats_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ServerStats_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_ServerStats_C_PreConstruct");
static_assert(sizeof(WDG_ServerStats_C_PreConstruct) == 0x000001, "Wrong size on WDG_ServerStats_C_PreConstruct");
static_assert(offsetof(WDG_ServerStats_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_ServerStats_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WDG_ServerStats.WDG_ServerStats_C.OnServerStatsChanged
// 0x0050 (0x0050 - 0x0000)
struct WDG_ServerStats_C_OnServerStatsChanged final
{
public:
	struct FMultiplayerServerStats                server_stats;                                      // 0x0000(0x0050)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_ServerStats_C_OnServerStatsChanged) == 0x000008, "Wrong alignment on WDG_ServerStats_C_OnServerStatsChanged");
static_assert(sizeof(WDG_ServerStats_C_OnServerStatsChanged) == 0x000050, "Wrong size on WDG_ServerStats_C_OnServerStatsChanged");
static_assert(offsetof(WDG_ServerStats_C_OnServerStatsChanged, server_stats) == 0x000000, "Member 'WDG_ServerStats_C_OnServerStatsChanged::server_stats' has a wrong offset!");

// Function WDG_ServerStats.WDG_ServerStats_C.OnStartWidget
// 0x00D8 (0x00D8 - 0x0000)
struct WDG_ServerStats_C_OnStartWidget final
{
public:
	class UAcGameInstance*                        GameInstance;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AAcRaceGameMode*                        raceGameMode;                                      // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class ACarAvatar*                             CarAvatar;                                         // 0x0010(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FHUDOptions                            HUDOptions;                                        // 0x0018(0x00C0)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)
};
static_assert(alignof(WDG_ServerStats_C_OnStartWidget) == 0x000008, "Wrong alignment on WDG_ServerStats_C_OnStartWidget");
static_assert(sizeof(WDG_ServerStats_C_OnStartWidget) == 0x0000D8, "Wrong size on WDG_ServerStats_C_OnStartWidget");
static_assert(offsetof(WDG_ServerStats_C_OnStartWidget, GameInstance) == 0x000000, "Member 'WDG_ServerStats_C_OnStartWidget::GameInstance' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_OnStartWidget, raceGameMode) == 0x000008, "Member 'WDG_ServerStats_C_OnStartWidget::raceGameMode' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_OnStartWidget, CarAvatar) == 0x000010, "Member 'WDG_ServerStats_C_OnStartWidget::CarAvatar' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_OnStartWidget, HUDOptions) == 0x000018, "Member 'WDG_ServerStats_C_OnStartWidget::HUDOptions' has a wrong offset!");

// Function WDG_ServerStats.WDG_ServerStats_C.Update
// 0x0100 (0x0100 - 0x0000)
struct WDG_ServerStats_C_Update final
{
public:
	struct FMultiplayerServerStats                serverStats;                                       // 0x0000(0x0050)(BlueprintVisible, BlueprintReadOnly, Parm)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0050(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0054(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0058(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x005C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_5D[0x3];                                       // 0x005D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x0060(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0064(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_1;                   // 0x0068(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6C[0x4];                                       // 0x006C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Array_Get_Item;                           // 0x0070(0x0018)()
	class FText                                   CallFunc_Array_Get_Item_1;                         // 0x0088(0x0018)()
	struct FSlateColor                            K2Node_MakeStruct_SlateColor;                      // 0x00A0(0x0028)(UObjectWrapper)
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_1;                    // 0x00C8(0x0028)()
	int32                                         Temp_int_Loop_Counter_Variable_1;                  // 0x00F0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_1;                // 0x00F4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_F5[0x3];                                       // 0x00F5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x00F8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ServerStats_C_Update) == 0x000008, "Wrong alignment on WDG_ServerStats_C_Update");
static_assert(sizeof(WDG_ServerStats_C_Update) == 0x000100, "Wrong size on WDG_ServerStats_C_Update");
static_assert(offsetof(WDG_ServerStats_C_Update, serverStats) == 0x000000, "Member 'WDG_ServerStats_C_Update::serverStats' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_Update, Temp_int_Loop_Counter_Variable) == 0x000050, "Member 'WDG_ServerStats_C_Update::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_Update, CallFunc_Array_Length_ReturnValue) == 0x000054, "Member 'WDG_ServerStats_C_Update::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_Update, CallFunc_Add_IntInt_ReturnValue) == 0x000058, "Member 'WDG_ServerStats_C_Update::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_Update, CallFunc_Less_IntInt_ReturnValue) == 0x00005C, "Member 'WDG_ServerStats_C_Update::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_Update, CallFunc_Array_Length_ReturnValue_1) == 0x000060, "Member 'WDG_ServerStats_C_Update::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_Update, Temp_int_Array_Index_Variable) == 0x000064, "Member 'WDG_ServerStats_C_Update::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_Update, Temp_int_Array_Index_Variable_1) == 0x000068, "Member 'WDG_ServerStats_C_Update::Temp_int_Array_Index_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_Update, CallFunc_Array_Get_Item) == 0x000070, "Member 'WDG_ServerStats_C_Update::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_Update, CallFunc_Array_Get_Item_1) == 0x000088, "Member 'WDG_ServerStats_C_Update::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_Update, K2Node_MakeStruct_SlateColor) == 0x0000A0, "Member 'WDG_ServerStats_C_Update::K2Node_MakeStruct_SlateColor' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_Update, K2Node_MakeStruct_SlateColor_1) == 0x0000C8, "Member 'WDG_ServerStats_C_Update::K2Node_MakeStruct_SlateColor_1' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_Update, Temp_int_Loop_Counter_Variable_1) == 0x0000F0, "Member 'WDG_ServerStats_C_Update::Temp_int_Loop_Counter_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_Update, CallFunc_Less_IntInt_ReturnValue_1) == 0x0000F4, "Member 'WDG_ServerStats_C_Update::CallFunc_Less_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_Update, CallFunc_Add_IntInt_ReturnValue_1) == 0x0000F8, "Member 'WDG_ServerStats_C_Update::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");

// Function WDG_ServerStats.WDG_ServerStats_C.AddTextToVBox
// 0x0060 (0x0060 - 0x0000)
struct WDG_ServerStats_C_AddTextToVBox final
{
public:
	class UVerticalBox*                           vbox;                                              // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ZeroConstructor, InstancedReference, ReferenceParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   text;                                              // 0x0008(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm)
	struct FSlateColor                            Color;                                             // 0x0020(0x0028)(BlueprintVisible, BlueprintReadOnly, Parm)
	bool                                          isHighlighted;                                     // 0x0048(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_49[0x7];                                       // 0x0049(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ServerStatsItem_C*                 CallFunc_Create_ReturnValue;                       // 0x0050(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UVerticalBoxSlot*                       CallFunc_AddChildToVerticalBox_ReturnValue;        // 0x0058(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ServerStats_C_AddTextToVBox) == 0x000008, "Wrong alignment on WDG_ServerStats_C_AddTextToVBox");
static_assert(sizeof(WDG_ServerStats_C_AddTextToVBox) == 0x000060, "Wrong size on WDG_ServerStats_C_AddTextToVBox");
static_assert(offsetof(WDG_ServerStats_C_AddTextToVBox, vbox) == 0x000000, "Member 'WDG_ServerStats_C_AddTextToVBox::vbox' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_AddTextToVBox, text) == 0x000008, "Member 'WDG_ServerStats_C_AddTextToVBox::text' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_AddTextToVBox, Color) == 0x000020, "Member 'WDG_ServerStats_C_AddTextToVBox::Color' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_AddTextToVBox, isHighlighted) == 0x000048, "Member 'WDG_ServerStats_C_AddTextToVBox::isHighlighted' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_AddTextToVBox, CallFunc_Create_ReturnValue) == 0x000050, "Member 'WDG_ServerStats_C_AddTextToVBox::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_AddTextToVBox, CallFunc_AddChildToVerticalBox_ReturnValue) == 0x000058, "Member 'WDG_ServerStats_C_AddTextToVBox::CallFunc_AddChildToVerticalBox_ReturnValue' has a wrong offset!");

// Function WDG_ServerStats.WDG_ServerStats_C.SetStatValue
// 0x0128 (0x0128 - 0x0000)
struct WDG_ServerStats_C_SetStatValue final
{
public:
	class UTextBlock*                             txtBlock;                                          // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Value;                                             // 0x0008(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          LowIsGood;                                         // 0x000C(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_D[0x3];                                        // 0x000D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         WarningColorStarts;                                // 0x0010(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         ErrorColorStarts;                                  // 0x0014(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSlateColor                            K2Node_MakeStruct_SlateColor;                      // 0x0018(0x0028)(UObjectWrapper)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_GreaterEqual_IntInt_ReturnValue;          // 0x0041(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue_1;           // 0x0042(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_GreaterEqual_IntInt_ReturnValue_1;        // 0x0043(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_44[0x4];                                       // 0x0044(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_IntToText_ReturnValue;               // 0x0048(0x0018)()
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_1;                    // 0x0060(0x0028)()
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_2;                    // 0x0088(0x0028)()
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_3;                    // 0x00B0(0x0028)(UObjectWrapper)
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_4;                    // 0x00D8(0x0028)()
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_5;                    // 0x0100(0x0028)()
};
static_assert(alignof(WDG_ServerStats_C_SetStatValue) == 0x000008, "Wrong alignment on WDG_ServerStats_C_SetStatValue");
static_assert(sizeof(WDG_ServerStats_C_SetStatValue) == 0x000128, "Wrong size on WDG_ServerStats_C_SetStatValue");
static_assert(offsetof(WDG_ServerStats_C_SetStatValue, txtBlock) == 0x000000, "Member 'WDG_ServerStats_C_SetStatValue::txtBlock' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_SetStatValue, Value) == 0x000008, "Member 'WDG_ServerStats_C_SetStatValue::Value' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_SetStatValue, LowIsGood) == 0x00000C, "Member 'WDG_ServerStats_C_SetStatValue::LowIsGood' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_SetStatValue, WarningColorStarts) == 0x000010, "Member 'WDG_ServerStats_C_SetStatValue::WarningColorStarts' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_SetStatValue, ErrorColorStarts) == 0x000014, "Member 'WDG_ServerStats_C_SetStatValue::ErrorColorStarts' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_SetStatValue, K2Node_MakeStruct_SlateColor) == 0x000018, "Member 'WDG_ServerStats_C_SetStatValue::K2Node_MakeStruct_SlateColor' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_SetStatValue, CallFunc_LessEqual_IntInt_ReturnValue) == 0x000040, "Member 'WDG_ServerStats_C_SetStatValue::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_SetStatValue, CallFunc_GreaterEqual_IntInt_ReturnValue) == 0x000041, "Member 'WDG_ServerStats_C_SetStatValue::CallFunc_GreaterEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_SetStatValue, CallFunc_LessEqual_IntInt_ReturnValue_1) == 0x000042, "Member 'WDG_ServerStats_C_SetStatValue::CallFunc_LessEqual_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_SetStatValue, CallFunc_GreaterEqual_IntInt_ReturnValue_1) == 0x000043, "Member 'WDG_ServerStats_C_SetStatValue::CallFunc_GreaterEqual_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_SetStatValue, CallFunc_Conv_IntToText_ReturnValue) == 0x000048, "Member 'WDG_ServerStats_C_SetStatValue::CallFunc_Conv_IntToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_SetStatValue, K2Node_MakeStruct_SlateColor_1) == 0x000060, "Member 'WDG_ServerStats_C_SetStatValue::K2Node_MakeStruct_SlateColor_1' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_SetStatValue, K2Node_MakeStruct_SlateColor_2) == 0x000088, "Member 'WDG_ServerStats_C_SetStatValue::K2Node_MakeStruct_SlateColor_2' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_SetStatValue, K2Node_MakeStruct_SlateColor_3) == 0x0000B0, "Member 'WDG_ServerStats_C_SetStatValue::K2Node_MakeStruct_SlateColor_3' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_SetStatValue, K2Node_MakeStruct_SlateColor_4) == 0x0000D8, "Member 'WDG_ServerStats_C_SetStatValue::K2Node_MakeStruct_SlateColor_4' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_SetStatValue, K2Node_MakeStruct_SlateColor_5) == 0x000100, "Member 'WDG_ServerStats_C_SetStatValue::K2Node_MakeStruct_SlateColor_5' has a wrong offset!");

// Function WDG_ServerStats.WDG_ServerStats_C.IsWidgetDefinitionEnabled
// 0x00D0 (0x00D0 - 0x0000)
struct WDG_ServerStats_C_IsWidgetDefinitionEnabled final
{
public:
	class UAcGameInstance*                        GameInstance;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FHUDOptions                            HUDOptions;                                        // 0x0008(0x00C0)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)
	bool                                          ReturnValue;                                       // 0x00C8(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x00C9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ServerStats_C_IsWidgetDefinitionEnabled) == 0x000008, "Wrong alignment on WDG_ServerStats_C_IsWidgetDefinitionEnabled");
static_assert(sizeof(WDG_ServerStats_C_IsWidgetDefinitionEnabled) == 0x0000D0, "Wrong size on WDG_ServerStats_C_IsWidgetDefinitionEnabled");
static_assert(offsetof(WDG_ServerStats_C_IsWidgetDefinitionEnabled, GameInstance) == 0x000000, "Member 'WDG_ServerStats_C_IsWidgetDefinitionEnabled::GameInstance' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_IsWidgetDefinitionEnabled, HUDOptions) == 0x000008, "Member 'WDG_ServerStats_C_IsWidgetDefinitionEnabled::HUDOptions' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_IsWidgetDefinitionEnabled, ReturnValue) == 0x0000C8, "Member 'WDG_ServerStats_C_IsWidgetDefinitionEnabled::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_IsWidgetDefinitionEnabled, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x0000C9, "Member 'WDG_ServerStats_C_IsWidgetDefinitionEnabled::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");

// Function WDG_ServerStats.WDG_ServerStats_C.ScaledNudgeByOffset
// 0x0030 (0x0030 - 0x0000)
struct WDG_ServerStats_C_ScaledNudgeByOffset final
{
public:
	struct FVector2D                              currentPosition;                                   // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UCanvasPanelSlot*                       Slot_0;                                            // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         HUDScale;                                          // 0x0010(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         Offset;                                            // 0x0014(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakVector2D_X;                          // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakVector2D_Y;                          // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue;          // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Add_FloatFloat_ReturnValue;               // 0x0024(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue;                 // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ServerStats_C_ScaledNudgeByOffset) == 0x000008, "Wrong alignment on WDG_ServerStats_C_ScaledNudgeByOffset");
static_assert(sizeof(WDG_ServerStats_C_ScaledNudgeByOffset) == 0x000030, "Wrong size on WDG_ServerStats_C_ScaledNudgeByOffset");
static_assert(offsetof(WDG_ServerStats_C_ScaledNudgeByOffset, currentPosition) == 0x000000, "Member 'WDG_ServerStats_C_ScaledNudgeByOffset::currentPosition' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ScaledNudgeByOffset, Slot_0) == 0x000008, "Member 'WDG_ServerStats_C_ScaledNudgeByOffset::Slot_0' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ScaledNudgeByOffset, HUDScale) == 0x000010, "Member 'WDG_ServerStats_C_ScaledNudgeByOffset::HUDScale' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ScaledNudgeByOffset, Offset) == 0x000014, "Member 'WDG_ServerStats_C_ScaledNudgeByOffset::Offset' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ScaledNudgeByOffset, CallFunc_BreakVector2D_X) == 0x000018, "Member 'WDG_ServerStats_C_ScaledNudgeByOffset::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ScaledNudgeByOffset, CallFunc_BreakVector2D_Y) == 0x00001C, "Member 'WDG_ServerStats_C_ScaledNudgeByOffset::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ScaledNudgeByOffset, CallFunc_Multiply_FloatFloat_ReturnValue) == 0x000020, "Member 'WDG_ServerStats_C_ScaledNudgeByOffset::CallFunc_Multiply_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ScaledNudgeByOffset, CallFunc_Add_FloatFloat_ReturnValue) == 0x000024, "Member 'WDG_ServerStats_C_ScaledNudgeByOffset::CallFunc_Add_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ServerStats_C_ScaledNudgeByOffset, CallFunc_MakeVector2D_ReturnValue) == 0x000028, "Member 'WDG_ServerStats_C_ScaledNudgeByOffset::CallFunc_MakeVector2D_ReturnValue' has a wrong offset!");

}

