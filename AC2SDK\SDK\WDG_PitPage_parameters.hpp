﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PitPage

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"
#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_PitPage.WDG_PitPage_C.ExecuteUbergraph_WDG_PitPage
// 0x0118 (0x0118 - 0x0000)
struct WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0006(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          K2Node_Event_fullscreen_1;                         // 0x0007(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          K2Node_Event_fullscreen;                           // 0x0009(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_A[0x6];                                        // 0x000A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x0010(0x0018)()
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_2;            // 0x0028(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_3;            // 0x0029(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_4;            // 0x002A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2B[0x5];                                       // 0x002B(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue;             // 0x0030(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_GetSize_ReturnValue;                      // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakVector2D_X;                          // 0x0040(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakVector2D_Y;                          // 0x0044(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue;                 // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue_1;           // 0x0050(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_GetPosition_ReturnValue;                  // 0x0058(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue_1;               // 0x0060(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakVector2D_X_1;                        // 0x0068(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakVector2D_Y_1;                        // 0x006C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue_2;               // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue_3;               // 0x0078(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_5;            // 0x0080(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_6;            // 0x0081(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_82[0x6];                                       // 0x0082(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UGridSlot*                              CallFunc_SlotAsGridSlot_ReturnValue;               // 0x0088(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UGridSlot*                              CallFunc_SlotAsGridSlot_ReturnValue_1;             // 0x0090(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_7;            // 0x0098(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_99[0x7];                                       // 0x0099(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue_2;           // 0x00A0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue;              // 0x00A8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance;             // 0x00B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x00B8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_B9[0x7];                                       // 0x00B9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_GetTrackName_ReturnValue;                 // 0x00C0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FName                                   CallFunc_Conv_StringToName_ReturnValue;            // 0x00D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GetCircuitInfo_ReturnValue;               // 0x00D8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_D9[0x7];                                       // 0x00D9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue_3;           // 0x00E0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FMargin                                CallFunc_GetOffsets_ReturnValue;                   // 0x00E8(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor)
	struct FMargin                                K2Node_SetFieldsInStruct_StructOut;                // 0x00F8(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor)
	struct FMargin                                K2Node_SetFieldsInStruct_StructOut_1;              // 0x0108(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage) == 0x000008, "Wrong alignment on WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage");
static_assert(sizeof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage) == 0x000118, "Wrong size on WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, EntryPoint) == 0x000000, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_MakeLiteralByte_ReturnValue) == 0x000004, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000005, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, K2Node_Event_IsDesignTime) == 0x000006, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, K2Node_Event_fullscreen_1) == 0x000007, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::K2Node_Event_fullscreen_1' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_Not_PreBool_ReturnValue) == 0x000008, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, K2Node_Event_fullscreen) == 0x000009, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::K2Node_Event_fullscreen' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_Conv_StringToText_ReturnValue) == 0x000010, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_MakeLiteralByte_ReturnValue_2) == 0x000028, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_MakeLiteralByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_MakeLiteralByte_ReturnValue_3) == 0x000029, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_MakeLiteralByte_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_MakeLiteralByte_ReturnValue_4) == 0x00002A, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_MakeLiteralByte_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_SlotAsCanvasSlot_ReturnValue) == 0x000030, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_SlotAsCanvasSlot_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_GetSize_ReturnValue) == 0x000038, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_GetSize_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_BreakVector2D_X) == 0x000040, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_BreakVector2D_Y) == 0x000044, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_MakeVector2D_ReturnValue) == 0x000048, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_MakeVector2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_SlotAsCanvasSlot_ReturnValue_1) == 0x000050, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_SlotAsCanvasSlot_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_GetPosition_ReturnValue) == 0x000058, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_GetPosition_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_MakeVector2D_ReturnValue_1) == 0x000060, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_MakeVector2D_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_BreakVector2D_X_1) == 0x000068, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_BreakVector2D_X_1' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_BreakVector2D_Y_1) == 0x00006C, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_BreakVector2D_Y_1' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_MakeVector2D_ReturnValue_2) == 0x000070, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_MakeVector2D_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_MakeVector2D_ReturnValue_3) == 0x000078, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_MakeVector2D_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_MakeLiteralByte_ReturnValue_5) == 0x000080, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_MakeLiteralByte_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_MakeLiteralByte_ReturnValue_6) == 0x000081, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_MakeLiteralByte_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_SlotAsGridSlot_ReturnValue) == 0x000088, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_SlotAsGridSlot_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_SlotAsGridSlot_ReturnValue_1) == 0x000090, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_SlotAsGridSlot_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_MakeLiteralByte_ReturnValue_7) == 0x000098, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_MakeLiteralByte_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_SlotAsCanvasSlot_ReturnValue_2) == 0x0000A0, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_SlotAsCanvasSlot_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_GetGameInstance_ReturnValue) == 0x0000A8, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_GetGameInstance_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, K2Node_DynamicCast_AsAc_Game_Instance) == 0x0000B0, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::K2Node_DynamicCast_AsAc_Game_Instance' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, K2Node_DynamicCast_bSuccess) == 0x0000B8, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_GetTrackName_ReturnValue) == 0x0000C0, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_GetTrackName_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_Conv_StringToName_ReturnValue) == 0x0000D0, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_Conv_StringToName_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_GetCircuitInfo_ReturnValue) == 0x0000D8, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_GetCircuitInfo_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_SlotAsCanvasSlot_ReturnValue_3) == 0x0000E0, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_SlotAsCanvasSlot_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, CallFunc_GetOffsets_ReturnValue) == 0x0000E8, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::CallFunc_GetOffsets_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, K2Node_SetFieldsInStruct_StructOut) == 0x0000F8, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::K2Node_SetFieldsInStruct_StructOut' has a wrong offset!");
static_assert(offsetof(WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage, K2Node_SetFieldsInStruct_StructOut_1) == 0x000108, "Member 'WDG_PitPage_C_ExecuteUbergraph_WDG_PitPage::K2Node_SetFieldsInStruct_StructOut_1' has a wrong offset!");

// Function WDG_PitPage.WDG_PitPage_C.OnSetShowCircuitName
// 0x0001 (0x0001 - 0x0000)
struct WDG_PitPage_C_OnSetShowCircuitName final
{
public:
	bool                                          fullscreen;                                        // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_PitPage_C_OnSetShowCircuitName) == 0x000001, "Wrong alignment on WDG_PitPage_C_OnSetShowCircuitName");
static_assert(sizeof(WDG_PitPage_C_OnSetShowCircuitName) == 0x000001, "Wrong size on WDG_PitPage_C_OnSetShowCircuitName");
static_assert(offsetof(WDG_PitPage_C_OnSetShowCircuitName, fullscreen) == 0x000000, "Member 'WDG_PitPage_C_OnSetShowCircuitName::fullscreen' has a wrong offset!");

// Function WDG_PitPage.WDG_PitPage_C.OnSetFullscreen
// 0x0001 (0x0001 - 0x0000)
struct WDG_PitPage_C_OnSetFullscreen final
{
public:
	bool                                          fullscreen;                                        // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_PitPage_C_OnSetFullscreen) == 0x000001, "Wrong alignment on WDG_PitPage_C_OnSetFullscreen");
static_assert(sizeof(WDG_PitPage_C_OnSetFullscreen) == 0x000001, "Wrong size on WDG_PitPage_C_OnSetFullscreen");
static_assert(offsetof(WDG_PitPage_C_OnSetFullscreen, fullscreen) == 0x000000, "Member 'WDG_PitPage_C_OnSetFullscreen::fullscreen' has a wrong offset!");

// Function WDG_PitPage.WDG_PitPage_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_PitPage_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_PitPage_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_PitPage_C_PreConstruct");
static_assert(sizeof(WDG_PitPage_C_PreConstruct) == 0x000001, "Wrong size on WDG_PitPage_C_PreConstruct");
static_assert(offsetof(WDG_PitPage_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_PitPage_C_PreConstruct::IsDesignTime' has a wrong offset!");

}

