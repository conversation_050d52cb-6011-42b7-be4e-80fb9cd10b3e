﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_UISessionTimer

#include "Basic.hpp"

#include "WDG_UISessionTimer_classes.hpp"
#include "WDG_UISessionTimer_parameters.hpp"


namespace SDK
{

// Function WDG_UISessionTimer.WDG_UISessionTimer_C.ExecuteUbergraph_WDG_UISessionTimer
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_UISessionTimer_C::ExecuteUbergraph_WDG_UISessionTimer(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_UISessionTimer_C", "ExecuteUbergraph_WDG_UISessionTimer");

	Params::WDG_UISessionTimer_C_ExecuteUbergraph_WDG_UISessionTimer Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_UISessionTimer.WDG_UISessionTimer_C.OnEverySecond
// (BlueprintCallable, BlueprintEvent)

void UWDG_UISessionTimer_C::OnEverySecond()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_UISessionTimer_C", "OnEverySecond");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_UISessionTimer.WDG_UISessionTimer_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_UISessionTimer_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_UISessionTimer_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_UISessionTimer.WDG_UISessionTimer_C.Destruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_UISessionTimer_C::Destruct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_UISessionTimer_C", "Destruct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_UISessionTimer.WDG_UISessionTimer_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_UISessionTimer_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_UISessionTimer_C", "PreConstruct");

	Params::WDG_UISessionTimer_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_UISessionTimer.WDG_UISessionTimer_C.IsTimeRemainingVisible
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool*                                   IsVisible_0                                            (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_UISessionTimer_C::IsTimeRemainingVisible(bool* IsVisible_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_UISessionTimer_C", "IsTimeRemainingVisible");

	Params::WDG_UISessionTimer_C_IsTimeRemainingVisible Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (IsVisible_0 != nullptr)
		*IsVisible_0 = Parms.IsVisible_0;
}

}

