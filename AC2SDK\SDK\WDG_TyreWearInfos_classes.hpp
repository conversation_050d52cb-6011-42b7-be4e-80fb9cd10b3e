﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TyreWearInfos

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_TyreWearInfos.WDG_TyreWearInfos_C
// 0x0040 (0x0668 - 0x0628)
class UWDG_TyreWearInfos_C final : public UTyreStausPanel
{
public:
	class UImage*                                 background;                                        // 0x0628(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             BlisterTitle;                                      // 0x0630(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             FlatSpotTitle;                                     // 0x0638(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             GrainTitle;                                        // 0x0640(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             NamedSlot_0;                                       // 0x0648(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             NamedSlot_1;                                       // 0x0650(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             TitleSlot;                                         // 0x0658(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             WearSlot;                                          // 0x0660(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_TyreWearInfos_C">();
	}
	static class UWDG_TyreWearInfos_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_TyreWearInfos_C>();
	}
};
static_assert(alignof(UWDG_TyreWearInfos_C) == 0x000008, "Wrong alignment on UWDG_TyreWearInfos_C");
static_assert(sizeof(UWDG_TyreWearInfos_C) == 0x000668, "Wrong size on UWDG_TyreWearInfos_C");
static_assert(offsetof(UWDG_TyreWearInfos_C, background) == 0x000628, "Member 'UWDG_TyreWearInfos_C::background' has a wrong offset!");
static_assert(offsetof(UWDG_TyreWearInfos_C, BlisterTitle) == 0x000630, "Member 'UWDG_TyreWearInfos_C::BlisterTitle' has a wrong offset!");
static_assert(offsetof(UWDG_TyreWearInfos_C, FlatSpotTitle) == 0x000638, "Member 'UWDG_TyreWearInfos_C::FlatSpotTitle' has a wrong offset!");
static_assert(offsetof(UWDG_TyreWearInfos_C, GrainTitle) == 0x000640, "Member 'UWDG_TyreWearInfos_C::GrainTitle' has a wrong offset!");
static_assert(offsetof(UWDG_TyreWearInfos_C, NamedSlot_0) == 0x000648, "Member 'UWDG_TyreWearInfos_C::NamedSlot_0' has a wrong offset!");
static_assert(offsetof(UWDG_TyreWearInfos_C, NamedSlot_1) == 0x000650, "Member 'UWDG_TyreWearInfos_C::NamedSlot_1' has a wrong offset!");
static_assert(offsetof(UWDG_TyreWearInfos_C, TitleSlot) == 0x000658, "Member 'UWDG_TyreWearInfos_C::TitleSlot' has a wrong offset!");
static_assert(offsetof(UWDG_TyreWearInfos_C, WearSlot) == 0x000660, "Member 'UWDG_TyreWearInfos_C::WearSlot' has a wrong offset!");

}

