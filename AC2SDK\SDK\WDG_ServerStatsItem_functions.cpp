﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ServerStatsItem

#include "Basic.hpp"

#include "WDG_ServerStatsItem_classes.hpp"
#include "WDG_ServerStatsItem_parameters.hpp"


namespace SDK
{

// Function WDG_ServerStatsItem.WDG_ServerStatsItem_C.SetText
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const class FText&                      text                                                   (BlueprintVisible, BlueprintReadOnly, Parm)
// const struct FSlateColor&               Color                                                  (BlueprintVisible, BlueprintReadOnly, Parm)
// bool                                    isHighlighted                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ServerStatsItem_C::SetText(const class FText& text, const struct FSlateColor& Color, bool isHighlighted)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ServerStatsItem_C", "SetText");

	Params::WDG_ServerStatsItem_C_SetText Parms{};

	Parms.text = std::move(text);
	Parms.Color = std::move(Color);
	Parms.isHighlighted = isHighlighted;

	UObject::ProcessEvent(Func, &Parms);
}

}

