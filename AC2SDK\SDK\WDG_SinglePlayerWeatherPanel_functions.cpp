﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SinglePlayerWeatherPanel

#include "Basic.hpp"

#include "WDG_SinglePlayerWeatherPanel_classes.hpp"
#include "WDG_SinglePlayerWeatherPanel_parameters.hpp"


namespace SDK
{

// Function WDG_SinglePlayerWeatherPanel.WDG_SinglePlayerWeatherPanel_C.ExecuteUbergraph_WDG_SinglePlayerWeatherPanel
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SinglePlayerWeatherPanel_C::ExecuteUbergraph_WDG_SinglePlayerWeatherPanel(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerWeatherPanel_C", "ExecuteUbergraph_WDG_SinglePlayerWeatherPanel");

	Params::WDG_SinglePlayerWeatherPanel_C_ExecuteUbergraph_WDG_SinglePlayerWeatherPanel Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SinglePlayerWeatherPanel.WDG_SinglePlayerWeatherPanel_C.BP_MouseLeave
// (Event, Public, BlueprintEvent)

void UWDG_SinglePlayerWeatherPanel_C::BP_MouseLeave()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerWeatherPanel_C", "BP_MouseLeave");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SinglePlayerWeatherPanel.WDG_SinglePlayerWeatherPanel_C.BP_MouseOver
// (Event, Public, BlueprintEvent)

void UWDG_SinglePlayerWeatherPanel_C::BP_MouseOver()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerWeatherPanel_C", "BP_MouseOver");

	UObject::ProcessEvent(Func, nullptr);
}

}

