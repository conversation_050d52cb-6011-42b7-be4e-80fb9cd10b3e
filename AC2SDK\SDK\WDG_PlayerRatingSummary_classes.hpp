﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PlayerRatingSummary

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "AC2_classes.hpp"
#include "Engine_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_PlayerRatingSummary.WDG_PlayerRatingSummary_C
// 0x01B0 (0x0790 - 0x05E0)
class UWDG_PlayerRatingSummary_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       OnComplete;                                        // 0x05E8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       LoadingPulse;                                      // 0x05F0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UVerticalBox*                           boxRatings;                                        // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                brdWrapper;                                        // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_RatingSummaryItem_C*               ratingCC;                                          // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_RatingSummaryItem_C*               ratingCN;                                          // 0x0610(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_RatingSummaryItem_C*               ratingCP;                                          // 0x0618(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_RatingSummaryItem_C*               ratingPC;                                          // 0x0620(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_RatingSummaryItem_C*               ratingRC;                                          // 0x0628(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_RatingSummaryItem_C*               ratingSA;                                          // 0x0630(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_RatingSummaryItem_C*               ratingTR;                                          // 0x0638(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtLoading;                                        // 0x0640(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtPlayerName;                                     // 0x0648(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	int32                                         SummaryRequestNo;                                  // 0x0650(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_654[0x4];                                      // 0x0654(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FOnlineServicesDriverSummary           LastValidResponse;                                 // 0x0658(0x0138)(Edit, BlueprintVisible, DisableEditOnInstance)

public:
	void ExecuteUbergraph_WDG_PlayerRatingSummary(int32 EntryPoint);
	void DriverSummaryReceived(const struct FOnlineServicesDriverSummary& summary);
	void OnAfterConstruct();
	void ToPercentWithoutSymbol(float Value, class FText* Return);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_PlayerRatingSummary_C">();
	}
	static class UWDG_PlayerRatingSummary_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_PlayerRatingSummary_C>();
	}
};
static_assert(alignof(UWDG_PlayerRatingSummary_C) == 0x000008, "Wrong alignment on UWDG_PlayerRatingSummary_C");
static_assert(sizeof(UWDG_PlayerRatingSummary_C) == 0x000790, "Wrong size on UWDG_PlayerRatingSummary_C");
static_assert(offsetof(UWDG_PlayerRatingSummary_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_PlayerRatingSummary_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerRatingSummary_C, OnComplete) == 0x0005E8, "Member 'UWDG_PlayerRatingSummary_C::OnComplete' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerRatingSummary_C, LoadingPulse) == 0x0005F0, "Member 'UWDG_PlayerRatingSummary_C::LoadingPulse' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerRatingSummary_C, boxRatings) == 0x0005F8, "Member 'UWDG_PlayerRatingSummary_C::boxRatings' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerRatingSummary_C, brdWrapper) == 0x000600, "Member 'UWDG_PlayerRatingSummary_C::brdWrapper' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerRatingSummary_C, ratingCC) == 0x000608, "Member 'UWDG_PlayerRatingSummary_C::ratingCC' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerRatingSummary_C, ratingCN) == 0x000610, "Member 'UWDG_PlayerRatingSummary_C::ratingCN' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerRatingSummary_C, ratingCP) == 0x000618, "Member 'UWDG_PlayerRatingSummary_C::ratingCP' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerRatingSummary_C, ratingPC) == 0x000620, "Member 'UWDG_PlayerRatingSummary_C::ratingPC' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerRatingSummary_C, ratingRC) == 0x000628, "Member 'UWDG_PlayerRatingSummary_C::ratingRC' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerRatingSummary_C, ratingSA) == 0x000630, "Member 'UWDG_PlayerRatingSummary_C::ratingSA' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerRatingSummary_C, ratingTR) == 0x000638, "Member 'UWDG_PlayerRatingSummary_C::ratingTR' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerRatingSummary_C, txtLoading) == 0x000640, "Member 'UWDG_PlayerRatingSummary_C::txtLoading' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerRatingSummary_C, txtPlayerName) == 0x000648, "Member 'UWDG_PlayerRatingSummary_C::txtPlayerName' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerRatingSummary_C, SummaryRequestNo) == 0x000650, "Member 'UWDG_PlayerRatingSummary_C::SummaryRequestNo' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerRatingSummary_C, LastValidResponse) == 0x000658, "Member 'UWDG_PlayerRatingSummary_C::LastValidResponse' has a wrong offset!");

}

