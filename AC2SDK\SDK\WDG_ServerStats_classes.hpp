﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ServerStats

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ServerStats.WDG_ServerStats_C
// 0x0058 (0x06B0 - 0x0658)
class UWDG_ServerStats_C final : public UAcRaceWidgetBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0658(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UBorder*                                background;                                        // 0x0660(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             textCPUAverage;                                    // 0x0668(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             textCPUMax;                                        // 0x0670(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             textPlayerPing;                                    // 0x0678(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             textQoSAvg;                                        // 0x0680(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             textQoSMin;                                        // 0x0688(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             textServerLatencyAverage;                          // 0x0690(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UVerticalBox*                           vboxMessages;                                      // 0x0698(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	struct FLinearColor                           BackgroundColor;                                   // 0x06A0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_ServerStats(int32 EntryPoint);
	void PreConstruct(bool IsDesignTime);
	void OnServerStatsChanged(const struct FMultiplayerServerStats& server_stats);
	void OnStartWidget(class UAcGameInstance* GameInstance, class AAcRaceGameMode* raceGameMode, class ACarAvatar* CarAvatar, const struct FHUDOptions& HUDOptions);
	void Update(const struct FMultiplayerServerStats& serverStats);
	void AddTextToVBox(class UVerticalBox*& vbox, const class FText& text, const struct FSlateColor& Color, bool isHighlighted);
	void SetStatValue(class UTextBlock* txtBlock, int32 Value, bool LowIsGood, int32 WarningColorStarts, int32 ErrorColorStarts);
	bool IsWidgetDefinitionEnabled(class UAcGameInstance* GameInstance, const struct FHUDOptions& HUDOptions);
	void ScaledNudgeByOffset(const struct FVector2D& currentPosition, class UCanvasPanelSlot* Slot_0, float HUDScale, float Offset);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ServerStats_C">();
	}
	static class UWDG_ServerStats_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ServerStats_C>();
	}
};
static_assert(alignof(UWDG_ServerStats_C) == 0x000008, "Wrong alignment on UWDG_ServerStats_C");
static_assert(sizeof(UWDG_ServerStats_C) == 0x0006B0, "Wrong size on UWDG_ServerStats_C");
static_assert(offsetof(UWDG_ServerStats_C, UberGraphFrame) == 0x000658, "Member 'UWDG_ServerStats_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_ServerStats_C, background) == 0x000660, "Member 'UWDG_ServerStats_C::background' has a wrong offset!");
static_assert(offsetof(UWDG_ServerStats_C, textCPUAverage) == 0x000668, "Member 'UWDG_ServerStats_C::textCPUAverage' has a wrong offset!");
static_assert(offsetof(UWDG_ServerStats_C, textCPUMax) == 0x000670, "Member 'UWDG_ServerStats_C::textCPUMax' has a wrong offset!");
static_assert(offsetof(UWDG_ServerStats_C, textPlayerPing) == 0x000678, "Member 'UWDG_ServerStats_C::textPlayerPing' has a wrong offset!");
static_assert(offsetof(UWDG_ServerStats_C, textQoSAvg) == 0x000680, "Member 'UWDG_ServerStats_C::textQoSAvg' has a wrong offset!");
static_assert(offsetof(UWDG_ServerStats_C, textQoSMin) == 0x000688, "Member 'UWDG_ServerStats_C::textQoSMin' has a wrong offset!");
static_assert(offsetof(UWDG_ServerStats_C, textServerLatencyAverage) == 0x000690, "Member 'UWDG_ServerStats_C::textServerLatencyAverage' has a wrong offset!");
static_assert(offsetof(UWDG_ServerStats_C, vboxMessages) == 0x000698, "Member 'UWDG_ServerStats_C::vboxMessages' has a wrong offset!");
static_assert(offsetof(UWDG_ServerStats_C, BackgroundColor) == 0x0006A0, "Member 'UWDG_ServerStats_C::BackgroundColor' has a wrong offset!");

}

