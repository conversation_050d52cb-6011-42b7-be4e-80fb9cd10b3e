﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SectorTimeItem

#include "Basic.hpp"

#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_SectorTimeItem.WDG_SectorTimeItem_C.ExecuteUbergraph_WDG_SectorTimeItem
// 0x00C8 (0x00C8 - 0x0000)
struct WDG_SectorTimeItem_C_ExecuteUbergraph_WDG_SectorTimeItem final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   CallFunc_MakeLiteralName_ReturnValue;              // 0x0004(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x000C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_D[0x3];                                        // 0x000D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class FName                                   CallFunc_MakeLiteralName_ReturnValue_1;            // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSlateFontInfo                         K2Node_MakeStruct_SlateFontInfo;                   // 0x0018(0x0058)(HasGetValueTypeHash)
	struct FSlateFontInfo                         K2Node_MakeStruct_SlateFontInfo_1;                 // 0x0070(0x0058)(HasGetValueTypeHash)
};
static_assert(alignof(WDG_SectorTimeItem_C_ExecuteUbergraph_WDG_SectorTimeItem) == 0x000008, "Wrong alignment on WDG_SectorTimeItem_C_ExecuteUbergraph_WDG_SectorTimeItem");
static_assert(sizeof(WDG_SectorTimeItem_C_ExecuteUbergraph_WDG_SectorTimeItem) == 0x0000C8, "Wrong size on WDG_SectorTimeItem_C_ExecuteUbergraph_WDG_SectorTimeItem");
static_assert(offsetof(WDG_SectorTimeItem_C_ExecuteUbergraph_WDG_SectorTimeItem, EntryPoint) == 0x000000, "Member 'WDG_SectorTimeItem_C_ExecuteUbergraph_WDG_SectorTimeItem::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_SectorTimeItem_C_ExecuteUbergraph_WDG_SectorTimeItem, CallFunc_MakeLiteralName_ReturnValue) == 0x000004, "Member 'WDG_SectorTimeItem_C_ExecuteUbergraph_WDG_SectorTimeItem::CallFunc_MakeLiteralName_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SectorTimeItem_C_ExecuteUbergraph_WDG_SectorTimeItem, K2Node_Event_IsDesignTime) == 0x00000C, "Member 'WDG_SectorTimeItem_C_ExecuteUbergraph_WDG_SectorTimeItem::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_SectorTimeItem_C_ExecuteUbergraph_WDG_SectorTimeItem, CallFunc_MakeLiteralName_ReturnValue_1) == 0x000010, "Member 'WDG_SectorTimeItem_C_ExecuteUbergraph_WDG_SectorTimeItem::CallFunc_MakeLiteralName_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SectorTimeItem_C_ExecuteUbergraph_WDG_SectorTimeItem, K2Node_MakeStruct_SlateFontInfo) == 0x000018, "Member 'WDG_SectorTimeItem_C_ExecuteUbergraph_WDG_SectorTimeItem::K2Node_MakeStruct_SlateFontInfo' has a wrong offset!");
static_assert(offsetof(WDG_SectorTimeItem_C_ExecuteUbergraph_WDG_SectorTimeItem, K2Node_MakeStruct_SlateFontInfo_1) == 0x000070, "Member 'WDG_SectorTimeItem_C_ExecuteUbergraph_WDG_SectorTimeItem::K2Node_MakeStruct_SlateFontInfo_1' has a wrong offset!");

// Function WDG_SectorTimeItem.WDG_SectorTimeItem_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_SectorTimeItem_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SectorTimeItem_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_SectorTimeItem_C_PreConstruct");
static_assert(sizeof(WDG_SectorTimeItem_C_PreConstruct) == 0x000001, "Wrong size on WDG_SectorTimeItem_C_PreConstruct");
static_assert(offsetof(WDG_SectorTimeItem_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_SectorTimeItem_C_PreConstruct::IsDesignTime' has a wrong offset!");

}

