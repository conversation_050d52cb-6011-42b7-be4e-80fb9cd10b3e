﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_StandingItemDoubleSize

#include "Basic.hpp"

#include "WDG_StandingItemDoubleSize_classes.hpp"
#include "WDG_StandingItemDoubleSize_parameters.hpp"


namespace SDK
{

// Function WDG_StandingItemDoubleSize.WDG_StandingItemDoubleSize_C.ExecuteUbergraph_WDG_StandingItemDoubleSize
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_StandingItemDoubleSize_C::ExecuteUbergraph_WDG_StandingItemDoubleSize(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_StandingItemDoubleSize_C", "ExecuteUbergraph_WDG_StandingItemDoubleSize");

	Params::WDG_StandingItemDoubleSize_C_ExecuteUbergraph_WDG_StandingItemDoubleSize Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_StandingItemDoubleSize.WDG_StandingItemDoubleSize_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_StandingItemDoubleSize_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_StandingItemDoubleSize_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}

}

