﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ReplayPanel

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ReplayPanel.WDG_ReplayPanel_C
// 0x0050 (0x0630 - 0x05E0)
class UWDG_ReplayPanel_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       OpacityAnim;                                       // 0x05E8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       ScaleAnim;                                         // 0x05F0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 HoverImageBox;                                     // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             IconSlot;                                          // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgDisabled;                                       // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 NormalImageBox;                                    // 0x0610(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             Replay_lbl;                                        // 0x0618(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             SubTitleSlot;                                      // 0x0620(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           WhiteText;                                         // 0x0628(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_ReplayPanel(int32 EntryPoint);
	void BP_MouseLeave();
	void BP_MouseOver();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ReplayPanel_C">();
	}
	static class UWDG_ReplayPanel_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ReplayPanel_C>();
	}
};
static_assert(alignof(UWDG_ReplayPanel_C) == 0x000008, "Wrong alignment on UWDG_ReplayPanel_C");
static_assert(sizeof(UWDG_ReplayPanel_C) == 0x000630, "Wrong size on UWDG_ReplayPanel_C");
static_assert(offsetof(UWDG_ReplayPanel_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_ReplayPanel_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayPanel_C, OpacityAnim) == 0x0005E8, "Member 'UWDG_ReplayPanel_C::OpacityAnim' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayPanel_C, ScaleAnim) == 0x0005F0, "Member 'UWDG_ReplayPanel_C::ScaleAnim' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayPanel_C, HoverImageBox) == 0x0005F8, "Member 'UWDG_ReplayPanel_C::HoverImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayPanel_C, IconSlot) == 0x000600, "Member 'UWDG_ReplayPanel_C::IconSlot' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayPanel_C, imgDisabled) == 0x000608, "Member 'UWDG_ReplayPanel_C::imgDisabled' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayPanel_C, NormalImageBox) == 0x000610, "Member 'UWDG_ReplayPanel_C::NormalImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayPanel_C, Replay_lbl) == 0x000618, "Member 'UWDG_ReplayPanel_C::Replay_lbl' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayPanel_C, SubTitleSlot) == 0x000620, "Member 'UWDG_ReplayPanel_C::SubTitleSlot' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayPanel_C, WhiteText) == 0x000628, "Member 'UWDG_ReplayPanel_C::WhiteText' has a wrong offset!");

}

