﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RatingDetailTCItem

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RatingDetailTCItem.WDG_RatingDetailTCItem_C
// 0x0010 (0x02A0 - 0x0290)
class UWDG_RatingDetailTCItem_C final : public URatingTCTrackItem
{
public:
	class UImage*                                 imgBase;                                           // 0x0290(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgStarsBackground;                                // 0x0298(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RatingDetailTCItem_C">();
	}
	static class UWDG_RatingDetailTCItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RatingDetailTCItem_C>();
	}
};
static_assert(alignof(UWDG_RatingDetailTCItem_C) == 0x000008, "Wrong alignment on UWDG_RatingDetailTCItem_C");
static_assert(sizeof(UWDG_RatingDetailTCItem_C) == 0x0002A0, "Wrong size on UWDG_RatingDetailTCItem_C");
static_assert(offsetof(UWDG_RatingDetailTCItem_C, imgBase) == 0x000290, "Member 'UWDG_RatingDetailTCItem_C::imgBase' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailTCItem_C, imgStarsBackground) == 0x000298, "Member 'UWDG_RatingDetailTCItem_C::imgStarsBackground' has a wrong offset!");

}

