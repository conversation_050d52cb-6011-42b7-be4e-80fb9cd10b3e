﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SpecialEventGenericPanel

#include "Basic.hpp"


namespace SDK::Params
{

// Function WDG_SpecialEventGenericPanel.WDG_SpecialEventGenericPanel_C.ExecuteUbergraph_WDG_SpecialEventGenericPanel
// 0x0004 (0x0004 - 0x0000)
struct WDG_SpecialEventGenericPanel_C_ExecuteUbergraph_WDG_SpecialEventGenericPanel final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SpecialEventGenericPanel_C_ExecuteUbergraph_WDG_SpecialEventGenericPanel) == 0x000004, "Wrong alignment on WDG_SpecialEventGenericPanel_C_ExecuteUbergraph_WDG_SpecialEventGenericPanel");
static_assert(sizeof(WDG_SpecialEventGenericPanel_C_ExecuteUbergraph_WDG_SpecialEventGenericPanel) == 0x000004, "Wrong size on WDG_SpecialEventGenericPanel_C_ExecuteUbergraph_WDG_SpecialEventGenericPanel");
static_assert(offsetof(WDG_SpecialEventGenericPanel_C_ExecuteUbergraph_WDG_SpecialEventGenericPanel, EntryPoint) == 0x000000, "Member 'WDG_SpecialEventGenericPanel_C_ExecuteUbergraph_WDG_SpecialEventGenericPanel::EntryPoint' has a wrong offset!");

}

