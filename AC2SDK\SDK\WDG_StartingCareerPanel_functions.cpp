﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_StartingCareerPanel

#include "Basic.hpp"

#include "WDG_StartingCareerPanel_classes.hpp"
#include "WDG_StartingCareerPanel_parameters.hpp"


namespace SDK
{

// Function WDG_StartingCareerPanel.WDG_StartingCareerPanel_C.ExecuteUbergraph_WDG_StartingCareerPanel
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_StartingCareerPanel_C::ExecuteUbergraph_WDG_StartingCareerPanel(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_StartingCareerPanel_C", "ExecuteUbergraph_WDG_StartingCareerPanel");

	Params::WDG_StartingCareerPanel_C_ExecuteUbergraph_WDG_StartingCareerPanel Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_StartingCareerPanel.WDG_StartingCareerPanel_C.BP_MouseLeave
// (Event, Public, BlueprintEvent)

void UWDG_StartingCareerPanel_C::BP_MouseLeave()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_StartingCareerPanel_C", "BP_MouseLeave");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_StartingCareerPanel.WDG_StartingCareerPanel_C.BP_MouseOver
// (Event, Public, BlueprintEvent)

void UWDG_StartingCareerPanel_C::BP_MouseOver()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_StartingCareerPanel_C", "BP_MouseOver");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_StartingCareerPanel.WDG_StartingCareerPanel_C.SetSlotColorText
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FLinearColor&              InColorAndOpacity_SpecifiedColor                       (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_StartingCareerPanel_C::SetSlotColorText(const struct FLinearColor& InColorAndOpacity_SpecifiedColor)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_StartingCareerPanel_C", "SetSlotColorText");

	Params::WDG_StartingCareerPanel_C_SetSlotColorText Parms{};

	Parms.InColorAndOpacity_SpecifiedColor = std::move(InColorAndOpacity_SpecifiedColor);

	UObject::ProcessEvent(Func, &Parms);
}

}

