﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TimeTable

#include "Basic.hpp"

#include "WDG_TimeTable_classes.hpp"
#include "WDG_TimeTable_parameters.hpp"


namespace SDK
{

// Function WDG_TimeTable.WDG_TimeTable_C.ExecuteUbergraph_WDG_TimeTable
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_TimeTable_C::ExecuteUbergraph_WDG_TimeTable(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TimeTable_C", "ExecuteUbergraph_WDG_TimeTable");

	Params::WDG_TimeTable_C_ExecuteUbergraph_WDG_TimeTable Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_TimeTable.WDG_TimeTable_C.OnPopulated
// (Event, Public, BlueprintEvent)

void UWDG_TimeTable_C::OnPopulated()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TimeTable_C", "OnPopulated");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_TimeTable.WDG_TimeTable_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_TimeTable_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TimeTable_C", "PreConstruct");

	Params::WDG_TimeTable_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}

}

