﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ViewOptionsPage

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ViewOptionsPage.WDG_ViewOptionsPage_C
// 0x0048 (0x07A0 - 0x0758)
class UWDG_ViewOptionsPage_C final : public UFovPage
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0758(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       HidePanel;                                         // 0x0760(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 Image_0;                                           // 0x0768(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgTopBackground;                                  // 0x0770(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_MainSelectorFitToParent_C*         MainSelector;                                      // 0x0778(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_BasicMessagePopup_C*               popupWarning;                                      // 0x0780(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	int32                                         TotalTimeS;                                        // 0x0788(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CurrentTimeS;                                      // 0x078C(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          SliderPicked_0;                                    // 0x0790(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	bool                                          CurrentPause;                                      // 0x0791(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	uint8                                         Pad_792[0x2];                                      // 0x0792(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         SliderPoint_BP;                                    // 0x0794(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         TimeMultiplier_BP;                                 // 0x0798(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         StartTimeS;                                        // 0x079C(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_ViewOptionsPage(int32 EntryPoint);
	void BP_StartPage();
	void BndEvt__popupWarning_K2Node_ComponentBoundEvent_1_OnNo__DelegateSignature();
	void BndEvt__WDG_BasicMessagePopup_K2Node_ComponentBoundEvent_0_OnYes__DelegateSignature();
	void OnUnsavedWarning();
	void OnSettingsChanged(bool SameAsOldSettings);
	void BP_OnMenuNavigationPreview(const EControllerActionType Input, bool isReleased);
	void BndEvt__MainSelector_K2Node_ComponentBoundEvent_2_OnChange__DelegateSignature(int32 New_Value);
	struct FEventReply OnPreviewKeyDown(const struct FGeometry& MyGeometry, const struct FKeyEvent& InKeyEvent);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ViewOptionsPage_C">();
	}
	static class UWDG_ViewOptionsPage_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ViewOptionsPage_C>();
	}
};
static_assert(alignof(UWDG_ViewOptionsPage_C) == 0x000008, "Wrong alignment on UWDG_ViewOptionsPage_C");
static_assert(sizeof(UWDG_ViewOptionsPage_C) == 0x0007A0, "Wrong size on UWDG_ViewOptionsPage_C");
static_assert(offsetof(UWDG_ViewOptionsPage_C, UberGraphFrame) == 0x000758, "Member 'UWDG_ViewOptionsPage_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_ViewOptionsPage_C, HidePanel) == 0x000760, "Member 'UWDG_ViewOptionsPage_C::HidePanel' has a wrong offset!");
static_assert(offsetof(UWDG_ViewOptionsPage_C, Image_0) == 0x000768, "Member 'UWDG_ViewOptionsPage_C::Image_0' has a wrong offset!");
static_assert(offsetof(UWDG_ViewOptionsPage_C, imgTopBackground) == 0x000770, "Member 'UWDG_ViewOptionsPage_C::imgTopBackground' has a wrong offset!");
static_assert(offsetof(UWDG_ViewOptionsPage_C, MainSelector) == 0x000778, "Member 'UWDG_ViewOptionsPage_C::MainSelector' has a wrong offset!");
static_assert(offsetof(UWDG_ViewOptionsPage_C, popupWarning) == 0x000780, "Member 'UWDG_ViewOptionsPage_C::popupWarning' has a wrong offset!");
static_assert(offsetof(UWDG_ViewOptionsPage_C, TotalTimeS) == 0x000788, "Member 'UWDG_ViewOptionsPage_C::TotalTimeS' has a wrong offset!");
static_assert(offsetof(UWDG_ViewOptionsPage_C, CurrentTimeS) == 0x00078C, "Member 'UWDG_ViewOptionsPage_C::CurrentTimeS' has a wrong offset!");
static_assert(offsetof(UWDG_ViewOptionsPage_C, SliderPicked_0) == 0x000790, "Member 'UWDG_ViewOptionsPage_C::SliderPicked_0' has a wrong offset!");
static_assert(offsetof(UWDG_ViewOptionsPage_C, CurrentPause) == 0x000791, "Member 'UWDG_ViewOptionsPage_C::CurrentPause' has a wrong offset!");
static_assert(offsetof(UWDG_ViewOptionsPage_C, SliderPoint_BP) == 0x000794, "Member 'UWDG_ViewOptionsPage_C::SliderPoint_BP' has a wrong offset!");
static_assert(offsetof(UWDG_ViewOptionsPage_C, TimeMultiplier_BP) == 0x000798, "Member 'UWDG_ViewOptionsPage_C::TimeMultiplier_BP' has a wrong offset!");
static_assert(offsetof(UWDG_ViewOptionsPage_C, StartTimeS) == 0x00079C, "Member 'UWDG_ViewOptionsPage_C::StartTimeS' has a wrong offset!");

}

