﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TrackMap

#include "Basic.hpp"

#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_TrackMap.WDG_TrackMap_C.ExecuteUbergraph_WDG_TrackMap
// 0x0020 (0x0020 - 0x0000)
struct WDG_TrackMap_C_ExecuteUbergraph_WDG_TrackMap final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue;             // 0x0008(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue_1;           // 0x0010(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue_2;           // 0x0018(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_TrackMap_C_ExecuteUbergraph_WDG_TrackMap) == 0x000008, "Wrong alignment on WDG_TrackMap_C_ExecuteUbergraph_WDG_TrackMap");
static_assert(sizeof(WDG_TrackMap_C_ExecuteUbergraph_WDG_TrackMap) == 0x000020, "Wrong size on WDG_TrackMap_C_ExecuteUbergraph_WDG_TrackMap");
static_assert(offsetof(WDG_TrackMap_C_ExecuteUbergraph_WDG_TrackMap, EntryPoint) == 0x000000, "Member 'WDG_TrackMap_C_ExecuteUbergraph_WDG_TrackMap::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_TrackMap_C_ExecuteUbergraph_WDG_TrackMap, CallFunc_SlotAsCanvasSlot_ReturnValue) == 0x000008, "Member 'WDG_TrackMap_C_ExecuteUbergraph_WDG_TrackMap::CallFunc_SlotAsCanvasSlot_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TrackMap_C_ExecuteUbergraph_WDG_TrackMap, CallFunc_SlotAsCanvasSlot_ReturnValue_1) == 0x000010, "Member 'WDG_TrackMap_C_ExecuteUbergraph_WDG_TrackMap::CallFunc_SlotAsCanvasSlot_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_TrackMap_C_ExecuteUbergraph_WDG_TrackMap, CallFunc_SlotAsCanvasSlot_ReturnValue_2) == 0x000018, "Member 'WDG_TrackMap_C_ExecuteUbergraph_WDG_TrackMap::CallFunc_SlotAsCanvasSlot_ReturnValue_2' has a wrong offset!");

// Function WDG_TrackMap.WDG_TrackMap_C.IsWidgetDefinitionEnabled
// 0x00D0 (0x00D0 - 0x0000)
struct WDG_TrackMap_C_IsWidgetDefinitionEnabled final
{
public:
	class UAcGameInstance*                        GameInstance;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FHUDOptions                            HUDOptions;                                        // 0x0008(0x00C0)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)
	bool                                          ReturnValue;                                       // 0x00C8(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsWidgetDefinitionEnabled_ReturnValue;    // 0x00C9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_TrackMap_C_IsWidgetDefinitionEnabled) == 0x000008, "Wrong alignment on WDG_TrackMap_C_IsWidgetDefinitionEnabled");
static_assert(sizeof(WDG_TrackMap_C_IsWidgetDefinitionEnabled) == 0x0000D0, "Wrong size on WDG_TrackMap_C_IsWidgetDefinitionEnabled");
static_assert(offsetof(WDG_TrackMap_C_IsWidgetDefinitionEnabled, GameInstance) == 0x000000, "Member 'WDG_TrackMap_C_IsWidgetDefinitionEnabled::GameInstance' has a wrong offset!");
static_assert(offsetof(WDG_TrackMap_C_IsWidgetDefinitionEnabled, HUDOptions) == 0x000008, "Member 'WDG_TrackMap_C_IsWidgetDefinitionEnabled::HUDOptions' has a wrong offset!");
static_assert(offsetof(WDG_TrackMap_C_IsWidgetDefinitionEnabled, ReturnValue) == 0x0000C8, "Member 'WDG_TrackMap_C_IsWidgetDefinitionEnabled::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TrackMap_C_IsWidgetDefinitionEnabled, CallFunc_IsWidgetDefinitionEnabled_ReturnValue) == 0x0000C9, "Member 'WDG_TrackMap_C_IsWidgetDefinitionEnabled::CallFunc_IsWidgetDefinitionEnabled_ReturnValue' has a wrong offset!");

}

