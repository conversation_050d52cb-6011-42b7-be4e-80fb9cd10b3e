﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_NewsItem

#include "Basic.hpp"

#include "WDG_NewsItem_classes.hpp"
#include "WDG_NewsItem_parameters.hpp"


namespace SDK
{

// Function WDG_NewsItem.WDG_NewsItem_C.ExecuteUbergraph_WDG_NewsItem
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_NewsItem_C::ExecuteUbergraph_WDG_NewsItem(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_NewsItem_C", "ExecuteUbergraph_WDG_NewsItem");

	Params::WDG_NewsItem_C_ExecuteUbergraph_WDG_NewsItem Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_NewsItem.WDG_NewsItem_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_NewsItem_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_NewsItem_C", "PreConstruct");

	Params::WDG_NewsItem_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_NewsItem.WDG_NewsItem_C.BndEvt__btnReadMore_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UWDG_GenericBarItem_C*            Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_NewsItem_C::BndEvt__btnReadMore_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature(class UWDG_GenericBarItem_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_NewsItem_C", "BndEvt__btnReadMore_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature");

	Params::WDG_NewsItem_C_BndEvt__btnReadMore_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature Parms{};

	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}

}

