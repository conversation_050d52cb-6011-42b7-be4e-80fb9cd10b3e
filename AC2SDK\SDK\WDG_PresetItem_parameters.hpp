﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PresetItem

#include "Basic.hpp"

#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_PresetItem.WDG_PresetItem_C.ExecuteUbergraph_WDG_PresetItem
// 0x0120 (0x0120 - 0x0000)
struct WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0006(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_7[0x1];                                        // 0x0007(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void(class UAcPanelBase* panel, bool mouse_over)> K2Node_CreateDelegate_OutputDelegate; // 0x0008(0x0010)(ZeroConstructor, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0019(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1A[0x6];                                       // 0x001A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPanelBase*                           K2Node_ComponentBoundEvent_Panel;                  // 0x0020(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_ComponentBoundEvent_mouse_over;             // 0x0028(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_29[0x7];                                       // 0x0029(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPanelBase*                           K2Node_CustomEvent_Panel;                          // 0x0030(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_CustomEvent_mouse_over;                     // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_39[0x7];                                       // 0x0039(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPanelBase*                           K2Node_DynamicCast_AsAc_Panel_Base;                // 0x0040(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0048(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_49[0x3];                                       // 0x0049(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_GetChildrenCount_ReturnValue;             // 0x004C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x0050(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_HasFocusedDescendants_ReturnValue;        // 0x0054(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_55[0x3];                                       // 0x0055(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FFocusEvent                            K2Node_Event_InFocusEvent_1;                       // 0x0058(0x0008)(NoDestructor)
	struct FFocusEvent                            K2Node_Event_InFocusEvent;                         // 0x0060(0x0008)(NoDestructor)
	bool                                          K2Node_Event_highlighted;                          // 0x0068(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_2;            // 0x0069(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6A[0x6];                                       // 0x006A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Conv_NameToString_ReturnValue;            // 0x0070(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	int32                                         Temp_int_Variable;                                 // 0x0080(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_84[0x4];                                       // 0x0084(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                CallFunc_GetChildAt_ReturnValue;                   // 0x0088(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GetIsEnabled_ReturnValue;                 // 0x0090(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsVisible_ReturnValue;                    // 0x0091(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_92[0x2];                                       // 0x0092(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0094(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0098(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x0099(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x009A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_3;            // 0x009B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_9C[0x4];                                       // 0x009C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Conv_TextToString_ReturnValue;            // 0x00A0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_IntToString_ReturnValue;             // 0x00B0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_NameToString_ReturnValue_1;          // 0x00C0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue;                // 0x00D0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_1;              // 0x00E0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_2;              // 0x00F0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_3;              // 0x0100(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_4;              // 0x0110(0x0010)(ZeroConstructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem) == 0x000008, "Wrong alignment on WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem");
static_assert(sizeof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem) == 0x000120, "Wrong size on WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, EntryPoint) == 0x000000, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, CallFunc_MakeLiteralByte_ReturnValue) == 0x000004, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, Temp_bool_Variable) == 0x000005, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, CallFunc_Not_PreBool_ReturnValue) == 0x000006, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, K2Node_CreateDelegate_OutputDelegate) == 0x000008, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000018, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, K2Node_Event_IsDesignTime) == 0x000019, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, K2Node_ComponentBoundEvent_Panel) == 0x000020, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::K2Node_ComponentBoundEvent_Panel' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, K2Node_ComponentBoundEvent_mouse_over) == 0x000028, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::K2Node_ComponentBoundEvent_mouse_over' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, K2Node_CustomEvent_Panel) == 0x000030, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::K2Node_CustomEvent_Panel' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, K2Node_CustomEvent_mouse_over) == 0x000038, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::K2Node_CustomEvent_mouse_over' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, K2Node_DynamicCast_AsAc_Panel_Base) == 0x000040, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::K2Node_DynamicCast_AsAc_Panel_Base' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, K2Node_DynamicCast_bSuccess) == 0x000048, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, CallFunc_GetChildrenCount_ReturnValue) == 0x00004C, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::CallFunc_GetChildrenCount_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, CallFunc_Subtract_IntInt_ReturnValue) == 0x000050, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, CallFunc_HasFocusedDescendants_ReturnValue) == 0x000054, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::CallFunc_HasFocusedDescendants_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, K2Node_Event_InFocusEvent_1) == 0x000058, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::K2Node_Event_InFocusEvent_1' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, K2Node_Event_InFocusEvent) == 0x000060, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::K2Node_Event_InFocusEvent' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, K2Node_Event_highlighted) == 0x000068, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::K2Node_Event_highlighted' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, CallFunc_MakeLiteralByte_ReturnValue_2) == 0x000069, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::CallFunc_MakeLiteralByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, CallFunc_Conv_NameToString_ReturnValue) == 0x000070, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::CallFunc_Conv_NameToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, Temp_int_Variable) == 0x000080, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, CallFunc_GetChildAt_ReturnValue) == 0x000088, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::CallFunc_GetChildAt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, CallFunc_GetIsEnabled_ReturnValue) == 0x000090, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::CallFunc_GetIsEnabled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, CallFunc_IsVisible_ReturnValue) == 0x000091, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::CallFunc_IsVisible_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, CallFunc_Add_IntInt_ReturnValue) == 0x000094, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, CallFunc_BooleanAND_ReturnValue) == 0x000098, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, CallFunc_LessEqual_IntInt_ReturnValue) == 0x000099, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, CallFunc_BooleanAND_ReturnValue_1) == 0x00009A, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, CallFunc_MakeLiteralByte_ReturnValue_3) == 0x00009B, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::CallFunc_MakeLiteralByte_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, CallFunc_Conv_TextToString_ReturnValue) == 0x0000A0, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::CallFunc_Conv_TextToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, CallFunc_Conv_IntToString_ReturnValue) == 0x0000B0, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::CallFunc_Conv_IntToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, CallFunc_Conv_NameToString_ReturnValue_1) == 0x0000C0, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::CallFunc_Conv_NameToString_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, CallFunc_Concat_StrStr_ReturnValue) == 0x0000D0, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::CallFunc_Concat_StrStr_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, CallFunc_Concat_StrStr_ReturnValue_1) == 0x0000E0, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::CallFunc_Concat_StrStr_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, CallFunc_Concat_StrStr_ReturnValue_2) == 0x0000F0, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::CallFunc_Concat_StrStr_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, CallFunc_Concat_StrStr_ReturnValue_3) == 0x000100, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::CallFunc_Concat_StrStr_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem, CallFunc_Concat_StrStr_ReturnValue_4) == 0x000110, "Member 'WDG_PresetItem_C_ExecuteUbergraph_WDG_PresetItem::CallFunc_Concat_StrStr_ReturnValue_4' has a wrong offset!");

// Function WDG_PresetItem.WDG_PresetItem_C.BP_SetHighlight
// 0x0001 (0x0001 - 0x0000)
struct WDG_PresetItem_C_BP_SetHighlight final
{
public:
	bool                                          highlighted;                                       // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_PresetItem_C_BP_SetHighlight) == 0x000001, "Wrong alignment on WDG_PresetItem_C_BP_SetHighlight");
static_assert(sizeof(WDG_PresetItem_C_BP_SetHighlight) == 0x000001, "Wrong size on WDG_PresetItem_C_BP_SetHighlight");
static_assert(offsetof(WDG_PresetItem_C_BP_SetHighlight, highlighted) == 0x000000, "Member 'WDG_PresetItem_C_BP_SetHighlight::highlighted' has a wrong offset!");

// Function WDG_PresetItem.WDG_PresetItem_C.OnAddedToFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_PresetItem_C_OnAddedToFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_PresetItem_C_OnAddedToFocusPath) == 0x000004, "Wrong alignment on WDG_PresetItem_C_OnAddedToFocusPath");
static_assert(sizeof(WDG_PresetItem_C_OnAddedToFocusPath) == 0x000008, "Wrong size on WDG_PresetItem_C_OnAddedToFocusPath");
static_assert(offsetof(WDG_PresetItem_C_OnAddedToFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_PresetItem_C_OnAddedToFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_PresetItem.WDG_PresetItem_C.OnRemovedFromFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_PresetItem_C_OnRemovedFromFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_PresetItem_C_OnRemovedFromFocusPath) == 0x000004, "Wrong alignment on WDG_PresetItem_C_OnRemovedFromFocusPath");
static_assert(sizeof(WDG_PresetItem_C_OnRemovedFromFocusPath) == 0x000008, "Wrong size on WDG_PresetItem_C_OnRemovedFromFocusPath");
static_assert(offsetof(WDG_PresetItem_C_OnRemovedFromFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_PresetItem_C_OnRemovedFromFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_PresetItem.WDG_PresetItem_C.OnMouseFocus_Event_0
// 0x0010 (0x0010 - 0x0000)
struct WDG_PresetItem_C_OnMouseFocus_Event_0 final
{
public:
	class UAcPanelBase*                           panel;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          mouse_over;                                        // 0x0008(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_PresetItem_C_OnMouseFocus_Event_0) == 0x000008, "Wrong alignment on WDG_PresetItem_C_OnMouseFocus_Event_0");
static_assert(sizeof(WDG_PresetItem_C_OnMouseFocus_Event_0) == 0x000010, "Wrong size on WDG_PresetItem_C_OnMouseFocus_Event_0");
static_assert(offsetof(WDG_PresetItem_C_OnMouseFocus_Event_0, panel) == 0x000000, "Member 'WDG_PresetItem_C_OnMouseFocus_Event_0::panel' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_OnMouseFocus_Event_0, mouse_over) == 0x000008, "Member 'WDG_PresetItem_C_OnMouseFocus_Event_0::mouse_over' has a wrong offset!");

// Function WDG_PresetItem.WDG_PresetItem_C.BndEvt__btnDel_K2Node_ComponentBoundEvent_1_OnAcPanelMouseEvent__DelegateSignature
// 0x0010 (0x0010 - 0x0000)
struct WDG_PresetItem_C_BndEvt__btnDel_K2Node_ComponentBoundEvent_1_OnAcPanelMouseEvent__DelegateSignature final
{
public:
	class UAcPanelBase*                           panel;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          mouse_over;                                        // 0x0008(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_PresetItem_C_BndEvt__btnDel_K2Node_ComponentBoundEvent_1_OnAcPanelMouseEvent__DelegateSignature) == 0x000008, "Wrong alignment on WDG_PresetItem_C_BndEvt__btnDel_K2Node_ComponentBoundEvent_1_OnAcPanelMouseEvent__DelegateSignature");
static_assert(sizeof(WDG_PresetItem_C_BndEvt__btnDel_K2Node_ComponentBoundEvent_1_OnAcPanelMouseEvent__DelegateSignature) == 0x000010, "Wrong size on WDG_PresetItem_C_BndEvt__btnDel_K2Node_ComponentBoundEvent_1_OnAcPanelMouseEvent__DelegateSignature");
static_assert(offsetof(WDG_PresetItem_C_BndEvt__btnDel_K2Node_ComponentBoundEvent_1_OnAcPanelMouseEvent__DelegateSignature, panel) == 0x000000, "Member 'WDG_PresetItem_C_BndEvt__btnDel_K2Node_ComponentBoundEvent_1_OnAcPanelMouseEvent__DelegateSignature::panel' has a wrong offset!");
static_assert(offsetof(WDG_PresetItem_C_BndEvt__btnDel_K2Node_ComponentBoundEvent_1_OnAcPanelMouseEvent__DelegateSignature, mouse_over) == 0x000008, "Member 'WDG_PresetItem_C_BndEvt__btnDel_K2Node_ComponentBoundEvent_1_OnAcPanelMouseEvent__DelegateSignature::mouse_over' has a wrong offset!");

// Function WDG_PresetItem.WDG_PresetItem_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_PresetItem_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_PresetItem_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_PresetItem_C_PreConstruct");
static_assert(sizeof(WDG_PresetItem_C_PreConstruct) == 0x000001, "Wrong size on WDG_PresetItem_C_PreConstruct");
static_assert(offsetof(WDG_PresetItem_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_PresetItem_C_PreConstruct::IsDesignTime' has a wrong offset!");

}

