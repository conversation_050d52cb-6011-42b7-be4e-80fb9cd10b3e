﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SpecialEventsLeaderboardLine

#include "Basic.hpp"


namespace SDK::Params
{

// Function WDG_SpecialEventsLeaderboardLine.WDG_SpecialEventsLeaderboardLine_C.ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine
// 0x0070 (0x0070 - 0x0000)
struct WDG_SpecialEventsLeaderboardLine_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6[0x2];                                        // 0x0006(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Int32ToLaptimeText_ReturnValue;           // 0x0008(0x0018)()
	class FText                                   CallFunc_Conv_IntToText_ReturnValue;               // 0x0020(0x0018)()
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x0038(0x0018)()
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_1;             // 0x0050(0x0018)()
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x0068(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SpecialEventsLeaderboardLine_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine) == 0x000008, "Wrong alignment on WDG_SpecialEventsLeaderboardLine_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine");
static_assert(sizeof(WDG_SpecialEventsLeaderboardLine_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine) == 0x000070, "Wrong size on WDG_SpecialEventsLeaderboardLine_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine");
static_assert(offsetof(WDG_SpecialEventsLeaderboardLine_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine, EntryPoint) == 0x000000, "Member 'WDG_SpecialEventsLeaderboardLine_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsLeaderboardLine_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine, CallFunc_MakeLiteralByte_ReturnValue) == 0x000004, "Member 'WDG_SpecialEventsLeaderboardLine_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsLeaderboardLine_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000005, "Member 'WDG_SpecialEventsLeaderboardLine_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsLeaderboardLine_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine, CallFunc_Int32ToLaptimeText_ReturnValue) == 0x000008, "Member 'WDG_SpecialEventsLeaderboardLine_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine::CallFunc_Int32ToLaptimeText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsLeaderboardLine_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine, CallFunc_Conv_IntToText_ReturnValue) == 0x000020, "Member 'WDG_SpecialEventsLeaderboardLine_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine::CallFunc_Conv_IntToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsLeaderboardLine_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine, CallFunc_Conv_StringToText_ReturnValue) == 0x000038, "Member 'WDG_SpecialEventsLeaderboardLine_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsLeaderboardLine_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine, CallFunc_Conv_IntToText_ReturnValue_1) == 0x000050, "Member 'WDG_SpecialEventsLeaderboardLine_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine::CallFunc_Conv_IntToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsLeaderboardLine_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine, CallFunc_Greater_IntInt_ReturnValue) == 0x000068, "Member 'WDG_SpecialEventsLeaderboardLine_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");

}

