﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ServerStatsItem

#include "Basic.hpp"

#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ServerStatsItem.WDG_ServerStatsItem_C
// 0x0010 (0x0270 - 0x0260)
class UWDG_ServerStatsItem_C final : public UUserWidget
{
public:
	class UBorder*                                Border_50;                                         // 0x0260(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_20;                                      // 0x0268(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void SetText(const class FText& text, const struct FSlateColor& Color, bool isHighlighted);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ServerStatsItem_C">();
	}
	static class UWDG_ServerStatsItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ServerStatsItem_C>();
	}
};
static_assert(alignof(UWDG_ServerStatsItem_C) == 0x000008, "Wrong alignment on UWDG_ServerStatsItem_C");
static_assert(sizeof(UWDG_ServerStatsItem_C) == 0x000270, "Wrong size on UWDG_ServerStatsItem_C");
static_assert(offsetof(UWDG_ServerStatsItem_C, Border_50) == 0x000260, "Member 'UWDG_ServerStatsItem_C::Border_50' has a wrong offset!");
static_assert(offsetof(UWDG_ServerStatsItem_C, TextBlock_20) == 0x000268, "Member 'UWDG_ServerStatsItem_C::TextBlock_20' has a wrong offset!");

}

