﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ReplayItem

#include "Basic.hpp"

#include "WDG_ReplayItem_classes.hpp"
#include "WDG_ReplayItem_parameters.hpp"


namespace SDK
{

// Function WDG_ReplayItem.WDG_ReplayItem_C.ExecuteUbergraph_WDG_ReplayItem
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ReplayItem_C::ExecuteUbergraph_WDG_ReplayItem(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayItem_C", "ExecuteUbergraph_WDG_ReplayItem");

	Params::WDG_ReplayItem_C_ExecuteUbergraph_WDG_ReplayItem Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ReplayItem.WDG_ReplayItem_C.BndEvt__btnSave_K2Node_ComponentBoundEvent_3_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayItem_C::BndEvt__btnSave_K2Node_ComponentBoundEvent_3_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayItem_C", "BndEvt__btnSave_K2Node_ComponentBoundEvent_3_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayItem.WDG_ReplayItem_C.BndEvt__btnDel_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayItem_C::BndEvt__btnDel_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayItem_C", "BndEvt__btnDel_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayItem.WDG_ReplayItem_C.BndEvt__btnPlay_K2Node_ComponentBoundEvent_5_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayItem_C::BndEvt__btnPlay_K2Node_ComponentBoundEvent_5_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayItem_C", "BndEvt__btnPlay_K2Node_ComponentBoundEvent_5_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayItem.WDG_ReplayItem_C.BP_SetHighlight
// (Event, Public, BlueprintEvent)
// Parameters:
// bool                                    highlighted                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ReplayItem_C::BP_SetHighlight(bool highlighted)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayItem_C", "BP_SetHighlight");

	Params::WDG_ReplayItem_C_BP_SetHighlight Parms{};

	Parms.highlighted = highlighted;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ReplayItem.WDG_ReplayItem_C.BP_MouseOver
// (Event, Public, BlueprintEvent)

void UWDG_ReplayItem_C::BP_MouseOver()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayItem_C", "BP_MouseOver");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayItem.WDG_ReplayItem_C.BP_MouseLeave
// (Event, Public, BlueprintEvent)

void UWDG_ReplayItem_C::BP_MouseLeave()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayItem_C", "BP_MouseLeave");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayItem.WDG_ReplayItem_C.OnAddedToFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_ReplayItem_C::OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayItem_C", "OnAddedToFocusPath");

	Params::WDG_ReplayItem_C_OnAddedToFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ReplayItem.WDG_ReplayItem_C.OnRemovedFromFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_ReplayItem_C::OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayItem_C", "OnRemovedFromFocusPath");

	Params::WDG_ReplayItem_C_OnRemovedFromFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ReplayItem.WDG_ReplayItem_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_ReplayItem_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayItem_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}

}

