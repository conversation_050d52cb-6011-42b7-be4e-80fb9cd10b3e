﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SinglePlayerPage

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "InputCore_structs.hpp"
#include "UMG_structs.hpp"
#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.ExecuteUbergraph_WDG_SinglePlayerPage
// 0x0108 (0x0108 - 0x0000)
struct WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESeasonType                                   K2Node_ComponentBoundEvent_Season;                 // 0x0004(0x0001)(ZeroConstructor, <PERSON><PERSON><PERSON><PERSON>ld<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	uint8                                         Pad_5[0x3];                                        // 0x0005(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void(ESeasonType new_season)>       K2Node_CreateDelegate_OutputDelegate;              // 0x0008(0x0010)(ZeroConstructor, NoDestructor)
	bool                                          CallFunc_SetFocusOnCurrentPanel_ReturnValue;       // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	EContentType                                  K2Node_ComponentBoundEvent_ContentId;              // 0x0019(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1A[0x6];                                       // 0x001A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue;               // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue_1;             // 0x0028(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcPageBase*                            CallFunc_GoToPage_ReturnValue;                     // 0x0030(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue_2;             // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue;              // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance;             // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0050(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_51[0x3];                                       // 0x0051(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTrackStatus                           CallFunc_getTrackStatusForUI_ReturnValue;          // 0x0054(0x001C)(NoDestructor)
	struct FWeatherData                           CallFunc_getWeatherDataForUI_ReturnValue;          // 0x0070(0x001C)(NoDestructor)
	uint8                                         Pad_8C[0x4];                                       // 0x008C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Conv_FloatToString_ReturnValue;           // 0x0090(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_IntToString_ReturnValue;             // 0x00A0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue;                // 0x00B0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_1;              // 0x00C0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_2;              // 0x00D0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_3;              // 0x00E0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_findPlaybleCarForEventType_ReturnValue;   // 0x00F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	ESeasonType                                   K2Node_CustomEvent_new_season;                     // 0x00F1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EContentType                                  CallFunc_SeasonToContentType_output;               // 0x00F2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_ValidateSeason_ReturnValue;               // 0x00F3(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	EContentType                                  K2Node_Event_content_type;                         // 0x00F4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESeasonType                                   K2Node_ComponentBoundEvent_Season_11;              // 0x00F5(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESeasonType                                   K2Node_ComponentBoundEvent_Season_10;              // 0x00F6(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESeasonType                                   K2Node_ComponentBoundEvent_Season_9;               // 0x00F7(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESeasonType                                   K2Node_ComponentBoundEvent_Season_8;               // 0x00F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESeasonType                                   K2Node_ComponentBoundEvent_Season_7;               // 0x00F9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESeasonType                                   K2Node_ComponentBoundEvent_Season_6;               // 0x00FA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESeasonType                                   K2Node_ComponentBoundEvent_Season_5;               // 0x00FB(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESeasonType                                   K2Node_ComponentBoundEvent_Season_4;               // 0x00FC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_ValidateSeason_ReturnValue_1;             // 0x00FD(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	EContentType                                  CallFunc_SeasonToContentType_output_1;             // 0x00FE(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESeasonType                                   K2Node_ComponentBoundEvent_Season_3;               // 0x00FF(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Array_Contains_ReturnValue;               // 0x0100(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	ESeasonType                                   K2Node_ComponentBoundEvent_Season_2;               // 0x0101(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x0102(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	ESeasonType                                   K2Node_ComponentBoundEvent_Season_1;               // 0x0103(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage) == 0x000008, "Wrong alignment on WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage");
static_assert(sizeof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage) == 0x000108, "Wrong size on WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, EntryPoint) == 0x000000, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, K2Node_ComponentBoundEvent_Season) == 0x000004, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::K2Node_ComponentBoundEvent_Season' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, K2Node_CreateDelegate_OutputDelegate) == 0x000008, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, CallFunc_SetFocusOnCurrentPanel_ReturnValue) == 0x000018, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::CallFunc_SetFocusOnCurrentPanel_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, K2Node_ComponentBoundEvent_ContentId) == 0x000019, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::K2Node_ComponentBoundEvent_ContentId' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, CallFunc_GetMenuManager_ReturnValue) == 0x000020, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::CallFunc_GetMenuManager_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, CallFunc_GetMenuManager_ReturnValue_1) == 0x000028, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::CallFunc_GetMenuManager_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, CallFunc_GoToPage_ReturnValue) == 0x000030, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::CallFunc_GoToPage_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, CallFunc_GetMenuManager_ReturnValue_2) == 0x000038, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::CallFunc_GetMenuManager_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, CallFunc_GetGameInstance_ReturnValue) == 0x000040, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::CallFunc_GetGameInstance_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, K2Node_DynamicCast_AsAc_Game_Instance) == 0x000048, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::K2Node_DynamicCast_AsAc_Game_Instance' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, K2Node_DynamicCast_bSuccess) == 0x000050, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, CallFunc_getTrackStatusForUI_ReturnValue) == 0x000054, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::CallFunc_getTrackStatusForUI_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, CallFunc_getWeatherDataForUI_ReturnValue) == 0x000070, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::CallFunc_getWeatherDataForUI_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, CallFunc_Conv_FloatToString_ReturnValue) == 0x000090, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::CallFunc_Conv_FloatToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, CallFunc_Conv_IntToString_ReturnValue) == 0x0000A0, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::CallFunc_Conv_IntToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, CallFunc_Concat_StrStr_ReturnValue) == 0x0000B0, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::CallFunc_Concat_StrStr_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, CallFunc_Concat_StrStr_ReturnValue_1) == 0x0000C0, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::CallFunc_Concat_StrStr_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, CallFunc_Concat_StrStr_ReturnValue_2) == 0x0000D0, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::CallFunc_Concat_StrStr_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, CallFunc_Concat_StrStr_ReturnValue_3) == 0x0000E0, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::CallFunc_Concat_StrStr_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, CallFunc_findPlaybleCarForEventType_ReturnValue) == 0x0000F0, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::CallFunc_findPlaybleCarForEventType_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, K2Node_CustomEvent_new_season) == 0x0000F1, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::K2Node_CustomEvent_new_season' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, CallFunc_SeasonToContentType_output) == 0x0000F2, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::CallFunc_SeasonToContentType_output' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, CallFunc_ValidateSeason_ReturnValue) == 0x0000F3, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::CallFunc_ValidateSeason_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, K2Node_Event_content_type) == 0x0000F4, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::K2Node_Event_content_type' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, K2Node_ComponentBoundEvent_Season_11) == 0x0000F5, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::K2Node_ComponentBoundEvent_Season_11' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, K2Node_ComponentBoundEvent_Season_10) == 0x0000F6, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::K2Node_ComponentBoundEvent_Season_10' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, K2Node_ComponentBoundEvent_Season_9) == 0x0000F7, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::K2Node_ComponentBoundEvent_Season_9' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, K2Node_ComponentBoundEvent_Season_8) == 0x0000F8, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::K2Node_ComponentBoundEvent_Season_8' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, K2Node_ComponentBoundEvent_Season_7) == 0x0000F9, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::K2Node_ComponentBoundEvent_Season_7' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, K2Node_ComponentBoundEvent_Season_6) == 0x0000FA, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::K2Node_ComponentBoundEvent_Season_6' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, K2Node_ComponentBoundEvent_Season_5) == 0x0000FB, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::K2Node_ComponentBoundEvent_Season_5' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, K2Node_ComponentBoundEvent_Season_4) == 0x0000FC, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::K2Node_ComponentBoundEvent_Season_4' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, CallFunc_ValidateSeason_ReturnValue_1) == 0x0000FD, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::CallFunc_ValidateSeason_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, CallFunc_SeasonToContentType_output_1) == 0x0000FE, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::CallFunc_SeasonToContentType_output_1' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, K2Node_ComponentBoundEvent_Season_3) == 0x0000FF, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::K2Node_ComponentBoundEvent_Season_3' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, CallFunc_Array_Contains_ReturnValue) == 0x000100, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::CallFunc_Array_Contains_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, K2Node_ComponentBoundEvent_Season_2) == 0x000101, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::K2Node_ComponentBoundEvent_Season_2' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, CallFunc_BooleanOR_ReturnValue) == 0x000102, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage, K2Node_ComponentBoundEvent_Season_1) == 0x000103, "Member 'WDG_SinglePlayerPage_C_ExecuteUbergraph_WDG_SinglePlayerPage::K2Node_ComponentBoundEvent_Season_1' has a wrong offset!");

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_season2024_K2Node_ComponentBoundEvent_8_SeasonNotChanged__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2024_K2Node_ComponentBoundEvent_8_SeasonNotChanged__DelegateSignature final
{
public:
	ESeasonType                                   Season;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2024_K2Node_ComponentBoundEvent_8_SeasonNotChanged__DelegateSignature) == 0x000001, "Wrong alignment on WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2024_K2Node_ComponentBoundEvent_8_SeasonNotChanged__DelegateSignature");
static_assert(sizeof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2024_K2Node_ComponentBoundEvent_8_SeasonNotChanged__DelegateSignature) == 0x000001, "Wrong size on WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2024_K2Node_ComponentBoundEvent_8_SeasonNotChanged__DelegateSignature");
static_assert(offsetof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2024_K2Node_ComponentBoundEvent_8_SeasonNotChanged__DelegateSignature, Season) == 0x000000, "Member 'WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2024_K2Node_ComponentBoundEvent_8_SeasonNotChanged__DelegateSignature::Season' has a wrong offset!");

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_seasonGT2_K2Node_ComponentBoundEvent_4_SeasonNotChanged__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonGT2_K2Node_ComponentBoundEvent_4_SeasonNotChanged__DelegateSignature final
{
public:
	ESeasonType                                   Season;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonGT2_K2Node_ComponentBoundEvent_4_SeasonNotChanged__DelegateSignature) == 0x000001, "Wrong alignment on WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonGT2_K2Node_ComponentBoundEvent_4_SeasonNotChanged__DelegateSignature");
static_assert(sizeof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonGT2_K2Node_ComponentBoundEvent_4_SeasonNotChanged__DelegateSignature) == 0x000001, "Wrong size on WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonGT2_K2Node_ComponentBoundEvent_4_SeasonNotChanged__DelegateSignature");
static_assert(offsetof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonGT2_K2Node_ComponentBoundEvent_4_SeasonNotChanged__DelegateSignature, Season) == 0x000000, "Member 'WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonGT2_K2Node_ComponentBoundEvent_4_SeasonNotChanged__DelegateSignature::Season' has a wrong offset!");

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_season2023_K2Node_ComponentBoundEvent_3_SeasonNotChanged__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2023_K2Node_ComponentBoundEvent_3_SeasonNotChanged__DelegateSignature final
{
public:
	ESeasonType                                   Season;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2023_K2Node_ComponentBoundEvent_3_SeasonNotChanged__DelegateSignature) == 0x000001, "Wrong alignment on WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2023_K2Node_ComponentBoundEvent_3_SeasonNotChanged__DelegateSignature");
static_assert(sizeof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2023_K2Node_ComponentBoundEvent_3_SeasonNotChanged__DelegateSignature) == 0x000001, "Wrong size on WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2023_K2Node_ComponentBoundEvent_3_SeasonNotChanged__DelegateSignature");
static_assert(offsetof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2023_K2Node_ComponentBoundEvent_3_SeasonNotChanged__DelegateSignature, Season) == 0x000000, "Member 'WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2023_K2Node_ComponentBoundEvent_3_SeasonNotChanged__DelegateSignature::Season' has a wrong offset!");

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_season2022_K2Node_ComponentBoundEvent_1_SeasonNotChanged__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2022_K2Node_ComponentBoundEvent_1_SeasonNotChanged__DelegateSignature final
{
public:
	ESeasonType                                   Season;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2022_K2Node_ComponentBoundEvent_1_SeasonNotChanged__DelegateSignature) == 0x000001, "Wrong alignment on WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2022_K2Node_ComponentBoundEvent_1_SeasonNotChanged__DelegateSignature");
static_assert(sizeof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2022_K2Node_ComponentBoundEvent_1_SeasonNotChanged__DelegateSignature) == 0x000001, "Wrong size on WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2022_K2Node_ComponentBoundEvent_1_SeasonNotChanged__DelegateSignature");
static_assert(offsetof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2022_K2Node_ComponentBoundEvent_1_SeasonNotChanged__DelegateSignature, Season) == 0x000000, "Member 'WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2022_K2Node_ComponentBoundEvent_1_SeasonNotChanged__DelegateSignature::Season' has a wrong offset!");

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_seasonOpen_K2Node_ComponentBoundEvent_21_SeasonNotChanged__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonOpen_K2Node_ComponentBoundEvent_21_SeasonNotChanged__DelegateSignature final
{
public:
	ESeasonType                                   Season;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonOpen_K2Node_ComponentBoundEvent_21_SeasonNotChanged__DelegateSignature) == 0x000001, "Wrong alignment on WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonOpen_K2Node_ComponentBoundEvent_21_SeasonNotChanged__DelegateSignature");
static_assert(sizeof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonOpen_K2Node_ComponentBoundEvent_21_SeasonNotChanged__DelegateSignature) == 0x000001, "Wrong size on WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonOpen_K2Node_ComponentBoundEvent_21_SeasonNotChanged__DelegateSignature");
static_assert(offsetof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonOpen_K2Node_ComponentBoundEvent_21_SeasonNotChanged__DelegateSignature, Season) == 0x000000, "Member 'WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonOpen_K2Node_ComponentBoundEvent_21_SeasonNotChanged__DelegateSignature::Season' has a wrong offset!");

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_seasonIGTC_K2Node_ComponentBoundEvent_19_SeasonNotChanged__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonIGTC_K2Node_ComponentBoundEvent_19_SeasonNotChanged__DelegateSignature final
{
public:
	ESeasonType                                   Season;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonIGTC_K2Node_ComponentBoundEvent_19_SeasonNotChanged__DelegateSignature) == 0x000001, "Wrong alignment on WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonIGTC_K2Node_ComponentBoundEvent_19_SeasonNotChanged__DelegateSignature");
static_assert(sizeof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonIGTC_K2Node_ComponentBoundEvent_19_SeasonNotChanged__DelegateSignature) == 0x000001, "Wrong size on WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonIGTC_K2Node_ComponentBoundEvent_19_SeasonNotChanged__DelegateSignature");
static_assert(offsetof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonIGTC_K2Node_ComponentBoundEvent_19_SeasonNotChanged__DelegateSignature, Season) == 0x000000, "Member 'WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonIGTC_K2Node_ComponentBoundEvent_19_SeasonNotChanged__DelegateSignature::Season' has a wrong offset!");

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_seasonGT4_K2Node_ComponentBoundEvent_18_SeasonNotChanged__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonGT4_K2Node_ComponentBoundEvent_18_SeasonNotChanged__DelegateSignature final
{
public:
	ESeasonType                                   Season;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonGT4_K2Node_ComponentBoundEvent_18_SeasonNotChanged__DelegateSignature) == 0x000001, "Wrong alignment on WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonGT4_K2Node_ComponentBoundEvent_18_SeasonNotChanged__DelegateSignature");
static_assert(sizeof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonGT4_K2Node_ComponentBoundEvent_18_SeasonNotChanged__DelegateSignature) == 0x000001, "Wrong size on WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonGT4_K2Node_ComponentBoundEvent_18_SeasonNotChanged__DelegateSignature");
static_assert(offsetof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonGT4_K2Node_ComponentBoundEvent_18_SeasonNotChanged__DelegateSignature, Season) == 0x000000, "Member 'WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonGT4_K2Node_ComponentBoundEvent_18_SeasonNotChanged__DelegateSignature::Season' has a wrong offset!");

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_seasonBGT_K2Node_ComponentBoundEvent_17_SeasonNotChanged__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonBGT_K2Node_ComponentBoundEvent_17_SeasonNotChanged__DelegateSignature final
{
public:
	ESeasonType                                   Season;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonBGT_K2Node_ComponentBoundEvent_17_SeasonNotChanged__DelegateSignature) == 0x000001, "Wrong alignment on WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonBGT_K2Node_ComponentBoundEvent_17_SeasonNotChanged__DelegateSignature");
static_assert(sizeof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonBGT_K2Node_ComponentBoundEvent_17_SeasonNotChanged__DelegateSignature) == 0x000001, "Wrong size on WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonBGT_K2Node_ComponentBoundEvent_17_SeasonNotChanged__DelegateSignature");
static_assert(offsetof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonBGT_K2Node_ComponentBoundEvent_17_SeasonNotChanged__DelegateSignature, Season) == 0x000000, "Member 'WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_seasonBGT_K2Node_ComponentBoundEvent_17_SeasonNotChanged__DelegateSignature::Season' has a wrong offset!");

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_season2021_K2Node_ComponentBoundEvent_12_SeasonNotChanged__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2021_K2Node_ComponentBoundEvent_12_SeasonNotChanged__DelegateSignature final
{
public:
	ESeasonType                                   Season;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2021_K2Node_ComponentBoundEvent_12_SeasonNotChanged__DelegateSignature) == 0x000001, "Wrong alignment on WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2021_K2Node_ComponentBoundEvent_12_SeasonNotChanged__DelegateSignature");
static_assert(sizeof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2021_K2Node_ComponentBoundEvent_12_SeasonNotChanged__DelegateSignature) == 0x000001, "Wrong size on WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2021_K2Node_ComponentBoundEvent_12_SeasonNotChanged__DelegateSignature");
static_assert(offsetof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2021_K2Node_ComponentBoundEvent_12_SeasonNotChanged__DelegateSignature, Season) == 0x000000, "Member 'WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2021_K2Node_ComponentBoundEvent_12_SeasonNotChanged__DelegateSignature::Season' has a wrong offset!");

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_season2020_K2Node_ComponentBoundEvent_6_SeasonNotChanged__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2020_K2Node_ComponentBoundEvent_6_SeasonNotChanged__DelegateSignature final
{
public:
	ESeasonType                                   Season;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2020_K2Node_ComponentBoundEvent_6_SeasonNotChanged__DelegateSignature) == 0x000001, "Wrong alignment on WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2020_K2Node_ComponentBoundEvent_6_SeasonNotChanged__DelegateSignature");
static_assert(sizeof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2020_K2Node_ComponentBoundEvent_6_SeasonNotChanged__DelegateSignature) == 0x000001, "Wrong size on WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2020_K2Node_ComponentBoundEvent_6_SeasonNotChanged__DelegateSignature");
static_assert(offsetof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2020_K2Node_ComponentBoundEvent_6_SeasonNotChanged__DelegateSignature, Season) == 0x000000, "Member 'WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2020_K2Node_ComponentBoundEvent_6_SeasonNotChanged__DelegateSignature::Season' has a wrong offset!");

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_season2019_K2Node_ComponentBoundEvent_2_SeasonNotChanged__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2019_K2Node_ComponentBoundEvent_2_SeasonNotChanged__DelegateSignature final
{
public:
	ESeasonType                                   Season;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2019_K2Node_ComponentBoundEvent_2_SeasonNotChanged__DelegateSignature) == 0x000001, "Wrong alignment on WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2019_K2Node_ComponentBoundEvent_2_SeasonNotChanged__DelegateSignature");
static_assert(sizeof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2019_K2Node_ComponentBoundEvent_2_SeasonNotChanged__DelegateSignature) == 0x000001, "Wrong size on WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2019_K2Node_ComponentBoundEvent_2_SeasonNotChanged__DelegateSignature");
static_assert(offsetof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2019_K2Node_ComponentBoundEvent_2_SeasonNotChanged__DelegateSignature, Season) == 0x000000, "Member 'WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2019_K2Node_ComponentBoundEvent_2_SeasonNotChanged__DelegateSignature::Season' has a wrong offset!");

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__WDG_SinglePlayerPage_season2018_K2Node_ComponentBoundEvent_0_SeasonNotChanged__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2018_K2Node_ComponentBoundEvent_0_SeasonNotChanged__DelegateSignature final
{
public:
	ESeasonType                                   Season;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2018_K2Node_ComponentBoundEvent_0_SeasonNotChanged__DelegateSignature) == 0x000001, "Wrong alignment on WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2018_K2Node_ComponentBoundEvent_0_SeasonNotChanged__DelegateSignature");
static_assert(sizeof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2018_K2Node_ComponentBoundEvent_0_SeasonNotChanged__DelegateSignature) == 0x000001, "Wrong size on WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2018_K2Node_ComponentBoundEvent_0_SeasonNotChanged__DelegateSignature");
static_assert(offsetof(WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2018_K2Node_ComponentBoundEvent_0_SeasonNotChanged__DelegateSignature, Season) == 0x000000, "Member 'WDG_SinglePlayerPage_C_BndEvt__WDG_SinglePlayerPage_season2018_K2Node_ComponentBoundEvent_0_SeasonNotChanged__DelegateSignature::Season' has a wrong offset!");

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.OnSeasonUnavailable
// 0x0001 (0x0001 - 0x0000)
struct WDG_SinglePlayerPage_C_OnSeasonUnavailable final
{
public:
	EContentType                                  content_type;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SinglePlayerPage_C_OnSeasonUnavailable) == 0x000001, "Wrong alignment on WDG_SinglePlayerPage_C_OnSeasonUnavailable");
static_assert(sizeof(WDG_SinglePlayerPage_C_OnSeasonUnavailable) == 0x000001, "Wrong size on WDG_SinglePlayerPage_C_OnSeasonUnavailable");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnSeasonUnavailable, content_type) == 0x000000, "Member 'WDG_SinglePlayerPage_C_OnSeasonUnavailable::content_type' has a wrong offset!");

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.OnSeasonChanged
// 0x0001 (0x0001 - 0x0000)
struct WDG_SinglePlayerPage_C_OnSeasonChanged final
{
public:
	ESeasonType                                   new_season;                                        // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SinglePlayerPage_C_OnSeasonChanged) == 0x000001, "Wrong alignment on WDG_SinglePlayerPage_C_OnSeasonChanged");
static_assert(sizeof(WDG_SinglePlayerPage_C_OnSeasonChanged) == 0x000001, "Wrong size on WDG_SinglePlayerPage_C_OnSeasonChanged");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnSeasonChanged, new_season) == 0x000000, "Member 'WDG_SinglePlayerPage_C_OnSeasonChanged::new_season' has a wrong offset!");

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.BndEvt__popupDLC_K2Node_ComponentBoundEvent_2_OnMissing__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_SinglePlayerPage_C_BndEvt__popupDLC_K2Node_ComponentBoundEvent_2_OnMissing__DelegateSignature final
{
public:
	EContentType                                  ContentId;                                         // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SinglePlayerPage_C_BndEvt__popupDLC_K2Node_ComponentBoundEvent_2_OnMissing__DelegateSignature) == 0x000001, "Wrong alignment on WDG_SinglePlayerPage_C_BndEvt__popupDLC_K2Node_ComponentBoundEvent_2_OnMissing__DelegateSignature");
static_assert(sizeof(WDG_SinglePlayerPage_C_BndEvt__popupDLC_K2Node_ComponentBoundEvent_2_OnMissing__DelegateSignature) == 0x000001, "Wrong size on WDG_SinglePlayerPage_C_BndEvt__popupDLC_K2Node_ComponentBoundEvent_2_OnMissing__DelegateSignature");
static_assert(offsetof(WDG_SinglePlayerPage_C_BndEvt__popupDLC_K2Node_ComponentBoundEvent_2_OnMissing__DelegateSignature, ContentId) == 0x000000, "Member 'WDG_SinglePlayerPage_C_BndEvt__popupDLC_K2Node_ComponentBoundEvent_2_OnMissing__DelegateSignature::ContentId' has a wrong offset!");

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.SetCurrentSeasonType
// 0x0001 (0x0001 - 0x0000)
struct WDG_SinglePlayerPage_C_SetCurrentSeasonType final
{
public:
	bool                                          Init;                                              // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SinglePlayerPage_C_SetCurrentSeasonType) == 0x000001, "Wrong alignment on WDG_SinglePlayerPage_C_SetCurrentSeasonType");
static_assert(sizeof(WDG_SinglePlayerPage_C_SetCurrentSeasonType) == 0x000001, "Wrong size on WDG_SinglePlayerPage_C_SetCurrentSeasonType");
static_assert(offsetof(WDG_SinglePlayerPage_C_SetCurrentSeasonType, Init) == 0x000000, "Member 'WDG_SinglePlayerPage_C_SetCurrentSeasonType::Init' has a wrong offset!");

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.OnPreviewMouseButtonDown
// 0x0328 (0x0328 - 0x0000)
struct WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FPointerEvent                          MouseEvent;                                        // 0x0038(0x0070)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	struct FEventReply                            ReturnValue;                                       // 0x00A8(0x00B8)(Parm, OutParm, ReturnParm)
	class FName                                   Temp_name_Variable;                                // 0x0160(0x0008)(ConstParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FKey                                   CallFunc_PointerEvent_GetEffectingButton_ReturnValue; // 0x0168(0x0018)(HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue;            // 0x0180(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_181[0x7];                                      // 0x0181(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_Handled_ReturnValue;                      // 0x0188(0x00B8)()
	bool                                          CallFunc_IsUserDevOrTester_ReturnValue;            // 0x0240(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_241[0x7];                                      // 0x0241(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue;               // 0x0248(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TSubclassOf<class UAcPageBase>                CallFunc_Map_Find_Value;                           // 0x0250(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          CallFunc_Map_Find_ReturnValue;                     // 0x0258(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_259[0x7];                                      // 0x0259(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPageBase*                            CallFunc_GoToPage_ReturnValue;                     // 0x0260(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FEventReply                            CallFunc_Unhandled_ReturnValue;                    // 0x0268(0x00B8)()
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0320(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x0321(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_2;                 // 0x0322(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown) == 0x000008, "Wrong alignment on WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown");
static_assert(sizeof(WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown) == 0x000328, "Wrong size on WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown, MyGeometry) == 0x000000, "Member 'WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown, MouseEvent) == 0x000038, "Member 'WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown::MouseEvent' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown, ReturnValue) == 0x0000A8, "Member 'WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown, Temp_name_Variable) == 0x000160, "Member 'WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown::Temp_name_Variable' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown, CallFunc_PointerEvent_GetEffectingButton_ReturnValue) == 0x000168, "Member 'WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown::CallFunc_PointerEvent_GetEffectingButton_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown, CallFunc_EqualEqual_KeyKey_ReturnValue) == 0x000180, "Member 'WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown::CallFunc_EqualEqual_KeyKey_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown, CallFunc_Handled_ReturnValue) == 0x000188, "Member 'WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown::CallFunc_Handled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown, CallFunc_IsUserDevOrTester_ReturnValue) == 0x000240, "Member 'WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown::CallFunc_IsUserDevOrTester_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown, CallFunc_GetMenuManager_ReturnValue) == 0x000248, "Member 'WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown::CallFunc_GetMenuManager_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown, CallFunc_Map_Find_Value) == 0x000250, "Member 'WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown::CallFunc_Map_Find_Value' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown, CallFunc_Map_Find_ReturnValue) == 0x000258, "Member 'WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown::CallFunc_Map_Find_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown, CallFunc_GoToPage_ReturnValue) == 0x000260, "Member 'WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown::CallFunc_GoToPage_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown, CallFunc_Unhandled_ReturnValue) == 0x000268, "Member 'WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown::CallFunc_Unhandled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown, CallFunc_BooleanAND_ReturnValue) == 0x000320, "Member 'WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown, CallFunc_BooleanAND_ReturnValue_1) == 0x000321, "Member 'WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown, CallFunc_BooleanAND_ReturnValue_2) == 0x000322, "Member 'WDG_SinglePlayerPage_C_OnPreviewMouseButtonDown::CallFunc_BooleanAND_ReturnValue_2' has a wrong offset!");

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.ValidateSeason
// 0x0004 (0x0004 - 0x0000)
struct WDG_SinglePlayerPage_C_ValidateSeason final
{
public:
	ESeasonType                                   season_type;                                       // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          ReturnValue;                                       // 0x0001(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor)
	EContentType                                  CallFunc_SeasonToContentType_output;               // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_CanPlay_Result;                           // 0x0003(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SinglePlayerPage_C_ValidateSeason) == 0x000001, "Wrong alignment on WDG_SinglePlayerPage_C_ValidateSeason");
static_assert(sizeof(WDG_SinglePlayerPage_C_ValidateSeason) == 0x000004, "Wrong size on WDG_SinglePlayerPage_C_ValidateSeason");
static_assert(offsetof(WDG_SinglePlayerPage_C_ValidateSeason, season_type) == 0x000000, "Member 'WDG_SinglePlayerPage_C_ValidateSeason::season_type' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ValidateSeason, ReturnValue) == 0x000001, "Member 'WDG_SinglePlayerPage_C_ValidateSeason::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ValidateSeason, CallFunc_SeasonToContentType_output) == 0x000002, "Member 'WDG_SinglePlayerPage_C_ValidateSeason::CallFunc_SeasonToContentType_output' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ValidateSeason, CallFunc_CanPlay_Result) == 0x000003, "Member 'WDG_SinglePlayerPage_C_ValidateSeason::CallFunc_CanPlay_Result' has a wrong offset!");

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.ToggleSeriesSelect
// 0x0018 (0x0018 - 0x0000)
struct WDG_SinglePlayerPage_C_ToggleSeriesSelect final
{
public:
	class UWDG_GenericBarItemSlanted_C*           SelectedButton;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<class UWDG_GenericBarItemSlanted_C*>   SelectorButtons;                                   // 0x0008(0x0010)(Edit, BlueprintVisible, ContainsInstancedReference)
};
static_assert(alignof(WDG_SinglePlayerPage_C_ToggleSeriesSelect) == 0x000008, "Wrong alignment on WDG_SinglePlayerPage_C_ToggleSeriesSelect");
static_assert(sizeof(WDG_SinglePlayerPage_C_ToggleSeriesSelect) == 0x000018, "Wrong size on WDG_SinglePlayerPage_C_ToggleSeriesSelect");
static_assert(offsetof(WDG_SinglePlayerPage_C_ToggleSeriesSelect, SelectedButton) == 0x000000, "Member 'WDG_SinglePlayerPage_C_ToggleSeriesSelect::SelectedButton' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ToggleSeriesSelect, SelectorButtons) == 0x000008, "Member 'WDG_SinglePlayerPage_C_ToggleSeriesSelect::SelectorButtons' has a wrong offset!");

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.OnPreviewKeyDown
// 0x02C8 (0x02C8 - 0x0000)
struct WDG_SinglePlayerPage_C_OnPreviewKeyDown final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FKeyEvent                              InKeyEvent;                                        // 0x0038(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm)
	struct FEventReply                            ReturnValue;                                       // 0x0070(0x00B8)(Parm, OutParm, ReturnParm)
	struct FKey                                   CallFunc_GetKey_ReturnValue;                       // 0x0128(0x0018)(HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue;            // 0x0140(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue_1;          // 0x0141(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_142[0x6];                                      // 0x0142(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_Handled_ReturnValue;                      // 0x0148(0x00B8)()
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x0200(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0201(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_202[0x6];                                      // 0x0202(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_Unhandled_ReturnValue;                    // 0x0208(0x00B8)()
	bool                                          CallFunc_HasFocusedDescendants_ReturnValue;        // 0x02C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SinglePlayerPage_C_OnPreviewKeyDown) == 0x000008, "Wrong alignment on WDG_SinglePlayerPage_C_OnPreviewKeyDown");
static_assert(sizeof(WDG_SinglePlayerPage_C_OnPreviewKeyDown) == 0x0002C8, "Wrong size on WDG_SinglePlayerPage_C_OnPreviewKeyDown");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewKeyDown, MyGeometry) == 0x000000, "Member 'WDG_SinglePlayerPage_C_OnPreviewKeyDown::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewKeyDown, InKeyEvent) == 0x000038, "Member 'WDG_SinglePlayerPage_C_OnPreviewKeyDown::InKeyEvent' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewKeyDown, ReturnValue) == 0x000070, "Member 'WDG_SinglePlayerPage_C_OnPreviewKeyDown::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewKeyDown, CallFunc_GetKey_ReturnValue) == 0x000128, "Member 'WDG_SinglePlayerPage_C_OnPreviewKeyDown::CallFunc_GetKey_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewKeyDown, CallFunc_EqualEqual_KeyKey_ReturnValue) == 0x000140, "Member 'WDG_SinglePlayerPage_C_OnPreviewKeyDown::CallFunc_EqualEqual_KeyKey_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewKeyDown, CallFunc_EqualEqual_KeyKey_ReturnValue_1) == 0x000141, "Member 'WDG_SinglePlayerPage_C_OnPreviewKeyDown::CallFunc_EqualEqual_KeyKey_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewKeyDown, CallFunc_Handled_ReturnValue) == 0x000148, "Member 'WDG_SinglePlayerPage_C_OnPreviewKeyDown::CallFunc_Handled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewKeyDown, CallFunc_BooleanOR_ReturnValue) == 0x000200, "Member 'WDG_SinglePlayerPage_C_OnPreviewKeyDown::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewKeyDown, CallFunc_BooleanAND_ReturnValue) == 0x000201, "Member 'WDG_SinglePlayerPage_C_OnPreviewKeyDown::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewKeyDown, CallFunc_Unhandled_ReturnValue) == 0x000208, "Member 'WDG_SinglePlayerPage_C_OnPreviewKeyDown::CallFunc_Unhandled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_OnPreviewKeyDown, CallFunc_HasFocusedDescendants_ReturnValue) == 0x0002C0, "Member 'WDG_SinglePlayerPage_C_OnPreviewKeyDown::CallFunc_HasFocusedDescendants_ReturnValue' has a wrong offset!");

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.GetSeasonButtonsArray
// 0x0020 (0x0020 - 0x0000)
struct WDG_SinglePlayerPage_C_GetSeasonButtonsArray final
{
public:
	TArray<class UWDG_SeasonItem_C*>              Array;                                             // 0x0000(0x0010)(Parm, OutParm, ContainsInstancedReference)
	TArray<class UWDG_SeasonItem_C*>              K2Node_MakeArray_Array;                            // 0x0010(0x0010)(ReferenceParm, ContainsInstancedReference)
};
static_assert(alignof(WDG_SinglePlayerPage_C_GetSeasonButtonsArray) == 0x000008, "Wrong alignment on WDG_SinglePlayerPage_C_GetSeasonButtonsArray");
static_assert(sizeof(WDG_SinglePlayerPage_C_GetSeasonButtonsArray) == 0x000020, "Wrong size on WDG_SinglePlayerPage_C_GetSeasonButtonsArray");
static_assert(offsetof(WDG_SinglePlayerPage_C_GetSeasonButtonsArray, Array) == 0x000000, "Member 'WDG_SinglePlayerPage_C_GetSeasonButtonsArray::Array' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_GetSeasonButtonsArray, K2Node_MakeArray_Array) == 0x000010, "Member 'WDG_SinglePlayerPage_C_GetSeasonButtonsArray::K2Node_MakeArray_Array' has a wrong offset!");

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.NavToSelectedSeason
// 0x0050 (0x0050 - 0x0000)
struct WDG_SinglePlayerPage_C_NavToSelectedSeason final
{
public:
	EUINavigation                                 Navigation_0;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                ReturnValue;                                       // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          Temp_bool_True_if_break_was_hit_Variable;          // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_12[0x2];                                       // 0x0012(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_19[0x3];                                       // 0x0019(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_24[0x4];                                       // 0x0024(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UWDG_SeasonItem_C*>              CallFunc_GetSeasonButtonsArray_Array;              // 0x0028(0x0010)(ReferenceParm, ContainsInstancedReference)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0038(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3C[0x4];                                       // 0x003C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_SeasonItem_C*                      CallFunc_Array_Get_Item;                           // 0x0040(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0048(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0049(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SinglePlayerPage_C_NavToSelectedSeason) == 0x000008, "Wrong alignment on WDG_SinglePlayerPage_C_NavToSelectedSeason");
static_assert(sizeof(WDG_SinglePlayerPage_C_NavToSelectedSeason) == 0x000050, "Wrong size on WDG_SinglePlayerPage_C_NavToSelectedSeason");
static_assert(offsetof(WDG_SinglePlayerPage_C_NavToSelectedSeason, Navigation_0) == 0x000000, "Member 'WDG_SinglePlayerPage_C_NavToSelectedSeason::Navigation_0' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_NavToSelectedSeason, ReturnValue) == 0x000008, "Member 'WDG_SinglePlayerPage_C_NavToSelectedSeason::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_NavToSelectedSeason, K2Node_SwitchEnum_CmpSuccess) == 0x000010, "Member 'WDG_SinglePlayerPage_C_NavToSelectedSeason::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_NavToSelectedSeason, Temp_bool_True_if_break_was_hit_Variable) == 0x000011, "Member 'WDG_SinglePlayerPage_C_NavToSelectedSeason::Temp_bool_True_if_break_was_hit_Variable' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_NavToSelectedSeason, Temp_int_Array_Index_Variable) == 0x000014, "Member 'WDG_SinglePlayerPage_C_NavToSelectedSeason::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_NavToSelectedSeason, CallFunc_Not_PreBool_ReturnValue) == 0x000018, "Member 'WDG_SinglePlayerPage_C_NavToSelectedSeason::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_NavToSelectedSeason, Temp_int_Loop_Counter_Variable) == 0x00001C, "Member 'WDG_SinglePlayerPage_C_NavToSelectedSeason::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_NavToSelectedSeason, CallFunc_Add_IntInt_ReturnValue) == 0x000020, "Member 'WDG_SinglePlayerPage_C_NavToSelectedSeason::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_NavToSelectedSeason, CallFunc_GetSeasonButtonsArray_Array) == 0x000028, "Member 'WDG_SinglePlayerPage_C_NavToSelectedSeason::CallFunc_GetSeasonButtonsArray_Array' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_NavToSelectedSeason, CallFunc_Array_Length_ReturnValue) == 0x000038, "Member 'WDG_SinglePlayerPage_C_NavToSelectedSeason::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_NavToSelectedSeason, CallFunc_Array_Get_Item) == 0x000040, "Member 'WDG_SinglePlayerPage_C_NavToSelectedSeason::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_NavToSelectedSeason, CallFunc_Less_IntInt_ReturnValue) == 0x000048, "Member 'WDG_SinglePlayerPage_C_NavToSelectedSeason::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_NavToSelectedSeason, CallFunc_BooleanAND_ReturnValue) == 0x000049, "Member 'WDG_SinglePlayerPage_C_NavToSelectedSeason::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");

// Function WDG_SinglePlayerPage.WDG_SinglePlayerPage_C.ToggleSeasonSelection
// 0x0018 (0x0018 - 0x0000)
struct WDG_SinglePlayerPage_C_ToggleSeasonSelection final
{
public:
	bool                                          ResetFocus;                                        // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationForward_ReturnValue;         // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationReverse_ReturnValue;         // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SinglePlayerPage_C_ToggleSeasonSelection) == 0x000008, "Wrong alignment on WDG_SinglePlayerPage_C_ToggleSeasonSelection");
static_assert(sizeof(WDG_SinglePlayerPage_C_ToggleSeasonSelection) == 0x000018, "Wrong size on WDG_SinglePlayerPage_C_ToggleSeasonSelection");
static_assert(offsetof(WDG_SinglePlayerPage_C_ToggleSeasonSelection, ResetFocus) == 0x000000, "Member 'WDG_SinglePlayerPage_C_ToggleSeasonSelection::ResetFocus' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ToggleSeasonSelection, CallFunc_PlayAnimationForward_ReturnValue) == 0x000008, "Member 'WDG_SinglePlayerPage_C_ToggleSeasonSelection::CallFunc_PlayAnimationForward_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SinglePlayerPage_C_ToggleSeasonSelection, CallFunc_PlayAnimationReverse_ReturnValue) == 0x000010, "Member 'WDG_SinglePlayerPage_C_ToggleSeasonSelection::CallFunc_PlayAnimationReverse_ReturnValue' has a wrong offset!");

}

