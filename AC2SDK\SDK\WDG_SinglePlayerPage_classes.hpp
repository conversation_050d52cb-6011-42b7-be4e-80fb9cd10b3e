﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SinglePlayerPage

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SinglePlayerPage.WDG_SinglePlayerPage_C
// 0x0170 (0x09F8 - 0x0888)
class UWDG_SinglePlayerPage_C final : public USinglePlayerColumnPage
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0888(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       SlideMain;                                         // 0x0890(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       ShowroomFade;                                      // 0x0898(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       PageFade;                                          // 0x08A0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 ACCLogoShowroom;                                   // 0x08A8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SinglePlayerLowerPanel_C*          AssistsPanel;                                      // 0x08B0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_FooterButton_C*                    Back;                                              // 0x08B8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 bgImage;                                           // 0x08C0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 BlancPainLogo;                                     // 0x08C8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           bodywrapper;                                       // 0x08D0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SeasonSelector_C*                  BtnSeasonSelector;                                 // 0x08D8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             CurrentSeasonType;                                 // 0x08E0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Header_C*                          Header;                                            // 0x08E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_100;                                         // 0x08F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Page_C*                            PageBase;                                          // 0x08F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_DLCWarningPopup_C*                 popupDLC;                                          // 0x0900(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SinglePlayerLowerPanel_C*          RealismPanel;                                      // 0x0908(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SeasonItem_C*                      season2018;                                        // 0x0910(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SeasonItem_C*                      season2019;                                        // 0x0918(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SeasonItem_C*                      season2020;                                        // 0x0920(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SeasonItem_C*                      season2021;                                        // 0x0928(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SeasonItem_C*                      season2022;                                        // 0x0930(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SeasonItem_C*                      season2023;                                        // 0x0938(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SeasonItem_C*                      season2024;                                        // 0x0940(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SeasonItem_C*                      seasonBGT;                                         // 0x0948(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SeasonItem_C*                      seasonGT2;                                         // 0x0950(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SeasonItem_C*                      seasonGT4;                                         // 0x0958(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SeasonItem_C*                      seasonIGTC;                                        // 0x0960(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SeasonItem_C*                      seasonOpen;                                        // 0x0968(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           seasonwrapper;                                     // 0x0970(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_StartSessionPanel_C*               StartPanel;                                        // 0x0978(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Footer_C*                          WDG_Footer;                                        // 0x0980(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SinglePlayerWeatherPanel_C*        WeatherPanel;                                      // 0x0988(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	TMap<ESeasonType, class FText>                SeasonTypeText;                                    // 0x0990(0x0050)(Edit, BlueprintVisible, DisableEditOnInstance)
	int32                                         RequiredDLCId;                                     // 0x09E0(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          ViewingSeasons;                                    // 0x09E4(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	uint8                                         Pad_9E5[0x3];                                      // 0x09E5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<ESeasonType>                           NoNagSeasons;                                      // 0x09E8(0x0010)(Edit, BlueprintVisible)

public:
	void ExecuteUbergraph_WDG_SinglePlayerPage(int32 EntryPoint);
	void BndEvt__WDG_SinglePlayerPage_season2024_K2Node_ComponentBoundEvent_8_SeasonNotChanged__DelegateSignature(ESeasonType Season);
	void BndEvt__WDG_SinglePlayerPage_seasonGT2_K2Node_ComponentBoundEvent_4_SeasonNotChanged__DelegateSignature(ESeasonType Season);
	void BndEvt__WDG_SinglePlayerPage_season2023_K2Node_ComponentBoundEvent_3_SeasonNotChanged__DelegateSignature(ESeasonType Season);
	void BndEvt__WDG_SinglePlayerPage_season2022_K2Node_ComponentBoundEvent_1_SeasonNotChanged__DelegateSignature(ESeasonType Season);
	void BndEvt__WDG_SinglePlayerPage_seasonOpen_K2Node_ComponentBoundEvent_21_SeasonNotChanged__DelegateSignature(ESeasonType Season);
	void BndEvt__WDG_SinglePlayerPage_seasonIGTC_K2Node_ComponentBoundEvent_19_SeasonNotChanged__DelegateSignature(ESeasonType Season);
	void BndEvt__WDG_SinglePlayerPage_seasonGT4_K2Node_ComponentBoundEvent_18_SeasonNotChanged__DelegateSignature(ESeasonType Season);
	void BndEvt__WDG_SinglePlayerPage_seasonBGT_K2Node_ComponentBoundEvent_17_SeasonNotChanged__DelegateSignature(ESeasonType Season);
	void BndEvt__WDG_SinglePlayerPage_season2021_K2Node_ComponentBoundEvent_12_SeasonNotChanged__DelegateSignature(ESeasonType Season);
	void BndEvt__WDG_SinglePlayerPage_season2020_K2Node_ComponentBoundEvent_6_SeasonNotChanged__DelegateSignature(ESeasonType Season);
	void BndEvt__WDG_SinglePlayerPage_season2019_K2Node_ComponentBoundEvent_2_SeasonNotChanged__DelegateSignature(ESeasonType Season);
	void BndEvt__WDG_SinglePlayerPage_season2018_K2Node_ComponentBoundEvent_0_SeasonNotChanged__DelegateSignature(ESeasonType Season);
	void OnSeasonUnavailable(EContentType content_type);
	void OnSeasonChanged(ESeasonType new_season);
	void BndEvt__WDG_SinglePlayerPage_BtnSeasonSelector_K2Node_ComponentBoundEvent_20_OnAcPanelForwardEvent__DelegateSignature();
	void BP_StartPage();
	void Construct();
	void BndEvt__popupDLC_K2Node_ComponentBoundEvent_1_OnNo__DelegateSignature();
	void BndEvt__popupDLC_K2Node_ComponentBoundEvent_2_OnMissing__DelegateSignature(EContentType ContentId);
	void BndEvt__popupDLC_K2Node_ComponentBoundEvent_0_OnYes__DelegateSignature();
	void SetCurrentSeasonType(bool Init);
	struct FEventReply OnPreviewMouseButtonDown(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent);
	bool ValidateSeason(ESeasonType season_type);
	void ToggleSeriesSelect(class UWDG_GenericBarItemSlanted_C* SelectedButton);
	struct FEventReply OnPreviewKeyDown(const struct FGeometry& MyGeometry, const struct FKeyEvent& InKeyEvent);
	void GetSeasonButtonsArray(TArray<class UWDG_SeasonItem_C*>* Array);
	class UWidget* NavToSelectedSeason(EUINavigation Navigation_0);
	void ToggleSeasonSelection(bool ResetFocus);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SinglePlayerPage_C">();
	}
	static class UWDG_SinglePlayerPage_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SinglePlayerPage_C>();
	}
};
static_assert(alignof(UWDG_SinglePlayerPage_C) == 0x000008, "Wrong alignment on UWDG_SinglePlayerPage_C");
static_assert(sizeof(UWDG_SinglePlayerPage_C) == 0x0009F8, "Wrong size on UWDG_SinglePlayerPage_C");
static_assert(offsetof(UWDG_SinglePlayerPage_C, UberGraphFrame) == 0x000888, "Member 'UWDG_SinglePlayerPage_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, SlideMain) == 0x000890, "Member 'UWDG_SinglePlayerPage_C::SlideMain' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, ShowroomFade) == 0x000898, "Member 'UWDG_SinglePlayerPage_C::ShowroomFade' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, PageFade) == 0x0008A0, "Member 'UWDG_SinglePlayerPage_C::PageFade' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, ACCLogoShowroom) == 0x0008A8, "Member 'UWDG_SinglePlayerPage_C::ACCLogoShowroom' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, AssistsPanel) == 0x0008B0, "Member 'UWDG_SinglePlayerPage_C::AssistsPanel' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, Back) == 0x0008B8, "Member 'UWDG_SinglePlayerPage_C::Back' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, bgImage) == 0x0008C0, "Member 'UWDG_SinglePlayerPage_C::bgImage' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, BlancPainLogo) == 0x0008C8, "Member 'UWDG_SinglePlayerPage_C::BlancPainLogo' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, bodywrapper) == 0x0008D0, "Member 'UWDG_SinglePlayerPage_C::bodywrapper' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, BtnSeasonSelector) == 0x0008D8, "Member 'UWDG_SinglePlayerPage_C::BtnSeasonSelector' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, CurrentSeasonType) == 0x0008E0, "Member 'UWDG_SinglePlayerPage_C::CurrentSeasonType' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, Header) == 0x0008E8, "Member 'UWDG_SinglePlayerPage_C::Header' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, Image_100) == 0x0008F0, "Member 'UWDG_SinglePlayerPage_C::Image_100' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, PageBase) == 0x0008F8, "Member 'UWDG_SinglePlayerPage_C::PageBase' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, popupDLC) == 0x000900, "Member 'UWDG_SinglePlayerPage_C::popupDLC' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, RealismPanel) == 0x000908, "Member 'UWDG_SinglePlayerPage_C::RealismPanel' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, season2018) == 0x000910, "Member 'UWDG_SinglePlayerPage_C::season2018' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, season2019) == 0x000918, "Member 'UWDG_SinglePlayerPage_C::season2019' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, season2020) == 0x000920, "Member 'UWDG_SinglePlayerPage_C::season2020' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, season2021) == 0x000928, "Member 'UWDG_SinglePlayerPage_C::season2021' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, season2022) == 0x000930, "Member 'UWDG_SinglePlayerPage_C::season2022' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, season2023) == 0x000938, "Member 'UWDG_SinglePlayerPage_C::season2023' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, season2024) == 0x000940, "Member 'UWDG_SinglePlayerPage_C::season2024' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, seasonBGT) == 0x000948, "Member 'UWDG_SinglePlayerPage_C::seasonBGT' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, seasonGT2) == 0x000950, "Member 'UWDG_SinglePlayerPage_C::seasonGT2' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, seasonGT4) == 0x000958, "Member 'UWDG_SinglePlayerPage_C::seasonGT4' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, seasonIGTC) == 0x000960, "Member 'UWDG_SinglePlayerPage_C::seasonIGTC' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, seasonOpen) == 0x000968, "Member 'UWDG_SinglePlayerPage_C::seasonOpen' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, seasonwrapper) == 0x000970, "Member 'UWDG_SinglePlayerPage_C::seasonwrapper' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, StartPanel) == 0x000978, "Member 'UWDG_SinglePlayerPage_C::StartPanel' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, WDG_Footer) == 0x000980, "Member 'UWDG_SinglePlayerPage_C::WDG_Footer' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, WeatherPanel) == 0x000988, "Member 'UWDG_SinglePlayerPage_C::WeatherPanel' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, SeasonTypeText) == 0x000990, "Member 'UWDG_SinglePlayerPage_C::SeasonTypeText' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, RequiredDLCId) == 0x0009E0, "Member 'UWDG_SinglePlayerPage_C::RequiredDLCId' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, ViewingSeasons) == 0x0009E4, "Member 'UWDG_SinglePlayerPage_C::ViewingSeasons' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPage_C, NoNagSeasons) == 0x0009E8, "Member 'UWDG_SinglePlayerPage_C::NoNagSeasons' has a wrong offset!");

}

