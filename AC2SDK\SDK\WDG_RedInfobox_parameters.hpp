﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RedInfobox

#include "Basic.hpp"


namespace SDK::Params
{

// Function WDG_RedInfobox.WDG_RedInfobox_C.ExecuteUbergraph_WDG_RedInfobox
// 0x0008 (0x0008 - 0x0000)
struct WDG_RedInfobox_C_ExecuteUbergraph_WDG_RedInfobox final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_RedInfobox_C_ExecuteUbergraph_WDG_RedInfobox) == 0x000004, "Wrong alignment on WDG_RedInfobox_C_ExecuteUbergraph_WDG_RedInfobox");
static_assert(sizeof(WDG_RedInfobox_C_ExecuteUbergraph_WDG_RedInfobox) == 0x000008, "Wrong size on WDG_RedInfobox_C_ExecuteUbergraph_WDG_RedInfobox");
static_assert(offsetof(WDG_RedInfobox_C_ExecuteUbergraph_WDG_RedInfobox, EntryPoint) == 0x000000, "Member 'WDG_RedInfobox_C_ExecuteUbergraph_WDG_RedInfobox::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_RedInfobox_C_ExecuteUbergraph_WDG_RedInfobox, K2Node_Event_IsDesignTime) == 0x000004, "Member 'WDG_RedInfobox_C_ExecuteUbergraph_WDG_RedInfobox::K2Node_Event_IsDesignTime' has a wrong offset!");

// Function WDG_RedInfobox.WDG_RedInfobox_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_RedInfobox_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_RedInfobox_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_RedInfobox_C_PreConstruct");
static_assert(sizeof(WDG_RedInfobox_C_PreConstruct) == 0x000001, "Wrong size on WDG_RedInfobox_C_PreConstruct");
static_assert(offsetof(WDG_RedInfobox_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_RedInfobox_C_PreConstruct::IsDesignTime' has a wrong offset!");

}

