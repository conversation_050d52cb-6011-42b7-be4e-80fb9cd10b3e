﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PlayerInfoPanel

#include "Basic.hpp"

#include "WDG_PlayerInfoPanel_classes.hpp"
#include "WDG_PlayerInfoPanel_parameters.hpp"


namespace SDK
{

// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.ExecuteUbergraph_WDG_PlayerInfoPanel
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_PlayerInfoPanel_C::ExecuteUbergraph_WDG_PlayerInfoPanel(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerInfoPanel_C", "ExecuteUbergraph_WDG_PlayerInfoPanel");

	Params::WDG_PlayerInfoPanel_C_ExecuteUbergraph_WDG_PlayerInfoPanel Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.UpdateButtonPressed
// (BlueprintCallable, BlueprintEvent)

void UWDG_PlayerInfoPanel_C::UpdateButtonPressed()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerInfoPanel_C", "UpdateButtonPressed");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.BndEvt__btnUpdate_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_PlayerInfoPanel_C::BndEvt__btnUpdate_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerInfoPanel_C", "BndEvt__btnUpdate_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.CustomEvent_2
// (HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const class FText&                      text                                                   (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// ETextCommit                             CommitMethod                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_PlayerInfoPanel_C::CustomEvent_2(const class FText& text, ETextCommit CommitMethod)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerInfoPanel_C", "CustomEvent_2");

	Params::WDG_PlayerInfoPanel_C_CustomEvent_2 Parms{};

	Parms.text = std::move(text);
	Parms.CommitMethod = CommitMethod;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.CustomEvent_1
// (HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const class FText&                      text                                                   (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// ETextCommit                             CommitMethod                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_PlayerInfoPanel_C::CustomEvent_1(const class FText& text, ETextCommit CommitMethod)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerInfoPanel_C", "CustomEvent_1");

	Params::WDG_PlayerInfoPanel_C_CustomEvent_1 Parms{};

	Parms.text = std::move(text);
	Parms.CommitMethod = CommitMethod;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.CustomEvent_0
// (HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// const class FText&                      text                                                   (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// ETextCommit                             CommitMethod                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_PlayerInfoPanel_C::CustomEvent_0(const class FText& text, ETextCommit CommitMethod)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerInfoPanel_C", "CustomEvent_0");

	Params::WDG_PlayerInfoPanel_C_CustomEvent_0 Parms{};

	Parms.text = std::move(text);
	Parms.CommitMethod = CommitMethod;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_2_AcTextInputEvent__DelegateSignature
// (HasOutParams, BlueprintEvent)
// Parameters:
// const class FText&                      text                                                   (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_PlayerInfoPanel_C::BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_2_AcTextInputEvent__DelegateSignature(const class FText& text)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerInfoPanel_C", "BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_2_AcTextInputEvent__DelegateSignature");

	Params::WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_2_AcTextInputEvent__DelegateSignature Parms{};

	Parms.text = std::move(text);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_1_AcTextInputEvent__DelegateSignature
// (HasOutParams, BlueprintEvent)
// Parameters:
// const class FText&                      text                                                   (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_PlayerInfoPanel_C::BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_1_AcTextInputEvent__DelegateSignature(const class FText& text)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerInfoPanel_C", "BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_1_AcTextInputEvent__DelegateSignature");

	Params::WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_1_AcTextInputEvent__DelegateSignature Parms{};

	Parms.text = std::move(text);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_0_AcTextInputEvent__DelegateSignature
// (HasOutParams, BlueprintEvent)
// Parameters:
// const class FText&                      text                                                   (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_PlayerInfoPanel_C::BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_0_AcTextInputEvent__DelegateSignature(const class FText& text)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerInfoPanel_C", "BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_0_AcTextInputEvent__DelegateSignature");

	Params::WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_0_AcTextInputEvent__DelegateSignature Parms{};

	Parms.text = std::move(text);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_10_AcTextInputEvent__DelegateSignature
// (HasOutParams, BlueprintEvent)
// Parameters:
// const class FText&                      text                                                   (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_PlayerInfoPanel_C::BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_10_AcTextInputEvent__DelegateSignature(const class FText& text)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerInfoPanel_C", "BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_10_AcTextInputEvent__DelegateSignature");

	Params::WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputLastName_K2Node_ComponentBoundEvent_10_AcTextInputEvent__DelegateSignature Parms{};

	Parms.text = std::move(text);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_9_AcTextInputEvent__DelegateSignature
// (HasOutParams, BlueprintEvent)
// Parameters:
// const class FText&                      text                                                   (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_PlayerInfoPanel_C::BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_9_AcTextInputEvent__DelegateSignature(const class FText& text)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerInfoPanel_C", "BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_9_AcTextInputEvent__DelegateSignature");

	Params::WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputFirstName_K2Node_ComponentBoundEvent_9_AcTextInputEvent__DelegateSignature Parms{};

	Parms.text = std::move(text);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_8_AcTextInputEvent__DelegateSignature
// (HasOutParams, BlueprintEvent)
// Parameters:
// const class FText&                      text                                                   (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)

void UWDG_PlayerInfoPanel_C::BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_8_AcTextInputEvent__DelegateSignature(const class FText& text)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerInfoPanel_C", "BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_8_AcTextInputEvent__DelegateSignature");

	Params::WDG_PlayerInfoPanel_C_BndEvt__WDG_PlayerInfoPanel_inputAbbreviation_K2Node_ComponentBoundEvent_8_AcTextInputEvent__DelegateSignature Parms{};

	Parms.text = std::move(text);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_PlayerInfoPanel_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerInfoPanel_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.Tick
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// float                                   InDeltaTime                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_PlayerInfoPanel_C::Tick(const struct FGeometry& MyGeometry, float InDeltaTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerInfoPanel_C", "Tick");

	Params::WDG_PlayerInfoPanel_C_Tick Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InDeltaTime = InDeltaTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.GenerateAbbreviation
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class FString                           ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, HasGetValueTypeHash)

class FString UWDG_PlayerInfoPanel_C::GenerateAbbreviation()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerInfoPanel_C", "GenerateAbbreviation");

	Params::WDG_PlayerInfoPanel_C_GenerateAbbreviation Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_PlayerInfoPanel.WDG_PlayerInfoPanel_C.ValidateAbbreviation
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const class FText&                      InText                                                 (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// bool                                    GenerateAbbreviation                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
// class FString*                          Value_As_String                                        (Parm, OutParm, ZeroConstructor, HasGetValueTypeHash)

void UWDG_PlayerInfoPanel_C::ValidateAbbreviation(const class FText& InText, bool GenerateAbbreviation, class FString* Value_As_String)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PlayerInfoPanel_C", "ValidateAbbreviation");

	Params::WDG_PlayerInfoPanel_C_ValidateAbbreviation Parms{};

	Parms.InText = std::move(InText);
	Parms.GenerateAbbreviation = GenerateAbbreviation;

	UObject::ProcessEvent(Func, &Parms);

	if (Value_As_String != nullptr)
		*Value_As_String = std::move(Parms.Value_As_String);
}

}

