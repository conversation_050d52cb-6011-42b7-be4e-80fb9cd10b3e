﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomFilter

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_ShowroomFilter.WDG_ShowroomFilter_C.ExecuteUbergraph_WDG_ShowroomFilter
// 0x0038 (0x0038 - 0x0000)
struct WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EShowroomCarFilterType                        Temp_byte_Variable;                                // 0x0004(0x0001)(ConstParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0006(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_2;            // 0x0007(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate;              // 0x0008(0x0010)(ZeroConstructor, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_3;            // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EShowroomCarFilterType                        Temp_byte_Variable_1;                              // 0x0019(0x0001)(ConstParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EShowroomCarFilterType                        Temp_byte_Variable_2;                              // 0x001A(0x0001)(ConstParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_4;            // 0x001B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EShowroomCarFilterType                        Temp_byte_Variable_3;                              // 0x001C(0x0001)(ConstParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_5;            // 0x001D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x001E(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsVisible_ReturnValue;                    // 0x001F(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	struct FFocusEvent                            K2Node_Event_InFocusEvent_1;                       // 0x0020(0x0008)(NoDestructor)
	struct FFocusEvent                            K2Node_Event_InFocusEvent;                         // 0x0028(0x0008)(NoDestructor)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsVisible_ReturnValue_1;                  // 0x0031(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_6;            // 0x0032(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_7;            // 0x0033(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Array_Contains_ReturnValue;               // 0x0034(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Array_Contains_ReturnValue_1;             // 0x0035(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Array_Contains_ReturnValue_2;             // 0x0036(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Array_Contains_ReturnValue_3;             // 0x0037(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter) == 0x000004, "Wrong alignment on WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter");
static_assert(sizeof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter) == 0x000038, "Wrong size on WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter");
static_assert(offsetof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter, EntryPoint) == 0x000000, "Member 'WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter, Temp_byte_Variable) == 0x000004, "Member 'WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter::Temp_byte_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter, CallFunc_MakeLiteralByte_ReturnValue) == 0x000005, "Member 'WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000006, "Member 'WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter, CallFunc_MakeLiteralByte_ReturnValue_2) == 0x000007, "Member 'WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter::CallFunc_MakeLiteralByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter, K2Node_CreateDelegate_OutputDelegate) == 0x000008, "Member 'WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter, CallFunc_MakeLiteralByte_ReturnValue_3) == 0x000018, "Member 'WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter::CallFunc_MakeLiteralByte_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter, Temp_byte_Variable_1) == 0x000019, "Member 'WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter::Temp_byte_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter, Temp_byte_Variable_2) == 0x00001A, "Member 'WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter::Temp_byte_Variable_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter, CallFunc_MakeLiteralByte_ReturnValue_4) == 0x00001B, "Member 'WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter::CallFunc_MakeLiteralByte_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter, Temp_byte_Variable_3) == 0x00001C, "Member 'WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter::Temp_byte_Variable_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter, CallFunc_MakeLiteralByte_ReturnValue_5) == 0x00001D, "Member 'WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter::CallFunc_MakeLiteralByte_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter, K2Node_Event_IsDesignTime) == 0x00001E, "Member 'WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter, CallFunc_IsVisible_ReturnValue) == 0x00001F, "Member 'WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter::CallFunc_IsVisible_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter, K2Node_Event_InFocusEvent_1) == 0x000020, "Member 'WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter::K2Node_Event_InFocusEvent_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter, K2Node_Event_InFocusEvent) == 0x000028, "Member 'WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter::K2Node_Event_InFocusEvent' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter, K2Node_SwitchEnum_CmpSuccess) == 0x000030, "Member 'WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter, CallFunc_IsVisible_ReturnValue_1) == 0x000031, "Member 'WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter::CallFunc_IsVisible_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter, CallFunc_MakeLiteralByte_ReturnValue_6) == 0x000032, "Member 'WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter::CallFunc_MakeLiteralByte_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter, CallFunc_MakeLiteralByte_ReturnValue_7) == 0x000033, "Member 'WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter::CallFunc_MakeLiteralByte_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter, CallFunc_Array_Contains_ReturnValue) == 0x000034, "Member 'WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter::CallFunc_Array_Contains_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter, CallFunc_Array_Contains_ReturnValue_1) == 0x000035, "Member 'WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter::CallFunc_Array_Contains_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter, CallFunc_Array_Contains_ReturnValue_2) == 0x000036, "Member 'WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter::CallFunc_Array_Contains_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter, CallFunc_Array_Contains_ReturnValue_3) == 0x000037, "Member 'WDG_ShowroomFilter_C_ExecuteUbergraph_WDG_ShowroomFilter::CallFunc_Array_Contains_ReturnValue_3' has a wrong offset!");

// Function WDG_ShowroomFilter.WDG_ShowroomFilter_C.OnRemovedFromFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomFilter_C_OnRemovedFromFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_ShowroomFilter_C_OnRemovedFromFocusPath) == 0x000004, "Wrong alignment on WDG_ShowroomFilter_C_OnRemovedFromFocusPath");
static_assert(sizeof(WDG_ShowroomFilter_C_OnRemovedFromFocusPath) == 0x000008, "Wrong size on WDG_ShowroomFilter_C_OnRemovedFromFocusPath");
static_assert(offsetof(WDG_ShowroomFilter_C_OnRemovedFromFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_ShowroomFilter_C_OnRemovedFromFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_ShowroomFilter.WDG_ShowroomFilter_C.OnAddedToFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomFilter_C_OnAddedToFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_ShowroomFilter_C_OnAddedToFocusPath) == 0x000004, "Wrong alignment on WDG_ShowroomFilter_C_OnAddedToFocusPath");
static_assert(sizeof(WDG_ShowroomFilter_C_OnAddedToFocusPath) == 0x000008, "Wrong size on WDG_ShowroomFilter_C_OnAddedToFocusPath");
static_assert(offsetof(WDG_ShowroomFilter_C_OnAddedToFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_ShowroomFilter_C_OnAddedToFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_ShowroomFilter.WDG_ShowroomFilter_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_ShowroomFilter_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomFilter_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_ShowroomFilter_C_PreConstruct");
static_assert(sizeof(WDG_ShowroomFilter_C_PreConstruct) == 0x000001, "Wrong size on WDG_ShowroomFilter_C_PreConstruct");
static_assert(offsetof(WDG_ShowroomFilter_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_ShowroomFilter_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WDG_ShowroomFilter.WDG_ShowroomFilter_C.UpdateTextLabel
// 0x0020 (0x0020 - 0x0000)
struct WDG_ShowroomFilter_C_UpdateTextLabel final
{
public:
	class FText                                   CallFunc_Map_Find_Value;                           // 0x0000(0x0018)()
	bool                                          CallFunc_Map_Find_ReturnValue;                     // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomFilter_C_UpdateTextLabel) == 0x000008, "Wrong alignment on WDG_ShowroomFilter_C_UpdateTextLabel");
static_assert(sizeof(WDG_ShowroomFilter_C_UpdateTextLabel) == 0x000020, "Wrong size on WDG_ShowroomFilter_C_UpdateTextLabel");
static_assert(offsetof(WDG_ShowroomFilter_C_UpdateTextLabel, CallFunc_Map_Find_Value) == 0x000000, "Member 'WDG_ShowroomFilter_C_UpdateTextLabel::CallFunc_Map_Find_Value' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomFilter_C_UpdateTextLabel, CallFunc_Map_Find_ReturnValue) == 0x000018, "Member 'WDG_ShowroomFilter_C_UpdateTextLabel::CallFunc_Map_Find_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomFilter.WDG_ShowroomFilter_C.SetActiveFilter
// 0x0001 (0x0001 - 0x0000)
struct WDG_ShowroomFilter_C_SetActiveFilter final
{
public:
	EShowroomCarFilterType                        activeFilter_0;                                    // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomFilter_C_SetActiveFilter) == 0x000001, "Wrong alignment on WDG_ShowroomFilter_C_SetActiveFilter");
static_assert(sizeof(WDG_ShowroomFilter_C_SetActiveFilter) == 0x000001, "Wrong size on WDG_ShowroomFilter_C_SetActiveFilter");
static_assert(offsetof(WDG_ShowroomFilter_C_SetActiveFilter, activeFilter_0) == 0x000000, "Member 'WDG_ShowroomFilter_C_SetActiveFilter::activeFilter_0' has a wrong offset!");

}

