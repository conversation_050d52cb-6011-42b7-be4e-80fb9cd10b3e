﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ServerInfoSessionItem

#include "Basic.hpp"

#include "WDG_ServerInfoSessionItem_classes.hpp"
#include "WDG_ServerInfoSessionItem_parameters.hpp"


namespace SDK
{

// Function WDG_ServerInfoSessionItem.WDG_ServerInfoSessionItem_C.ExecuteUbergraph_WDG_ServerInfoSessionItem
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ServerInfoSessionItem_C::ExecuteUbergraph_WDG_ServerInfoSessionItem(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ServerInfoSessionItem_C", "ExecuteUbergraph_WDG_ServerInfoSessionItem");

	Params::WDG_ServerInfoSessionItem_C_ExecuteUbergraph_WDG_ServerInfoSessionItem Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ServerInfoSessionItem.WDG_ServerInfoSessionItem_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_ServerInfoSessionItem_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ServerInfoSessionItem_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ServerInfoSessionItem.WDG_ServerInfoSessionItem_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ServerInfoSessionItem_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ServerInfoSessionItem_C", "PreConstruct");

	Params::WDG_ServerInfoSessionItem_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}

}

