﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_WeatherTypePanel

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_WeatherTypePanel.WDG_WeatherTypePanel_C
// 0x0098 (0x0678 - 0x05E0)
class UWDG_WeatherTypePanel_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       Opacity;                                           // 0x05E8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       Scale;                                             // 0x05F0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UBorder*                                background;                                        // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtIcon;                                           // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtMainTitle;                                      // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	struct FMeteoInfos                            meteoInfo;                                         // 0x0610(0x0040)(Edit, BlueprintVisible, DisableEditOnInstance)
	EWeatherPresetType                            weatherType;                                       // 0x0650(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          IsSelected;                                        // 0x0651(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_652[0x6];                                      // 0x0652(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	TMulticastInlineDelegate<void(bool IsSelected, class UWDG_WeatherTypePanel_C* Source)> SelectedChanged; // 0x0658(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void(EWeatherPresetType weatherType, class UWDG_WeatherTypePanel_C* Source)> OnSelected; // 0x0668(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)

public:
	void ExecuteUbergraph_WDG_WeatherTypePanel(int32 EntryPoint);
	void Forward();
	void Construct();
	void OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent);
	void OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent);
	void PreConstruct(bool IsDesignTime);
	void Set_Colors_Based_on_Selected(bool is_selected);
	void SetSelected(bool IsSelected_0, bool* Changed);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_WeatherTypePanel_C">();
	}
	static class UWDG_WeatherTypePanel_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_WeatherTypePanel_C>();
	}
};
static_assert(alignof(UWDG_WeatherTypePanel_C) == 0x000008, "Wrong alignment on UWDG_WeatherTypePanel_C");
static_assert(sizeof(UWDG_WeatherTypePanel_C) == 0x000678, "Wrong size on UWDG_WeatherTypePanel_C");
static_assert(offsetof(UWDG_WeatherTypePanel_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_WeatherTypePanel_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherTypePanel_C, Opacity) == 0x0005E8, "Member 'UWDG_WeatherTypePanel_C::Opacity' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherTypePanel_C, Scale) == 0x0005F0, "Member 'UWDG_WeatherTypePanel_C::Scale' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherTypePanel_C, background) == 0x0005F8, "Member 'UWDG_WeatherTypePanel_C::background' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherTypePanel_C, txtIcon) == 0x000600, "Member 'UWDG_WeatherTypePanel_C::txtIcon' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherTypePanel_C, txtMainTitle) == 0x000608, "Member 'UWDG_WeatherTypePanel_C::txtMainTitle' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherTypePanel_C, meteoInfo) == 0x000610, "Member 'UWDG_WeatherTypePanel_C::meteoInfo' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherTypePanel_C, weatherType) == 0x000650, "Member 'UWDG_WeatherTypePanel_C::weatherType' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherTypePanel_C, IsSelected) == 0x000651, "Member 'UWDG_WeatherTypePanel_C::IsSelected' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherTypePanel_C, SelectedChanged) == 0x000658, "Member 'UWDG_WeatherTypePanel_C::SelectedChanged' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherTypePanel_C, OnSelected) == 0x000668, "Member 'UWDG_WeatherTypePanel_C::OnSelected' has a wrong offset!");

}

