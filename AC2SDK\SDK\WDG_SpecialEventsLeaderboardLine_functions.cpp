﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SpecialEventsLeaderboardLine

#include "Basic.hpp"

#include "WDG_SpecialEventsLeaderboardLine_classes.hpp"
#include "WDG_SpecialEventsLeaderboardLine_parameters.hpp"


namespace SDK
{

// Function WDG_SpecialEventsLeaderboardLine.WDG_SpecialEventsLeaderboardLine_C.ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SpecialEventsLeaderboardLine_C::ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventsLeaderboardLine_C", "ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine");

	Params::WDG_SpecialEventsLeaderboardLine_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardLine Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEventsLeaderboardLine.WDG_SpecialEventsLeaderboardLine_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_SpecialEventsLeaderboardLine_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventsLeaderboardLine_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}

}

