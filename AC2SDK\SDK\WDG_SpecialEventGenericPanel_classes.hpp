﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SpecialEventGenericPanel

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SpecialEventGenericPanel.WDG_SpecialEventGenericPanel_C
// 0x0060 (0x0640 - 0x05E0)
class UWDG_SpecialEventGenericPanel_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       Scale;                                             // 0x05E8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       Hover;                                             // 0x05F0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 BottomHighlight;                                   // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             IconSlot;                                          // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_0;                                           // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 LeftHighlight;                                     // 0x0610(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             LevelSlot;                                         // 0x0618(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             PercentSlot;                                       // 0x0620(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 RightHighlight;                                    // 0x0628(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             TitleSlot;                                         // 0x0630(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 TopHighlight;                                      // 0x0638(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_SpecialEventGenericPanel(int32 EntryPoint);
	void BP_MouseLeave();
	void BP_MouseOver();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SpecialEventGenericPanel_C">();
	}
	static class UWDG_SpecialEventGenericPanel_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SpecialEventGenericPanel_C>();
	}
};
static_assert(alignof(UWDG_SpecialEventGenericPanel_C) == 0x000008, "Wrong alignment on UWDG_SpecialEventGenericPanel_C");
static_assert(sizeof(UWDG_SpecialEventGenericPanel_C) == 0x000640, "Wrong size on UWDG_SpecialEventGenericPanel_C");
static_assert(offsetof(UWDG_SpecialEventGenericPanel_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_SpecialEventGenericPanel_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventGenericPanel_C, Scale) == 0x0005E8, "Member 'UWDG_SpecialEventGenericPanel_C::Scale' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventGenericPanel_C, Hover) == 0x0005F0, "Member 'UWDG_SpecialEventGenericPanel_C::Hover' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventGenericPanel_C, BottomHighlight) == 0x0005F8, "Member 'UWDG_SpecialEventGenericPanel_C::BottomHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventGenericPanel_C, IconSlot) == 0x000600, "Member 'UWDG_SpecialEventGenericPanel_C::IconSlot' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventGenericPanel_C, Image_0) == 0x000608, "Member 'UWDG_SpecialEventGenericPanel_C::Image_0' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventGenericPanel_C, LeftHighlight) == 0x000610, "Member 'UWDG_SpecialEventGenericPanel_C::LeftHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventGenericPanel_C, LevelSlot) == 0x000618, "Member 'UWDG_SpecialEventGenericPanel_C::LevelSlot' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventGenericPanel_C, PercentSlot) == 0x000620, "Member 'UWDG_SpecialEventGenericPanel_C::PercentSlot' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventGenericPanel_C, RightHighlight) == 0x000628, "Member 'UWDG_SpecialEventGenericPanel_C::RightHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventGenericPanel_C, TitleSlot) == 0x000630, "Member 'UWDG_SpecialEventGenericPanel_C::TitleSlot' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventGenericPanel_C, TopHighlight) == 0x000638, "Member 'UWDG_SpecialEventGenericPanel_C::TopHighlight' has a wrong offset!");

}

