﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_MPQuickjoinSlotCPInviteSlot

#include "Basic.hpp"

#include "WDG_MPQuickjoinSlotCPInviteSlot_classes.hpp"
#include "WDG_MPQuickjoinSlotCPInviteSlot_parameters.hpp"


namespace SDK
{

// Function WDG_MPQuickjoinSlotCPInviteSlot.WDG_MPQuickjoinSlotCPInviteSlot_C.ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_MPQuickjoinSlotCPInviteSlot_C::ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MPQuickjoinSlotCPInviteSlot_C", "ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot");

	Params::WDG_MPQuickjoinSlotCPInviteSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlotCPInviteSlot Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MPQuickjoinSlotCPInviteSlot.WDG_MPQuickjoinSlotCPInviteSlot_C.OnSetInviteState
// (BlueprintCallable, BlueprintEvent)

void UWDG_MPQuickjoinSlotCPInviteSlot_C::OnSetInviteState()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MPQuickjoinSlotCPInviteSlot_C", "OnSetInviteState");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MPQuickjoinSlotCPInviteSlot.WDG_MPQuickjoinSlotCPInviteSlot_C.OnManualDataRefresh
// (BlueprintCallable, BlueprintEvent)

void UWDG_MPQuickjoinSlotCPInviteSlot_C::OnManualDataRefresh()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MPQuickjoinSlotCPInviteSlot_C", "OnManualDataRefresh");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MPQuickjoinSlotCPInviteSlot.WDG_MPQuickjoinSlotCPInviteSlot_C.BP_MouseLeave
// (Event, Public, BlueprintEvent)

void UWDG_MPQuickjoinSlotCPInviteSlot_C::BP_MouseLeave()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MPQuickjoinSlotCPInviteSlot_C", "BP_MouseLeave");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MPQuickjoinSlotCPInviteSlot.WDG_MPQuickjoinSlotCPInviteSlot_C.BP_MouseOver
// (Event, Public, BlueprintEvent)

void UWDG_MPQuickjoinSlotCPInviteSlot_C::BP_MouseOver()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MPQuickjoinSlotCPInviteSlot_C", "BP_MouseOver");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MPQuickjoinSlotCPInviteSlot.WDG_MPQuickjoinSlotCPInviteSlot_C.Tick
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// float                                   InDeltaTime                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_MPQuickjoinSlotCPInviteSlot_C::Tick(const struct FGeometry& MyGeometry, float InDeltaTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MPQuickjoinSlotCPInviteSlot_C", "Tick");

	Params::WDG_MPQuickjoinSlotCPInviteSlot_C_Tick Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InDeltaTime = InDeltaTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MPQuickjoinSlotCPInviteSlot.WDG_MPQuickjoinSlotCPInviteSlot_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_MPQuickjoinSlotCPInviteSlot_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MPQuickjoinSlotCPInviteSlot_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_MPQuickjoinSlotCPInviteSlot.WDG_MPQuickjoinSlotCPInviteSlot_C.SetInviteState
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FOnlineServicesCPInvitationState&inviteState                                            (BlueprintVisible, BlueprintReadOnly, Parm)
// const class FText&                      waitingIndicatorText                                   (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_MPQuickjoinSlotCPInviteSlot_C::SetInviteState(const struct FOnlineServicesCPInvitationState& inviteState, const class FText& waitingIndicatorText)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MPQuickjoinSlotCPInviteSlot_C", "SetInviteState");

	Params::WDG_MPQuickjoinSlotCPInviteSlot_C_SetInviteState Parms{};

	Parms.inviteState = std::move(inviteState);
	Parms.waitingIndicatorText = std::move(waitingIndicatorText);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MPQuickjoinSlotCPInviteSlot.WDG_MPQuickjoinSlotCPInviteSlot_C.GetMinutesRemainingText
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FDateTime&                 SessionEndUtc                                          (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
// class FText*                            SessionEndText                                         (Parm, OutParm)

void UWDG_MPQuickjoinSlotCPInviteSlot_C::GetMinutesRemainingText(const struct FDateTime& SessionEndUtc, class FText* SessionEndText)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MPQuickjoinSlotCPInviteSlot_C", "GetMinutesRemainingText");

	Params::WDG_MPQuickjoinSlotCPInviteSlot_C_GetMinutesRemainingText Parms{};

	Parms.SessionEndUtc = std::move(SessionEndUtc);

	UObject::ProcessEvent(Func, &Parms);

	if (SessionEndText != nullptr)
		*SessionEndText = std::move(Parms.SessionEndText);
}


// Function WDG_MPQuickjoinSlotCPInviteSlot.WDG_MPQuickjoinSlotCPInviteSlot_C.UpdateTimer
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool*                                   TimeUp                                                 (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_MPQuickjoinSlotCPInviteSlot_C::UpdateTimer(bool* TimeUp)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MPQuickjoinSlotCPInviteSlot_C", "UpdateTimer");

	Params::WDG_MPQuickjoinSlotCPInviteSlot_C_UpdateTimer Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (TimeUp != nullptr)
		*TimeUp = Parms.TimeUp;
}


// Function WDG_MPQuickjoinSlotCPInviteSlot.WDG_MPQuickjoinSlotCPInviteSlot_C.SetCountdownOrErrorMessage
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    isError                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
// const class FText&                      text                                                   (BlueprintVisible, BlueprintReadOnly, Parm)
// const struct FLinearColor&              BackgroundColor                                        (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FLinearColor&              ForegroundColor_0                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    UsePulseAnimation                                      (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_MPQuickjoinSlotCPInviteSlot_C::SetCountdownOrErrorMessage(bool isError, const class FText& text, const struct FLinearColor& BackgroundColor, const struct FLinearColor& ForegroundColor_0, bool UsePulseAnimation)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MPQuickjoinSlotCPInviteSlot_C", "SetCountdownOrErrorMessage");

	Params::WDG_MPQuickjoinSlotCPInviteSlot_C_SetCountdownOrErrorMessage Parms{};

	Parms.isError = isError;
	Parms.text = std::move(text);
	Parms.BackgroundColor = std::move(BackgroundColor);
	Parms.ForegroundColor_0 = std::move(ForegroundColor_0);
	Parms.UsePulseAnimation = UsePulseAnimation;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MPQuickjoinSlotCPInviteSlot.WDG_MPQuickjoinSlotCPInviteSlot_C.FormatBanMessage
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   Days                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class FText*                            text                                                   (Parm, OutParm)

void UWDG_MPQuickjoinSlotCPInviteSlot_C::FormatBanMessage(int32 Days, class FText* text)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MPQuickjoinSlotCPInviteSlot_C", "FormatBanMessage");

	Params::WDG_MPQuickjoinSlotCPInviteSlot_C_FormatBanMessage Parms{};

	Parms.Days = Days;

	UObject::ProcessEvent(Func, &Parms);

	if (text != nullptr)
		*text = std::move(Parms.text);
}

}

