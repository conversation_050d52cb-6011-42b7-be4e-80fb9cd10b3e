﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_Page

#include "Basic.hpp"


namespace SDK::Params
{

// Function WDG_Page.WDG_Page_C.ExecuteUbergraph_WDG_Page
// 0x0108 (0x0108 - 0x0000)
struct WDG_Page_C_ExecuteUbergraph_WDG_Page final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class AGameModeBase*                          CallFunc_GetGameMode_ReturnValue;                  // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AAcRaceGameMode*                        K2Node_DynamicCast_AsAc_Race_Game_Mode;            // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_19[0x7];                                       // 0x0019(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class AAcMenuGameMode*                        K2Node_DynamicCast_AsAc_Menu_Game_Mode;            // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x0028(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_29[0x7];                                       // 0x0029(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UViewOptionsLibrary*                    CallFunc_getViewLibrary_ReturnValue;               // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UViewOptionsLibrary*                    CallFunc_getViewLibrary_ReturnValue_1;             // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_GetProjectVersion_ReturnValue;            // 0x0040(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_GetProjectVersion_ReturnValue_1;          // 0x0050(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x0060(0x0018)()
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_1;          // 0x0078(0x0018)()
	class FString                                 CallFunc_Conv_TextToString_ReturnValue;            // 0x0090(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_TextToString_ReturnValue_1;          // 0x00A0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue;                // 0x00B0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_1;              // 0x00C0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_2;          // 0x00D0(0x0018)()
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_3;          // 0x00E8(0x0018)()
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0100(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_Page_C_ExecuteUbergraph_WDG_Page) == 0x000008, "Wrong alignment on WDG_Page_C_ExecuteUbergraph_WDG_Page");
static_assert(sizeof(WDG_Page_C_ExecuteUbergraph_WDG_Page) == 0x000108, "Wrong size on WDG_Page_C_ExecuteUbergraph_WDG_Page");
static_assert(offsetof(WDG_Page_C_ExecuteUbergraph_WDG_Page, EntryPoint) == 0x000000, "Member 'WDG_Page_C_ExecuteUbergraph_WDG_Page::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_Page_C_ExecuteUbergraph_WDG_Page, CallFunc_GetGameMode_ReturnValue) == 0x000008, "Member 'WDG_Page_C_ExecuteUbergraph_WDG_Page::CallFunc_GetGameMode_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_Page_C_ExecuteUbergraph_WDG_Page, K2Node_DynamicCast_AsAc_Race_Game_Mode) == 0x000010, "Member 'WDG_Page_C_ExecuteUbergraph_WDG_Page::K2Node_DynamicCast_AsAc_Race_Game_Mode' has a wrong offset!");
static_assert(offsetof(WDG_Page_C_ExecuteUbergraph_WDG_Page, K2Node_DynamicCast_bSuccess) == 0x000018, "Member 'WDG_Page_C_ExecuteUbergraph_WDG_Page::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_Page_C_ExecuteUbergraph_WDG_Page, K2Node_DynamicCast_AsAc_Menu_Game_Mode) == 0x000020, "Member 'WDG_Page_C_ExecuteUbergraph_WDG_Page::K2Node_DynamicCast_AsAc_Menu_Game_Mode' has a wrong offset!");
static_assert(offsetof(WDG_Page_C_ExecuteUbergraph_WDG_Page, K2Node_DynamicCast_bSuccess_1) == 0x000028, "Member 'WDG_Page_C_ExecuteUbergraph_WDG_Page::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_Page_C_ExecuteUbergraph_WDG_Page, CallFunc_getViewLibrary_ReturnValue) == 0x000030, "Member 'WDG_Page_C_ExecuteUbergraph_WDG_Page::CallFunc_getViewLibrary_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_Page_C_ExecuteUbergraph_WDG_Page, CallFunc_getViewLibrary_ReturnValue_1) == 0x000038, "Member 'WDG_Page_C_ExecuteUbergraph_WDG_Page::CallFunc_getViewLibrary_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_Page_C_ExecuteUbergraph_WDG_Page, CallFunc_GetProjectVersion_ReturnValue) == 0x000040, "Member 'WDG_Page_C_ExecuteUbergraph_WDG_Page::CallFunc_GetProjectVersion_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_Page_C_ExecuteUbergraph_WDG_Page, CallFunc_GetProjectVersion_ReturnValue_1) == 0x000050, "Member 'WDG_Page_C_ExecuteUbergraph_WDG_Page::CallFunc_GetProjectVersion_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_Page_C_ExecuteUbergraph_WDG_Page, CallFunc_Conv_StringToText_ReturnValue) == 0x000060, "Member 'WDG_Page_C_ExecuteUbergraph_WDG_Page::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_Page_C_ExecuteUbergraph_WDG_Page, CallFunc_Conv_StringToText_ReturnValue_1) == 0x000078, "Member 'WDG_Page_C_ExecuteUbergraph_WDG_Page::CallFunc_Conv_StringToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_Page_C_ExecuteUbergraph_WDG_Page, CallFunc_Conv_TextToString_ReturnValue) == 0x000090, "Member 'WDG_Page_C_ExecuteUbergraph_WDG_Page::CallFunc_Conv_TextToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_Page_C_ExecuteUbergraph_WDG_Page, CallFunc_Conv_TextToString_ReturnValue_1) == 0x0000A0, "Member 'WDG_Page_C_ExecuteUbergraph_WDG_Page::CallFunc_Conv_TextToString_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_Page_C_ExecuteUbergraph_WDG_Page, CallFunc_Concat_StrStr_ReturnValue) == 0x0000B0, "Member 'WDG_Page_C_ExecuteUbergraph_WDG_Page::CallFunc_Concat_StrStr_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_Page_C_ExecuteUbergraph_WDG_Page, CallFunc_Concat_StrStr_ReturnValue_1) == 0x0000C0, "Member 'WDG_Page_C_ExecuteUbergraph_WDG_Page::CallFunc_Concat_StrStr_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_Page_C_ExecuteUbergraph_WDG_Page, CallFunc_Conv_StringToText_ReturnValue_2) == 0x0000D0, "Member 'WDG_Page_C_ExecuteUbergraph_WDG_Page::CallFunc_Conv_StringToText_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_Page_C_ExecuteUbergraph_WDG_Page, CallFunc_Conv_StringToText_ReturnValue_3) == 0x0000E8, "Member 'WDG_Page_C_ExecuteUbergraph_WDG_Page::CallFunc_Conv_StringToText_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_Page_C_ExecuteUbergraph_WDG_Page, K2Node_Event_IsDesignTime) == 0x000100, "Member 'WDG_Page_C_ExecuteUbergraph_WDG_Page::K2Node_Event_IsDesignTime' has a wrong offset!");

// Function WDG_Page.WDG_Page_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_Page_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_Page_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_Page_C_PreConstruct");
static_assert(sizeof(WDG_Page_C_PreConstruct) == 0x000001, "Wrong size on WDG_Page_C_PreConstruct");
static_assert(offsetof(WDG_Page_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_Page_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WDG_Page.WDG_Page_C.UseBlurInsteadOfBackground
// 0x0018 (0x0018 - 0x0000)
struct WDG_Page_C_UseBlurInsteadOfBackground final
{
public:
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue;              // 0x0000(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance;             // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_IsHMDEnabled_ReturnValue;                 // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_Page_C_UseBlurInsteadOfBackground) == 0x000008, "Wrong alignment on WDG_Page_C_UseBlurInsteadOfBackground");
static_assert(sizeof(WDG_Page_C_UseBlurInsteadOfBackground) == 0x000018, "Wrong size on WDG_Page_C_UseBlurInsteadOfBackground");
static_assert(offsetof(WDG_Page_C_UseBlurInsteadOfBackground, CallFunc_GetGameInstance_ReturnValue) == 0x000000, "Member 'WDG_Page_C_UseBlurInsteadOfBackground::CallFunc_GetGameInstance_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_Page_C_UseBlurInsteadOfBackground, K2Node_DynamicCast_AsAc_Game_Instance) == 0x000008, "Member 'WDG_Page_C_UseBlurInsteadOfBackground::K2Node_DynamicCast_AsAc_Game_Instance' has a wrong offset!");
static_assert(offsetof(WDG_Page_C_UseBlurInsteadOfBackground, K2Node_DynamicCast_bSuccess) == 0x000010, "Member 'WDG_Page_C_UseBlurInsteadOfBackground::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_Page_C_UseBlurInsteadOfBackground, CallFunc_IsHMDEnabled_ReturnValue) == 0x000011, "Member 'WDG_Page_C_UseBlurInsteadOfBackground::CallFunc_IsHMDEnabled_ReturnValue' has a wrong offset!");

}

