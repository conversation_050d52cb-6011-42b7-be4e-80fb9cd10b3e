﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PresetsItem

#include "Basic.hpp"

#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_PresetsItem.WDG_PresetsItem_C.ExecuteUbergraph_WDG_PresetsItem
// 0x0108 (0x0108 - 0x0000)
struct WDG_PresetsItem_C_ExecuteUbergraph_WDG_PresetsItem final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateColor                            CallFunc_GetMenuColor_ReturnValue;                 // 0x0008(0x0028)()
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_1;              // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSlateColor                            CallFunc_GetMenuColor_ReturnValue_1;               // 0x0040(0x0028)()
	struct FSlateColor                            CallFunc_GetMenuColor_ReturnValue_2;               // 0x0068(0x0028)()
	struct FSlateColor                            CallFunc_GetMenuColor_ReturnValue_3;               // 0x0090(0x0028)()
	struct FSlateColor                            CallFunc_GetMenuColor_ReturnValue_4;               // 0x00B8(0x0028)()
	struct FSlateColor                            CallFunc_GetMenuColor_ReturnValue_5;               // 0x00E0(0x0028)()
};
static_assert(alignof(WDG_PresetsItem_C_ExecuteUbergraph_WDG_PresetsItem) == 0x000008, "Wrong alignment on WDG_PresetsItem_C_ExecuteUbergraph_WDG_PresetsItem");
static_assert(sizeof(WDG_PresetsItem_C_ExecuteUbergraph_WDG_PresetsItem) == 0x000108, "Wrong size on WDG_PresetsItem_C_ExecuteUbergraph_WDG_PresetsItem");
static_assert(offsetof(WDG_PresetsItem_C_ExecuteUbergraph_WDG_PresetsItem, EntryPoint) == 0x000000, "Member 'WDG_PresetsItem_C_ExecuteUbergraph_WDG_PresetsItem::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_PresetsItem_C_ExecuteUbergraph_WDG_PresetsItem, CallFunc_GetMenuColor_ReturnValue) == 0x000008, "Member 'WDG_PresetsItem_C_ExecuteUbergraph_WDG_PresetsItem::CallFunc_GetMenuColor_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PresetsItem_C_ExecuteUbergraph_WDG_PresetsItem, CallFunc_PlayAnimation_ReturnValue) == 0x000030, "Member 'WDG_PresetsItem_C_ExecuteUbergraph_WDG_PresetsItem::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_PresetsItem_C_ExecuteUbergraph_WDG_PresetsItem, CallFunc_PlayAnimation_ReturnValue_1) == 0x000038, "Member 'WDG_PresetsItem_C_ExecuteUbergraph_WDG_PresetsItem::CallFunc_PlayAnimation_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PresetsItem_C_ExecuteUbergraph_WDG_PresetsItem, CallFunc_GetMenuColor_ReturnValue_1) == 0x000040, "Member 'WDG_PresetsItem_C_ExecuteUbergraph_WDG_PresetsItem::CallFunc_GetMenuColor_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_PresetsItem_C_ExecuteUbergraph_WDG_PresetsItem, CallFunc_GetMenuColor_ReturnValue_2) == 0x000068, "Member 'WDG_PresetsItem_C_ExecuteUbergraph_WDG_PresetsItem::CallFunc_GetMenuColor_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_PresetsItem_C_ExecuteUbergraph_WDG_PresetsItem, CallFunc_GetMenuColor_ReturnValue_3) == 0x000090, "Member 'WDG_PresetsItem_C_ExecuteUbergraph_WDG_PresetsItem::CallFunc_GetMenuColor_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_PresetsItem_C_ExecuteUbergraph_WDG_PresetsItem, CallFunc_GetMenuColor_ReturnValue_4) == 0x0000B8, "Member 'WDG_PresetsItem_C_ExecuteUbergraph_WDG_PresetsItem::CallFunc_GetMenuColor_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_PresetsItem_C_ExecuteUbergraph_WDG_PresetsItem, CallFunc_GetMenuColor_ReturnValue_5) == 0x0000E0, "Member 'WDG_PresetsItem_C_ExecuteUbergraph_WDG_PresetsItem::CallFunc_GetMenuColor_ReturnValue_5' has a wrong offset!");

}

