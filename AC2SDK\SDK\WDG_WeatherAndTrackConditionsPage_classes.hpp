﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_WeatherAndTrackConditionsPage

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "AC2_classes.hpp"
#include "SlateCore_structs.hpp"
#include "Engine_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_WeatherAndTrackConditionsPage.WDG_WeatherAndTrackConditionsPage_C
// 0x0190 (0x06B8 - 0x0528)
class UWDG_WeatherAndTrackConditionsPage_C final : public UAcPageBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0528(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       PageFade;                                          // 0x0530(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldD<PERSON>, <PERSON><PERSON><PERSON><PERSON>, NoDestru<PERSON>, HasGetValueTypeHash)
	class UWDG_FooterButton_C*                    Back;                                              // 0x0538(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 bgImage;                                           // 0x0540(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_WeatherTypePanel_C*                btnCloudy;                                         // 0x0548(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_WeatherTypePanel_C*                btnCustom;                                         // 0x0550(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_WeatherTypePanel_C*                btnHeavyRain;                                      // 0x0558(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_WeatherTypePanel_C*                btnLightRain;                                      // 0x0560(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_WeatherTypePanel_C*                btnMediumRain;                                     // 0x0568(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_WeatherTypePanel_C*                btnStorm;                                          // 0x0570(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_WeatherTypePanel_C*                btnSunny;                                          // 0x0578(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  Continue;                                          // 0x0580(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             modeTitle;                                         // 0x0588(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_HelpInMenu_C*                      RealismHelp;                                       // 0x0590(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           Settings;                                          // 0x0598(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_HorizontalSlider_C*                sliderAmbientTemperature;                          // 0x05A0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_HorizontalSlider_C*                sliderCloudCover;                                  // 0x05A8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_HorizontalSlider_C*                sliderDynamicWeather;                              // 0x05B0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_HorizontalSlider_C*                sliderGripLevel;                                   // 0x05B8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_HorizontalSlider_C*                sliderPrecipitation;                               // 0x05C0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_HorizontalSlider_C*                sliderRandomWeather;                               // 0x05C8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_HorizontalSlider_C*                sliderStandingWater;                               // 0x05D0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_HorizontalSlider_C*                sliderSurface;                                     // 0x05D8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_HorizontalSlider_C*                sliderVariability;                                 // 0x05E0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UVerticalBox*                           TrackSettings;                                     // 0x05E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  WDG_GenericBarItem;                                // 0x05F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UVerticalBox*                           WeatherPresetContainer;                            // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UVerticalBox*                           WeatherSettings;                                   // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	TMulticastInlineDelegate<void(EWeatherPresetType weatherType)> WeatherPresetChanged;             // 0x0608(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	EWeatherPresetType                            ActiveWeatherType;                                 // 0x0618(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_619[0x3];                                      // 0x0619(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FWeatherStatus                         ActiveWeatherStatus;                               // 0x061C(0x0020)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor)
	struct FWeatherData                           ActiveWeatherData;                                 // 0x063C(0x001C)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor)
	struct FTrackStatus                           ActiveTrackStatus;                                 // 0x0658(0x001C)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor)
	int32                                         DynamicWeather;                                    // 0x0674(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         MinimumCloudCoverForRain;                          // 0x0678(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         MaxRainForFullCloudCover;                          // 0x067C(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         RainLevelThreshold;                                // 0x0680(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         GripLevel;                                         // 0x0684(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         variability;                                       // 0x0688(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         T;                                                 // 0x068C(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_68D[0x3];                                      // 0x068D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         MaximumStandingWaterDryTrack;                      // 0x0690(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         MinimumWetnessForFloodedTrack;                     // 0x0694(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FTrackStatus                           NewVar_0;                                          // 0x0698(0x001C)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor)

public:
	void ExecuteUbergraph_WDG_WeatherAndTrackConditionsPage(int32 EntryPoint);
	void Variability_Changed(class UGenericSelectorItem* Source, int32 current_index, int32 current_value);
	void Dynamic_Weather_Changed(class UGenericSelectorItem* Source, int32 current_index, int32 current_value);
	void Ambient_Temperature_Changed(class UGenericSelectorItem* Source, int32 current_index, int32 current_value);
	void CustomEvent_0(class UGenericSelectorItem* Source, int32 current_index, int32 current_value);
	void Weather_Variability_Changed(class UGenericSelectorItem* Source, int32 current_index, int32 current_value);
	void BndEvt__sliderStandingWater_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature(class UGenericSelectorItem* Source, int32 current_index, int32 current_value);
	void BndEvt__sliderGripLevel_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature(class UGenericSelectorItem* Source, int32 current_index, int32 current_value);
	void BndEvt__sliderSurface_K2Node_ComponentBoundEvent_2_SelectorChanged__DelegateSignature(class UGenericSelectorItem* Source, int32 current_index, int32 current_value);
	void BndEvt__sliderRandomWeather_K2Node_ComponentBoundEvent_0_SelectorChanged__DelegateSignature(class UGenericSelectorItem* Source, int32 current_index, int32 current_value);
	void BndEvt__WDG_GenericBarItem_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature();
	void Weather_Preset_Changed(EWeatherPresetType weatherType);
	void BP_OnBackward();
	void BackButtonClicked();
	void OnForward();
	void CustomEvent_1(EWeatherPresetType weatherType, class UWDG_WeatherTypePanel_C* Source);
	void BP_StartPage();
	void CloudRainRules(bool ByCloudCover);
	void WetnessRules(bool ByWetness);
	class UWidget* FocusOnDynamicSlider(EUINavigation Navigation_0);
	struct FEventReply OnPreviewKeyDown(const struct FGeometry& MyGeometry, const struct FKeyEvent& InKeyEvent);
	void GetBiasedWetnessPuddles(float DryWeight, float CapForDry, float* MaxWetnessPuddles);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_WeatherAndTrackConditionsPage_C">();
	}
	static class UWDG_WeatherAndTrackConditionsPage_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_WeatherAndTrackConditionsPage_C>();
	}
};
static_assert(alignof(UWDG_WeatherAndTrackConditionsPage_C) == 0x000008, "Wrong alignment on UWDG_WeatherAndTrackConditionsPage_C");
static_assert(sizeof(UWDG_WeatherAndTrackConditionsPage_C) == 0x0006B8, "Wrong size on UWDG_WeatherAndTrackConditionsPage_C");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, UberGraphFrame) == 0x000528, "Member 'UWDG_WeatherAndTrackConditionsPage_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, PageFade) == 0x000530, "Member 'UWDG_WeatherAndTrackConditionsPage_C::PageFade' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, Back) == 0x000538, "Member 'UWDG_WeatherAndTrackConditionsPage_C::Back' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, bgImage) == 0x000540, "Member 'UWDG_WeatherAndTrackConditionsPage_C::bgImage' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, btnCloudy) == 0x000548, "Member 'UWDG_WeatherAndTrackConditionsPage_C::btnCloudy' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, btnCustom) == 0x000550, "Member 'UWDG_WeatherAndTrackConditionsPage_C::btnCustom' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, btnHeavyRain) == 0x000558, "Member 'UWDG_WeatherAndTrackConditionsPage_C::btnHeavyRain' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, btnLightRain) == 0x000560, "Member 'UWDG_WeatherAndTrackConditionsPage_C::btnLightRain' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, btnMediumRain) == 0x000568, "Member 'UWDG_WeatherAndTrackConditionsPage_C::btnMediumRain' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, btnStorm) == 0x000570, "Member 'UWDG_WeatherAndTrackConditionsPage_C::btnStorm' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, btnSunny) == 0x000578, "Member 'UWDG_WeatherAndTrackConditionsPage_C::btnSunny' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, Continue) == 0x000580, "Member 'UWDG_WeatherAndTrackConditionsPage_C::Continue' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, modeTitle) == 0x000588, "Member 'UWDG_WeatherAndTrackConditionsPage_C::modeTitle' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, RealismHelp) == 0x000590, "Member 'UWDG_WeatherAndTrackConditionsPage_C::RealismHelp' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, Settings) == 0x000598, "Member 'UWDG_WeatherAndTrackConditionsPage_C::Settings' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, sliderAmbientTemperature) == 0x0005A0, "Member 'UWDG_WeatherAndTrackConditionsPage_C::sliderAmbientTemperature' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, sliderCloudCover) == 0x0005A8, "Member 'UWDG_WeatherAndTrackConditionsPage_C::sliderCloudCover' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, sliderDynamicWeather) == 0x0005B0, "Member 'UWDG_WeatherAndTrackConditionsPage_C::sliderDynamicWeather' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, sliderGripLevel) == 0x0005B8, "Member 'UWDG_WeatherAndTrackConditionsPage_C::sliderGripLevel' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, sliderPrecipitation) == 0x0005C0, "Member 'UWDG_WeatherAndTrackConditionsPage_C::sliderPrecipitation' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, sliderRandomWeather) == 0x0005C8, "Member 'UWDG_WeatherAndTrackConditionsPage_C::sliderRandomWeather' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, sliderStandingWater) == 0x0005D0, "Member 'UWDG_WeatherAndTrackConditionsPage_C::sliderStandingWater' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, sliderSurface) == 0x0005D8, "Member 'UWDG_WeatherAndTrackConditionsPage_C::sliderSurface' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, sliderVariability) == 0x0005E0, "Member 'UWDG_WeatherAndTrackConditionsPage_C::sliderVariability' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, TrackSettings) == 0x0005E8, "Member 'UWDG_WeatherAndTrackConditionsPage_C::TrackSettings' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, WDG_GenericBarItem) == 0x0005F0, "Member 'UWDG_WeatherAndTrackConditionsPage_C::WDG_GenericBarItem' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, WeatherPresetContainer) == 0x0005F8, "Member 'UWDG_WeatherAndTrackConditionsPage_C::WeatherPresetContainer' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, WeatherSettings) == 0x000600, "Member 'UWDG_WeatherAndTrackConditionsPage_C::WeatherSettings' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, WeatherPresetChanged) == 0x000608, "Member 'UWDG_WeatherAndTrackConditionsPage_C::WeatherPresetChanged' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, ActiveWeatherType) == 0x000618, "Member 'UWDG_WeatherAndTrackConditionsPage_C::ActiveWeatherType' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, ActiveWeatherStatus) == 0x00061C, "Member 'UWDG_WeatherAndTrackConditionsPage_C::ActiveWeatherStatus' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, ActiveWeatherData) == 0x00063C, "Member 'UWDG_WeatherAndTrackConditionsPage_C::ActiveWeatherData' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, ActiveTrackStatus) == 0x000658, "Member 'UWDG_WeatherAndTrackConditionsPage_C::ActiveTrackStatus' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, DynamicWeather) == 0x000674, "Member 'UWDG_WeatherAndTrackConditionsPage_C::DynamicWeather' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, MinimumCloudCoverForRain) == 0x000678, "Member 'UWDG_WeatherAndTrackConditionsPage_C::MinimumCloudCoverForRain' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, MaxRainForFullCloudCover) == 0x00067C, "Member 'UWDG_WeatherAndTrackConditionsPage_C::MaxRainForFullCloudCover' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, RainLevelThreshold) == 0x000680, "Member 'UWDG_WeatherAndTrackConditionsPage_C::RainLevelThreshold' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, GripLevel) == 0x000684, "Member 'UWDG_WeatherAndTrackConditionsPage_C::GripLevel' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, variability) == 0x000688, "Member 'UWDG_WeatherAndTrackConditionsPage_C::variability' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, T) == 0x00068C, "Member 'UWDG_WeatherAndTrackConditionsPage_C::T' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, MaximumStandingWaterDryTrack) == 0x000690, "Member 'UWDG_WeatherAndTrackConditionsPage_C::MaximumStandingWaterDryTrack' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, MinimumWetnessForFloodedTrack) == 0x000694, "Member 'UWDG_WeatherAndTrackConditionsPage_C::MinimumWetnessForFloodedTrack' has a wrong offset!");
static_assert(offsetof(UWDG_WeatherAndTrackConditionsPage_C, NewVar_0) == 0x000698, "Member 'UWDG_WeatherAndTrackConditionsPage_C::NewVar_0' has a wrong offset!");

}

