﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SetupAeroInfo

#include "Basic.hpp"

#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SetupAeroInfo.WDG_SetupAeroInfo_C
// 0x0020 (0x0280 - 0x0260)
class UWDG_SetupAeroInfo_C final : public UUserWidget
{
public:
	class UImage*                                 background;                                        // 0x0260(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             NamedSlot_3;                                       // 0x0268(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             OMItitle;                                          // 0x0270(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             TitleSlot;                                         // 0x0278(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SetupAeroInfo_C">();
	}
	static class UWDG_SetupAeroInfo_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SetupAeroInfo_C>();
	}
};
static_assert(alignof(UWDG_SetupAeroInfo_C) == 0x000008, "Wrong alignment on UWDG_SetupAeroInfo_C");
static_assert(sizeof(UWDG_SetupAeroInfo_C) == 0x000280, "Wrong size on UWDG_SetupAeroInfo_C");
static_assert(offsetof(UWDG_SetupAeroInfo_C, background) == 0x000260, "Member 'UWDG_SetupAeroInfo_C::background' has a wrong offset!");
static_assert(offsetof(UWDG_SetupAeroInfo_C, NamedSlot_3) == 0x000268, "Member 'UWDG_SetupAeroInfo_C::NamedSlot_3' has a wrong offset!");
static_assert(offsetof(UWDG_SetupAeroInfo_C, OMItitle) == 0x000270, "Member 'UWDG_SetupAeroInfo_C::OMItitle' has a wrong offset!");
static_assert(offsetof(UWDG_SetupAeroInfo_C, TitleSlot) == 0x000278, "Member 'UWDG_SetupAeroInfo_C::TitleSlot' has a wrong offset!");

}

