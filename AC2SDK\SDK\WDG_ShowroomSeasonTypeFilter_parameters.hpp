﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomSeasonTypeFilter

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_ShowroomSeasonTypeFilter.WDG_ShowroomSeasonTypeFilter_C.ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter
// 0x0050 (0x0050 - 0x0000)
struct WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0004(0x0004)(ZeroConstructor, Is<PERSON>lainOldData, NoD<PERSON>ru<PERSON>, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsVisible_ReturnValue;                    // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_12[0x2];                                       // 0x0012(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FFocusEvent                            K2Node_Event_InFocusEvent_1;                       // 0x0014(0x0008)(NoDestructor)
	struct FFocusEvent                            K2Node_Event_InFocusEvent;                         // 0x001C(0x0008)(NoDestructor)
	bool                                          CallFunc_IsVisible_ReturnValue_1;                  // 0x0024(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_25[0x3];                                       // 0x0025(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_GenericBarItem_C*                  CallFunc_Array_Get_Item;                           // 0x0028(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Conv_ByteToInt_ReturnValue;               // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0034(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0039(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_3A[0x2];                                       // 0x003A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate;              // 0x003C(0x0010)(ZeroConstructor, NoDestructor)
};
static_assert(alignof(WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter) == 0x000008, "Wrong alignment on WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter");
static_assert(sizeof(WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter) == 0x000050, "Wrong size on WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter, EntryPoint) == 0x000000, "Member 'WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter, Temp_int_Array_Index_Variable) == 0x000004, "Member 'WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter, Temp_int_Loop_Counter_Variable) == 0x000008, "Member 'WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter, CallFunc_Add_IntInt_ReturnValue) == 0x00000C, "Member 'WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter, K2Node_Event_IsDesignTime) == 0x000010, "Member 'WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter, CallFunc_IsVisible_ReturnValue) == 0x000011, "Member 'WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter::CallFunc_IsVisible_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter, K2Node_Event_InFocusEvent_1) == 0x000014, "Member 'WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter::K2Node_Event_InFocusEvent_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter, K2Node_Event_InFocusEvent) == 0x00001C, "Member 'WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter::K2Node_Event_InFocusEvent' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter, CallFunc_IsVisible_ReturnValue_1) == 0x000024, "Member 'WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter::CallFunc_IsVisible_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter, CallFunc_Array_Get_Item) == 0x000028, "Member 'WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter, CallFunc_Conv_ByteToInt_ReturnValue) == 0x000030, "Member 'WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter::CallFunc_Conv_ByteToInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter, CallFunc_Array_Length_ReturnValue) == 0x000034, "Member 'WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x000038, "Member 'WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter, CallFunc_Less_IntInt_ReturnValue) == 0x000039, "Member 'WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter, K2Node_CreateDelegate_OutputDelegate) == 0x00003C, "Member 'WDG_ShowroomSeasonTypeFilter_C_ExecuteUbergraph_WDG_ShowroomSeasonTypeFilter::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");

// Function WDG_ShowroomSeasonTypeFilter.WDG_ShowroomSeasonTypeFilter_C.OnRemovedFromFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomSeasonTypeFilter_C_OnRemovedFromFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_ShowroomSeasonTypeFilter_C_OnRemovedFromFocusPath) == 0x000004, "Wrong alignment on WDG_ShowroomSeasonTypeFilter_C_OnRemovedFromFocusPath");
static_assert(sizeof(WDG_ShowroomSeasonTypeFilter_C_OnRemovedFromFocusPath) == 0x000008, "Wrong size on WDG_ShowroomSeasonTypeFilter_C_OnRemovedFromFocusPath");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_OnRemovedFromFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_ShowroomSeasonTypeFilter_C_OnRemovedFromFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_ShowroomSeasonTypeFilter.WDG_ShowroomSeasonTypeFilter_C.OnAddedToFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomSeasonTypeFilter_C_OnAddedToFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_ShowroomSeasonTypeFilter_C_OnAddedToFocusPath) == 0x000004, "Wrong alignment on WDG_ShowroomSeasonTypeFilter_C_OnAddedToFocusPath");
static_assert(sizeof(WDG_ShowroomSeasonTypeFilter_C_OnAddedToFocusPath) == 0x000008, "Wrong size on WDG_ShowroomSeasonTypeFilter_C_OnAddedToFocusPath");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_OnAddedToFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_ShowroomSeasonTypeFilter_C_OnAddedToFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_ShowroomSeasonTypeFilter.WDG_ShowroomSeasonTypeFilter_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_ShowroomSeasonTypeFilter_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomSeasonTypeFilter_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_ShowroomSeasonTypeFilter_C_PreConstruct");
static_assert(sizeof(WDG_ShowroomSeasonTypeFilter_C_PreConstruct) == 0x000001, "Wrong size on WDG_ShowroomSeasonTypeFilter_C_PreConstruct");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_ShowroomSeasonTypeFilter_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WDG_ShowroomSeasonTypeFilter.WDG_ShowroomSeasonTypeFilter_C.UpdateTextLabel
// 0x0018 (0x0018 - 0x0000)
struct WDG_ShowroomSeasonTypeFilter_C_UpdateTextLabel final
{
public:
	class FText                                   CallFunc_SeasonToFullText_YearText;                // 0x0000(0x0018)()
};
static_assert(alignof(WDG_ShowroomSeasonTypeFilter_C_UpdateTextLabel) == 0x000008, "Wrong alignment on WDG_ShowroomSeasonTypeFilter_C_UpdateTextLabel");
static_assert(sizeof(WDG_ShowroomSeasonTypeFilter_C_UpdateTextLabel) == 0x000018, "Wrong size on WDG_ShowroomSeasonTypeFilter_C_UpdateTextLabel");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_UpdateTextLabel, CallFunc_SeasonToFullText_YearText) == 0x000000, "Member 'WDG_ShowroomSeasonTypeFilter_C_UpdateTextLabel::CallFunc_SeasonToFullText_YearText' has a wrong offset!");

// Function WDG_ShowroomSeasonTypeFilter.WDG_ShowroomSeasonTypeFilter_C.SetActiveFilter
// 0x0001 (0x0001 - 0x0000)
struct WDG_ShowroomSeasonTypeFilter_C_SetActiveFilter final
{
public:
	ESeasonType                                   activeFilter_0;                                    // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomSeasonTypeFilter_C_SetActiveFilter) == 0x000001, "Wrong alignment on WDG_ShowroomSeasonTypeFilter_C_SetActiveFilter");
static_assert(sizeof(WDG_ShowroomSeasonTypeFilter_C_SetActiveFilter) == 0x000001, "Wrong size on WDG_ShowroomSeasonTypeFilter_C_SetActiveFilter");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_SetActiveFilter, activeFilter_0) == 0x000000, "Member 'WDG_ShowroomSeasonTypeFilter_C_SetActiveFilter::activeFilter_0' has a wrong offset!");

// Function WDG_ShowroomSeasonTypeFilter.WDG_ShowroomSeasonTypeFilter_C.ItemSelected
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomSeasonTypeFilter_C_ItemSelected final
{
public:
	class UWDG_GenericBarItem_C*                  Item;                                              // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_Conv_IntToByte_ReturnValue;               // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_GetValidValue_ReturnValue;                // 0x0009(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomSeasonTypeFilter_C_ItemSelected) == 0x000008, "Wrong alignment on WDG_ShowroomSeasonTypeFilter_C_ItemSelected");
static_assert(sizeof(WDG_ShowroomSeasonTypeFilter_C_ItemSelected) == 0x000010, "Wrong size on WDG_ShowroomSeasonTypeFilter_C_ItemSelected");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_ItemSelected, Item) == 0x000000, "Member 'WDG_ShowroomSeasonTypeFilter_C_ItemSelected::Item' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_ItemSelected, CallFunc_Conv_IntToByte_ReturnValue) == 0x000008, "Member 'WDG_ShowroomSeasonTypeFilter_C_ItemSelected::CallFunc_Conv_IntToByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_ItemSelected, CallFunc_GetValidValue_ReturnValue) == 0x000009, "Member 'WDG_ShowroomSeasonTypeFilter_C_ItemSelected::CallFunc_GetValidValue_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomSeasonTypeFilter.WDG_ShowroomSeasonTypeFilter_C.CreateItem
// 0x0058 (0x0058 - 0x0000)
struct WDG_ShowroomSeasonTypeFilter_C_CreateItem final
{
public:
	ESeasonType                                   Season;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_GenericBarItem_C*                  Item;                                              // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  CallFunc_Create_ReturnValue;                       // 0x0010(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FText                                   CallFunc_SeasonToFullText_YearText;                // 0x0018(0x0018)()
	int32                                         CallFunc_Conv_ByteToInt_ReturnValue;               // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void(class UWDG_GenericBarItem_C* Sender)> K2Node_CreateDelegate_OutputDelegate;       // 0x0034(0x0010)(ZeroConstructor, NoDestructor)
	struct FLinearColor                           Temp_struct_Variable;                              // 0x0044(0x0010)(ConstParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomSeasonTypeFilter_C_CreateItem) == 0x000008, "Wrong alignment on WDG_ShowroomSeasonTypeFilter_C_CreateItem");
static_assert(sizeof(WDG_ShowroomSeasonTypeFilter_C_CreateItem) == 0x000058, "Wrong size on WDG_ShowroomSeasonTypeFilter_C_CreateItem");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_CreateItem, Season) == 0x000000, "Member 'WDG_ShowroomSeasonTypeFilter_C_CreateItem::Season' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_CreateItem, Item) == 0x000008, "Member 'WDG_ShowroomSeasonTypeFilter_C_CreateItem::Item' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_CreateItem, CallFunc_Create_ReturnValue) == 0x000010, "Member 'WDG_ShowroomSeasonTypeFilter_C_CreateItem::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_CreateItem, CallFunc_SeasonToFullText_YearText) == 0x000018, "Member 'WDG_ShowroomSeasonTypeFilter_C_CreateItem::CallFunc_SeasonToFullText_YearText' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_CreateItem, CallFunc_Conv_ByteToInt_ReturnValue) == 0x000030, "Member 'WDG_ShowroomSeasonTypeFilter_C_CreateItem::CallFunc_Conv_ByteToInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_CreateItem, K2Node_CreateDelegate_OutputDelegate) == 0x000034, "Member 'WDG_ShowroomSeasonTypeFilter_C_CreateItem::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_CreateItem, Temp_struct_Variable) == 0x000044, "Member 'WDG_ShowroomSeasonTypeFilter_C_CreateItem::Temp_struct_Variable' has a wrong offset!");

// Function WDG_ShowroomSeasonTypeFilter.WDG_ShowroomSeasonTypeFilter_C.Populate
// 0x0058 (0x0058 - 0x0000)
struct WDG_ShowroomSeasonTypeFilter_C_Populate final
{
public:
	TArray<ESeasonType>                           Source;                                            // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x001C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1D[0x3];                                       // 0x001D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	ESeasonType                                   CallFunc_Array_Get_Item;                           // 0x0024(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NotEqual_ByteByte_ReturnValue;            // 0x0025(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_26[0x2];                                       // 0x0026(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_GenericBarItem_C*                  CallFunc_CreateItem_Item;                          // 0x0028(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue;                    // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_34[0x4];                                       // 0x0034(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_GenericBarItem_C*                  CallFunc_CreateItem_Item_1;                        // 0x0038(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Add_ReturnValue_1;                  // 0x0040(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_44[0x4];                                       // 0x0044(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue;                     // 0x0048(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue_1;                   // 0x0050(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomSeasonTypeFilter_C_Populate) == 0x000008, "Wrong alignment on WDG_ShowroomSeasonTypeFilter_C_Populate");
static_assert(sizeof(WDG_ShowroomSeasonTypeFilter_C_Populate) == 0x000058, "Wrong size on WDG_ShowroomSeasonTypeFilter_C_Populate");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_Populate, Source) == 0x000000, "Member 'WDG_ShowroomSeasonTypeFilter_C_Populate::Source' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_Populate, Temp_int_Loop_Counter_Variable) == 0x000010, "Member 'WDG_ShowroomSeasonTypeFilter_C_Populate::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_Populate, CallFunc_Array_Length_ReturnValue) == 0x000014, "Member 'WDG_ShowroomSeasonTypeFilter_C_Populate::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_Populate, CallFunc_Add_IntInt_ReturnValue) == 0x000018, "Member 'WDG_ShowroomSeasonTypeFilter_C_Populate::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_Populate, CallFunc_Less_IntInt_ReturnValue) == 0x00001C, "Member 'WDG_ShowroomSeasonTypeFilter_C_Populate::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_Populate, Temp_int_Array_Index_Variable) == 0x000020, "Member 'WDG_ShowroomSeasonTypeFilter_C_Populate::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_Populate, CallFunc_Array_Get_Item) == 0x000024, "Member 'WDG_ShowroomSeasonTypeFilter_C_Populate::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_Populate, CallFunc_NotEqual_ByteByte_ReturnValue) == 0x000025, "Member 'WDG_ShowroomSeasonTypeFilter_C_Populate::CallFunc_NotEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_Populate, CallFunc_CreateItem_Item) == 0x000028, "Member 'WDG_ShowroomSeasonTypeFilter_C_Populate::CallFunc_CreateItem_Item' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_Populate, CallFunc_Array_Add_ReturnValue) == 0x000030, "Member 'WDG_ShowroomSeasonTypeFilter_C_Populate::CallFunc_Array_Add_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_Populate, CallFunc_CreateItem_Item_1) == 0x000038, "Member 'WDG_ShowroomSeasonTypeFilter_C_Populate::CallFunc_CreateItem_Item_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_Populate, CallFunc_Array_Add_ReturnValue_1) == 0x000040, "Member 'WDG_ShowroomSeasonTypeFilter_C_Populate::CallFunc_Array_Add_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_Populate, CallFunc_AddChild_ReturnValue) == 0x000048, "Member 'WDG_ShowroomSeasonTypeFilter_C_Populate::CallFunc_AddChild_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_Populate, CallFunc_AddChild_ReturnValue_1) == 0x000050, "Member 'WDG_ShowroomSeasonTypeFilter_C_Populate::CallFunc_AddChild_ReturnValue_1' has a wrong offset!");

// Function WDG_ShowroomSeasonTypeFilter.WDG_ShowroomSeasonTypeFilter_C.SetSizeHolderIfLonger
// 0x0060 (0x0060 - 0x0000)
struct WDG_ShowroomSeasonTypeFilter_C_SetSizeHolderIfLonger final
{
public:
	class FText                                   text;                                              // 0x0000(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm)
	class FString                                 CallFunc_Conv_TextToString_ReturnValue;            // 0x0018(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	int32                                         CallFunc_Len_ReturnValue;                          // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2C[0x4];                                       // 0x002C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_GetText_ReturnValue;                      // 0x0030(0x0018)()
	class FString                                 CallFunc_Conv_TextToString_ReturnValue_1;          // 0x0048(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	int32                                         CallFunc_Len_ReturnValue_1;                        // 0x0058(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x005C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomSeasonTypeFilter_C_SetSizeHolderIfLonger) == 0x000008, "Wrong alignment on WDG_ShowroomSeasonTypeFilter_C_SetSizeHolderIfLonger");
static_assert(sizeof(WDG_ShowroomSeasonTypeFilter_C_SetSizeHolderIfLonger) == 0x000060, "Wrong size on WDG_ShowroomSeasonTypeFilter_C_SetSizeHolderIfLonger");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_SetSizeHolderIfLonger, text) == 0x000000, "Member 'WDG_ShowroomSeasonTypeFilter_C_SetSizeHolderIfLonger::text' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_SetSizeHolderIfLonger, CallFunc_Conv_TextToString_ReturnValue) == 0x000018, "Member 'WDG_ShowroomSeasonTypeFilter_C_SetSizeHolderIfLonger::CallFunc_Conv_TextToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_SetSizeHolderIfLonger, CallFunc_Len_ReturnValue) == 0x000028, "Member 'WDG_ShowroomSeasonTypeFilter_C_SetSizeHolderIfLonger::CallFunc_Len_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_SetSizeHolderIfLonger, CallFunc_GetText_ReturnValue) == 0x000030, "Member 'WDG_ShowroomSeasonTypeFilter_C_SetSizeHolderIfLonger::CallFunc_GetText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_SetSizeHolderIfLonger, CallFunc_Conv_TextToString_ReturnValue_1) == 0x000048, "Member 'WDG_ShowroomSeasonTypeFilter_C_SetSizeHolderIfLonger::CallFunc_Conv_TextToString_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_SetSizeHolderIfLonger, CallFunc_Len_ReturnValue_1) == 0x000058, "Member 'WDG_ShowroomSeasonTypeFilter_C_SetSizeHolderIfLonger::CallFunc_Len_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomSeasonTypeFilter_C_SetSizeHolderIfLonger, CallFunc_Greater_IntInt_ReturnValue) == 0x00005C, "Member 'WDG_ShowroomSeasonTypeFilter_C_SetSizeHolderIfLonger::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");

}

