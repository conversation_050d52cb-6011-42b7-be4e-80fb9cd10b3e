﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TVSessionTimeTable

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "UMG_structs.hpp"


namespace SDK::Params
{

// Function WDG_TVSessionTimeTable.WDG_TVSessionTimeTable_C.ExecuteUbergraph_WDG_TVSessionTimeTable
// 0x0010 (0x0010 - 0x0000)
struct WDG_TVSessionTimeTable_C_ExecuteUbergraph_WDG_TVSessionTimeTable final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_isWidgetVisible;                      // 0x0004(0x0001)(ZeroConstructor, Is<PERSON>lainOldData, NoDestructor)
	uint8                                         Pad_5[0x3];                                        // 0x0005(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue;              // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_TVSessionTimeTable_C_ExecuteUbergraph_WDG_TVSessionTimeTable) == 0x000008, "Wrong alignment on WDG_TVSessionTimeTable_C_ExecuteUbergraph_WDG_TVSessionTimeTable");
static_assert(sizeof(WDG_TVSessionTimeTable_C_ExecuteUbergraph_WDG_TVSessionTimeTable) == 0x000010, "Wrong size on WDG_TVSessionTimeTable_C_ExecuteUbergraph_WDG_TVSessionTimeTable");
static_assert(offsetof(WDG_TVSessionTimeTable_C_ExecuteUbergraph_WDG_TVSessionTimeTable, EntryPoint) == 0x000000, "Member 'WDG_TVSessionTimeTable_C_ExecuteUbergraph_WDG_TVSessionTimeTable::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_TVSessionTimeTable_C_ExecuteUbergraph_WDG_TVSessionTimeTable, K2Node_Event_isWidgetVisible) == 0x000004, "Member 'WDG_TVSessionTimeTable_C_ExecuteUbergraph_WDG_TVSessionTimeTable::K2Node_Event_isWidgetVisible' has a wrong offset!");
static_assert(offsetof(WDG_TVSessionTimeTable_C_ExecuteUbergraph_WDG_TVSessionTimeTable, CallFunc_GetOwningPlayer_ReturnValue) == 0x000008, "Member 'WDG_TVSessionTimeTable_C_ExecuteUbergraph_WDG_TVSessionTimeTable::CallFunc_GetOwningPlayer_ReturnValue' has a wrong offset!");

// Function WDG_TVSessionTimeTable.WDG_TVSessionTimeTable_C.OnHudVisibility
// 0x0001 (0x0001 - 0x0000)
struct WDG_TVSessionTimeTable_C_OnHudVisibility final
{
public:
	bool                                          IsWidgetVisible;                                   // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_TVSessionTimeTable_C_OnHudVisibility) == 0x000001, "Wrong alignment on WDG_TVSessionTimeTable_C_OnHudVisibility");
static_assert(sizeof(WDG_TVSessionTimeTable_C_OnHudVisibility) == 0x000001, "Wrong size on WDG_TVSessionTimeTable_C_OnHudVisibility");
static_assert(offsetof(WDG_TVSessionTimeTable_C_OnHudVisibility, IsWidgetVisible) == 0x000000, "Member 'WDG_TVSessionTimeTable_C_OnHudVisibility::IsWidgetVisible' has a wrong offset!");

// Function WDG_TVSessionTimeTable.WDG_TVSessionTimeTable_C.OnMouseWheel
// 0x0250 (0x0250 - 0x0000)
struct WDG_TVSessionTimeTable_C_OnMouseWheel final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FPointerEvent                          MouseEvent;                                        // 0x0038(0x0070)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	struct FEventReply                            ReturnValue;                                       // 0x00A8(0x00B8)(Parm, OutParm, ReturnParm)
	struct FEventReply                            CallFunc_Handled_ReturnValue;                      // 0x0160(0x00B8)()
	float                                         CallFunc_PointerEvent_GetWheelDelta_ReturnValue;   // 0x0218(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue;          // 0x021C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_GetScrollOffset_ReturnValue;              // 0x0220(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetChildrenCount_ReturnValue;             // 0x0224(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_FloatToString_ReturnValue;           // 0x0228(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	float                                         CallFunc_Multiply_IntFloat_ReturnValue;            // 0x0238(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Subtract_FloatFloat_ReturnValue;          // 0x023C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Add_FloatFloat_ReturnValue;               // 0x0240(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_FClamp_ReturnValue;                       // 0x0244(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_FClamp_ReturnValue_1;                     // 0x0248(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_TVSessionTimeTable_C_OnMouseWheel) == 0x000008, "Wrong alignment on WDG_TVSessionTimeTable_C_OnMouseWheel");
static_assert(sizeof(WDG_TVSessionTimeTable_C_OnMouseWheel) == 0x000250, "Wrong size on WDG_TVSessionTimeTable_C_OnMouseWheel");
static_assert(offsetof(WDG_TVSessionTimeTable_C_OnMouseWheel, MyGeometry) == 0x000000, "Member 'WDG_TVSessionTimeTable_C_OnMouseWheel::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_TVSessionTimeTable_C_OnMouseWheel, MouseEvent) == 0x000038, "Member 'WDG_TVSessionTimeTable_C_OnMouseWheel::MouseEvent' has a wrong offset!");
static_assert(offsetof(WDG_TVSessionTimeTable_C_OnMouseWheel, ReturnValue) == 0x0000A8, "Member 'WDG_TVSessionTimeTable_C_OnMouseWheel::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TVSessionTimeTable_C_OnMouseWheel, CallFunc_Handled_ReturnValue) == 0x000160, "Member 'WDG_TVSessionTimeTable_C_OnMouseWheel::CallFunc_Handled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TVSessionTimeTable_C_OnMouseWheel, CallFunc_PointerEvent_GetWheelDelta_ReturnValue) == 0x000218, "Member 'WDG_TVSessionTimeTable_C_OnMouseWheel::CallFunc_PointerEvent_GetWheelDelta_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TVSessionTimeTable_C_OnMouseWheel, CallFunc_Multiply_FloatFloat_ReturnValue) == 0x00021C, "Member 'WDG_TVSessionTimeTable_C_OnMouseWheel::CallFunc_Multiply_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TVSessionTimeTable_C_OnMouseWheel, CallFunc_GetScrollOffset_ReturnValue) == 0x000220, "Member 'WDG_TVSessionTimeTable_C_OnMouseWheel::CallFunc_GetScrollOffset_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TVSessionTimeTable_C_OnMouseWheel, CallFunc_GetChildrenCount_ReturnValue) == 0x000224, "Member 'WDG_TVSessionTimeTable_C_OnMouseWheel::CallFunc_GetChildrenCount_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TVSessionTimeTable_C_OnMouseWheel, CallFunc_Conv_FloatToString_ReturnValue) == 0x000228, "Member 'WDG_TVSessionTimeTable_C_OnMouseWheel::CallFunc_Conv_FloatToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TVSessionTimeTable_C_OnMouseWheel, CallFunc_Multiply_IntFloat_ReturnValue) == 0x000238, "Member 'WDG_TVSessionTimeTable_C_OnMouseWheel::CallFunc_Multiply_IntFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TVSessionTimeTable_C_OnMouseWheel, CallFunc_Subtract_FloatFloat_ReturnValue) == 0x00023C, "Member 'WDG_TVSessionTimeTable_C_OnMouseWheel::CallFunc_Subtract_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TVSessionTimeTable_C_OnMouseWheel, CallFunc_Add_FloatFloat_ReturnValue) == 0x000240, "Member 'WDG_TVSessionTimeTable_C_OnMouseWheel::CallFunc_Add_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TVSessionTimeTable_C_OnMouseWheel, CallFunc_FClamp_ReturnValue) == 0x000244, "Member 'WDG_TVSessionTimeTable_C_OnMouseWheel::CallFunc_FClamp_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TVSessionTimeTable_C_OnMouseWheel, CallFunc_FClamp_ReturnValue_1) == 0x000248, "Member 'WDG_TVSessionTimeTable_C_OnMouseWheel::CallFunc_FClamp_ReturnValue_1' has a wrong offset!");

}

