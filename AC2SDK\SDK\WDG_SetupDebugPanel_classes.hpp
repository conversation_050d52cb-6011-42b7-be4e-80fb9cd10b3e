﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SetupDebugPanel

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SetupDebugPanel.WDG_SetupDebugPanel_C
// 0x0008 (0x0650 - 0x0648)
class UWDG_SetupDebugPanel_C final : public UPhysicsInfoPanel
{
public:
	class UImage*                                 Image_0;                                           // 0x0648(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SetupDebugPanel_C">();
	}
	static class UWDG_SetupDebugPanel_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SetupDebugPanel_C>();
	}
};
static_assert(alignof(UWDG_SetupDebugPanel_C) == 0x000008, "Wrong alignment on UWDG_SetupDebugPanel_C");
static_assert(sizeof(UWDG_SetupDebugPanel_C) == 0x000650, "Wrong size on UWDG_SetupDebugPanel_C");
static_assert(offsetof(UWDG_SetupDebugPanel_C, Image_0) == 0x000648, "Member 'UWDG_SetupDebugPanel_C::Image_0' has a wrong offset!");

}

