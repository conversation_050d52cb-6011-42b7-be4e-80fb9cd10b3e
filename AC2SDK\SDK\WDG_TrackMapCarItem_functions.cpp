﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TrackMapCarItem

#include "Basic.hpp"

#include "WDG_TrackMapCarItem_classes.hpp"
#include "WDG_TrackMapCarItem_parameters.hpp"


namespace SDK
{

// Function WDG_TrackMapCarItem.WDG_TrackMapCarItem_C.ExecuteUbergraph_WDG_TrackMapCarItem
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_TrackMapCarItem_C::ExecuteUbergraph_WDG_TrackMapCarItem(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TrackMapCarItem_C", "ExecuteUbergraph_WDG_TrackMapCarItem");

	Params::WDG_TrackMapCarItem_C_ExecuteUbergraph_WDG_TrackMapCarItem Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_TrackMapCarItem.WDG_TrackMapCarItem_C.PositionChanged
// (Event, Public, BlueprintEvent)
// Parameters:
// int32                                   Position_0                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_TrackMapCarItem_C::PositionChanged(int32 Position_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TrackMapCarItem_C", "PositionChanged");

	Params::WDG_TrackMapCarItem_C_PositionChanged Parms{};

	Parms.Position_0 = Position_0;

	UObject::ProcessEvent(Func, &Parms);
}

}

