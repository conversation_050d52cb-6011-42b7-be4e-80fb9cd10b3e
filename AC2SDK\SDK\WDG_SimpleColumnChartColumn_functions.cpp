﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SimpleColumnChartColumn

#include "Basic.hpp"

#include "WDG_SimpleColumnChartColumn_classes.hpp"
#include "WDG_SimpleColumnChartColumn_parameters.hpp"


namespace SDK
{

// Function WDG_SimpleColumnChartColumn.WDG_SimpleColumnChartColumn_C.ExecuteUbergraph_WDG_SimpleColumnChartColumn
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SimpleColumnChartColumn_C::ExecuteUbergraph_WDG_SimpleColumnChartColumn(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SimpleColumnChartColumn_C", "ExecuteUbergraph_WDG_SimpleColumnChartColumn");

	Params::WDG_SimpleColumnChartColumn_C_ExecuteUbergraph_WDG_SimpleColumnChartColumn Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SimpleColumnChartColumn.WDG_SimpleColumnChartColumn_C.OnRemovedFromFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_SimpleColumnChartColumn_C::OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SimpleColumnChartColumn_C", "OnRemovedFromFocusPath");

	Params::WDG_SimpleColumnChartColumn_C_OnRemovedFromFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SimpleColumnChartColumn.WDG_SimpleColumnChartColumn_C.OnAddedToFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_SimpleColumnChartColumn_C::OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SimpleColumnChartColumn_C", "OnAddedToFocusPath");

	Params::WDG_SimpleColumnChartColumn_C_OnAddedToFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SimpleColumnChartColumn.WDG_SimpleColumnChartColumn_C.BP_MouseOver
// (Event, Public, BlueprintEvent)

void UWDG_SimpleColumnChartColumn_C::BP_MouseOver()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SimpleColumnChartColumn_C", "BP_MouseOver");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SimpleColumnChartColumn.WDG_SimpleColumnChartColumn_C.BP_MouseLeave
// (Event, Public, BlueprintEvent)

void UWDG_SimpleColumnChartColumn_C::BP_MouseLeave()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SimpleColumnChartColumn_C", "BP_MouseLeave");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SimpleColumnChartColumn.WDG_SimpleColumnChartColumn_C.SetValue
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// float                                   barValue                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   barId                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    isBarNavigable                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SimpleColumnChartColumn_C::SetValue(float barValue, int32 barId, bool isBarNavigable)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SimpleColumnChartColumn_C", "SetValue");

	Params::WDG_SimpleColumnChartColumn_C_SetValue Parms{};

	Parms.barValue = barValue;
	Parms.barId = barId;
	Parms.isBarNavigable = isBarNavigable;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SimpleColumnChartColumn.WDG_SimpleColumnChartColumn_C.SetBarHighlighted
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    isFocused                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SimpleColumnChartColumn_C::SetBarHighlighted(bool isFocused)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SimpleColumnChartColumn_C", "SetBarHighlighted");

	Params::WDG_SimpleColumnChartColumn_C_SetBarHighlighted Parms{};

	Parms.isFocused = isFocused;

	UObject::ProcessEvent(Func, &Parms);
}

}

