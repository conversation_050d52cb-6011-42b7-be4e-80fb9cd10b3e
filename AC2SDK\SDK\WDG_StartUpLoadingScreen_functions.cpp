﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_StartUpLoadingScreen

#include "Basic.hpp"

#include "WDG_StartUpLoadingScreen_classes.hpp"
#include "WDG_StartUpLoadingScreen_parameters.hpp"


namespace SDK
{

// Function WDG_StartUpLoadingScreen.WDG_StartUpLoadingScreen_C.ExecuteUbergraph_WDG_StartUpLoadingScreen
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_StartUpLoadingScreen_C::ExecuteUbergraph_WDG_StartUpLoadingScreen(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_StartUpLoadingScreen_C", "ExecuteUbergraph_WDG_StartUpLoadingScreen");

	Params::WDG_StartUpLoadingScreen_C_ExecuteUbergraph_WDG_StartUpLoadingScreen Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_StartUpLoadingScreen.WDG_StartUpLoadingScreen_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_StartUpLoadingScreen_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_StartUpLoadingScreen_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_StartUpLoadingScreen.WDG_StartUpLoadingScreen_C.Get_Loading_lbl_Text_0
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class FText                             ReturnValue                                            (Parm, OutParm, ReturnParm)

class FText UWDG_StartUpLoadingScreen_C::Get_Loading_lbl_Text_0()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_StartUpLoadingScreen_C", "Get_Loading_lbl_Text_0");

	Params::WDG_StartUpLoadingScreen_C_Get_Loading_lbl_Text_0 Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}

}

