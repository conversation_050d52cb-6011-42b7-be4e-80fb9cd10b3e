﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_MultiplayerAdvancedSettings

#include "Basic.hpp"

#include "WDG_MultiplayerAdvancedSettings_classes.hpp"
#include "WDG_MultiplayerAdvancedSettings_parameters.hpp"


namespace SDK
{

// Function WDG_MultiplayerAdvancedSettings.WDG_MultiplayerAdvancedSettings_C.ExecuteUbergraph_WDG_MultiplayerAdvancedSettings
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_MultiplayerAdvancedSettings_C::ExecuteUbergraph_WDG_MultiplayerAdvancedSettings(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerAdvancedSettings_C", "ExecuteUbergraph_WDG_MultiplayerAdvancedSettings");

	Params::WDG_MultiplayerAdvancedSettings_C_ExecuteUbergraph_WDG_MultiplayerAdvancedSettings Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_MultiplayerAdvancedSettings.WDG_MultiplayerAdvancedSettings_C.BP_StartPage
// (Event, Public, BlueprintEvent)

void UWDG_MultiplayerAdvancedSettings_C::BP_StartPage()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_MultiplayerAdvancedSettings_C", "BP_StartPage");

	UObject::ProcessEvent(Func, nullptr);
}

}

