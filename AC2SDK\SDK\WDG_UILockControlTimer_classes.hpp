﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_UILockControlTimer

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_UILockControlTimer.WDG_UILockControlTimer_C
// 0x0050 (0x06A8 - 0x0658)
class UWDG_UILockControlTimer_C final : public UAcRaceWidgetBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0658(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UBorder*                                background;                                        // 0x0660(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         Formation;                                         // 0x0668(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_1;                                       // 0x0670(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         Time;                                              // 0x0678(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtInfo;                                           // 0x0680(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtRemainingTime;                                  // 0x0688(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           Wrapper;                                           // 0x0690(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class AAcRaceGameMode*                        raceGameMode;                                      // 0x0698(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         TimeRemaining;                                     // 0x06A0(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_UILockControlTimer(int32 EntryPoint);
	void Construct();
	void PreConstruct(bool IsDesignTime);
	void Destruct();
	void OnEverySecond();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_UILockControlTimer_C">();
	}
	static class UWDG_UILockControlTimer_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_UILockControlTimer_C>();
	}
};
static_assert(alignof(UWDG_UILockControlTimer_C) == 0x000008, "Wrong alignment on UWDG_UILockControlTimer_C");
static_assert(sizeof(UWDG_UILockControlTimer_C) == 0x0006A8, "Wrong size on UWDG_UILockControlTimer_C");
static_assert(offsetof(UWDG_UILockControlTimer_C, UberGraphFrame) == 0x000658, "Member 'UWDG_UILockControlTimer_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_UILockControlTimer_C, background) == 0x000660, "Member 'UWDG_UILockControlTimer_C::background' has a wrong offset!");
static_assert(offsetof(UWDG_UILockControlTimer_C, Formation) == 0x000668, "Member 'UWDG_UILockControlTimer_C::Formation' has a wrong offset!");
static_assert(offsetof(UWDG_UILockControlTimer_C, TextBlock_1) == 0x000670, "Member 'UWDG_UILockControlTimer_C::TextBlock_1' has a wrong offset!");
static_assert(offsetof(UWDG_UILockControlTimer_C, Time) == 0x000678, "Member 'UWDG_UILockControlTimer_C::Time' has a wrong offset!");
static_assert(offsetof(UWDG_UILockControlTimer_C, txtInfo) == 0x000680, "Member 'UWDG_UILockControlTimer_C::txtInfo' has a wrong offset!");
static_assert(offsetof(UWDG_UILockControlTimer_C, txtRemainingTime) == 0x000688, "Member 'UWDG_UILockControlTimer_C::txtRemainingTime' has a wrong offset!");
static_assert(offsetof(UWDG_UILockControlTimer_C, Wrapper) == 0x000690, "Member 'UWDG_UILockControlTimer_C::Wrapper' has a wrong offset!");
static_assert(offsetof(UWDG_UILockControlTimer_C, raceGameMode) == 0x000698, "Member 'UWDG_UILockControlTimer_C::raceGameMode' has a wrong offset!");
static_assert(offsetof(UWDG_UILockControlTimer_C, TimeRemaining) == 0x0006A0, "Member 'UWDG_UILockControlTimer_C::TimeRemaining' has a wrong offset!");

}

