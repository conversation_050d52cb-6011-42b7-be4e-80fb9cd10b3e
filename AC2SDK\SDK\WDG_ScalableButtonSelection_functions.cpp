﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ScalableButtonSelection

#include "Basic.hpp"

#include "WDG_ScalableButtonSelection_classes.hpp"
#include "WDG_ScalableButtonSelection_parameters.hpp"


namespace SDK
{

// Function WDG_ScalableButtonSelection.WDG_ScalableButtonSelection_C.ExecuteUbergraph_WDG_ScalableButtonSelection
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ScalableButtonSelection_C::ExecuteUbergraph_WDG_ScalableButtonSelection(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ScalableButtonSelection_C", "ExecuteUbergraph_WDG_ScalableButtonSelection");

	Params::WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ScalableButtonSelection.WDG_ScalableButtonSelection_C.BP_SetupElementResize
// (Event, Public, BlueprintEvent)

void UWDG_ScalableButtonSelection_C::BP_SetupElementResize()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ScalableButtonSelection_C", "BP_SetupElementResize");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ScalableButtonSelection.WDG_ScalableButtonSelection_C.BP_MouseLeave
// (Event, Public, BlueprintEvent)

void UWDG_ScalableButtonSelection_C::BP_MouseLeave()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ScalableButtonSelection_C", "BP_MouseLeave");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ScalableButtonSelection.WDG_ScalableButtonSelection_C.BP_MouseOver
// (Event, Public, BlueprintEvent)

void UWDG_ScalableButtonSelection_C::BP_MouseOver()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ScalableButtonSelection_C", "BP_MouseOver");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ScalableButtonSelection.WDG_ScalableButtonSelection_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_ScalableButtonSelection_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ScalableButtonSelection_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}

}

