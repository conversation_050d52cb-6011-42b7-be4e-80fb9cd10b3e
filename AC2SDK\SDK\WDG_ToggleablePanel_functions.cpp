﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ToggleablePanel

#include "Basic.hpp"

#include "WDG_ToggleablePanel_classes.hpp"
#include "WDG_ToggleablePanel_parameters.hpp"


namespace SDK
{

// Function WDG_ToggleablePanel.WDG_ToggleablePanel_C.ExecuteUbergraph_WDG_ToggleablePanel
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ToggleablePanel_C::ExecuteUbergraph_WDG_ToggleablePanel(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ToggleablePanel_C", "ExecuteUbergraph_WDG_ToggleablePanel");

	Params::WDG_ToggleablePanel_C_ExecuteUbergraph_WDG_ToggleablePanel Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ToggleablePanel.WDG_ToggleablePanel_C.BndEvt__btnToggleDetectedDevices_K2Node_ComponentBoundEvent_1_OnButtonPressed__DelegateSignature
// (BlueprintEvent)

void UWDG_ToggleablePanel_C::BndEvt__btnToggleDetectedDevices_K2Node_ComponentBoundEvent_1_OnButtonPressed__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ToggleablePanel_C", "BndEvt__btnToggleDetectedDevices_K2Node_ComponentBoundEvent_1_OnButtonPressed__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ToggleablePanel.WDG_ToggleablePanel_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_ToggleablePanel_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ToggleablePanel_C", "PreConstruct");

	Params::WDG_ToggleablePanel_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ToggleablePanel.WDG_ToggleablePanel_C.GetPanelExpanded
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// ESlateVisibility                        ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

ESlateVisibility UWDG_ToggleablePanel_C::GetPanelExpanded()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ToggleablePanel_C", "GetPanelExpanded");

	Params::WDG_ToggleablePanel_C_GetPanelExpanded Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}

}

