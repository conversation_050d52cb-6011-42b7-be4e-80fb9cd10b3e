﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TimeTableItem

#include "Basic.hpp"

#include "WDG_TimeTableItem_classes.hpp"
#include "WDG_TimeTableItem_parameters.hpp"


namespace SDK
{

// Function WDG_TimeTableItem.WDG_TimeTableItem_C.ExecuteUbergraph_WDG_TimeTableItem
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_TimeTableItem_C::ExecuteUbergraph_WDG_TimeTableItem(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TimeTableItem_C", "ExecuteUbergraph_WDG_TimeTableItem");

	Params::WDG_TimeTableItem_C_ExecuteUbergraph_WDG_TimeTableItem Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_TimeTableItem.WDG_TimeTableItem_C.OnTimeTableItemUpdate
// (Event, Public, BlueprintEvent)
// Parameters:
// const struct FTimeTableEntry&           Entry                                                  (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_TimeTableItem_C::OnTimeTableItemUpdate(const struct FTimeTableEntry& Entry)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TimeTableItem_C", "OnTimeTableItemUpdate");

	Params::WDG_TimeTableItem_C_OnTimeTableItemUpdate Parms{};

	Parms.Entry = std::move(Entry);

	UObject::ProcessEvent(Func, &Parms);
}

}

