﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ReplayItem

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "AC2_classes.hpp"
#include "Engine_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ReplayItem.WDG_ReplayItem_C
// 0x0120 (0x0700 - 0x05E0)
class UWDG_ReplayItem_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UBorder*                                background;                                        // 0x05E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                brdCareer;                                         // 0x05F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                brdChampionship;                                   // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                brdHighlight;                                      // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                brdMulti;                                          // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                brdSessionType;                                    // 0x0610(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnDel;                                            // 0x0618(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnPlay;                                           // 0x0620(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnSave;                                           // 0x0628(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         controls;                                          // 0x0630(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_2;                                       // 0x0638(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtCar;                                            // 0x0640(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtDate;                                           // 0x0648(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtLength;                                         // 0x0650(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtServer;                                         // 0x0658(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtSessionType;                                    // 0x0660(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtTrack;                                          // 0x0668(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	struct FReplayInfo                            ReplayInfo;                                        // 0x0670(0x0048)(Edit, BlueprintVisible, DisableEditOnInstance)
	EReplayStore                                  replayStore;                                       // 0x06B8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	EReplayStore                                  DefaultNavigation;                                 // 0x06B9(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6BA[0x6];                                      // 0x06BA(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                FocusedButton;                                     // 0x06C0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TMulticastInlineDelegate<void(const struct FReplayInfo& ReplayInfo, EReplayStore replayStore)> OnLoadReplay; // 0x06C8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void(const struct FReplayInfo& ReplayInfo, EReplayStore replayStore)> OnDelReplay; // 0x06D8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMulticastInlineDelegate<void(const struct FReplayInfo& ReplayInfo)> OnSaveReplay;               // 0x06E8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	class AAcMenuGameMode*                        GameMode;                                          // 0x06F8(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_ReplayItem(int32 EntryPoint);
	void BndEvt__btnSave_K2Node_ComponentBoundEvent_3_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__btnDel_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__btnPlay_K2Node_ComponentBoundEvent_5_OnAcPanelForwardEvent__DelegateSignature();
	void BP_SetHighlight(bool highlighted);
	void BP_MouseOver();
	void BP_MouseLeave();
	void OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent);
	void OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent);
	void Construct();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ReplayItem_C">();
	}
	static class UWDG_ReplayItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ReplayItem_C>();
	}
};
static_assert(alignof(UWDG_ReplayItem_C) == 0x000008, "Wrong alignment on UWDG_ReplayItem_C");
static_assert(sizeof(UWDG_ReplayItem_C) == 0x000700, "Wrong size on UWDG_ReplayItem_C");
static_assert(offsetof(UWDG_ReplayItem_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_ReplayItem_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, background) == 0x0005E8, "Member 'UWDG_ReplayItem_C::background' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, brdCareer) == 0x0005F0, "Member 'UWDG_ReplayItem_C::brdCareer' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, brdChampionship) == 0x0005F8, "Member 'UWDG_ReplayItem_C::brdChampionship' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, brdHighlight) == 0x000600, "Member 'UWDG_ReplayItem_C::brdHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, brdMulti) == 0x000608, "Member 'UWDG_ReplayItem_C::brdMulti' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, brdSessionType) == 0x000610, "Member 'UWDG_ReplayItem_C::brdSessionType' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, btnDel) == 0x000618, "Member 'UWDG_ReplayItem_C::btnDel' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, btnPlay) == 0x000620, "Member 'UWDG_ReplayItem_C::btnPlay' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, btnSave) == 0x000628, "Member 'UWDG_ReplayItem_C::btnSave' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, controls) == 0x000630, "Member 'UWDG_ReplayItem_C::controls' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, TextBlock_2) == 0x000638, "Member 'UWDG_ReplayItem_C::TextBlock_2' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, txtCar) == 0x000640, "Member 'UWDG_ReplayItem_C::txtCar' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, txtDate) == 0x000648, "Member 'UWDG_ReplayItem_C::txtDate' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, txtLength) == 0x000650, "Member 'UWDG_ReplayItem_C::txtLength' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, txtServer) == 0x000658, "Member 'UWDG_ReplayItem_C::txtServer' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, txtSessionType) == 0x000660, "Member 'UWDG_ReplayItem_C::txtSessionType' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, txtTrack) == 0x000668, "Member 'UWDG_ReplayItem_C::txtTrack' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, ReplayInfo) == 0x000670, "Member 'UWDG_ReplayItem_C::ReplayInfo' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, replayStore) == 0x0006B8, "Member 'UWDG_ReplayItem_C::replayStore' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, DefaultNavigation) == 0x0006B9, "Member 'UWDG_ReplayItem_C::DefaultNavigation' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, FocusedButton) == 0x0006C0, "Member 'UWDG_ReplayItem_C::FocusedButton' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, OnLoadReplay) == 0x0006C8, "Member 'UWDG_ReplayItem_C::OnLoadReplay' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, OnDelReplay) == 0x0006D8, "Member 'UWDG_ReplayItem_C::OnDelReplay' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, OnSaveReplay) == 0x0006E8, "Member 'UWDG_ReplayItem_C::OnSaveReplay' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayItem_C, GameMode) == 0x0006F8, "Member 'UWDG_ReplayItem_C::GameMode' has a wrong offset!");

}

