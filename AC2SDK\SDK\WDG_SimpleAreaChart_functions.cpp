﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SimpleAreaChart

#include "Basic.hpp"

#include "WDG_SimpleAreaChart_classes.hpp"
#include "WDG_SimpleAreaChart_parameters.hpp"


namespace SDK
{

// Function WDG_SimpleAreaChart.WDG_SimpleAreaChart_C.ExecuteUbergraph_WDG_SimpleAreaChart
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SimpleAreaChart_C::ExecuteUbergraph_WDG_SimpleAreaChart(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SimpleAreaChart_C", "ExecuteUbergraph_WDG_SimpleAreaChart");

	Params::WDG_SimpleAreaChart_C_ExecuteUbergraph_WDG_SimpleAreaChart Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SimpleAreaChart.WDG_SimpleAreaChart_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_SimpleAreaChart_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SimpleAreaChart_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SimpleAreaChart.WDG_SimpleAreaChart_C.OnPaint
// (BlueprintCosmetic, Event, Public, HasOutParams, BlueprintCallable, BlueprintEvent, Const)
// Parameters:
// struct FPaintContext&                   Context                                                (BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)

void UWDG_SimpleAreaChart_C::OnPaint(struct FPaintContext& Context) const
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SimpleAreaChart_C", "OnPaint");

	Params::WDG_SimpleAreaChart_C_OnPaint Parms{};

	Parms.Context = std::move(Context);

	UObject::ProcessEvent(Func, &Parms);

	Context = std::move(Parms.Context);
}

}

