﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WPB_RaceTransitionPage

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WPB_RaceTransitionPage.WPB_RaceTransitionPage_C
// 0x0048 (0x0590 - 0x0548)
class UWPB_RaceTransitionPage_C final : public URaceTransitionPage
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0548(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       PageFade;                                          // 0x0550(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 background;                                        // 0x0558(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_354;                                         // 0x0560(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtSavingReplay;                                   // 0x0568(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtVersion;                                        // 0x0570(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class FText                                   txtEAVersion;                                      // 0x0578(0x0018)(Edit, BlueprintVisible, DisableEditOnInstance)

public:
	void ExecuteUbergraph_WPB_RaceTransitionPage(int32 EntryPoint);
	void BP_StartPage();
	void Tick(const struct FGeometry& MyGeometry, float InDeltaTime);
	void Construct();
	void StartFade(float timeMult);
	void EndFade(float timeMult);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WPB_RaceTransitionPage_C">();
	}
	static class UWPB_RaceTransitionPage_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWPB_RaceTransitionPage_C>();
	}
};
static_assert(alignof(UWPB_RaceTransitionPage_C) == 0x000008, "Wrong alignment on UWPB_RaceTransitionPage_C");
static_assert(sizeof(UWPB_RaceTransitionPage_C) == 0x000590, "Wrong size on UWPB_RaceTransitionPage_C");
static_assert(offsetof(UWPB_RaceTransitionPage_C, UberGraphFrame) == 0x000548, "Member 'UWPB_RaceTransitionPage_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWPB_RaceTransitionPage_C, PageFade) == 0x000550, "Member 'UWPB_RaceTransitionPage_C::PageFade' has a wrong offset!");
static_assert(offsetof(UWPB_RaceTransitionPage_C, background) == 0x000558, "Member 'UWPB_RaceTransitionPage_C::background' has a wrong offset!");
static_assert(offsetof(UWPB_RaceTransitionPage_C, Image_354) == 0x000560, "Member 'UWPB_RaceTransitionPage_C::Image_354' has a wrong offset!");
static_assert(offsetof(UWPB_RaceTransitionPage_C, txtSavingReplay) == 0x000568, "Member 'UWPB_RaceTransitionPage_C::txtSavingReplay' has a wrong offset!");
static_assert(offsetof(UWPB_RaceTransitionPage_C, txtVersion) == 0x000570, "Member 'UWPB_RaceTransitionPage_C::txtVersion' has a wrong offset!");
static_assert(offsetof(UWPB_RaceTransitionPage_C, txtEAVersion) == 0x000578, "Member 'UWPB_RaceTransitionPage_C::txtEAVersion' has a wrong offset!");

}

