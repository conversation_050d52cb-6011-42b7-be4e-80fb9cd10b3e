﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RatingSummaryItem

#include "Basic.hpp"

#include "WDG_RatingSummaryItem_classes.hpp"
#include "WDG_RatingSummaryItem_parameters.hpp"


namespace SDK
{

// Function WDG_RatingSummaryItem.WDG_RatingSummaryItem_C.ExecuteUbergraph_WDG_RatingSummaryItem
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_RatingSummaryItem_C::ExecuteUbergraph_WDG_RatingSummaryItem(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_RatingSummaryItem_C", "ExecuteUbergraph_WDG_RatingSummaryItem");

	Params::WDG_RatingSummaryItem_C_ExecuteUbergraph_WDG_RatingSummaryItem Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_RatingSummaryItem.WDG_RatingSummaryItem_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_RatingSummaryItem_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_RatingSummaryItem_C", "PreConstruct");

	Params::WDG_RatingSummaryItem_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_RatingSummaryItem.WDG_RatingSummaryItem_C.SetValueText
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class FText                             ReturnValue                                            (Parm, OutParm, ReturnParm)

class FText UWDG_RatingSummaryItem_C::SetValueText()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_RatingSummaryItem_C", "SetValueText");

	Params::WDG_RatingSummaryItem_C_SetValueText Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_RatingSummaryItem.WDG_RatingSummaryItem_C.GetValueAsPercent
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// float                                   ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

float UWDG_RatingSummaryItem_C::GetValueAsPercent()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_RatingSummaryItem_C", "GetValueAsPercent");

	Params::WDG_RatingSummaryItem_C_GetValueAsPercent Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}

}

