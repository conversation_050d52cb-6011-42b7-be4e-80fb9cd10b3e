﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PitstopInfo

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"
#include "CoreUObject_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_PitstopInfo.WDG_PitstopInfo_C
// 0x0210 (0x07F0 - 0x05E0)
class UWDG_PitstopInfo_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UBorder*                                borderPitRules;                                    // 0x05E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldD<PERSON>, RepSki<PERSON>, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           driverStintPanel;                                  // 0x05F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                infoBorder;                                        // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           Infos;                                             // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UGridPanel*                             ListRequirements;                                  // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UVerticalBox*                           ListStintInfo;                                     // 0x0610(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UVerticalBox*                           ListStints;                                        // 0x0618(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         MandatoryCounter;                                  // 0x0620(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             PitstopTotal;                                      // 0x0628(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             requiredDriverSwap;                                // 0x0630(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             requiredPitWindow;                                 // 0x0638(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             requiredRefuel;                                    // 0x0640(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             requiredTyreChange;                                // 0x0648(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           Wrapper;                                           // 0x0650(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class ACarAvatar*                             currentCar;                                        // 0x0658(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FPitstopMFD                            currentSettings;                                   // 0x0660(0x00B8)(Edit, BlueprintVisible, DisableEditOnInstance)
	int32                                         Step;                                              // 0x0718(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         FuelToAddValue;                                    // 0x071C(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         StepFloat;                                         // 0x0720(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          IsDisabled;                                        // 0x0724(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	uint8                                         Pad_725[0x3];                                      // 0x0725(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TMulticastInlineDelegate<void(EMfdChangeType Change_Type)> ValuesChanged;                        // 0x0728(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	int32                                         TotalPitStrategies;                                // 0x0738(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         currentTyreSet;                                    // 0x073C(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         TyreSetToSet;                                      // 0x0740(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         TotalTyreSets;                                     // 0x0744(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<int32>                                 AvailableTyreSetIndexes;                           // 0x0748(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	bool                                          EnableInput;                                       // 0x0758(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	uint8                                         Pad_759[0x7];                                      // 0x0759(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UWDG_DriverStintItem_C*>         DriverStints;                                      // 0x0760(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, ContainsInstancedReference)
	struct FLinearColor                           ValidColor;                                        // 0x0770(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           InvalidColor;                                      // 0x0780(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AAcRaceGameMode*                        raceGameMode;                                      // 0x0790(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSlateColor                            InvalidSlateColor;                                 // 0x0798(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance)
	struct FSlateColor                            ValidSlateColor;                                   // 0x07C0(0x0028)(Edit, BlueprintVisible, DisableEditOnInstance)
	bool                                          driverSwapVisible;                                 // 0x07E8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	bool                                          ChatWidgetFocusState;                              // 0x07E9(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	bool                                          ShouldShow;                                        // 0x07EA(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_7EB[0x1];                                      // 0x07EB(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         DeltaTick;                                         // 0x07EC(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_PitstopInfo(int32 EntryPoint);
	void OnAfterConstruct();
	void Tick(const struct FGeometry& MyGeometry, float InDeltaTime);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_PitstopInfo_C">();
	}
	static class UWDG_PitstopInfo_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_PitstopInfo_C>();
	}
};
static_assert(alignof(UWDG_PitstopInfo_C) == 0x000008, "Wrong alignment on UWDG_PitstopInfo_C");
static_assert(sizeof(UWDG_PitstopInfo_C) == 0x0007F0, "Wrong size on UWDG_PitstopInfo_C");
static_assert(offsetof(UWDG_PitstopInfo_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_PitstopInfo_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, borderPitRules) == 0x0005E8, "Member 'UWDG_PitstopInfo_C::borderPitRules' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, driverStintPanel) == 0x0005F0, "Member 'UWDG_PitstopInfo_C::driverStintPanel' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, infoBorder) == 0x0005F8, "Member 'UWDG_PitstopInfo_C::infoBorder' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, Infos) == 0x000600, "Member 'UWDG_PitstopInfo_C::Infos' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, ListRequirements) == 0x000608, "Member 'UWDG_PitstopInfo_C::ListRequirements' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, ListStintInfo) == 0x000610, "Member 'UWDG_PitstopInfo_C::ListStintInfo' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, ListStints) == 0x000618, "Member 'UWDG_PitstopInfo_C::ListStints' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, MandatoryCounter) == 0x000620, "Member 'UWDG_PitstopInfo_C::MandatoryCounter' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, PitstopTotal) == 0x000628, "Member 'UWDG_PitstopInfo_C::PitstopTotal' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, requiredDriverSwap) == 0x000630, "Member 'UWDG_PitstopInfo_C::requiredDriverSwap' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, requiredPitWindow) == 0x000638, "Member 'UWDG_PitstopInfo_C::requiredPitWindow' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, requiredRefuel) == 0x000640, "Member 'UWDG_PitstopInfo_C::requiredRefuel' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, requiredTyreChange) == 0x000648, "Member 'UWDG_PitstopInfo_C::requiredTyreChange' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, Wrapper) == 0x000650, "Member 'UWDG_PitstopInfo_C::Wrapper' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, currentCar) == 0x000658, "Member 'UWDG_PitstopInfo_C::currentCar' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, currentSettings) == 0x000660, "Member 'UWDG_PitstopInfo_C::currentSettings' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, Step) == 0x000718, "Member 'UWDG_PitstopInfo_C::Step' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, FuelToAddValue) == 0x00071C, "Member 'UWDG_PitstopInfo_C::FuelToAddValue' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, StepFloat) == 0x000720, "Member 'UWDG_PitstopInfo_C::StepFloat' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, IsDisabled) == 0x000724, "Member 'UWDG_PitstopInfo_C::IsDisabled' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, ValuesChanged) == 0x000728, "Member 'UWDG_PitstopInfo_C::ValuesChanged' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, TotalPitStrategies) == 0x000738, "Member 'UWDG_PitstopInfo_C::TotalPitStrategies' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, currentTyreSet) == 0x00073C, "Member 'UWDG_PitstopInfo_C::currentTyreSet' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, TyreSetToSet) == 0x000740, "Member 'UWDG_PitstopInfo_C::TyreSetToSet' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, TotalTyreSets) == 0x000744, "Member 'UWDG_PitstopInfo_C::TotalTyreSets' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, AvailableTyreSetIndexes) == 0x000748, "Member 'UWDG_PitstopInfo_C::AvailableTyreSetIndexes' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, EnableInput) == 0x000758, "Member 'UWDG_PitstopInfo_C::EnableInput' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, DriverStints) == 0x000760, "Member 'UWDG_PitstopInfo_C::DriverStints' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, ValidColor) == 0x000770, "Member 'UWDG_PitstopInfo_C::ValidColor' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, InvalidColor) == 0x000780, "Member 'UWDG_PitstopInfo_C::InvalidColor' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, raceGameMode) == 0x000790, "Member 'UWDG_PitstopInfo_C::raceGameMode' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, InvalidSlateColor) == 0x000798, "Member 'UWDG_PitstopInfo_C::InvalidSlateColor' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, ValidSlateColor) == 0x0007C0, "Member 'UWDG_PitstopInfo_C::ValidSlateColor' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, driverSwapVisible) == 0x0007E8, "Member 'UWDG_PitstopInfo_C::driverSwapVisible' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, ChatWidgetFocusState) == 0x0007E9, "Member 'UWDG_PitstopInfo_C::ChatWidgetFocusState' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, ShouldShow) == 0x0007EA, "Member 'UWDG_PitstopInfo_C::ShouldShow' has a wrong offset!");
static_assert(offsetof(UWDG_PitstopInfo_C, DeltaTick) == 0x0007EC, "Member 'UWDG_PitstopInfo_C::DeltaTick' has a wrong offset!");

}

