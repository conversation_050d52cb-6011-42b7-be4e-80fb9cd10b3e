﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomTileItemHorizontal

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"
#include "SlateCore_structs.hpp"
#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.ExecuteUbergraph_WDG_ShowroomTileItemHorizontal
// 0x07C0 (0x07C0 - 0x0000)
struct WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0004(0x0001)(ZeroConstructor, Is<PERSON>lainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_6[0x2];                                        // 0x0006(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsSelected;                           // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_11[0x3];                                       // 0x0011(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess_1;                    // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_19[0x3];                                       // 0x0019(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FFocusEvent                            K2Node_Event_InFocusEvent_1;                       // 0x001C(0x0008)(NoDestructor)
	class FName                                   CallFunc_GetControllerActionTypeAsName_ReturnValue; // 0x0024(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsListeningForInputAction_ReturnValue;    // 0x002C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2D[0x3];                                       // 0x002D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class FName                                   CallFunc_GetControllerActionTypeAsName_ReturnValue_1; // 0x0030(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsListeningForInputAction_ReturnValue_1;  // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_39[0x3];                                       // 0x0039(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FFocusEvent                            K2Node_Event_InFocusEvent;                         // 0x003C(0x0008)(NoDestructor)
	uint8                                         Pad_44[0x4];                                       // 0x0044(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue;             // 0x0048(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FMargin                                K2Node_MakeStruct_Margin;                          // 0x0050(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	class UWidgetSwitcherSlot*                    K2Node_DynamicCast_AsWidget_Switcher_Slot;         // 0x0060(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0068(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_69[0x7];                                       // 0x0069(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidgetSwitcherSlot*                    K2Node_DynamicCast_AsWidget_Switcher_Slot_1;       // 0x0070(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x0078(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_79[0x3];                                       // 0x0079(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FMargin                                K2Node_MakeStruct_Margin_1;                        // 0x007C(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor)
	struct FMargin                                K2Node_MakeStruct_Margin_2;                        // 0x008C(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor)
	struct FMargin                                K2Node_MakeStruct_Margin_3;                        // 0x009C(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_AC[0x4];                                       // 0x00AC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UGridSlot*                              CallFunc_SlotAsGridSlot_ReturnValue;               // 0x00B0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_GenericMappedLabel_C*              CallFunc_Create_ReturnValue;                       // 0x00B8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue_1;           // 0x00C0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FMargin                                K2Node_MakeStruct_Margin_4;                        // 0x00C8(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor)
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue_2;           // 0x00D8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate;              // 0x00E0(0x0010)(ZeroConstructor, NoDestructor)
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue_3;           // 0x00F0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate_1;            // 0x00F8(0x0010)(ZeroConstructor, NoDestructor)
	struct FSkinColor                             K2Node_CustomEvent_Color;                          // 0x0108(0x0160)()
	struct FDriverInfo                            K2Node_Event_driverInfo;                           // 0x0268(0x00F0)()
	class FName                                   K2Node_Event_DriverKey;                            // 0x0358(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsCustom;                             // 0x0360(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_361[0x3];                                      // 0x0361(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           K2Node_MakeStruct_LinearColor;                     // 0x0364(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_374[0x4];                                      // 0x0374(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FCarInfo                               K2Node_Event_carInfo;                              // 0x0378(0x00E0)()
	class FName                                   K2Node_Event_CarKey;                               // 0x0458(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FModelInfo                             K2Node_Event_ModelInfo;                            // 0x0460(0x01A8)()
	struct FTeamInfo                              K2Node_Event_TeamInfo;                             // 0x0608(0x0038)()
	ERaceEventType                                CallFunc_Array_Get_Item;                           // 0x0640(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NotEqual_ByteByte_ReturnValue;            // 0x0641(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_642[0x2];                                      // 0x0642(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class FName                                   CallFunc_Conv_StringToName_ReturnValue;            // 0x0644(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Len_ReturnValue;                          // 0x064C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x0650(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_651[0x7];                                      // 0x0651(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x0658(0x0018)()
	bool                                          CallFunc_NotEqual_StrStr_ReturnValue;              // 0x0670(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_671[0x3];                                      // 0x0671(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0674(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0678(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_679[0x3];                                      // 0x0679(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x067C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0680(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_681[0x3];                                      // 0x0681(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0684(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0688(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_689[0x7];                                      // 0x0689(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue;                     // 0x0690(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetChildrenCount_ReturnValue;             // 0x0698(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x069C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x069D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_69E[0x2];                                      // 0x069E(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateColor                            K2Node_MakeStruct_SlateColor;                      // 0x06A0(0x0028)()
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_1;                    // 0x06C8(0x0028)(UObjectWrapper)
	bool                                          CallFunc_NotEqual_ByteByte_ReturnValue_1;          // 0x06F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x06F1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6F2[0x6];                                      // 0x06F2(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_2;                    // 0x06F8(0x0028)(UObjectWrapper)
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_3;                    // 0x0720(0x0028)()
	struct FSlateFontInfo                         K2Node_MakeStruct_SlateFontInfo;                   // 0x0748(0x0058)(HasGetValueTypeHash)
	float                                         CallFunc_Add_FloatFloat_ReturnValue;               // 0x07A0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Add_FloatFloat_ReturnValue_1;             // 0x07A4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Divide_FloatFloat_ReturnValue;            // 0x07A8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_GreaterEqual_FloatFloat_ReturnValue;      // 0x07AC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_7AD[0x3];                                      // 0x07AD(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FMargin                                K2Node_MakeStruct_Margin_5;                        // 0x07B0(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
};
static_assert(alignof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal) == 0x000008, "Wrong alignment on WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal");
static_assert(sizeof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal) == 0x0007C0, "Wrong size on WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, EntryPoint) == 0x000000, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_MakeLiteralByte_ReturnValue) == 0x000004, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_SwitchEnum_CmpSuccess) == 0x000005, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_PlayAnimation_ReturnValue) == 0x000008, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_Event_IsSelected) == 0x000010, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_Event_IsSelected' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, Temp_int_Array_Index_Variable) == 0x000014, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_SwitchEnum_CmpSuccess_1) == 0x000018, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_SwitchEnum_CmpSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_Event_InFocusEvent_1) == 0x00001C, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_Event_InFocusEvent_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_GetControllerActionTypeAsName_ReturnValue) == 0x000024, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_GetControllerActionTypeAsName_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_IsListeningForInputAction_ReturnValue) == 0x00002C, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_IsListeningForInputAction_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_GetControllerActionTypeAsName_ReturnValue_1) == 0x000030, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_GetControllerActionTypeAsName_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_IsListeningForInputAction_ReturnValue_1) == 0x000038, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_IsListeningForInputAction_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_Event_InFocusEvent) == 0x00003C, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_Event_InFocusEvent' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_SlotAsCanvasSlot_ReturnValue) == 0x000048, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_SlotAsCanvasSlot_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_MakeStruct_Margin) == 0x000050, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_MakeStruct_Margin' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_DynamicCast_AsWidget_Switcher_Slot) == 0x000060, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_DynamicCast_AsWidget_Switcher_Slot' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_DynamicCast_bSuccess) == 0x000068, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_DynamicCast_AsWidget_Switcher_Slot_1) == 0x000070, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_DynamicCast_AsWidget_Switcher_Slot_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_DynamicCast_bSuccess_1) == 0x000078, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_MakeStruct_Margin_1) == 0x00007C, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_MakeStruct_Margin_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_MakeStruct_Margin_2) == 0x00008C, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_MakeStruct_Margin_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_MakeStruct_Margin_3) == 0x00009C, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_MakeStruct_Margin_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_SlotAsGridSlot_ReturnValue) == 0x0000B0, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_SlotAsGridSlot_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_Create_ReturnValue) == 0x0000B8, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_SlotAsCanvasSlot_ReturnValue_1) == 0x0000C0, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_SlotAsCanvasSlot_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_MakeStruct_Margin_4) == 0x0000C8, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_MakeStruct_Margin_4' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_SlotAsCanvasSlot_ReturnValue_2) == 0x0000D8, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_SlotAsCanvasSlot_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_CreateDelegate_OutputDelegate) == 0x0000E0, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_SlotAsCanvasSlot_ReturnValue_3) == 0x0000F0, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_SlotAsCanvasSlot_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_CreateDelegate_OutputDelegate_1) == 0x0000F8, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_CreateDelegate_OutputDelegate_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_CustomEvent_Color) == 0x000108, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_CustomEvent_Color' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_Event_driverInfo) == 0x000268, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_Event_driverInfo' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_Event_DriverKey) == 0x000358, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_Event_DriverKey' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_Event_IsCustom) == 0x000360, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_Event_IsCustom' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_MakeStruct_LinearColor) == 0x000364, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_MakeStruct_LinearColor' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_Event_carInfo) == 0x000378, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_Event_carInfo' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_Event_CarKey) == 0x000458, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_Event_CarKey' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_Event_ModelInfo) == 0x000460, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_Event_ModelInfo' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_Event_TeamInfo) == 0x000608, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_Event_TeamInfo' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_Array_Get_Item) == 0x000640, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_NotEqual_ByteByte_ReturnValue) == 0x000641, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_NotEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_Conv_StringToName_ReturnValue) == 0x000644, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_Conv_StringToName_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_Len_ReturnValue) == 0x00064C, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_Len_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_Greater_IntInt_ReturnValue) == 0x000650, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_Conv_StringToText_ReturnValue) == 0x000658, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_NotEqual_StrStr_ReturnValue) == 0x000670, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_NotEqual_StrStr_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_Array_Length_ReturnValue) == 0x000674, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_BooleanAND_ReturnValue) == 0x000678, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, Temp_int_Loop_Counter_Variable) == 0x00067C, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_Less_IntInt_ReturnValue) == 0x000680, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_Add_IntInt_ReturnValue) == 0x000684, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_Event_IsDesignTime) == 0x000688, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_AddChild_ReturnValue) == 0x000690, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_AddChild_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_GetChildrenCount_ReturnValue) == 0x000698, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_GetChildrenCount_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x00069C, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_LessEqual_IntInt_ReturnValue) == 0x00069D, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_MakeStruct_SlateColor) == 0x0006A0, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_MakeStruct_SlateColor' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_MakeStruct_SlateColor_1) == 0x0006C8, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_MakeStruct_SlateColor_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_NotEqual_ByteByte_ReturnValue_1) == 0x0006F0, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_NotEqual_ByteByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x0006F1, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_MakeStruct_SlateColor_2) == 0x0006F8, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_MakeStruct_SlateColor_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_MakeStruct_SlateColor_3) == 0x000720, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_MakeStruct_SlateColor_3' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_MakeStruct_SlateFontInfo) == 0x000748, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_MakeStruct_SlateFontInfo' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_Add_FloatFloat_ReturnValue) == 0x0007A0, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_Add_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_Add_FloatFloat_ReturnValue_1) == 0x0007A4, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_Add_FloatFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_Divide_FloatFloat_ReturnValue) == 0x0007A8, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_Divide_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, CallFunc_GreaterEqual_FloatFloat_ReturnValue) == 0x0007AC, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::CallFunc_GreaterEqual_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal, K2Node_MakeStruct_Margin_5) == 0x0007B0, "Member 'WDG_ShowroomTileItemHorizontal_C_ExecuteUbergraph_WDG_ShowroomTileItemHorizontal::K2Node_MakeStruct_Margin_5' has a wrong offset!");

// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.UpdateTeam
// 0x0038 (0x0038 - 0x0000)
struct WDG_ShowroomTileItemHorizontal_C_UpdateTeam final
{
public:
	struct FTeamInfo                              TeamInfo_0;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_ShowroomTileItemHorizontal_C_UpdateTeam) == 0x000008, "Wrong alignment on WDG_ShowroomTileItemHorizontal_C_UpdateTeam");
static_assert(sizeof(WDG_ShowroomTileItemHorizontal_C_UpdateTeam) == 0x000038, "Wrong size on WDG_ShowroomTileItemHorizontal_C_UpdateTeam");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_UpdateTeam, TeamInfo_0) == 0x000000, "Member 'WDG_ShowroomTileItemHorizontal_C_UpdateTeam::TeamInfo_0' has a wrong offset!");

// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.UpdateModel
// 0x01A8 (0x01A8 - 0x0000)
struct WDG_ShowroomTileItemHorizontal_C_UpdateModel final
{
public:
	struct FModelInfo                             ModelInfo_0;                                       // 0x0000(0x01A8)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_ShowroomTileItemHorizontal_C_UpdateModel) == 0x000008, "Wrong alignment on WDG_ShowroomTileItemHorizontal_C_UpdateModel");
static_assert(sizeof(WDG_ShowroomTileItemHorizontal_C_UpdateModel) == 0x0001A8, "Wrong size on WDG_ShowroomTileItemHorizontal_C_UpdateModel");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_UpdateModel, ModelInfo_0) == 0x000000, "Member 'WDG_ShowroomTileItemHorizontal_C_UpdateModel::ModelInfo_0' has a wrong offset!");

// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.UpdateCar
// 0x00E8 (0x00E8 - 0x0000)
struct WDG_ShowroomTileItemHorizontal_C_UpdateCar final
{
public:
	struct FCarInfo                               CarInfo_0;                                         // 0x0000(0x00E0)(BlueprintVisible, BlueprintReadOnly, Parm)
	class FName                                   CarKey;                                            // 0x00E0(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomTileItemHorizontal_C_UpdateCar) == 0x000008, "Wrong alignment on WDG_ShowroomTileItemHorizontal_C_UpdateCar");
static_assert(sizeof(WDG_ShowroomTileItemHorizontal_C_UpdateCar) == 0x0000E8, "Wrong size on WDG_ShowroomTileItemHorizontal_C_UpdateCar");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_UpdateCar, CarInfo_0) == 0x000000, "Member 'WDG_ShowroomTileItemHorizontal_C_UpdateCar::CarInfo_0' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_UpdateCar, CarKey) == 0x0000E0, "Member 'WDG_ShowroomTileItemHorizontal_C_UpdateCar::CarKey' has a wrong offset!");

// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.UpdateDriver
// 0x0100 (0x0100 - 0x0000)
struct WDG_ShowroomTileItemHorizontal_C_UpdateDriver final
{
public:
	struct FDriverInfo                            DriverInfo_0;                                      // 0x0000(0x00F0)(BlueprintVisible, BlueprintReadOnly, Parm)
	class FName                                   DriverKey;                                         // 0x00F0(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          isCustom;                                          // 0x00F8(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomTileItemHorizontal_C_UpdateDriver) == 0x000008, "Wrong alignment on WDG_ShowroomTileItemHorizontal_C_UpdateDriver");
static_assert(sizeof(WDG_ShowroomTileItemHorizontal_C_UpdateDriver) == 0x000100, "Wrong size on WDG_ShowroomTileItemHorizontal_C_UpdateDriver");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_UpdateDriver, DriverInfo_0) == 0x000000, "Member 'WDG_ShowroomTileItemHorizontal_C_UpdateDriver::DriverInfo_0' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_UpdateDriver, DriverKey) == 0x0000F0, "Member 'WDG_ShowroomTileItemHorizontal_C_UpdateDriver::DriverKey' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_UpdateDriver, isCustom) == 0x0000F8, "Member 'WDG_ShowroomTileItemHorizontal_C_UpdateDriver::isCustom' has a wrong offset!");

// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.UpdateColor
// 0x0160 (0x0160 - 0x0000)
struct WDG_ShowroomTileItemHorizontal_C_UpdateColor final
{
public:
	struct FSkinColor                             Color;                                             // 0x0000(0x0160)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_ShowroomTileItemHorizontal_C_UpdateColor) == 0x000008, "Wrong alignment on WDG_ShowroomTileItemHorizontal_C_UpdateColor");
static_assert(sizeof(WDG_ShowroomTileItemHorizontal_C_UpdateColor) == 0x000160, "Wrong size on WDG_ShowroomTileItemHorizontal_C_UpdateColor");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_UpdateColor, Color) == 0x000000, "Member 'WDG_ShowroomTileItemHorizontal_C_UpdateColor::Color' has a wrong offset!");

// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.OnRemovedFromFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomTileItemHorizontal_C_OnRemovedFromFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_ShowroomTileItemHorizontal_C_OnRemovedFromFocusPath) == 0x000004, "Wrong alignment on WDG_ShowroomTileItemHorizontal_C_OnRemovedFromFocusPath");
static_assert(sizeof(WDG_ShowroomTileItemHorizontal_C_OnRemovedFromFocusPath) == 0x000008, "Wrong size on WDG_ShowroomTileItemHorizontal_C_OnRemovedFromFocusPath");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_OnRemovedFromFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_ShowroomTileItemHorizontal_C_OnRemovedFromFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.OnAddedToFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomTileItemHorizontal_C_OnAddedToFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_ShowroomTileItemHorizontal_C_OnAddedToFocusPath) == 0x000004, "Wrong alignment on WDG_ShowroomTileItemHorizontal_C_OnAddedToFocusPath");
static_assert(sizeof(WDG_ShowroomTileItemHorizontal_C_OnAddedToFocusPath) == 0x000008, "Wrong size on WDG_ShowroomTileItemHorizontal_C_OnAddedToFocusPath");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_OnAddedToFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_ShowroomTileItemHorizontal_C_OnAddedToFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.SetSelected
// 0x0001 (0x0001 - 0x0000)
struct WDG_ShowroomTileItemHorizontal_C_SetSelected final
{
public:
	bool                                          IsSelected_0;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomTileItemHorizontal_C_SetSelected) == 0x000001, "Wrong alignment on WDG_ShowroomTileItemHorizontal_C_SetSelected");
static_assert(sizeof(WDG_ShowroomTileItemHorizontal_C_SetSelected) == 0x000001, "Wrong size on WDG_ShowroomTileItemHorizontal_C_SetSelected");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetSelected, IsSelected_0) == 0x000000, "Member 'WDG_ShowroomTileItemHorizontal_C_SetSelected::IsSelected_0' has a wrong offset!");

// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_ShowroomTileItemHorizontal_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomTileItemHorizontal_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_ShowroomTileItemHorizontal_C_PreConstruct");
static_assert(sizeof(WDG_ShowroomTileItemHorizontal_C_PreConstruct) == 0x000001, "Wrong size on WDG_ShowroomTileItemHorizontal_C_PreConstruct");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_ShowroomTileItemHorizontal_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.ShouldAnimate
// 0x000C (0x000C - 0x0000)
struct WDG_ShowroomTileItemHorizontal_C_ShouldAnimate final
{
public:
	class FName                                   New_Key;                                           // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_NotEqual_NameName_ReturnValue;            // 0x0009(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x000A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomTileItemHorizontal_C_ShouldAnimate) == 0x000004, "Wrong alignment on WDG_ShowroomTileItemHorizontal_C_ShouldAnimate");
static_assert(sizeof(WDG_ShowroomTileItemHorizontal_C_ShouldAnimate) == 0x00000C, "Wrong size on WDG_ShowroomTileItemHorizontal_C_ShouldAnimate");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ShouldAnimate, New_Key) == 0x000000, "Member 'WDG_ShowroomTileItemHorizontal_C_ShouldAnimate::New_Key' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ShouldAnimate, CallFunc_Not_PreBool_ReturnValue) == 0x000008, "Member 'WDG_ShowroomTileItemHorizontal_C_ShouldAnimate::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ShouldAnimate, CallFunc_NotEqual_NameName_ReturnValue) == 0x000009, "Member 'WDG_ShowroomTileItemHorizontal_C_ShouldAnimate::CallFunc_NotEqual_NameName_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_ShouldAnimate, CallFunc_BooleanAND_ReturnValue) == 0x00000A, "Member 'WDG_ShowroomTileItemHorizontal_C_ShouldAnimate::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.SetTileColor
// 0x0010 (0x0010 - 0x0000)
struct WDG_ShowroomTileItemHorizontal_C_SetTileColor final
{
public:
	struct FLinearColor                           InColorAndOpacity;                                 // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomTileItemHorizontal_C_SetTileColor) == 0x000004, "Wrong alignment on WDG_ShowroomTileItemHorizontal_C_SetTileColor");
static_assert(sizeof(WDG_ShowroomTileItemHorizontal_C_SetTileColor) == 0x000010, "Wrong size on WDG_ShowroomTileItemHorizontal_C_SetTileColor");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetTileColor, InColorAndOpacity) == 0x000000, "Member 'WDG_ShowroomTileItemHorizontal_C_SetTileColor::InColorAndOpacity' has a wrong offset!");

// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.SetTileColorByCode
// 0x00D0 (0x00D0 - 0x0000)
struct WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode final
{
public:
	int32                                         ColorCode;                                         // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Color;                                             // 0x0004(0x0010)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_14[0x4];                                       // 0x0014(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_IntToText_ReturnValue;               // 0x0018(0x0018)()
	struct FSlateColor                            K2Node_MakeStruct_SlateColor;                      // 0x0030(0x0028)(UObjectWrapper)
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_1;                    // 0x0058(0x0028)(UObjectWrapper)
	struct FLinearColor                           CallFunc_getSkinColorByCode_ReturnValue;           // 0x0080(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakColor_R;                             // 0x0090(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakColor_G;                             // 0x0094(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakColor_B;                             // 0x0098(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakColor_A;                             // 0x009C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_RGBToHSV_H;                               // 0x00A0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_RGBToHSV_S;                               // 0x00A4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_RGBToHSV_V;                               // 0x00A8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_RGBToHSV_A;                               // 0x00AC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_FloatFloat_ReturnValue;           // 0x00B0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Greater_FloatFloat_ReturnValue_1;         // 0x00B1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_B2[0x2];                                       // 0x00B2(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           K2Node_MakeStruct_LinearColor;                     // 0x00B4(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	float                                         CallFunc_Add_FloatFloat_ReturnValue;               // 0x00C4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Add_FloatFloat_ReturnValue_1;             // 0x00C8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NearlyEqual_FloatFloat_ReturnValue;       // 0x00CC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x00CD(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x00CE(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x00CF(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode) == 0x000008, "Wrong alignment on WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode");
static_assert(sizeof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode) == 0x0000D0, "Wrong size on WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode, ColorCode) == 0x000000, "Member 'WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode::ColorCode' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode, Color) == 0x000004, "Member 'WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode::Color' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode, CallFunc_Conv_IntToText_ReturnValue) == 0x000018, "Member 'WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode::CallFunc_Conv_IntToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode, K2Node_MakeStruct_SlateColor) == 0x000030, "Member 'WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode::K2Node_MakeStruct_SlateColor' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode, K2Node_MakeStruct_SlateColor_1) == 0x000058, "Member 'WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode::K2Node_MakeStruct_SlateColor_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode, CallFunc_getSkinColorByCode_ReturnValue) == 0x000080, "Member 'WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode::CallFunc_getSkinColorByCode_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode, CallFunc_BreakColor_R) == 0x000090, "Member 'WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode::CallFunc_BreakColor_R' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode, CallFunc_BreakColor_G) == 0x000094, "Member 'WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode::CallFunc_BreakColor_G' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode, CallFunc_BreakColor_B) == 0x000098, "Member 'WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode::CallFunc_BreakColor_B' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode, CallFunc_BreakColor_A) == 0x00009C, "Member 'WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode::CallFunc_BreakColor_A' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode, CallFunc_RGBToHSV_H) == 0x0000A0, "Member 'WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode::CallFunc_RGBToHSV_H' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode, CallFunc_RGBToHSV_S) == 0x0000A4, "Member 'WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode::CallFunc_RGBToHSV_S' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode, CallFunc_RGBToHSV_V) == 0x0000A8, "Member 'WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode::CallFunc_RGBToHSV_V' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode, CallFunc_RGBToHSV_A) == 0x0000AC, "Member 'WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode::CallFunc_RGBToHSV_A' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode, CallFunc_Greater_FloatFloat_ReturnValue) == 0x0000B0, "Member 'WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode::CallFunc_Greater_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode, CallFunc_Greater_FloatFloat_ReturnValue_1) == 0x0000B1, "Member 'WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode::CallFunc_Greater_FloatFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode, K2Node_MakeStruct_LinearColor) == 0x0000B4, "Member 'WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode::K2Node_MakeStruct_LinearColor' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode, CallFunc_Add_FloatFloat_ReturnValue) == 0x0000C4, "Member 'WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode::CallFunc_Add_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode, CallFunc_Add_FloatFloat_ReturnValue_1) == 0x0000C8, "Member 'WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode::CallFunc_Add_FloatFloat_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode, CallFunc_NearlyEqual_FloatFloat_ReturnValue) == 0x0000CC, "Member 'WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode::CallFunc_NearlyEqual_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode, CallFunc_BooleanAND_ReturnValue) == 0x0000CD, "Member 'WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode, CallFunc_Not_PreBool_ReturnValue) == 0x0000CE, "Member 'WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode, CallFunc_BooleanAND_ReturnValue_1) == 0x0000CF, "Member 'WDG_ShowroomTileItemHorizontal_C_SetTileColorByCode::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");

// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.SetLabelFixedWidth
// 0x0001 (0x0001 - 0x0000)
struct WDG_ShowroomTileItemHorizontal_C_SetLabelFixedWidth final
{
public:
	bool                                          CallFunc_Greater_FloatFloat_ReturnValue;           // 0x0000(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomTileItemHorizontal_C_SetLabelFixedWidth) == 0x000001, "Wrong alignment on WDG_ShowroomTileItemHorizontal_C_SetLabelFixedWidth");
static_assert(sizeof(WDG_ShowroomTileItemHorizontal_C_SetLabelFixedWidth) == 0x000001, "Wrong size on WDG_ShowroomTileItemHorizontal_C_SetLabelFixedWidth");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetLabelFixedWidth, CallFunc_Greater_FloatFloat_ReturnValue) == 0x000000, "Member 'WDG_ShowroomTileItemHorizontal_C_SetLabelFixedWidth::CallFunc_Greater_FloatFloat_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomTileItemHorizontal.WDG_ShowroomTileItemHorizontal_C.SetColorTextLabel
// 0x0018 (0x0018 - 0x0000)
struct WDG_ShowroomTileItemHorizontal_C_SetColorTextLabel final
{
public:
	class FText                                   InText;                                            // 0x0000(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_ShowroomTileItemHorizontal_C_SetColorTextLabel) == 0x000008, "Wrong alignment on WDG_ShowroomTileItemHorizontal_C_SetColorTextLabel");
static_assert(sizeof(WDG_ShowroomTileItemHorizontal_C_SetColorTextLabel) == 0x000018, "Wrong size on WDG_ShowroomTileItemHorizontal_C_SetColorTextLabel");
static_assert(offsetof(WDG_ShowroomTileItemHorizontal_C_SetColorTextLabel, InText) == 0x000000, "Member 'WDG_ShowroomTileItemHorizontal_C_SetColorTextLabel::InText' has a wrong offset!");

}

