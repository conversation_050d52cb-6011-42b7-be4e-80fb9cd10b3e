﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RaceRatingDetailTrackCompetence

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RaceRatingDetailTrackCompetence.WDG_RaceRatingDetailTrackCompetence_C
// 0x0000 (0x02C8 - 0x02C8)
class UWDG_RaceRatingDetailTrackCompetence_C final : public URatingDetailTrackCompetence
{
public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RaceRatingDetailTrackCompetence_C">();
	}
	static class UWDG_RaceRatingDetailTrackCompetence_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RaceRatingDetailTrackCompetence_C>();
	}
};
static_assert(alignof(UWDG_RaceRatingDetailTrackCompetence_C) == 0x000008, "Wrong alignment on UWDG_RaceRatingDetailTrackCompetence_C");
static_assert(sizeof(UWDG_RaceRatingDetailTrackCompetence_C) == 0x0002C8, "Wrong size on UWDG_RaceRatingDetailTrackCompetence_C");

}

