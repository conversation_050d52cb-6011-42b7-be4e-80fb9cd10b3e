﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SavedGamesPage

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "SlateCore_structs.hpp"
#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_SavedGamesPage.WDG_SavedGamesPage_C.ExecuteUbergraph_WDG_SavedGamesPage
// 0x07E0 (0x07E0 - 0x0000)
struct WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0004(0x0001)(ZeroConstructor, <PERSON><PERSON><PERSON><PERSON>ld<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	uint8                                         Pad_5[0x3];                                        // 0x0005(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void(const struct FAcSaveGameHeader& SaveGame, class UWDG_SaveGameItem_C* Sender)> K2Node_CreateDelegate_OutputDelegate; // 0x0008(0x0010)(ZeroConstructor, NoDestructor)
	TDelegate<void(const struct FAcSaveGameHeader& SaveGame, class UWDG_SaveGameItem_C* Sender)> K2Node_CreateDelegate_OutputDelegate_1; // 0x0018(0x0010)(ZeroConstructor, NoDestructor)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_2C[0x4];                                       // 0x002C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Conv_NameToString_ReturnValue;            // 0x0030(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FName                                   Temp_name_Variable;                                // 0x0040(0x0008)(ConstParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue;                // 0x0048(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0058(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_59[0x7];                                       // 0x0059(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue;              // 0x0060(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_SaveGameItem_C*                    CallFunc_Create_ReturnValue;                       // 0x0068(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<struct FAcSaveGameHeader>              CallFunc_GetSavegameList_headers;                  // 0x0070(0x0010)(ReferenceParm)
	TArray<struct FAcSaveGameHeader>              CallFunc_GetSinglePlayer_ReturnValue;              // 0x0080(0x0010)(ReferenceParm)
	struct FAcSaveGameHeader                      CallFunc_Array_Get_Item;                           // 0x0090(0x0158)()
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x01E8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x01EC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_ConvertInt32ToFormattedTime_ReturnValue;  // 0x01F0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x0200(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_201[0x7];                                      // 0x0201(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x0208(0x0018)()
	class FText                                   CallFunc_Conv_IntToText_ReturnValue;               // 0x0220(0x0018)()
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_1;          // 0x0238(0x0018)()
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_2;          // 0x0250(0x0018)()
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_1;             // 0x0268(0x0018)()
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x0280(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_281[0x3];                                      // 0x0281(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_BreakDateTime_Year;                       // 0x0284(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakDateTime_Month;                      // 0x0288(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakDateTime_Day;                        // 0x028C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakDateTime_Hour;                       // 0x0290(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakDateTime_Minute;                     // 0x0294(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakDateTime_Second;                     // 0x0298(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakDateTime_Millisecond;                // 0x029C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_PadNumberWithZeroes_ReturnValue;          // 0x02A0(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData;              // 0x02B0(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_3;          // 0x02F0(0x0018)()
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData_1;            // 0x0308(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData_2;            // 0x0348(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData_3;            // 0x0388(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_2;             // 0x03C8(0x0018)()
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x03E0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3E4[0x4];                                      // 0x03E4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData_4;            // 0x03E8(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	TArray<struct FFormatArgumentData>            K2Node_MakeArray_Array;                            // 0x0428(0x0010)(ReferenceParm)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0438(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_439[0x7];                                      // 0x0439(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Format_ReturnValue;                       // 0x0440(0x0018)()
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0458(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_45C[0x4];                                      // 0x045C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_GetCommonGameModeText_ReturnValue;        // 0x0460(0x0018)()
	class FText                                   CallFunc_GetCommonSessionTypeText_ReturnValue;     // 0x0478(0x0018)()
	class UClass*                                 CallFunc_Map_Find_Value;                           // 0x0490(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Map_Find_ReturnValue;                     // 0x0498(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_499[0x7];                                      // 0x0499(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue;              // 0x04A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance;             // 0x04A8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x04B0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_4B1[0x7];                                      // 0x04B1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPageBase*                            CallFunc_GoToPage_ReturnValue;                     // 0x04B8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x04C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          K2Node_ComponentBoundEvent_HasConfirmation;        // 0x04C1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_4C2[0x6];                                      // 0x04C2(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 K2Node_CustomEvent_FileName;                       // 0x04C8(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_DeleteSavegame_ReturnValue;               // 0x04D8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_4D9[0x3];                                      // 0x04D9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FFocusEvent                            K2Node_Event_InFocusEvent;                         // 0x04DC(0x0008)(NoDestructor)
	bool                                          CallFunc_HasAnyChildren_ReturnValue;               // 0x04E4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_4E5[0x3];                                      // 0x04E5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                CallFunc_GetChildAt_ReturnValue;                   // 0x04E8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcPanelBase*                           K2Node_DynamicCast_AsAc_Panel_Base;                // 0x04F0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x04F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_4F9[0x7];                                      // 0x04F9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue;               // 0x0500(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue;                     // 0x0508(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FAcSaveGameHeader                      K2Node_CustomEvent_SaveGame_1;                     // 0x0510(0x0158)()
	class UWDG_SaveGameItem_C*                    K2Node_CustomEvent_Sender_1;                       // 0x0668(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FAcSaveGameHeader                      K2Node_CustomEvent_SaveGame;                       // 0x0670(0x0158)()
	class UWDG_SaveGameItem_C*                    K2Node_CustomEvent_Sender;                         // 0x07C8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_HasAccessToContent_ReturnValue;           // 0x07D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_HasAccessToCarModel_ReturnValue;          // 0x07D1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_7D2[0x2];                                      // 0x07D2(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class FName                                   CallFunc_Conv_StringToName_ReturnValue;            // 0x07D4(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_HasAccessToTrack_ReturnValue;             // 0x07DC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x07DD(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x07DE(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage) == 0x000008, "Wrong alignment on WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage");
static_assert(sizeof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage) == 0x0007E0, "Wrong size on WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, EntryPoint) == 0x000000, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_MakeLiteralByte_ReturnValue) == 0x000004, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, K2Node_CreateDelegate_OutputDelegate) == 0x000008, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, K2Node_CreateDelegate_OutputDelegate_1) == 0x000018, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::K2Node_CreateDelegate_OutputDelegate_1' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, Temp_int_Array_Index_Variable) == 0x000028, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_Conv_NameToString_ReturnValue) == 0x000030, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_Conv_NameToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, Temp_name_Variable) == 0x000040, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::Temp_name_Variable' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_Concat_StrStr_ReturnValue) == 0x000048, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_Concat_StrStr_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000058, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_GetOwningPlayer_ReturnValue) == 0x000060, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_GetOwningPlayer_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_Create_ReturnValue) == 0x000068, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_GetSavegameList_headers) == 0x000070, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_GetSavegameList_headers' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_GetSinglePlayer_ReturnValue) == 0x000080, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_GetSinglePlayer_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_Array_Get_Item) == 0x000090, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_Array_Length_ReturnValue) == 0x0001E8, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_Array_Length_ReturnValue_1) == 0x0001EC, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_ConvertInt32ToFormattedTime_ReturnValue) == 0x0001F0, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_ConvertInt32ToFormattedTime_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x000200, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_Conv_StringToText_ReturnValue) == 0x000208, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_Conv_IntToText_ReturnValue) == 0x000220, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_Conv_IntToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_Conv_StringToText_ReturnValue_1) == 0x000238, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_Conv_StringToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_Conv_StringToText_ReturnValue_2) == 0x000250, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_Conv_StringToText_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_Conv_IntToText_ReturnValue_1) == 0x000268, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_Conv_IntToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_Greater_IntInt_ReturnValue) == 0x000280, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_BreakDateTime_Year) == 0x000284, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_BreakDateTime_Year' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_BreakDateTime_Month) == 0x000288, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_BreakDateTime_Month' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_BreakDateTime_Day) == 0x00028C, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_BreakDateTime_Day' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_BreakDateTime_Hour) == 0x000290, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_BreakDateTime_Hour' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_BreakDateTime_Minute) == 0x000294, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_BreakDateTime_Minute' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_BreakDateTime_Second) == 0x000298, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_BreakDateTime_Second' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_BreakDateTime_Millisecond) == 0x00029C, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_BreakDateTime_Millisecond' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_PadNumberWithZeroes_ReturnValue) == 0x0002A0, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_PadNumberWithZeroes_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, K2Node_MakeStruct_FormatArgumentData) == 0x0002B0, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::K2Node_MakeStruct_FormatArgumentData' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_Conv_StringToText_ReturnValue_3) == 0x0002F0, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_Conv_StringToText_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, K2Node_MakeStruct_FormatArgumentData_1) == 0x000308, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::K2Node_MakeStruct_FormatArgumentData_1' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, K2Node_MakeStruct_FormatArgumentData_2) == 0x000348, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::K2Node_MakeStruct_FormatArgumentData_2' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, K2Node_MakeStruct_FormatArgumentData_3) == 0x000388, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::K2Node_MakeStruct_FormatArgumentData_3' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_Conv_IntToText_ReturnValue_2) == 0x0003C8, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_Conv_IntToText_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, Temp_int_Loop_Counter_Variable) == 0x0003E0, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, K2Node_MakeStruct_FormatArgumentData_4) == 0x0003E8, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::K2Node_MakeStruct_FormatArgumentData_4' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, K2Node_MakeArray_Array) == 0x000428, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_Less_IntInt_ReturnValue) == 0x000438, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_Format_ReturnValue) == 0x000440, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_Format_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_Add_IntInt_ReturnValue) == 0x000458, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_GetCommonGameModeText_ReturnValue) == 0x000460, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_GetCommonGameModeText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_GetCommonSessionTypeText_ReturnValue) == 0x000478, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_GetCommonSessionTypeText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_Map_Find_Value) == 0x000490, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_Map_Find_Value' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_Map_Find_ReturnValue) == 0x000498, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_Map_Find_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_GetGameInstance_ReturnValue) == 0x0004A0, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_GetGameInstance_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, K2Node_DynamicCast_AsAc_Game_Instance) == 0x0004A8, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::K2Node_DynamicCast_AsAc_Game_Instance' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, K2Node_DynamicCast_bSuccess) == 0x0004B0, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_GoToPage_ReturnValue) == 0x0004B8, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_GoToPage_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_IsValid_ReturnValue) == 0x0004C0, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, K2Node_ComponentBoundEvent_HasConfirmation) == 0x0004C1, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::K2Node_ComponentBoundEvent_HasConfirmation' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, K2Node_CustomEvent_FileName) == 0x0004C8, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::K2Node_CustomEvent_FileName' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_DeleteSavegame_ReturnValue) == 0x0004D8, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_DeleteSavegame_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, K2Node_Event_InFocusEvent) == 0x0004DC, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::K2Node_Event_InFocusEvent' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_HasAnyChildren_ReturnValue) == 0x0004E4, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_HasAnyChildren_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_GetChildAt_ReturnValue) == 0x0004E8, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_GetChildAt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, K2Node_DynamicCast_AsAc_Panel_Base) == 0x0004F0, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::K2Node_DynamicCast_AsAc_Panel_Base' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, K2Node_DynamicCast_bSuccess_1) == 0x0004F8, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_GetMenuManager_ReturnValue) == 0x000500, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_GetMenuManager_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_AddChild_ReturnValue) == 0x000508, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_AddChild_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, K2Node_CustomEvent_SaveGame_1) == 0x000510, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::K2Node_CustomEvent_SaveGame_1' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, K2Node_CustomEvent_Sender_1) == 0x000668, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::K2Node_CustomEvent_Sender_1' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, K2Node_CustomEvent_SaveGame) == 0x000670, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::K2Node_CustomEvent_SaveGame' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, K2Node_CustomEvent_Sender) == 0x0007C8, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::K2Node_CustomEvent_Sender' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_HasAccessToContent_ReturnValue) == 0x0007D0, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_HasAccessToContent_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_HasAccessToCarModel_ReturnValue) == 0x0007D1, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_HasAccessToCarModel_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_Conv_StringToName_ReturnValue) == 0x0007D4, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_Conv_StringToName_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_HasAccessToTrack_ReturnValue) == 0x0007DC, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_HasAccessToTrack_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_BooleanAND_ReturnValue) == 0x0007DD, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage, CallFunc_BooleanAND_ReturnValue_1) == 0x0007DE, "Member 'WDG_SavedGamesPage_C_ExecuteUbergraph_WDG_SavedGamesPage::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");

// Function WDG_SavedGamesPage.WDG_SavedGamesPage_C.OnLoad
// 0x0160 (0x0160 - 0x0000)
struct WDG_SavedGamesPage_C_OnLoad final
{
public:
	struct FAcSaveGameHeader                      SaveGame;                                          // 0x0000(0x0158)(BlueprintVisible, BlueprintReadOnly, Parm)
	class UWDG_SaveGameItem_C*                    Sender;                                            // 0x0158(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SavedGamesPage_C_OnLoad) == 0x000008, "Wrong alignment on WDG_SavedGamesPage_C_OnLoad");
static_assert(sizeof(WDG_SavedGamesPage_C_OnLoad) == 0x000160, "Wrong size on WDG_SavedGamesPage_C_OnLoad");
static_assert(offsetof(WDG_SavedGamesPage_C_OnLoad, SaveGame) == 0x000000, "Member 'WDG_SavedGamesPage_C_OnLoad::SaveGame' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_OnLoad, Sender) == 0x000158, "Member 'WDG_SavedGamesPage_C_OnLoad::Sender' has a wrong offset!");

// Function WDG_SavedGamesPage.WDG_SavedGamesPage_C.OnDel
// 0x0160 (0x0160 - 0x0000)
struct WDG_SavedGamesPage_C_OnDel final
{
public:
	struct FAcSaveGameHeader                      SaveGame;                                          // 0x0000(0x0158)(BlueprintVisible, BlueprintReadOnly, Parm)
	class UWDG_SaveGameItem_C*                    Sender;                                            // 0x0158(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SavedGamesPage_C_OnDel) == 0x000008, "Wrong alignment on WDG_SavedGamesPage_C_OnDel");
static_assert(sizeof(WDG_SavedGamesPage_C_OnDel) == 0x000160, "Wrong size on WDG_SavedGamesPage_C_OnDel");
static_assert(offsetof(WDG_SavedGamesPage_C_OnDel, SaveGame) == 0x000000, "Member 'WDG_SavedGamesPage_C_OnDel::SaveGame' has a wrong offset!");
static_assert(offsetof(WDG_SavedGamesPage_C_OnDel, Sender) == 0x000158, "Member 'WDG_SavedGamesPage_C_OnDel::Sender' has a wrong offset!");

// Function WDG_SavedGamesPage.WDG_SavedGamesPage_C.OnRemovedFromFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_SavedGamesPage_C_OnRemovedFromFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_SavedGamesPage_C_OnRemovedFromFocusPath) == 0x000004, "Wrong alignment on WDG_SavedGamesPage_C_OnRemovedFromFocusPath");
static_assert(sizeof(WDG_SavedGamesPage_C_OnRemovedFromFocusPath) == 0x000008, "Wrong size on WDG_SavedGamesPage_C_OnRemovedFromFocusPath");
static_assert(offsetof(WDG_SavedGamesPage_C_OnRemovedFromFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_SavedGamesPage_C_OnRemovedFromFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_SavedGamesPage.WDG_SavedGamesPage_C.OnDeleteFile
// 0x0010 (0x0010 - 0x0000)
struct WDG_SavedGamesPage_C_OnDeleteFile final
{
public:
	class FString                                 Filename;                                          // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SavedGamesPage_C_OnDeleteFile) == 0x000008, "Wrong alignment on WDG_SavedGamesPage_C_OnDeleteFile");
static_assert(sizeof(WDG_SavedGamesPage_C_OnDeleteFile) == 0x000010, "Wrong size on WDG_SavedGamesPage_C_OnDeleteFile");
static_assert(offsetof(WDG_SavedGamesPage_C_OnDeleteFile, Filename) == 0x000000, "Member 'WDG_SavedGamesPage_C_OnDeleteFile::Filename' has a wrong offset!");

// Function WDG_SavedGamesPage.WDG_SavedGamesPage_C.BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_3_OnShow__DelegateSignature
// 0x0001 (0x0001 - 0x0000)
struct WDG_SavedGamesPage_C_BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_3_OnShow__DelegateSignature final
{
public:
	bool                                          HasConfirmation;                                   // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SavedGamesPage_C_BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_3_OnShow__DelegateSignature) == 0x000001, "Wrong alignment on WDG_SavedGamesPage_C_BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_3_OnShow__DelegateSignature");
static_assert(sizeof(WDG_SavedGamesPage_C_BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_3_OnShow__DelegateSignature) == 0x000001, "Wrong size on WDG_SavedGamesPage_C_BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_3_OnShow__DelegateSignature");
static_assert(offsetof(WDG_SavedGamesPage_C_BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_3_OnShow__DelegateSignature, HasConfirmation) == 0x000000, "Member 'WDG_SavedGamesPage_C_BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_3_OnShow__DelegateSignature::HasConfirmation' has a wrong offset!");

}

