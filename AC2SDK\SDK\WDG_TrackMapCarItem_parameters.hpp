﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TrackMapCarItem

#include "Basic.hpp"

#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_TrackMapCarItem.WDG_TrackMapCarItem_C.ExecuteUbergraph_WDG_TrackMapCarItem
// 0x0130 (0x0130 - 0x0000)
struct WDG_TrackMapCarItem_C_ExecuteUbergraph_WDG_TrackMapCarItem final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_Event_position;                             // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_9[0x7];                                        // 0x0009(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue;             // 0x0010(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSlateBrush                            K2Node_MakeStruct_SlateBrush;                      // 0x0018(0x0088)()
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue_1;           // 0x00A0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSlateBrush                            K2Node_MakeStruct_SlateBrush_1;                    // 0x00A8(0x0088)()
};
static_assert(alignof(WDG_TrackMapCarItem_C_ExecuteUbergraph_WDG_TrackMapCarItem) == 0x000008, "Wrong alignment on WDG_TrackMapCarItem_C_ExecuteUbergraph_WDG_TrackMapCarItem");
static_assert(sizeof(WDG_TrackMapCarItem_C_ExecuteUbergraph_WDG_TrackMapCarItem) == 0x000130, "Wrong size on WDG_TrackMapCarItem_C_ExecuteUbergraph_WDG_TrackMapCarItem");
static_assert(offsetof(WDG_TrackMapCarItem_C_ExecuteUbergraph_WDG_TrackMapCarItem, EntryPoint) == 0x000000, "Member 'WDG_TrackMapCarItem_C_ExecuteUbergraph_WDG_TrackMapCarItem::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_TrackMapCarItem_C_ExecuteUbergraph_WDG_TrackMapCarItem, K2Node_Event_position) == 0x000004, "Member 'WDG_TrackMapCarItem_C_ExecuteUbergraph_WDG_TrackMapCarItem::K2Node_Event_position' has a wrong offset!");
static_assert(offsetof(WDG_TrackMapCarItem_C_ExecuteUbergraph_WDG_TrackMapCarItem, CallFunc_Less_IntInt_ReturnValue) == 0x000008, "Member 'WDG_TrackMapCarItem_C_ExecuteUbergraph_WDG_TrackMapCarItem::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TrackMapCarItem_C_ExecuteUbergraph_WDG_TrackMapCarItem, CallFunc_SlotAsCanvasSlot_ReturnValue) == 0x000010, "Member 'WDG_TrackMapCarItem_C_ExecuteUbergraph_WDG_TrackMapCarItem::CallFunc_SlotAsCanvasSlot_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TrackMapCarItem_C_ExecuteUbergraph_WDG_TrackMapCarItem, K2Node_MakeStruct_SlateBrush) == 0x000018, "Member 'WDG_TrackMapCarItem_C_ExecuteUbergraph_WDG_TrackMapCarItem::K2Node_MakeStruct_SlateBrush' has a wrong offset!");
static_assert(offsetof(WDG_TrackMapCarItem_C_ExecuteUbergraph_WDG_TrackMapCarItem, CallFunc_SlotAsCanvasSlot_ReturnValue_1) == 0x0000A0, "Member 'WDG_TrackMapCarItem_C_ExecuteUbergraph_WDG_TrackMapCarItem::CallFunc_SlotAsCanvasSlot_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_TrackMapCarItem_C_ExecuteUbergraph_WDG_TrackMapCarItem, K2Node_MakeStruct_SlateBrush_1) == 0x0000A8, "Member 'WDG_TrackMapCarItem_C_ExecuteUbergraph_WDG_TrackMapCarItem::K2Node_MakeStruct_SlateBrush_1' has a wrong offset!");

// Function WDG_TrackMapCarItem.WDG_TrackMapCarItem_C.PositionChanged
// 0x0004 (0x0004 - 0x0000)
struct WDG_TrackMapCarItem_C_PositionChanged final
{
public:
	int32                                         Position_0;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_TrackMapCarItem_C_PositionChanged) == 0x000004, "Wrong alignment on WDG_TrackMapCarItem_C_PositionChanged");
static_assert(sizeof(WDG_TrackMapCarItem_C_PositionChanged) == 0x000004, "Wrong size on WDG_TrackMapCarItem_C_PositionChanged");
static_assert(offsetof(WDG_TrackMapCarItem_C_PositionChanged, Position_0) == 0x000000, "Member 'WDG_TrackMapCarItem_C_PositionChanged::Position_0' has a wrong offset!");

}

