#include <Windows.h>
#include <iostream>
#include <fstream>
#include <string>
#include <filesystem>
#include <fstream>

#include "SDK.hpp"
#include "SDK/Basic.hpp"
#include "SDK/Engine_classes.hpp"
#include "SDK/WDG_HudMfdRealtimeItemBase_classes.hpp"
#include "SDK/UMG_classes.hpp"

using namespace SDK;

const char *GetPenaltyTypeName(EPenaltyType penaltyType)
{
  switch (penaltyType) {
    case EPenaltyType::None:
      return "None";
    case EPenaltyType::DriveThrough:
      return "Drive Through";
    case EPenaltyType::StopAndGo_10:
      return "Stop & Go 10s";
    case EPenaltyType::StopAndGo_20:
      return "Stop & Go 20s";
    case EPenaltyType::StopAndGo_30:
      return "Stop & Go 30s";
    case EPenaltyType::PostRaceTime:
      return "Post Race Time";
    case EPenaltyType::Disqualified:
      return "Disqualified";
    case EPenaltyType::RemoveBestLaptime:
      return "Remove Best Laptime";
    default:
      return "Unknown";
  }
}

DWORD MainThread(HMODULE Module)
{
  // Create logs directory if it doesn't exist
  std::filesystem::create_directories("Z:\\Work\\UpWork\\AC2\\AC2SDK\\logs");

  // Open log file for writing
  std::ofstream logFile("Z:\\Work\\UpWork\\AC2\\AC2SDK\\logs\\dll.log", std::ios::out | std::ios::trunc);
  if (!logFile.is_open()) {
    return 1;
  }

  logFile << std::endl;
  logFile << "=====================================" << std::endl;
  logFile << "====== AC2 ESSENTIAL RACE DATA ======" << std::endl;
  logFile << "=====================================" << std::endl;

  /* Functions returning "static" instances */
  UEngine *engine = UEngine::GetEngine();
  UWorld *world = UWorld::GetWorld();

  // Get player car for current car data
  ACarAvatar *playerCar = nullptr;
  UBP_ACCUtils_C::ACC_GetPlayerCarAvatar(world, &playerCar);

  // Get weather information
  AGameModeBase *gameMode = UGameplayStatics::GetGameMode(world);
  AAcRaceGameMode *raceGameMode = static_cast<AAcRaceGameMode *>(gameMode);

  if (raceGameMode) {
    FWeatherStatus weatherStatus = raceGameMode->getWeatherStatusForUI();
    float timeOfWeekendSeconds = raceGameMode->getTimeOfDayAsSecondsUI();

    logFile << std::endl;
    logFile << "====== CURRENT WEATHER ======" << std::endl;
    logFile << "Cloud Level: " << weatherStatus.CloudLevel << std::endl;
    logFile << "Rain Level: " << weatherStatus.RainLevel << std::endl;
    logFile << "Ambient Temperature: " << weatherStatus.ambientTemperature << "C" << std::endl;
    logFile << "Road Temperature: " << weatherStatus.RoadTemperature << "C" << std::endl;
    logFile << "Wind Speed: " << weatherStatus.windSpeed << " m/s" << std::endl;
    logFile << "Wind Direction: " << weatherStatus.windDirection << "" << std::endl;
    logFile << "Time of Weekend (seconds): " << timeOfWeekendSeconds << std::endl;
  }

  // Get all cars and penalty data
  TArray<AActor *> foundCars;
  UGameplayStatics::GetAllActorsOfClass(world, ACarAvatar::StaticClass(), &foundCars);

  TArray<UUserWidget *> realtimeItems;
  UWidgetBlueprintLibrary::GetAllWidgetsOfClass(world, &realtimeItems, UWDG_HudMfdRealtimeItemBase_C::StaticClass(), false);

  logFile << std::endl;
  logFile << "====== ALL CARS ESSENTIAL DATA ======" << std::endl;

  for (int32 i = 0; i < foundCars.Num(); i++) {
    ACarAvatar *carAvatar = static_cast<ACarAvatar *>(foundCars[i]);
    if (carAvatar) {
      FCarInfo carInfo = carAvatar->getCarInfoUI();

      logFile << "=== CAR " << (i + 1) << " ===" << std::endl;
      logFile << "Car Number: " << carInfo.RaceNumber << std::endl;
      logFile << "Team Name: " << carInfo.TeamName.ToString() << std::endl;

      // Find penalty information
      EPenaltyType penaltyType = EPenaltyType::None;
      uint8 penaltyWeight = 0;

      for (UUserWidget *widget : realtimeItems) {
        UWDG_HudMfdRealtimeItemBase_C *realtimeItem = static_cast<UWDG_HudMfdRealtimeItemBase_C *>(widget);
        if (realtimeItem && realtimeItem->CarNumber == carInfo.RaceNumber) {
          penaltyType = realtimeItem->PenaltyType;
          penaltyWeight = realtimeItem->PenaltyWeight;
          break;
        }
      }

      logFile << "Penalty Type: " << GetPenaltyTypeName(penaltyType) << std::endl;
      logFile << "Penalty Weight: " << (int)penaltyWeight << " seconds" << std::endl;

      // Get pitstop data from car avatar
      FPitstopMFD pitstopMFD = carAvatar->PitstopMFD;

      // Sector times - TODO: Need to access FTimeTableEntry or similar data structure
      logFile << "SectorOne: [TODO - Access needed]" << std::endl;
      logFile << "SectorTwo: [TODO - Access needed]" << std::endl;
      logFile << "SectorThree: [TODO - Access needed]" << std::endl;
      logFile << "Remaining Mandatory Pitstops: " << (int)pitstopMFD.missingMandatoryPitstops << std::endl;

      // Show tyre data only for player car
      if (carAvatar == playerCar) {
        logFile << "=== PLAYER CAR TYRE DATA ===" << std::endl;
        TArray<FTyreSet> tyreSets = carAvatar->tyreSets;
        logFile << "Tyre Sets Available: " << tyreSets.Num() << std::endl;
        logFile << "Current Tyre Set Index: " << carAvatar->currentTyreSetIndex << std::endl;

        // Get tyre suspension state for each wheel (tyresStatus equivalent)
        logFile << "Tyre Status (Core Temp/Pressure):" << std::endl;
        for (int32 wheelIndex = 0; wheelIndex < 4; wheelIndex++) {
          FTyreSuspState tyreState = carAvatar->GetTyreSuspState(wheelIndex);
          const char *wheelNames[] = {"FL", "FR", "RL", "RR"};
          logFile << "  " << wheelNames[wheelIndex] << ": " << tyreState.coreTemp << "C / " << tyreState.pressure << " PSI" << std::endl;
        }
      }
    }
  }

  return 0;
}

BOOL APIENTRY DllMain(HMODULE hModule, DWORD reason, LPVOID lpReserved)
{
  switch (reason) {
    case DLL_PROCESS_ATTACH:
      CreateThread(0, 0, (LPTHREAD_START_ROUTINE)MainThread, hModule, 0, 0);
      break;
  }

  return TRUE;
}