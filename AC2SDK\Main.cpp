#include <Windows.h>
#include <iostream>
#include <string>
#include "SDK.hpp"
#include "SDK/Basic.hpp"
#include "SDK/Engine_classes.hpp"
#include "SDK/WDG_HudMfdRealtimeItemBase_classes.hpp"
#include "SDK/UMG_classes.hpp"

void ClearConsole();

using namespace SDK;

const char *GetPenaltyTypeName(EPenaltyType penaltyType)
{
  switch (penaltyType) {
    case EPenaltyType::None:
      return "None";
    case EPenaltyType::DriveThrough:
      return "Drive Through";
    case EPenaltyType::StopAndGo_10:
      return "Stop & Go 10s";
    case EPenaltyType::StopAndGo_20:
      return "Stop & Go 20s";
    case EPenaltyType::StopAndGo_30:
      return "Stop & Go 30s";
    case EPenaltyType::PostRaceTime:
      return "Post Race Time";
    case EPenaltyType::Disqualified:
      return "Disqualified";
    case EPenaltyType::RemoveBestLaptime:
      return "Remove Best Laptime";
    default:
      return "Unknown";
  }
}

DWORD MainThread(HMODULE Module)
{
  if (!AllocConsole()) {
    return 1;
  }

  FILE *dummy;
  freopen_s(&dummy, "CONOUT$", "w", stdout);
  freopen_s(&dummy, "CONIN$", "r", stdin);
  freopen_s(&dummy, "CONOUT$", "w", stderr);

  std::cout << std::endl;
  std::cout << "=====================================" << std::endl;
  std::cout << "====== AC2 ESSENTIAL RACE DATA ======" << std::endl;
  std::cout << "=====================================" << std::endl;

  /* Functions returning "static" instances */
  UEngine *engine = UEngine::GetEngine();
  UWorld *world = UWorld::GetWorld();

  // Get player car for current car data
  ACarAvatar *playerCar = nullptr;
  UBP_ACCUtils_C::ACC_GetPlayerCarAvatar(world, &playerCar);

  // Get weather information
  AGameModeBase *gameMode = UGameplayStatics::GetGameMode(world);
  AAcRaceGameMode *raceGameMode = static_cast<AAcRaceGameMode *>(gameMode);

  if (raceGameMode) {
    FWeatherStatus weatherStatus = raceGameMode->getWeatherStatusForUI();
    float timeOfWeekendSeconds = raceGameMode->getTimeOfDayAsSecondsUI();

    std::cout << std::endl;
    std::cout << "====== CURRENT WEATHER ======" << std::endl;
    std::cout << "Cloud Level: " << weatherStatus.CloudLevel << std::endl;
    std::cout << "Rain Level: " << weatherStatus.RainLevel << std::endl;
    std::cout << "Ambient Temperature: " << weatherStatus.ambientTemperature << "C" << std::endl;
    std::cout << "Road Temperature: " << weatherStatus.RoadTemperature << "C" << std::endl;
    std::cout << "Wind Speed: " << weatherStatus.windSpeed << " m/s" << std::endl;
    std::cout << "Wind Direction: " << weatherStatus.windDirection << "" << std::endl;
    std::cout << "Time of Weekend (seconds): " << timeOfWeekendSeconds << std::endl;
  }

  // Get all cars and penalty data
  TArray<AActor *> foundCars;
  UGameplayStatics::GetAllActorsOfClass(world, ACarAvatar::StaticClass(), &foundCars);

  TArray<UUserWidget *> realtimeItems;
  UWidgetBlueprintLibrary::GetAllWidgetsOfClass(world, &realtimeItems, UWDG_HudMfdRealtimeItemBase_C::StaticClass(), false);

  std::cout << std::endl;
  std::cout << "====== ALL CARS ESSENTIAL DATA ======" << std::endl;

  for (int32 i = 0; i < foundCars.Num(); i++) {
    ACarAvatar *carAvatar = static_cast<ACarAvatar *>(foundCars[i]);
    if (carAvatar) {
      FCarInfo carInfo = carAvatar->getCarInfoUI();

      std::cout << "=== CAR " << (i + 1) << " ===" << std::endl;
      std::cout << "Car Number: " << carInfo.RaceNumber << std::endl;
      std::cout << "Team Name: " << carInfo.TeamName.ToString() << std::endl;

      // Find penalty information
      EPenaltyType penaltyType = EPenaltyType::None;
      uint8 penaltyWeight = 0;

      for (UUserWidget *widget : realtimeItems) {
        UWDG_HudMfdRealtimeItemBase_C *realtimeItem = static_cast<UWDG_HudMfdRealtimeItemBase_C *>(widget);
        if (realtimeItem && realtimeItem->CarNumber == carInfo.RaceNumber) {
          penaltyType = realtimeItem->PenaltyType;
          penaltyWeight = realtimeItem->PenaltyWeight;
          break;
        }
      }

      std::cout << "Penalty Type: " << GetPenaltyTypeName(penaltyType) << std::endl;
      std::cout << "Penalty Weight: " << (int)penaltyWeight << " seconds" << std::endl;

      // Get pitstop data from car avatar
      FPitstopMFD pitstopMFD = carAvatar->PitstopMFD;

      // Sector times - TODO: Need to access FTimeTableEntry or similar data structure
      std::cout << "SectorOne: [TODO - Access needed]" << std::endl;
      std::cout << "SectorTwo: [TODO - Access needed]" << std::endl;
      std::cout << "SectorThree: [TODO - Access needed]" << std::endl;
      std::cout << "Remaining Mandatory Pitstops: " << (int)pitstopMFD.missingMandatoryPitstops << std::endl;

      // Show tyre data only for player car
      if (carAvatar == playerCar) {
        std::cout << "=== PLAYER CAR TYRE DATA ===" << std::endl;
        TArray<FTyreSet> tyreSets = carAvatar->tyreSets;
        std::cout << "Tyre Sets Available: " << tyreSets.Num() << std::endl;
        std::cout << "Current Tyre Set Index: " << carAvatar->currentTyreSetIndex << std::endl;

        // Get tyre suspension state for each wheel (tyresStatus equivalent)
        std::cout << "Tyre Status (Core Temp/Pressure):" << std::endl;
        for (int32 wheelIndex = 0; wheelIndex < 4; wheelIndex++) {
          FTyreSuspState tyreState = carAvatar->GetTyreSuspState(wheelIndex);
          const char *wheelNames[] = {"FL", "FR", "RL", "RR"};
          std::cout << "  " << wheelNames[wheelIndex] << ": " << tyreState.coreTemp << "C / " << tyreState.pressure << " PSI" << std::endl;
        }
      }
    }
  }

  return 0;
}

void ClearConsole()
{

  HANDLE hConsole = GetStdHandle(STD_OUTPUT_HANDLE);
  if (hConsole == INVALID_HANDLE_VALUE) {

    return;
  }

  CONSOLE_SCREEN_BUFFER_INFO csbi;
  if (!GetConsoleScreenBufferInfo(hConsole, &csbi)) {

    return;
  }

  DWORD dwConSize = csbi.dwSize.X * csbi.dwSize.Y;
  COORD coordScreen = {0, 0};
  DWORD dwCharsWritten;
  if (!FillConsoleOutputCharacter(hConsole, (TCHAR)' ', dwConSize, coordScreen, &dwCharsWritten)) {
    return;
  }

  if (!GetConsoleScreenBufferInfo(hConsole, &csbi)) {
    return;
  }

  if (!FillConsoleOutputAttribute(hConsole, csbi.wAttributes, dwConSize, coordScreen, &dwCharsWritten)) {
    return;
  }

  SetConsoleCursorPosition(hConsole, coordScreen);
}

BOOL APIENTRY DllMain(HMODULE hModule, DWORD reason, LPVOID lpReserved)
{
  switch (reason) {
    case DLL_PROCESS_ATTACH:
      CreateThread(0, 0, (LPTHREAD_START_ROUTINE)MainThread, hModule, 0, 0);
      break;
  }

  return TRUE;
}