﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RaceRatingDetailCarControl

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RaceRatingDetailCarControl.WDG_RaceRatingDetailCarControl_C
// 0x0000 (0x02A0 - 0x02A0)
class UWDG_RaceRatingDetailCarControl_C final : public URatingDetailCarControl
{
public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RaceRatingDetailCarControl_C">();
	}
	static class UWDG_RaceRatingDetailCarControl_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RaceRatingDetailCarControl_C>();
	}
};
static_assert(alignof(UWDG_RaceRatingDetailCarControl_C) == 0x000008, "Wrong alignment on UWDG_RaceRatingDetailCarControl_C");
static_assert(sizeof(UWDG_RaceRatingDetailCarControl_C) == 0x0002A0, "Wrong size on UWDG_RaceRatingDetailCarControl_C");

}

