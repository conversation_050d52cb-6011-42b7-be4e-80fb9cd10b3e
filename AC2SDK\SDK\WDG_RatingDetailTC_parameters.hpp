﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RatingDetailTC

#include "Basic.hpp"


namespace SDK::Params
{

// Function WDG_RatingDetailTC.WDG_RatingDetailTC_C.ExecuteUbergraph_WDG_RatingDetailTC
// 0x0008 (0x0008 - 0x0000)
struct WDG_RatingDetailTC_C_ExecuteUbergraph_WDG_RatingDetailTC final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_CanPlay_Result;                           // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_CanPlay_Result_1;                         // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_RatingDetailTC_C_ExecuteUbergraph_WDG_RatingDetailTC) == 0x000004, "Wrong alignment on WDG_RatingDetailTC_C_ExecuteUbergraph_WDG_RatingDetailTC");
static_assert(sizeof(WDG_RatingDetailTC_C_ExecuteUbergraph_WDG_RatingDetailTC) == 0x000008, "Wrong size on WDG_RatingDetailTC_C_ExecuteUbergraph_WDG_RatingDetailTC");
static_assert(offsetof(WDG_RatingDetailTC_C_ExecuteUbergraph_WDG_RatingDetailTC, EntryPoint) == 0x000000, "Member 'WDG_RatingDetailTC_C_ExecuteUbergraph_WDG_RatingDetailTC::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_RatingDetailTC_C_ExecuteUbergraph_WDG_RatingDetailTC, CallFunc_CanPlay_Result) == 0x000004, "Member 'WDG_RatingDetailTC_C_ExecuteUbergraph_WDG_RatingDetailTC::CallFunc_CanPlay_Result' has a wrong offset!");
static_assert(offsetof(WDG_RatingDetailTC_C_ExecuteUbergraph_WDG_RatingDetailTC, CallFunc_CanPlay_Result_1) == 0x000005, "Member 'WDG_RatingDetailTC_C_ExecuteUbergraph_WDG_RatingDetailTC::CallFunc_CanPlay_Result_1' has a wrong offset!");

}

