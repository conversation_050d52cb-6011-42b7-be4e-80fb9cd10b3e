﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_OpenSeriesEventSelector

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "Engine_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_OpenSeriesEventSelector.WDG_OpenSeriesEventSelector_C
// 0x00C0 (0x0320 - 0x0260)
class UWDG_OpenSeriesEventSelector_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0260(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       pulsetext;                                         // 0x0268(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UScrollBox*                             scrollAvailable;                                   // 0x0270(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UScrollBox*                             scrollSelected;                                    // 0x0278(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UDataTable*                             PresetTable;                                       // 0x0280(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TMap<class FName, class FText>                CircuitList;                                       // 0x0288(0x0050)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<class UWDG_SeriesEventItem_C*>         SelectedCircuits;                                  // 0x02D8(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, ContainsInstancedReference)
	TMulticastInlineDelegate<void(TArray<class FName>& circuits)> OnPopulated;                       // 0x02E8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TArray<int32>                                 CircuitIndexes;                                    // 0x02F8(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	TArray<class UWDG_GenericBarItem_C*>          AvailableCircuits;                                 // 0x0308(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance, ContainsInstancedReference)
	class UAcGameInstance*                        GameInstance;                                      // 0x0318(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_OpenSeriesEventSelector(int32 EntryPoint);
	void Construct();
	void AddCircuit(class UWDG_GenericBarItem_C* Item);
	void DelCircuit(class UWDG_SeriesEventItem_C* Sender);
	void ReOrderCircuit(class UWDG_SeriesEventItem_C* Sender, EUINavigation Direction);
	void PopulateSelectedCircuits();
	class UWidget* DoCustomNavigation_0(EUINavigation Navigation_0);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_OpenSeriesEventSelector_C">();
	}
	static class UWDG_OpenSeriesEventSelector_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_OpenSeriesEventSelector_C>();
	}
};
static_assert(alignof(UWDG_OpenSeriesEventSelector_C) == 0x000008, "Wrong alignment on UWDG_OpenSeriesEventSelector_C");
static_assert(sizeof(UWDG_OpenSeriesEventSelector_C) == 0x000320, "Wrong size on UWDG_OpenSeriesEventSelector_C");
static_assert(offsetof(UWDG_OpenSeriesEventSelector_C, UberGraphFrame) == 0x000260, "Member 'UWDG_OpenSeriesEventSelector_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_OpenSeriesEventSelector_C, pulsetext) == 0x000268, "Member 'UWDG_OpenSeriesEventSelector_C::pulsetext' has a wrong offset!");
static_assert(offsetof(UWDG_OpenSeriesEventSelector_C, scrollAvailable) == 0x000270, "Member 'UWDG_OpenSeriesEventSelector_C::scrollAvailable' has a wrong offset!");
static_assert(offsetof(UWDG_OpenSeriesEventSelector_C, scrollSelected) == 0x000278, "Member 'UWDG_OpenSeriesEventSelector_C::scrollSelected' has a wrong offset!");
static_assert(offsetof(UWDG_OpenSeriesEventSelector_C, PresetTable) == 0x000280, "Member 'UWDG_OpenSeriesEventSelector_C::PresetTable' has a wrong offset!");
static_assert(offsetof(UWDG_OpenSeriesEventSelector_C, CircuitList) == 0x000288, "Member 'UWDG_OpenSeriesEventSelector_C::CircuitList' has a wrong offset!");
static_assert(offsetof(UWDG_OpenSeriesEventSelector_C, SelectedCircuits) == 0x0002D8, "Member 'UWDG_OpenSeriesEventSelector_C::SelectedCircuits' has a wrong offset!");
static_assert(offsetof(UWDG_OpenSeriesEventSelector_C, OnPopulated) == 0x0002E8, "Member 'UWDG_OpenSeriesEventSelector_C::OnPopulated' has a wrong offset!");
static_assert(offsetof(UWDG_OpenSeriesEventSelector_C, CircuitIndexes) == 0x0002F8, "Member 'UWDG_OpenSeriesEventSelector_C::CircuitIndexes' has a wrong offset!");
static_assert(offsetof(UWDG_OpenSeriesEventSelector_C, AvailableCircuits) == 0x000308, "Member 'UWDG_OpenSeriesEventSelector_C::AvailableCircuits' has a wrong offset!");
static_assert(offsetof(UWDG_OpenSeriesEventSelector_C, GameInstance) == 0x000318, "Member 'UWDG_OpenSeriesEventSelector_C::GameInstance' has a wrong offset!");

}

