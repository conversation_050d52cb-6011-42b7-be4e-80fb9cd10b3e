﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ScalableButtonSelection

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"


namespace SDK::Params
{

// Function WDG_ScalableButtonSelection.WDG_ScalableButtonSelection_C.ExecuteUbergraph_WDG_ScalableButtonSelection
// 0x0050 (0x0050 - 0x0000)
struct WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_1;              // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue;             // 0x0018(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue_1;           // 0x0020(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0028(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_29[0x7];                                       // 0x0029(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue_2;           // 0x0030(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_GetSize_ReturnValue;                      // 0x0038(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue_3;           // 0x0040(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_GetPosition_ReturnValue;                  // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection) == 0x000008, "Wrong alignment on WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection");
static_assert(sizeof(WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection) == 0x000050, "Wrong size on WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection");
static_assert(offsetof(WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection, EntryPoint) == 0x000000, "Member 'WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection, CallFunc_PlayAnimation_ReturnValue) == 0x000008, "Member 'WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection, CallFunc_PlayAnimation_ReturnValue_1) == 0x000010, "Member 'WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection::CallFunc_PlayAnimation_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection, CallFunc_SlotAsCanvasSlot_ReturnValue) == 0x000018, "Member 'WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection::CallFunc_SlotAsCanvasSlot_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection, CallFunc_SlotAsCanvasSlot_ReturnValue_1) == 0x000020, "Member 'WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection::CallFunc_SlotAsCanvasSlot_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection, CallFunc_IsValid_ReturnValue) == 0x000028, "Member 'WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection, CallFunc_SlotAsCanvasSlot_ReturnValue_2) == 0x000030, "Member 'WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection::CallFunc_SlotAsCanvasSlot_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection, CallFunc_GetSize_ReturnValue) == 0x000038, "Member 'WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection::CallFunc_GetSize_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection, CallFunc_SlotAsCanvasSlot_ReturnValue_3) == 0x000040, "Member 'WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection::CallFunc_SlotAsCanvasSlot_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection, CallFunc_GetPosition_ReturnValue) == 0x000048, "Member 'WDG_ScalableButtonSelection_C_ExecuteUbergraph_WDG_ScalableButtonSelection::CallFunc_GetPosition_ReturnValue' has a wrong offset!");

}

