﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomTileItem

#include "Basic.hpp"

#include "WDG_ShowroomTileItem_classes.hpp"
#include "WDG_ShowroomTileItem_parameters.hpp"


namespace SDK
{

// Function WDG_ShowroomTileItem.WDG_ShowroomTileItem_C.ExecuteUbergraph_WDG_ShowroomTileItem
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ShowroomTileItem_C::ExecuteUbergraph_WDG_ShowroomTileItem(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileItem_C", "ExecuteUbergraph_WDG_ShowroomTileItem");

	Params::WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ShowroomTileItem.WDG_ShowroomTileItem_C.UpdateModel
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FModelInfo&                ModelInfo_0                                            (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_ShowroomTileItem_C::UpdateModel(const struct FModelInfo& ModelInfo_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ShowroomTileItem_C", "UpdateModel");

	Params::WDG_ShowroomTileItem_C_UpdateModel Parms{};

	Parms.ModelInfo_0 = std::move(ModelInfo_0);

	UObject::ProcessEvent(Func, &Parms);
}

}

