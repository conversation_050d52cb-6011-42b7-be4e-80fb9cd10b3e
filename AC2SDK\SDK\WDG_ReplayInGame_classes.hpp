﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ReplayInGame

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ReplayInGame.WDG_ReplayInGame_C
// 0x0208 (0x08D8 - 0x06D0)
class UWDG_ReplayInGame_C final : public UReplayHUD
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x06D0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       TransitionFade;                                    // 0x06D8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       HidePanel;                                         // 0x06E0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UAcControllerIcon*                      AcControllerIcon_0;                                // 0x06E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UAcControllerIcon*                      AcControllerIcon_1;                                // 0x06F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UAcControllerIcon*                      AcControllerIcon_3;                                // 0x06F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UAcControllerIcon*                      AcControllerIcon_447;                              // 0x0700(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UAcControllerIcon*                      AcControllerIcon_451;                              // 0x0708(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UExtendedButton_C*                      btnJumpBack;                                       // 0x0710(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UExtendedButton_C*                      btnJumpForward;                                    // 0x0718(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UExtendedButton_C*                      btnNextCam;                                        // 0x0720(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UExtendedButton_C*                      btnNextCar;                                        // 0x0728(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UExtendedButton_C*                      btnPlayPause;                                      // 0x0730(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UExtendedButton_C*                      btnPrevCam;                                        // 0x0738(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UExtendedButton_C*                      btnPrevCar;                                        // 0x0740(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UExtendedButton_C*                      btnQuit;                                           // 0x0748(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UExtendedButton_C*                      btnReverse;                                        // 0x0750(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UExtendedButton_C*                      btnSaveHighlights;                                 // 0x0758(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UExtendedButton_C*                      btnSaveReplay;                                     // 0x0760(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UExtendedButton_C*                      btnSlowMotion;                                     // 0x0768(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UExtendedButton_C*                      btnSpeedDecrease;                                  // 0x0770(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UExtendedButton_C*                      btnSpeedIncrease;                                  // 0x0778(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_BasicMessagePopup_C*               confirmationModal;                                 // 0x0780(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_BasicMessagePopup_C*               fileOperationPopup;                                // 0x0788(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 HorizontalTimeCursor;                              // 0x0790(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ReplayButton_C*                    HUD;                                               // 0x0798(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_0;                                           // 0x07A0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_2;                                           // 0x07A8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_173;                                         // 0x07B0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgHistoryBackground;                              // 0x07B8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           MouseDetector;                                     // 0x07C0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           ReplayPanel;                                       // 0x07C8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ReplayButton_C*                    Results;                                           // 0x07D0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ReplayButton_C*                    Reverse;                                           // 0x07D8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ReplayButton_C*                    SaveHighlights_WDG_ReplayInGame_C;                 // 0x07E0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ReplayButton_C*                    SaveReplay_WDG_ReplayInGame_C;                     // 0x07E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_HudMfdRealtimePosition_C*          Standings;                                         // 0x07F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_1;                                       // 0x07F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_2;                                       // 0x0800(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_3;                                       // 0x0808(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_5;                                       // 0x0810(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_6;                                       // 0x0818(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_8;                                       // 0x0820(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_9;                                       // 0x0828(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_12;                                      // 0x0830(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_16;                                      // 0x0838(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_TimeTable_C*                       TimeTable;                                         // 0x0840(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UExtendedButton_C*                      togglehud;                                         // 0x0848(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UExtendedButton_C*                      toggleresults;                                     // 0x0850(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtBtnReverse;                                     // 0x0858(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtPlaybackSpeed;                                  // 0x0860(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 VerticalTimeCursor;                                // 0x0868(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_CurrentlyViewedHighlight_C*        viewedHighlight;                                   // 0x0870(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ReplayTimeLineMarker_C*            WDG_ReplayTimeLineMarker;                          // 0x0878(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	int32                                         TotalTimeS;                                        // 0x0880(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CurrentTimeS;                                      // 0x0884(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CurrentPause;                                      // 0x0888(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	uint8                                         Pad_889[0x3];                                      // 0x0889(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         SliderPoint_BP;                                    // 0x088C(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         TimeMultiplier_BP;                                 // 0x0890(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         StartTimeS;                                        // 0x0894(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FTimerHandle                           dialogTimer;                                       // 0x0898(0x0008)(Edit, BlueprintVisible, DisableEditOnInstance, NoDestructor, HasGetValueTypeHash)
	TArray<EControllerActionType>                 ActionsToShowMenuFor;                              // 0x08A0(0x0010)(Edit, BlueprintVisible)
	TArray<float>                                 ReplaySpeeds;                                      // 0x08B0(0x0010)(Edit, BlueprintVisible, DisableEditOnInstance)
	float                                         CurrentReplaySpeed;                                // 0x08C0(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_8C4[0x4];                                      // 0x08C4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<EControllerActionType>                 ActionsToPassToGame;                               // 0x08C8(0x0010)(Edit, BlueprintVisible)

public:
	void ExecuteUbergraph_WDG_ReplayInGame(int32 EntryPoint);
	void BndEvt__toggleresults_K2Node_ComponentBoundEvent_0_OnButtonPressed__DelegateSignature();
	void OnFadeTriggered(float Duration);
	void Tick(const struct FGeometry& MyGeometry, float InDeltaTime);
	void OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent);
	void CustomEvent();
	void CustomEvent_0();
	void BP_StartPage();
	void Destruct();
	void OnReplayHUDStarted();
	void Remove_PB();
	void BndEvt__TimeSlider_K2Node_ComponentBoundEvent_69_OnMouseCaptureEndEvent__DelegateSignature();
	void BndEvt__TimeSlider_K2Node_ComponentBoundEvent_21_OnMouseCaptureBeginEvent__DelegateSignature();
	void OnQuitRequested();
	void BndEvt__btnQuit_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__btnQuit_K2Node_ComponentBoundEvent_3_OnButtonPressed__DelegateSignature();
	void BndEvt__confirmationModal_K2Node_ComponentBoundEvent_1_OnNo__DelegateSignature();
	void BndEvt__confirmationModal_K2Node_ComponentBoundEvent_0_OnYes__DelegateSignature();
	void BndEvt__btnSpeedIncrease_K2Node_ComponentBoundEvent_17_OnButtonPressed__DelegateSignature();
	void BndEvt__btnSpeedDecrease_K2Node_ComponentBoundEvent_16_OnButtonPressed__DelegateSignature();
	void BndEvt__togglehud_K2Node_ComponentBoundEvent_0_OnButtonPressed__DelegateSignature();
	void BndEvt__btnReverse_K2Node_ComponentBoundEvent_14_OnButtonPressed__DelegateSignature();
	void BndEvt__btnPlayPause_K2Node_ComponentBoundEvent_2_OnButtonPressed__DelegateSignature();
	void BndEvt__btnPhoto_K2Node_ComponentBoundEvent_13_OnButtonClickedEvent__DelegateSignature();
	void BndEvt__btnSaveReplay_K2Node_ComponentBoundEvent_11_OnButtonPressed__DelegateSignature();
	void BndEvt__btnSaveHighlights_K2Node_ComponentBoundEvent_10_OnButtonPressed__DelegateSignature();
	void BndEvt__btnNextCam_K2Node_ComponentBoundEvent_9_OnButtonPressed__DelegateSignature();
	void BndEvt__btnPrevCam_K2Node_ComponentBoundEvent_8_OnButtonPressed__DelegateSignature();
	void BndEvt__btnPrevCar_K2Node_ComponentBoundEvent_7_OnButtonPressed__DelegateSignature();
	void BndEvt__btnNextCar_K2Node_ComponentBoundEvent_6_OnButtonPressed__DelegateSignature();
	void BndEvt__btnIncreaseSpeed_K2Node_ComponentBoundEvent_5_OnButtonPressed__DelegateSignature();
	void BndEvt__ExtendedButton_K2Node_ComponentBoundEvent_4_OnButtonPressed__DelegateSignature();
	void BndEvt__btnSlowMotion_K2Node_ComponentBoundEvent_3_OnButtonPressed__DelegateSignature();
	void BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_12_OnHide__DelegateSignature();
	void OnSaveHighlights(int32 ResultCode);
	void OnSaveReplay(int32 ResultCode);
	class FText GetCurrentTime();
	class FText Get_TotalTime_Text_0();
	class FText Get_TimeMultiplierText_Text_0();
	struct FEventReply OnPreviewKeyDown(const struct FGeometry& MyGeometry, const struct FKeyEvent& InKeyEvent);
	void HideSaveResultDialog();
	void ShowSaveResultDialog(const class FText& text);
	class FText GetPlaybackSpeedTxt();
	void GetCurrentSpeedIndex(int32* Index_0);
	bool ShouldAllowPanelUp();
	bool IsStandingsFocused();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ReplayInGame_C">();
	}
	static class UWDG_ReplayInGame_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ReplayInGame_C>();
	}
};
static_assert(alignof(UWDG_ReplayInGame_C) == 0x000008, "Wrong alignment on UWDG_ReplayInGame_C");
static_assert(sizeof(UWDG_ReplayInGame_C) == 0x0008D8, "Wrong size on UWDG_ReplayInGame_C");
static_assert(offsetof(UWDG_ReplayInGame_C, UberGraphFrame) == 0x0006D0, "Member 'UWDG_ReplayInGame_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, TransitionFade) == 0x0006D8, "Member 'UWDG_ReplayInGame_C::TransitionFade' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, HidePanel) == 0x0006E0, "Member 'UWDG_ReplayInGame_C::HidePanel' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, AcControllerIcon_0) == 0x0006E8, "Member 'UWDG_ReplayInGame_C::AcControllerIcon_0' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, AcControllerIcon_1) == 0x0006F0, "Member 'UWDG_ReplayInGame_C::AcControllerIcon_1' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, AcControllerIcon_3) == 0x0006F8, "Member 'UWDG_ReplayInGame_C::AcControllerIcon_3' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, AcControllerIcon_447) == 0x000700, "Member 'UWDG_ReplayInGame_C::AcControllerIcon_447' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, AcControllerIcon_451) == 0x000708, "Member 'UWDG_ReplayInGame_C::AcControllerIcon_451' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, btnJumpBack) == 0x000710, "Member 'UWDG_ReplayInGame_C::btnJumpBack' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, btnJumpForward) == 0x000718, "Member 'UWDG_ReplayInGame_C::btnJumpForward' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, btnNextCam) == 0x000720, "Member 'UWDG_ReplayInGame_C::btnNextCam' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, btnNextCar) == 0x000728, "Member 'UWDG_ReplayInGame_C::btnNextCar' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, btnPlayPause) == 0x000730, "Member 'UWDG_ReplayInGame_C::btnPlayPause' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, btnPrevCam) == 0x000738, "Member 'UWDG_ReplayInGame_C::btnPrevCam' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, btnPrevCar) == 0x000740, "Member 'UWDG_ReplayInGame_C::btnPrevCar' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, btnQuit) == 0x000748, "Member 'UWDG_ReplayInGame_C::btnQuit' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, btnReverse) == 0x000750, "Member 'UWDG_ReplayInGame_C::btnReverse' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, btnSaveHighlights) == 0x000758, "Member 'UWDG_ReplayInGame_C::btnSaveHighlights' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, btnSaveReplay) == 0x000760, "Member 'UWDG_ReplayInGame_C::btnSaveReplay' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, btnSlowMotion) == 0x000768, "Member 'UWDG_ReplayInGame_C::btnSlowMotion' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, btnSpeedDecrease) == 0x000770, "Member 'UWDG_ReplayInGame_C::btnSpeedDecrease' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, btnSpeedIncrease) == 0x000778, "Member 'UWDG_ReplayInGame_C::btnSpeedIncrease' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, confirmationModal) == 0x000780, "Member 'UWDG_ReplayInGame_C::confirmationModal' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, fileOperationPopup) == 0x000788, "Member 'UWDG_ReplayInGame_C::fileOperationPopup' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, HorizontalTimeCursor) == 0x000790, "Member 'UWDG_ReplayInGame_C::HorizontalTimeCursor' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, HUD) == 0x000798, "Member 'UWDG_ReplayInGame_C::HUD' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, Image_0) == 0x0007A0, "Member 'UWDG_ReplayInGame_C::Image_0' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, Image_2) == 0x0007A8, "Member 'UWDG_ReplayInGame_C::Image_2' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, Image_173) == 0x0007B0, "Member 'UWDG_ReplayInGame_C::Image_173' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, imgHistoryBackground) == 0x0007B8, "Member 'UWDG_ReplayInGame_C::imgHistoryBackground' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, MouseDetector) == 0x0007C0, "Member 'UWDG_ReplayInGame_C::MouseDetector' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, ReplayPanel) == 0x0007C8, "Member 'UWDG_ReplayInGame_C::ReplayPanel' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, Results) == 0x0007D0, "Member 'UWDG_ReplayInGame_C::Results' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, Reverse) == 0x0007D8, "Member 'UWDG_ReplayInGame_C::Reverse' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, SaveHighlights_WDG_ReplayInGame_C) == 0x0007E0, "Member 'UWDG_ReplayInGame_C::SaveHighlights_WDG_ReplayInGame_C' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, SaveReplay_WDG_ReplayInGame_C) == 0x0007E8, "Member 'UWDG_ReplayInGame_C::SaveReplay_WDG_ReplayInGame_C' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, Standings) == 0x0007F0, "Member 'UWDG_ReplayInGame_C::Standings' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, TextBlock_1) == 0x0007F8, "Member 'UWDG_ReplayInGame_C::TextBlock_1' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, TextBlock_2) == 0x000800, "Member 'UWDG_ReplayInGame_C::TextBlock_2' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, TextBlock_3) == 0x000808, "Member 'UWDG_ReplayInGame_C::TextBlock_3' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, TextBlock_5) == 0x000810, "Member 'UWDG_ReplayInGame_C::TextBlock_5' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, TextBlock_6) == 0x000818, "Member 'UWDG_ReplayInGame_C::TextBlock_6' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, TextBlock_8) == 0x000820, "Member 'UWDG_ReplayInGame_C::TextBlock_8' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, TextBlock_9) == 0x000828, "Member 'UWDG_ReplayInGame_C::TextBlock_9' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, TextBlock_12) == 0x000830, "Member 'UWDG_ReplayInGame_C::TextBlock_12' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, TextBlock_16) == 0x000838, "Member 'UWDG_ReplayInGame_C::TextBlock_16' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, TimeTable) == 0x000840, "Member 'UWDG_ReplayInGame_C::TimeTable' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, togglehud) == 0x000848, "Member 'UWDG_ReplayInGame_C::togglehud' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, toggleresults) == 0x000850, "Member 'UWDG_ReplayInGame_C::toggleresults' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, txtBtnReverse) == 0x000858, "Member 'UWDG_ReplayInGame_C::txtBtnReverse' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, txtPlaybackSpeed) == 0x000860, "Member 'UWDG_ReplayInGame_C::txtPlaybackSpeed' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, VerticalTimeCursor) == 0x000868, "Member 'UWDG_ReplayInGame_C::VerticalTimeCursor' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, viewedHighlight) == 0x000870, "Member 'UWDG_ReplayInGame_C::viewedHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, WDG_ReplayTimeLineMarker) == 0x000878, "Member 'UWDG_ReplayInGame_C::WDG_ReplayTimeLineMarker' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, TotalTimeS) == 0x000880, "Member 'UWDG_ReplayInGame_C::TotalTimeS' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, CurrentTimeS) == 0x000884, "Member 'UWDG_ReplayInGame_C::CurrentTimeS' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, CurrentPause) == 0x000888, "Member 'UWDG_ReplayInGame_C::CurrentPause' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, SliderPoint_BP) == 0x00088C, "Member 'UWDG_ReplayInGame_C::SliderPoint_BP' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, TimeMultiplier_BP) == 0x000890, "Member 'UWDG_ReplayInGame_C::TimeMultiplier_BP' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, StartTimeS) == 0x000894, "Member 'UWDG_ReplayInGame_C::StartTimeS' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, dialogTimer) == 0x000898, "Member 'UWDG_ReplayInGame_C::dialogTimer' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, ActionsToShowMenuFor) == 0x0008A0, "Member 'UWDG_ReplayInGame_C::ActionsToShowMenuFor' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, ReplaySpeeds) == 0x0008B0, "Member 'UWDG_ReplayInGame_C::ReplaySpeeds' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, CurrentReplaySpeed) == 0x0008C0, "Member 'UWDG_ReplayInGame_C::CurrentReplaySpeed' has a wrong offset!");
static_assert(offsetof(UWDG_ReplayInGame_C, ActionsToPassToGame) == 0x0008C8, "Member 'UWDG_ReplayInGame_C::ActionsToPassToGame' has a wrong offset!");

}

