﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TrackMap

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_TrackMap.WDG_TrackMap_C
// 0x0020 (0x0710 - 0x06F0)
class UWDG_TrackMap_C final : public UMapWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x06F0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UImage*                                 background;                                        // 0x06F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           Restrictor;                                        // 0x0700(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class URetainerBox*                           retainer;                                          // 0x0708(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_TrackMap(int32 EntryPoint);
	void MapModeChanged();
	bool IsWidgetDefinitionEnabled(class UAcGameInstance* GameInstance, const struct FHUDOptions& HUDOptions);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_TrackMap_C">();
	}
	static class UWDG_TrackMap_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_TrackMap_C>();
	}
};
static_assert(alignof(UWDG_TrackMap_C) == 0x000008, "Wrong alignment on UWDG_TrackMap_C");
static_assert(sizeof(UWDG_TrackMap_C) == 0x000710, "Wrong size on UWDG_TrackMap_C");
static_assert(offsetof(UWDG_TrackMap_C, UberGraphFrame) == 0x0006F0, "Member 'UWDG_TrackMap_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_TrackMap_C, background) == 0x0006F8, "Member 'UWDG_TrackMap_C::background' has a wrong offset!");
static_assert(offsetof(UWDG_TrackMap_C, Restrictor) == 0x000700, "Member 'UWDG_TrackMap_C::Restrictor' has a wrong offset!");
static_assert(offsetof(UWDG_TrackMap_C, retainer) == 0x000708, "Member 'UWDG_TrackMap_C::retainer' has a wrong offset!");

}

