﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SeasonSelector

#include "Basic.hpp"

#include "WDG_SeasonSelector_classes.hpp"
#include "WDG_SeasonSelector_parameters.hpp"


namespace SDK
{

// Function WDG_SeasonSelector.WDG_SeasonSelector_C.ExecuteUbergraph_WDG_SeasonSelector
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SeasonSelector_C::ExecuteUbergraph_WDG_SeasonSelector(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonSelector_C", "ExecuteUbergraph_WDG_SeasonSelector");

	Params::WDG_SeasonSelector_C_ExecuteUbergraph_WDG_SeasonSelector Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SeasonSelector.WDG_SeasonSelector_C.OnSeasonChanged_Event_0
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// ESeasonType                             new_season                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SeasonSelector_C::OnSeasonChanged_Event_0(ESeasonType new_season)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonSelector_C", "OnSeasonChanged_Event_0");

	Params::WDG_SeasonSelector_C_OnSeasonChanged_Event_0 Parms{};

	Parms.new_season = new_season;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SeasonSelector.WDG_SeasonSelector_C.ForceRestyle
// (BlueprintCallable, BlueprintEvent)

void UWDG_SeasonSelector_C::ForceRestyle()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonSelector_C", "ForceRestyle");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SeasonSelector.WDG_SeasonSelector_C.Toggled
// (BlueprintCallable, BlueprintEvent)

void UWDG_SeasonSelector_C::Toggled()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonSelector_C", "Toggled");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SeasonSelector.WDG_SeasonSelector_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_SeasonSelector_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonSelector_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SeasonSelector.WDG_SeasonSelector_C.OnRemovedFromFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_SeasonSelector_C::OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonSelector_C", "OnRemovedFromFocusPath");

	Params::WDG_SeasonSelector_C_OnRemovedFromFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SeasonSelector.WDG_SeasonSelector_C.BP_MouseOver
// (Event, Public, BlueprintEvent)

void UWDG_SeasonSelector_C::BP_MouseOver()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonSelector_C", "BP_MouseOver");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SeasonSelector.WDG_SeasonSelector_C.BP_MouseLeave
// (Event, Public, BlueprintEvent)

void UWDG_SeasonSelector_C::BP_MouseLeave()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonSelector_C", "BP_MouseLeave");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SeasonSelector.WDG_SeasonSelector_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SeasonSelector_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonSelector_C", "PreConstruct");

	Params::WDG_SeasonSelector_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SeasonSelector.WDG_SeasonSelector_C.BP_UpdateActivity
// (Event, Public, BlueprintEvent)
// Parameters:
// bool                                    Active                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SeasonSelector_C::BP_UpdateActivity(bool Active)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonSelector_C", "BP_UpdateActivity");

	Params::WDG_SeasonSelector_C_BP_UpdateActivity Parms{};

	Parms.Active = Active;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SeasonSelector.WDG_SeasonSelector_C.SetTitleText
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const class FText&                      text                                                   (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_SeasonSelector_C::SetTitleText(const class FText& text)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonSelector_C", "SetTitleText");

	Params::WDG_SeasonSelector_C_SetTitleText Parms{};

	Parms.text = std::move(text);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SeasonSelector.WDG_SeasonSelector_C.SetToggled
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    IsToggledOn                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
// bool                                    ForceTextColor                                         (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
// bool                                    TriggerEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SeasonSelector_C::SetToggled(bool IsToggledOn, bool ForceTextColor, bool TriggerEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonSelector_C", "SetToggled");

	Params::WDG_SeasonSelector_C_SetToggled Parms{};

	Parms.IsToggledOn = IsToggledOn;
	Parms.ForceTextColor = ForceTextColor;
	Parms.TriggerEvent = TriggerEvent;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SeasonSelector.WDG_SeasonSelector_C.SetNormalColor
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_SeasonSelector_C::SetNormalColor()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonSelector_C", "SetNormalColor");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SeasonSelector.WDG_SeasonSelector_C.PulseAnimation
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_SeasonSelector_C::PulseAnimation()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonSelector_C", "PulseAnimation");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SeasonSelector.WDG_SeasonSelector_C.SetSlantImageWidth
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UImage*                           SlantImage                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SeasonSelector_C::SetSlantImageWidth(class UImage* SlantImage)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonSelector_C", "SetSlantImageWidth");

	Params::WDG_SeasonSelector_C_SetSlantImageWidth Parms{};

	Parms.SlantImage = SlantImage;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SeasonSelector.WDG_SeasonSelector_C.ResetAnimation
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_SeasonSelector_C::ResetAnimation()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonSelector_C", "ResetAnimation");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SeasonSelector.WDG_SeasonSelector_C.SetSeason
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// ESeasonType                             Season                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SeasonSelector_C::SetSeason(ESeasonType Season)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SeasonSelector_C", "SetSeason");

	Params::WDG_SeasonSelector_C_SetSeason Parms{};

	Parms.Season = Season;

	UObject::ProcessEvent(Func, &Parms);
}

}

