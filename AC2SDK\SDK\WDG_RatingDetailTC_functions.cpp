﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RatingDetailTC

#include "Basic.hpp"

#include "WDG_RatingDetailTC_classes.hpp"
#include "WDG_RatingDetailTC_parameters.hpp"


namespace SDK
{

// Function WDG_RatingDetailTC.WDG_RatingDetailTC_C.ExecuteUbergraph_WDG_RatingDetailTC
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_RatingDetailTC_C::ExecuteUbergraph_WDG_RatingDetailTC(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_RatingDetailTC_C", "ExecuteUbergraph_WDG_RatingDetailTC");

	Params::WDG_RatingDetailTC_C_ExecuteUbergraph_WDG_RatingDetailTC Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_RatingDetailTC.WDG_RatingDetailTC_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_RatingDetailTC_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_RatingDetailTC_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}

}

