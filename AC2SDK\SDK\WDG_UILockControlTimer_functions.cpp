﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_UILockControlTimer

#include "Basic.hpp"

#include "WDG_UILockControlTimer_classes.hpp"
#include "WDG_UILockControlTimer_parameters.hpp"


namespace SDK
{

// Function WDG_UILockControlTimer.WDG_UILockControlTimer_C.ExecuteUbergraph_WDG_UILockControlTimer
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_UILockControlTimer_C::ExecuteUbergraph_WDG_UILockControlTimer(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_UILockControlTimer_C", "ExecuteUbergraph_WDG_UILockControlTimer");

	Params::WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_UILockControlTimer.WDG_UILockControlTimer_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_UILockControlTimer_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_UILockControlTimer_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_UILockControlTimer.WDG_UILockControlTimer_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_UILockControlTimer_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_UILockControlTimer_C", "PreConstruct");

	Params::WDG_UILockControlTimer_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_UILockControlTimer.WDG_UILockControlTimer_C.Destruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_UILockControlTimer_C::Destruct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_UILockControlTimer_C", "Destruct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_UILockControlTimer.WDG_UILockControlTimer_C.OnEverySecond
// (BlueprintCallable, BlueprintEvent)

void UWDG_UILockControlTimer_C::OnEverySecond()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_UILockControlTimer_C", "OnEverySecond");

	UObject::ProcessEvent(Func, nullptr);
}

}

