﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PhysicsInfoPanel

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_PhysicsInfoPanel.WDG_PhysicsInfoPanel_C
// 0x0050 (0x0698 - 0x0648)
class UWDG_PhysicsInfoPanel_C final : public UPhysicsInfoPanel
{
public:
	class UImage*                                 background;                                        // 0x0648(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             DiscWearSlot;                                      // 0x0650(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_3;                                           // 0x0658(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_4;                                           // 0x0660(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             NamedSlot_1;                                       // 0x0668(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             OMItitle;                                          // 0x0670(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             PadWearSlot;                                       // 0x0678(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             PsiTitle;                                          // 0x0680(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             TitleSlot;                                         // 0x0688(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             WearSlot;                                          // 0x0690(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_PhysicsInfoPanel_C">();
	}
	static class UWDG_PhysicsInfoPanel_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_PhysicsInfoPanel_C>();
	}
};
static_assert(alignof(UWDG_PhysicsInfoPanel_C) == 0x000008, "Wrong alignment on UWDG_PhysicsInfoPanel_C");
static_assert(sizeof(UWDG_PhysicsInfoPanel_C) == 0x000698, "Wrong size on UWDG_PhysicsInfoPanel_C");
static_assert(offsetof(UWDG_PhysicsInfoPanel_C, background) == 0x000648, "Member 'UWDG_PhysicsInfoPanel_C::background' has a wrong offset!");
static_assert(offsetof(UWDG_PhysicsInfoPanel_C, DiscWearSlot) == 0x000650, "Member 'UWDG_PhysicsInfoPanel_C::DiscWearSlot' has a wrong offset!");
static_assert(offsetof(UWDG_PhysicsInfoPanel_C, Image_3) == 0x000658, "Member 'UWDG_PhysicsInfoPanel_C::Image_3' has a wrong offset!");
static_assert(offsetof(UWDG_PhysicsInfoPanel_C, Image_4) == 0x000660, "Member 'UWDG_PhysicsInfoPanel_C::Image_4' has a wrong offset!");
static_assert(offsetof(UWDG_PhysicsInfoPanel_C, NamedSlot_1) == 0x000668, "Member 'UWDG_PhysicsInfoPanel_C::NamedSlot_1' has a wrong offset!");
static_assert(offsetof(UWDG_PhysicsInfoPanel_C, OMItitle) == 0x000670, "Member 'UWDG_PhysicsInfoPanel_C::OMItitle' has a wrong offset!");
static_assert(offsetof(UWDG_PhysicsInfoPanel_C, PadWearSlot) == 0x000678, "Member 'UWDG_PhysicsInfoPanel_C::PadWearSlot' has a wrong offset!");
static_assert(offsetof(UWDG_PhysicsInfoPanel_C, PsiTitle) == 0x000680, "Member 'UWDG_PhysicsInfoPanel_C::PsiTitle' has a wrong offset!");
static_assert(offsetof(UWDG_PhysicsInfoPanel_C, TitleSlot) == 0x000688, "Member 'UWDG_PhysicsInfoPanel_C::TitleSlot' has a wrong offset!");
static_assert(offsetof(UWDG_PhysicsInfoPanel_C, WearSlot) == 0x000690, "Member 'UWDG_PhysicsInfoPanel_C::WearSlot' has a wrong offset!");

}

