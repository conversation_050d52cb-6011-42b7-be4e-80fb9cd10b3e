﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SpecialEventsPanel

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SpecialEventsPanel.WDG_SpecialEventsPanel_C
// 0x0070 (0x0650 - 0x05E0)
class UWDG_SpecialEventsPanel_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       OpacityAnim;                                       // 0x05E8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       ScaleAnim;                                         // 0x05F0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 HoverImageBox;                                     // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             NamedSlot_0;                                       // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             NamedSlot_1;                                       // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 NormalImageBox;                                    // 0x0610(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           OrangeText;                                        // 0x0618(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           PanelToHide;                                       // 0x0620(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             SpecialEvents_lbl;                                 // 0x0628(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             TitleSlot;                                         // 0x0630(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TotEvents_lbl;                                     // 0x0638(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             UnlockedEvents_lbl;                                // 0x0640(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           WhiteText;                                         // 0x0648(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_SpecialEventsPanel(int32 EntryPoint);
	void PreConstruct(bool IsDesignTime);
	void BP_MouseLeave();
	void BP_MouseOver();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SpecialEventsPanel_C">();
	}
	static class UWDG_SpecialEventsPanel_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SpecialEventsPanel_C>();
	}
};
static_assert(alignof(UWDG_SpecialEventsPanel_C) == 0x000008, "Wrong alignment on UWDG_SpecialEventsPanel_C");
static_assert(sizeof(UWDG_SpecialEventsPanel_C) == 0x000650, "Wrong size on UWDG_SpecialEventsPanel_C");
static_assert(offsetof(UWDG_SpecialEventsPanel_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_SpecialEventsPanel_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPanel_C, OpacityAnim) == 0x0005E8, "Member 'UWDG_SpecialEventsPanel_C::OpacityAnim' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPanel_C, ScaleAnim) == 0x0005F0, "Member 'UWDG_SpecialEventsPanel_C::ScaleAnim' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPanel_C, HoverImageBox) == 0x0005F8, "Member 'UWDG_SpecialEventsPanel_C::HoverImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPanel_C, NamedSlot_0) == 0x000600, "Member 'UWDG_SpecialEventsPanel_C::NamedSlot_0' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPanel_C, NamedSlot_1) == 0x000608, "Member 'UWDG_SpecialEventsPanel_C::NamedSlot_1' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPanel_C, NormalImageBox) == 0x000610, "Member 'UWDG_SpecialEventsPanel_C::NormalImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPanel_C, OrangeText) == 0x000618, "Member 'UWDG_SpecialEventsPanel_C::OrangeText' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPanel_C, PanelToHide) == 0x000620, "Member 'UWDG_SpecialEventsPanel_C::PanelToHide' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPanel_C, SpecialEvents_lbl) == 0x000628, "Member 'UWDG_SpecialEventsPanel_C::SpecialEvents_lbl' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPanel_C, TitleSlot) == 0x000630, "Member 'UWDG_SpecialEventsPanel_C::TitleSlot' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPanel_C, TotEvents_lbl) == 0x000638, "Member 'UWDG_SpecialEventsPanel_C::TotEvents_lbl' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPanel_C, UnlockedEvents_lbl) == 0x000640, "Member 'UWDG_SpecialEventsPanel_C::UnlockedEvents_lbl' has a wrong offset!");
static_assert(offsetof(UWDG_SpecialEventsPanel_C, WhiteText) == 0x000648, "Member 'UWDG_SpecialEventsPanel_C::WhiteText' has a wrong offset!");

}

