﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_NewsItem

#include "Basic.hpp"


namespace SDK::Params
{

// Function WDG_NewsItem.WDG_NewsItem_C.ExecuteUbergraph_WDG_NewsItem
// 0x0018 (0x0018 - 0x0000)
struct WDG_NewsItem_C_ExecuteUbergraph_WDG_NewsItem final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_GenericBarItem_C*                  K2Node_ComponentBoundEvent_Sender;                 // 0x0008(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldD<PERSON>, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NotEqual_StrStr_ReturnValue;              // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_NotEqual_StrStr_ReturnValue_1;            // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0012(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_NewsItem_C_ExecuteUbergraph_WDG_NewsItem) == 0x000008, "Wrong alignment on WDG_NewsItem_C_ExecuteUbergraph_WDG_NewsItem");
static_assert(sizeof(WDG_NewsItem_C_ExecuteUbergraph_WDG_NewsItem) == 0x000018, "Wrong size on WDG_NewsItem_C_ExecuteUbergraph_WDG_NewsItem");
static_assert(offsetof(WDG_NewsItem_C_ExecuteUbergraph_WDG_NewsItem, EntryPoint) == 0x000000, "Member 'WDG_NewsItem_C_ExecuteUbergraph_WDG_NewsItem::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_NewsItem_C_ExecuteUbergraph_WDG_NewsItem, K2Node_ComponentBoundEvent_Sender) == 0x000008, "Member 'WDG_NewsItem_C_ExecuteUbergraph_WDG_NewsItem::K2Node_ComponentBoundEvent_Sender' has a wrong offset!");
static_assert(offsetof(WDG_NewsItem_C_ExecuteUbergraph_WDG_NewsItem, CallFunc_NotEqual_StrStr_ReturnValue) == 0x000010, "Member 'WDG_NewsItem_C_ExecuteUbergraph_WDG_NewsItem::CallFunc_NotEqual_StrStr_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_NewsItem_C_ExecuteUbergraph_WDG_NewsItem, CallFunc_NotEqual_StrStr_ReturnValue_1) == 0x000011, "Member 'WDG_NewsItem_C_ExecuteUbergraph_WDG_NewsItem::CallFunc_NotEqual_StrStr_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_NewsItem_C_ExecuteUbergraph_WDG_NewsItem, K2Node_Event_IsDesignTime) == 0x000012, "Member 'WDG_NewsItem_C_ExecuteUbergraph_WDG_NewsItem::K2Node_Event_IsDesignTime' has a wrong offset!");

// Function WDG_NewsItem.WDG_NewsItem_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_NewsItem_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_NewsItem_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_NewsItem_C_PreConstruct");
static_assert(sizeof(WDG_NewsItem_C_PreConstruct) == 0x000001, "Wrong size on WDG_NewsItem_C_PreConstruct");
static_assert(offsetof(WDG_NewsItem_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_NewsItem_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WDG_NewsItem.WDG_NewsItem_C.BndEvt__btnReadMore_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_NewsItem_C_BndEvt__btnReadMore_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature final
{
public:
	class UWDG_GenericBarItem_C*                  Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_NewsItem_C_BndEvt__btnReadMore_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature) == 0x000008, "Wrong alignment on WDG_NewsItem_C_BndEvt__btnReadMore_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature");
static_assert(sizeof(WDG_NewsItem_C_BndEvt__btnReadMore_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature) == 0x000008, "Wrong size on WDG_NewsItem_C_BndEvt__btnReadMore_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature");
static_assert(offsetof(WDG_NewsItem_C_BndEvt__btnReadMore_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature, Sender) == 0x000000, "Member 'WDG_NewsItem_C_BndEvt__btnReadMore_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature::Sender' has a wrong offset!");

}

