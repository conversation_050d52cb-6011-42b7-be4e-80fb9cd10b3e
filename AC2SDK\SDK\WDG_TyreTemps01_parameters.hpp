﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TyreTemps01

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "Slate_structs.hpp"
#include "CoreUObject_structs.hpp"


namespace SDK::Params
{

// Function WDG_TyreTemps01.WDG_TyreTemps01_C.ExecuteUbergraph_WDG_TyreTemps01
// 0x0568 (0x0568 - 0x0000)
struct WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01 final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcGameInstance*                        K2Node_Event_gameInstance;                         // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AAcRaceGameMode*                        K2Node_Event_raceGameMode;                         // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class ACarAvatar*                             K2Node_Event_carAvatar;                            // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FHUDOptions                            K2Node_Event_hudOptions;                           // 0x0020(0x00C0)(ConstParm, NoDestructor)
	struct FAnchors                               K2Node_MakeStruct_Anchors;                         // 0x00E0(0x0010)(NoDestructor)
	bool                                          CallFunc_isOnline_ReturnValue;                     // 0x00F0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_F1[0x3];                                       // 0x00F1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Multiply_FloatFloat_ReturnValue;          // 0x00F4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x00F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x00F9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue_1;          // 0x00FA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_FB[0x5];                                       // 0x00FB(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	class UPanelWidget*                           CallFunc_GetParent_ReturnValue;                    // 0x0100(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue;             // 0x0108(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UScaleBox*                              K2Node_DynamicCast_AsScale_Box;                    // 0x0110(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0118(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0119(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_11A[0x2];                                      // 0x011A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              CallFunc_GetPosition_ReturnValue;                  // 0x011C(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakVector2D_X;                          // 0x0124(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakVector2D_Y;                          // 0x0128(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue;                 // 0x012C(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FAnchors                               K2Node_MakeStruct_Anchors_1;                       // 0x0134(0x0010)(NoDestructor)
	uint8                                         Pad_144[0x4];                                      // 0x0144(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UPanelWidget*                           CallFunc_GetParent_ReturnValue_1;                  // 0x0148(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FRaceHUDState                          K2Node_Event_state;                                // 0x0150(0x03E0)(ConstParm)
	class UCanvasPanelSlot*                       CallFunc_SlotAsCanvasSlot_ReturnValue_1;           // 0x0530(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x0538(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0539(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_53A[0x2];                                      // 0x053A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FVector2D                              CallFunc_GetPosition_ReturnValue_1;                // 0x053C(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakVector2D_X_1;                        // 0x0544(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_BreakVector2D_Y_1;                        // 0x0548(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FVector2D                              CallFunc_MakeVector2D_ReturnValue_1;               // 0x054C(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_554[0x4];                                      // 0x0554(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UScaleBox*                              K2Node_DynamicCast_AsScale_Box_1;                  // 0x0558(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x0560(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
};
static_assert(alignof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01) == 0x000008, "Wrong alignment on WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01");
static_assert(sizeof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01) == 0x000568, "Wrong size on WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, EntryPoint) == 0x000000, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, K2Node_Event_gameInstance) == 0x000008, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::K2Node_Event_gameInstance' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, K2Node_Event_raceGameMode) == 0x000010, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::K2Node_Event_raceGameMode' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, K2Node_Event_carAvatar) == 0x000018, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::K2Node_Event_carAvatar' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, K2Node_Event_hudOptions) == 0x000020, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::K2Node_Event_hudOptions' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, K2Node_MakeStruct_Anchors) == 0x0000E0, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::K2Node_MakeStruct_Anchors' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, CallFunc_isOnline_ReturnValue) == 0x0000F0, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::CallFunc_isOnline_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, CallFunc_Multiply_FloatFloat_ReturnValue) == 0x0000F4, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::CallFunc_Multiply_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x0000F8, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, CallFunc_Greater_IntInt_ReturnValue) == 0x0000F9, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, CallFunc_EqualEqual_IntInt_ReturnValue_1) == 0x0000FA, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::CallFunc_EqualEqual_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, CallFunc_GetParent_ReturnValue) == 0x000100, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::CallFunc_GetParent_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, CallFunc_SlotAsCanvasSlot_ReturnValue) == 0x000108, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::CallFunc_SlotAsCanvasSlot_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, K2Node_DynamicCast_AsScale_Box) == 0x000110, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::K2Node_DynamicCast_AsScale_Box' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, K2Node_DynamicCast_bSuccess) == 0x000118, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, CallFunc_IsValid_ReturnValue) == 0x000119, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, CallFunc_GetPosition_ReturnValue) == 0x00011C, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::CallFunc_GetPosition_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, CallFunc_BreakVector2D_X) == 0x000124, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::CallFunc_BreakVector2D_X' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, CallFunc_BreakVector2D_Y) == 0x000128, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::CallFunc_BreakVector2D_Y' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, CallFunc_MakeVector2D_ReturnValue) == 0x00012C, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::CallFunc_MakeVector2D_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, K2Node_MakeStruct_Anchors_1) == 0x000134, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::K2Node_MakeStruct_Anchors_1' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, CallFunc_GetParent_ReturnValue_1) == 0x000148, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::CallFunc_GetParent_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, K2Node_Event_state) == 0x000150, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::K2Node_Event_state' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, CallFunc_SlotAsCanvasSlot_ReturnValue_1) == 0x000530, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::CallFunc_SlotAsCanvasSlot_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, CallFunc_IsValid_ReturnValue_1) == 0x000538, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, CallFunc_BooleanAND_ReturnValue) == 0x000539, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, CallFunc_GetPosition_ReturnValue_1) == 0x00053C, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::CallFunc_GetPosition_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, CallFunc_BreakVector2D_X_1) == 0x000544, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::CallFunc_BreakVector2D_X_1' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, CallFunc_BreakVector2D_Y_1) == 0x000548, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::CallFunc_BreakVector2D_Y_1' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, CallFunc_MakeVector2D_ReturnValue_1) == 0x00054C, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::CallFunc_MakeVector2D_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, K2Node_DynamicCast_AsScale_Box_1) == 0x000558, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::K2Node_DynamicCast_AsScale_Box_1' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01, K2Node_DynamicCast_bSuccess_1) == 0x000560, "Member 'WDG_TyreTemps01_C_ExecuteUbergraph_WDG_TyreTemps01::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");

// Function WDG_TyreTemps01.WDG_TyreTemps01_C.OnStartWidget
// 0x00D8 (0x00D8 - 0x0000)
struct WDG_TyreTemps01_C_OnStartWidget final
{
public:
	class UAcGameInstance*                        GameInstance;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AAcRaceGameMode*                        raceGameMode;                                      // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class ACarAvatar*                             CarAvatar;                                         // 0x0010(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FHUDOptions                            HUDOptions;                                        // 0x0018(0x00C0)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)
};
static_assert(alignof(WDG_TyreTemps01_C_OnStartWidget) == 0x000008, "Wrong alignment on WDG_TyreTemps01_C_OnStartWidget");
static_assert(sizeof(WDG_TyreTemps01_C_OnStartWidget) == 0x0000D8, "Wrong size on WDG_TyreTemps01_C_OnStartWidget");
static_assert(offsetof(WDG_TyreTemps01_C_OnStartWidget, GameInstance) == 0x000000, "Member 'WDG_TyreTemps01_C_OnStartWidget::GameInstance' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_OnStartWidget, raceGameMode) == 0x000008, "Member 'WDG_TyreTemps01_C_OnStartWidget::raceGameMode' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_OnStartWidget, CarAvatar) == 0x000010, "Member 'WDG_TyreTemps01_C_OnStartWidget::CarAvatar' has a wrong offset!");
static_assert(offsetof(WDG_TyreTemps01_C_OnStartWidget, HUDOptions) == 0x000018, "Member 'WDG_TyreTemps01_C_OnStartWidget::HUDOptions' has a wrong offset!");

// Function WDG_TyreTemps01.WDG_TyreTemps01_C.OnHudTick
// 0x03E0 (0x03E0 - 0x0000)
struct WDG_TyreTemps01_C_OnHudTick final
{
public:
	struct FRaceHUDState                          State;                                             // 0x0000(0x03E0)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_TyreTemps01_C_OnHudTick) == 0x000008, "Wrong alignment on WDG_TyreTemps01_C_OnHudTick");
static_assert(sizeof(WDG_TyreTemps01_C_OnHudTick) == 0x0003E0, "Wrong size on WDG_TyreTemps01_C_OnHudTick");
static_assert(offsetof(WDG_TyreTemps01_C_OnHudTick, State) == 0x000000, "Member 'WDG_TyreTemps01_C_OnHudTick::State' has a wrong offset!");

}

