﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SinglePlayerLowerPanel

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SinglePlayerLowerPanel.WDG_SinglePlayerLowerPanel_C
// 0x00B0 (0x0690 - 0x05E0)
class UWDG_SinglePlayerLowerPanel_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       Scale;                                             // 0x05E8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       Hover;                                             // 0x05F0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 BaseRed01;                                         // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 BaseRed02;                                         // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 BottomHighlight;                                   // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           CanvasPanel_3;                                     // 0x0610(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           CanvasPanel_4;                                     // 0x0618(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 HoverImageBox;                                     // 0x0620(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             IconSlot;                                          // 0x0628(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_4;                                           // 0x0630(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_6;                                           // 0x0638(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_9;                                           // 0x0640(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgDivisor;                                        // 0x0648(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgEventColor;                                     // 0x0650(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 LeftHighlight;                                     // 0x0658(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             LevelSlot;                                         // 0x0660(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 NormalImageBox;                                    // 0x0668(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             PercentSlot;                                       // 0x0670(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 RightHighlight;                                    // 0x0678(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             TitleSlot;                                         // 0x0680(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 TopHighlight;                                      // 0x0688(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_SinglePlayerLowerPanel(int32 EntryPoint);
	void BP_MouseLeave();
	void BP_MouseOver();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SinglePlayerLowerPanel_C">();
	}
	static class UWDG_SinglePlayerLowerPanel_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SinglePlayerLowerPanel_C>();
	}
};
static_assert(alignof(UWDG_SinglePlayerLowerPanel_C) == 0x000008, "Wrong alignment on UWDG_SinglePlayerLowerPanel_C");
static_assert(sizeof(UWDG_SinglePlayerLowerPanel_C) == 0x000690, "Wrong size on UWDG_SinglePlayerLowerPanel_C");
static_assert(offsetof(UWDG_SinglePlayerLowerPanel_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_SinglePlayerLowerPanel_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerLowerPanel_C, Scale) == 0x0005E8, "Member 'UWDG_SinglePlayerLowerPanel_C::Scale' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerLowerPanel_C, Hover) == 0x0005F0, "Member 'UWDG_SinglePlayerLowerPanel_C::Hover' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerLowerPanel_C, BaseRed01) == 0x0005F8, "Member 'UWDG_SinglePlayerLowerPanel_C::BaseRed01' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerLowerPanel_C, BaseRed02) == 0x000600, "Member 'UWDG_SinglePlayerLowerPanel_C::BaseRed02' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerLowerPanel_C, BottomHighlight) == 0x000608, "Member 'UWDG_SinglePlayerLowerPanel_C::BottomHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerLowerPanel_C, CanvasPanel_3) == 0x000610, "Member 'UWDG_SinglePlayerLowerPanel_C::CanvasPanel_3' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerLowerPanel_C, CanvasPanel_4) == 0x000618, "Member 'UWDG_SinglePlayerLowerPanel_C::CanvasPanel_4' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerLowerPanel_C, HoverImageBox) == 0x000620, "Member 'UWDG_SinglePlayerLowerPanel_C::HoverImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerLowerPanel_C, IconSlot) == 0x000628, "Member 'UWDG_SinglePlayerLowerPanel_C::IconSlot' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerLowerPanel_C, Image_4) == 0x000630, "Member 'UWDG_SinglePlayerLowerPanel_C::Image_4' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerLowerPanel_C, Image_6) == 0x000638, "Member 'UWDG_SinglePlayerLowerPanel_C::Image_6' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerLowerPanel_C, Image_9) == 0x000640, "Member 'UWDG_SinglePlayerLowerPanel_C::Image_9' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerLowerPanel_C, imgDivisor) == 0x000648, "Member 'UWDG_SinglePlayerLowerPanel_C::imgDivisor' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerLowerPanel_C, imgEventColor) == 0x000650, "Member 'UWDG_SinglePlayerLowerPanel_C::imgEventColor' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerLowerPanel_C, LeftHighlight) == 0x000658, "Member 'UWDG_SinglePlayerLowerPanel_C::LeftHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerLowerPanel_C, LevelSlot) == 0x000660, "Member 'UWDG_SinglePlayerLowerPanel_C::LevelSlot' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerLowerPanel_C, NormalImageBox) == 0x000668, "Member 'UWDG_SinglePlayerLowerPanel_C::NormalImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerLowerPanel_C, PercentSlot) == 0x000670, "Member 'UWDG_SinglePlayerLowerPanel_C::PercentSlot' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerLowerPanel_C, RightHighlight) == 0x000678, "Member 'UWDG_SinglePlayerLowerPanel_C::RightHighlight' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerLowerPanel_C, TitleSlot) == 0x000680, "Member 'UWDG_SinglePlayerLowerPanel_C::TitleSlot' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerLowerPanel_C, TopHighlight) == 0x000688, "Member 'UWDG_SinglePlayerLowerPanel_C::TopHighlight' has a wrong offset!");

}

