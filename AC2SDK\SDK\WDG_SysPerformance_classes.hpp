﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SysPerformance

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SysPerformance.WDG_SysPerformance_C
// 0x0000 (0x0688 - 0x0688)
class UWDG_SysPerformance_C final : public USysPerformanceWidget
{
public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SysPerformance_C">();
	}
	static class UWDG_SysPerformance_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SysPerformance_C>();
	}
};
static_assert(alignof(UWDG_SysPerformance_C) == 0x000008, "Wrong alignment on UWDG_SysPerformance_C");
static_assert(sizeof(UWDG_SysPerformance_C) == 0x000688, "Wrong size on UWDG_SysPerformance_C");

}

