﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_UISessionTimer

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"
#include "CoreUObject_structs.hpp"
#include "UMG_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_UISessionTimer.WDG_UISessionTimer_C
// 0x0188 (0x07E0 - 0x0658)
class UWDG_UISessionTimer_C final : public UAcRaceWidgetBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0658(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UBorder*                                background;                                        // 0x0660(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                borderInfo;                                        // 0x0668(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgFlag;                                           // 0x0670(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtInfo;                                           // 0x0678(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtRemainingTime;                                  // 0x0680(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           Wrapper;                                           // 0x0688(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class AAcRaceGameMode*                        raceGameMode;                                      // 0x0690(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         TimeRemaining;                                     // 0x0698(0x0004)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          IsOfflineSession;                                  // 0x069C(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	bool                                          UpdateVisibility;                                  // 0x069D(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	uint8                                         Pad_69E[0x2];                                      // 0x069E(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	TMulticastInlineDelegate<void(const struct FLinearColor& flagColor, const struct FSlateBrush& FlagTexture, ESessionStatus SessionStatus)> OnFlagStatusUpdate; // 0x06A0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	struct FAnchorData                            Position;                                          // 0x06B0(0x0028)(Edit, BlueprintVisible, NoDestructor, ExposeOnSpawn)
	bool                                          OverridePosition;                                  // 0x06D8(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_6D9[0x3];                                      // 0x06D9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           CurrentFlagColor;                                  // 0x06DC(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	ERaceSessionPhase                             CurrentSessionPhase;                               // 0x06EC(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6ED[0x3];                                      // 0x06ED(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateColor                            OverTimeColor;                                     // 0x06F0(0x0028)(Edit, BlueprintVisible)
	int32                                         TimeToChangeColor;                                 // 0x0718(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          IsLockControlsTimer;                               // 0x071C(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_71D[0x3];                                      // 0x071D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           BackgroundColor;                                   // 0x0720(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, ExposeOnSpawn, HasGetValueTypeHash)
	ESessionStatus                                Session_Status;                                    // 0x0730(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_731[0x7];                                      // 0x0731(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateBrush                            CurrentFlagBrush;                                  // 0x0738(0x0088)(Edit, BlueprintVisible, ExposeOnSpawn)
	struct FLinearColor                           InfoTextColor;                                     // 0x07C0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           BorderInfoColor;                                   // 0x07D0(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_UISessionTimer(int32 EntryPoint);
	void OnEverySecond();
	void Construct();
	void Destruct();
	void PreConstruct(bool IsDesignTime);
	void IsTimeRemainingVisible(bool* IsVisible_0);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_UISessionTimer_C">();
	}
	static class UWDG_UISessionTimer_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_UISessionTimer_C>();
	}
};
static_assert(alignof(UWDG_UISessionTimer_C) == 0x000008, "Wrong alignment on UWDG_UISessionTimer_C");
static_assert(sizeof(UWDG_UISessionTimer_C) == 0x0007E0, "Wrong size on UWDG_UISessionTimer_C");
static_assert(offsetof(UWDG_UISessionTimer_C, UberGraphFrame) == 0x000658, "Member 'UWDG_UISessionTimer_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_UISessionTimer_C, background) == 0x000660, "Member 'UWDG_UISessionTimer_C::background' has a wrong offset!");
static_assert(offsetof(UWDG_UISessionTimer_C, borderInfo) == 0x000668, "Member 'UWDG_UISessionTimer_C::borderInfo' has a wrong offset!");
static_assert(offsetof(UWDG_UISessionTimer_C, imgFlag) == 0x000670, "Member 'UWDG_UISessionTimer_C::imgFlag' has a wrong offset!");
static_assert(offsetof(UWDG_UISessionTimer_C, txtInfo) == 0x000678, "Member 'UWDG_UISessionTimer_C::txtInfo' has a wrong offset!");
static_assert(offsetof(UWDG_UISessionTimer_C, txtRemainingTime) == 0x000680, "Member 'UWDG_UISessionTimer_C::txtRemainingTime' has a wrong offset!");
static_assert(offsetof(UWDG_UISessionTimer_C, Wrapper) == 0x000688, "Member 'UWDG_UISessionTimer_C::Wrapper' has a wrong offset!");
static_assert(offsetof(UWDG_UISessionTimer_C, raceGameMode) == 0x000690, "Member 'UWDG_UISessionTimer_C::raceGameMode' has a wrong offset!");
static_assert(offsetof(UWDG_UISessionTimer_C, TimeRemaining) == 0x000698, "Member 'UWDG_UISessionTimer_C::TimeRemaining' has a wrong offset!");
static_assert(offsetof(UWDG_UISessionTimer_C, IsOfflineSession) == 0x00069C, "Member 'UWDG_UISessionTimer_C::IsOfflineSession' has a wrong offset!");
static_assert(offsetof(UWDG_UISessionTimer_C, UpdateVisibility) == 0x00069D, "Member 'UWDG_UISessionTimer_C::UpdateVisibility' has a wrong offset!");
static_assert(offsetof(UWDG_UISessionTimer_C, OnFlagStatusUpdate) == 0x0006A0, "Member 'UWDG_UISessionTimer_C::OnFlagStatusUpdate' has a wrong offset!");
static_assert(offsetof(UWDG_UISessionTimer_C, Position) == 0x0006B0, "Member 'UWDG_UISessionTimer_C::Position' has a wrong offset!");
static_assert(offsetof(UWDG_UISessionTimer_C, OverridePosition) == 0x0006D8, "Member 'UWDG_UISessionTimer_C::OverridePosition' has a wrong offset!");
static_assert(offsetof(UWDG_UISessionTimer_C, CurrentFlagColor) == 0x0006DC, "Member 'UWDG_UISessionTimer_C::CurrentFlagColor' has a wrong offset!");
static_assert(offsetof(UWDG_UISessionTimer_C, CurrentSessionPhase) == 0x0006EC, "Member 'UWDG_UISessionTimer_C::CurrentSessionPhase' has a wrong offset!");
static_assert(offsetof(UWDG_UISessionTimer_C, OverTimeColor) == 0x0006F0, "Member 'UWDG_UISessionTimer_C::OverTimeColor' has a wrong offset!");
static_assert(offsetof(UWDG_UISessionTimer_C, TimeToChangeColor) == 0x000718, "Member 'UWDG_UISessionTimer_C::TimeToChangeColor' has a wrong offset!");
static_assert(offsetof(UWDG_UISessionTimer_C, IsLockControlsTimer) == 0x00071C, "Member 'UWDG_UISessionTimer_C::IsLockControlsTimer' has a wrong offset!");
static_assert(offsetof(UWDG_UISessionTimer_C, BackgroundColor) == 0x000720, "Member 'UWDG_UISessionTimer_C::BackgroundColor' has a wrong offset!");
static_assert(offsetof(UWDG_UISessionTimer_C, Session_Status) == 0x000730, "Member 'UWDG_UISessionTimer_C::Session_Status' has a wrong offset!");
static_assert(offsetof(UWDG_UISessionTimer_C, CurrentFlagBrush) == 0x000738, "Member 'UWDG_UISessionTimer_C::CurrentFlagBrush' has a wrong offset!");
static_assert(offsetof(UWDG_UISessionTimer_C, InfoTextColor) == 0x0007C0, "Member 'UWDG_UISessionTimer_C::InfoTextColor' has a wrong offset!");
static_assert(offsetof(UWDG_UISessionTimer_C, BorderInfoColor) == 0x0007D0, "Member 'UWDG_UISessionTimer_C::BorderInfoColor' has a wrong offset!");

}

