﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ReplayInGame

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "UMG_structs.hpp"


namespace SDK::Params
{

// Function WDG_ReplayInGame.WDG_ReplayInGame_C.ExecuteUbergraph_WDG_ReplayInGame
// 0x01A8 (0x01A8 - 0x0000)
struct WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0004(0x0001)(ZeroConstructor, Is<PERSON>lainOldData, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	uint8                                         Pad_5[0x3];                                        // 0x0005(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x000C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D[0x3];                                        // 0x000D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_1;                   // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_2;            // 0x001C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_HasFocusedDescendants_ReturnValue;        // 0x001D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_3;            // 0x001E(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x001F(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	int32                                         Temp_int_Loop_Counter_Variable_1;                  // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x0024(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_4;            // 0x0028(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_29[0x3];                                       // 0x0029(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate;              // 0x002C(0x0010)(ZeroConstructor, NoDestructor)
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate_1;            // 0x003C(0x0010)(ZeroConstructor, NoDestructor)
	int32                                         K2Node_Event_resultCode_1;                         // 0x004C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_Event_resultCode;                           // 0x0050(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchInteger_CmpSuccess;                   // 0x0054(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          K2Node_SwitchInteger_CmpSuccess_1;                 // 0x0055(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_ReverseClick_ReturnValue;                 // 0x0056(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_57[0x1];                                       // 0x0057(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateColor                            K2Node_MakeStruct_SlateColor;                      // 0x0058(0x0028)()
	bool                                          CallFunc_IsReverse_ReturnValue;                    // 0x0080(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_81[0x7];                                       // 0x0081(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_1;                    // 0x0088(0x0028)(UObjectWrapper)
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue;              // 0x00B0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class APlayerCarController*                   K2Node_DynamicCast_AsPlayer_Car_Controller;        // 0x00B8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x00C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_C1[0x3];                                       // 0x00C1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_LastIndex_ReturnValue;              // 0x00C4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Find_ReturnValue;                   // 0x00C8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetCurrentSpeedIndex_Index;               // 0x00CC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x00D0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_D1[0x3];                                       // 0x00D1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x00D4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_2;                 // 0x00D8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x00DC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_DD[0x3];                                       // 0x00DD(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable;                                 // 0x00E0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Array_Get_Item;                           // 0x00E4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_5;            // 0x00E8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsVisible_ReturnValue;                    // 0x00E9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_1;                // 0x00EA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_HasHighlights_ReturnValue;                // 0x00EB(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_6;            // 0x00EC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsSavingAllowed_ReturnValue;              // 0x00ED(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x00EE(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	EControllerActionType                         CallFunc_Array_Get_Item_1;                         // 0x00EF(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x00F0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FName                                   CallFunc_GetControllerActionTypeAsName_ReturnValue; // 0x00F4(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_1;                // 0x00FC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_7;            // 0x00FD(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_HasFocusedDescendants_ReturnValue_1;      // 0x00FE(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	EControllerActionType                         CallFunc_Array_Get_Item_2;                         // 0x00FF(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue_2;                // 0x0100(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_101[0x3];                                      // 0x0101(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class FName                                   CallFunc_GetControllerActionTypeAsName_ReturnValue_1; // 0x0104(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x010C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_2;                // 0x0110(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0111(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x0112(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_GetIsEnabled_ReturnValue;                 // 0x0113(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_2;                 // 0x0114(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_8;            // 0x0115(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue_3;                 // 0x0116(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_117[0x1];                                      // 0x0117(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	struct FFocusEvent                            K2Node_Event_InFocusEvent;                         // 0x0118(0x0008)(NoDestructor)
	struct FGeometry                              K2Node_Event_MyGeometry;                           // 0x0120(0x0038)(IsPlainOldData, NoDestructor)
	float                                         K2Node_Event_InDeltaTime;                          // 0x0158(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_HasAnyUserFocus_ReturnValue;              // 0x015C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_HasFocusedDescendants_ReturnValue_2;      // 0x015D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x015E(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_15F[0x1];                                      // 0x015F(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         K2Node_Event_duration;                             // 0x0160(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsHMDEnabled_ReturnValue;                 // 0x0164(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_165[0x3];                                      // 0x0165(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0168(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_FloatFloat_ReturnValue;              // 0x0170(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_4;                 // 0x0171(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsVisible_ReturnValue_1;                  // 0x0172(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_9;            // 0x0173(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_174[0x4];                                      // 0x0174(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue_1;            // 0x0178(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class APlayerCarController*                   K2Node_DynamicCast_AsPlayer_Car_Controller_1;      // 0x0180(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess_1;                     // 0x0188(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_189[0x3];                                      // 0x0189(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable_1;                               // 0x018C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         CallFunc_Array_Get_Item_3;                         // 0x0190(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsHMDEnabled_ReturnValue_1;               // 0x0194(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_3;                // 0x0195(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_HasReverse_ReturnValue;                   // 0x0196(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsSavingAllowed_ReturnValue_1;            // 0x0197(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	class UReplayManager*                         CallFunc_GetReplayManager_ReturnValue;             // 0x0198(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_hasSessionResult_ReturnValue;             // 0x01A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame) == 0x000008, "Wrong alignment on WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame");
static_assert(sizeof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame) == 0x0001A8, "Wrong size on WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, EntryPoint) == 0x000000, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_MakeLiteralByte_ReturnValue) == 0x000004, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, Temp_int_Array_Index_Variable) == 0x000008, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x00000C, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, Temp_int_Loop_Counter_Variable) == 0x000010, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_Add_IntInt_ReturnValue) == 0x000014, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, Temp_int_Array_Index_Variable_1) == 0x000018, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::Temp_int_Array_Index_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_MakeLiteralByte_ReturnValue_2) == 0x00001C, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_MakeLiteralByte_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_HasFocusedDescendants_ReturnValue) == 0x00001D, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_HasFocusedDescendants_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_MakeLiteralByte_ReturnValue_3) == 0x00001E, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_MakeLiteralByte_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_Not_PreBool_ReturnValue) == 0x00001F, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, Temp_int_Loop_Counter_Variable_1) == 0x000020, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::Temp_int_Loop_Counter_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_Add_IntInt_ReturnValue_1) == 0x000024, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_MakeLiteralByte_ReturnValue_4) == 0x000028, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_MakeLiteralByte_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, K2Node_CreateDelegate_OutputDelegate) == 0x00002C, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, K2Node_CreateDelegate_OutputDelegate_1) == 0x00003C, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::K2Node_CreateDelegate_OutputDelegate_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, K2Node_Event_resultCode_1) == 0x00004C, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::K2Node_Event_resultCode_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, K2Node_Event_resultCode) == 0x000050, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::K2Node_Event_resultCode' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, K2Node_SwitchInteger_CmpSuccess) == 0x000054, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::K2Node_SwitchInteger_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, K2Node_SwitchInteger_CmpSuccess_1) == 0x000055, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::K2Node_SwitchInteger_CmpSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_ReverseClick_ReturnValue) == 0x000056, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_ReverseClick_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, K2Node_MakeStruct_SlateColor) == 0x000058, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::K2Node_MakeStruct_SlateColor' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_IsReverse_ReturnValue) == 0x000080, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_IsReverse_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, K2Node_MakeStruct_SlateColor_1) == 0x000088, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::K2Node_MakeStruct_SlateColor_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_GetOwningPlayer_ReturnValue) == 0x0000B0, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_GetOwningPlayer_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, K2Node_DynamicCast_AsPlayer_Car_Controller) == 0x0000B8, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::K2Node_DynamicCast_AsPlayer_Car_Controller' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, K2Node_DynamicCast_bSuccess) == 0x0000C0, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_Array_LastIndex_ReturnValue) == 0x0000C4, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_Array_LastIndex_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_Array_Find_ReturnValue) == 0x0000C8, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_Array_Find_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_GetCurrentSpeedIndex_Index) == 0x0000CC, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_GetCurrentSpeedIndex_Index' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_Less_IntInt_ReturnValue) == 0x0000D0, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_Subtract_IntInt_ReturnValue) == 0x0000D4, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_Add_IntInt_ReturnValue_2) == 0x0000D8, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_Add_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_Greater_IntInt_ReturnValue) == 0x0000DC, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, Temp_int_Variable) == 0x0000E0, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_Array_Get_Item) == 0x0000E4, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_MakeLiteralByte_ReturnValue_5) == 0x0000E8, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_MakeLiteralByte_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_IsVisible_ReturnValue) == 0x0000E9, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_IsVisible_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_Not_PreBool_ReturnValue_1) == 0x0000EA, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_Not_PreBool_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_HasHighlights_ReturnValue) == 0x0000EB, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_HasHighlights_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_MakeLiteralByte_ReturnValue_6) == 0x0000EC, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_MakeLiteralByte_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_IsSavingAllowed_ReturnValue) == 0x0000ED, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_IsSavingAllowed_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_BooleanAND_ReturnValue) == 0x0000EE, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_Array_Get_Item_1) == 0x0000EF, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_Array_Length_ReturnValue) == 0x0000F0, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_GetControllerActionTypeAsName_ReturnValue) == 0x0000F4, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_GetControllerActionTypeAsName_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_Less_IntInt_ReturnValue_1) == 0x0000FC, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_Less_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_MakeLiteralByte_ReturnValue_7) == 0x0000FD, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_MakeLiteralByte_ReturnValue_7' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_HasFocusedDescendants_ReturnValue_1) == 0x0000FE, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_HasFocusedDescendants_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_Array_Get_Item_2) == 0x0000FF, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_Array_Get_Item_2' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_Not_PreBool_ReturnValue_2) == 0x000100, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_Not_PreBool_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_GetControllerActionTypeAsName_ReturnValue_1) == 0x000104, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_GetControllerActionTypeAsName_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_Array_Length_ReturnValue_1) == 0x00010C, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_Less_IntInt_ReturnValue_2) == 0x000110, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_Less_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_IsValid_ReturnValue) == 0x000111, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_BooleanAND_ReturnValue_1) == 0x000112, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_GetIsEnabled_ReturnValue) == 0x000113, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_GetIsEnabled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_BooleanAND_ReturnValue_2) == 0x000114, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_BooleanAND_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_MakeLiteralByte_ReturnValue_8) == 0x000115, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_MakeLiteralByte_ReturnValue_8' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_BooleanAND_ReturnValue_3) == 0x000116, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_BooleanAND_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, K2Node_Event_InFocusEvent) == 0x000118, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::K2Node_Event_InFocusEvent' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, K2Node_Event_MyGeometry) == 0x000120, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::K2Node_Event_MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, K2Node_Event_InDeltaTime) == 0x000158, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::K2Node_Event_InDeltaTime' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_HasAnyUserFocus_ReturnValue) == 0x00015C, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_HasAnyUserFocus_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_HasFocusedDescendants_ReturnValue_2) == 0x00015D, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_HasFocusedDescendants_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_BooleanOR_ReturnValue) == 0x00015E, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, K2Node_Event_duration) == 0x000160, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::K2Node_Event_duration' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_IsHMDEnabled_ReturnValue) == 0x000164, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_IsHMDEnabled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_PlayAnimation_ReturnValue) == 0x000168, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_Less_FloatFloat_ReturnValue) == 0x000170, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_Less_FloatFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_BooleanAND_ReturnValue_4) == 0x000171, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_BooleanAND_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_IsVisible_ReturnValue_1) == 0x000172, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_IsVisible_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_MakeLiteralByte_ReturnValue_9) == 0x000173, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_MakeLiteralByte_ReturnValue_9' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_GetOwningPlayer_ReturnValue_1) == 0x000178, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_GetOwningPlayer_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, K2Node_DynamicCast_AsPlayer_Car_Controller_1) == 0x000180, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::K2Node_DynamicCast_AsPlayer_Car_Controller_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, K2Node_DynamicCast_bSuccess_1) == 0x000188, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::K2Node_DynamicCast_bSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, Temp_int_Variable_1) == 0x00018C, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::Temp_int_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_Array_Get_Item_3) == 0x000190, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_Array_Get_Item_3' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_IsHMDEnabled_ReturnValue_1) == 0x000194, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_IsHMDEnabled_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_Not_PreBool_ReturnValue_3) == 0x000195, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_Not_PreBool_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_HasReverse_ReturnValue) == 0x000196, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_HasReverse_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_IsSavingAllowed_ReturnValue_1) == 0x000197, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_IsSavingAllowed_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_GetReplayManager_ReturnValue) == 0x000198, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_GetReplayManager_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame, CallFunc_hasSessionResult_ReturnValue) == 0x0001A0, "Member 'WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame::CallFunc_hasSessionResult_ReturnValue' has a wrong offset!");

// Function WDG_ReplayInGame.WDG_ReplayInGame_C.OnFadeTriggered
// 0x0004 (0x0004 - 0x0000)
struct WDG_ReplayInGame_C_OnFadeTriggered final
{
public:
	float                                         Duration;                                          // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ReplayInGame_C_OnFadeTriggered) == 0x000004, "Wrong alignment on WDG_ReplayInGame_C_OnFadeTriggered");
static_assert(sizeof(WDG_ReplayInGame_C_OnFadeTriggered) == 0x000004, "Wrong size on WDG_ReplayInGame_C_OnFadeTriggered");
static_assert(offsetof(WDG_ReplayInGame_C_OnFadeTriggered, Duration) == 0x000000, "Member 'WDG_ReplayInGame_C_OnFadeTriggered::Duration' has a wrong offset!");

// Function WDG_ReplayInGame.WDG_ReplayInGame_C.Tick
// 0x003C (0x003C - 0x0000)
struct WDG_ReplayInGame_C_Tick final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	float                                         InDeltaTime;                                       // 0x0038(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ReplayInGame_C_Tick) == 0x000004, "Wrong alignment on WDG_ReplayInGame_C_Tick");
static_assert(sizeof(WDG_ReplayInGame_C_Tick) == 0x00003C, "Wrong size on WDG_ReplayInGame_C_Tick");
static_assert(offsetof(WDG_ReplayInGame_C_Tick, MyGeometry) == 0x000000, "Member 'WDG_ReplayInGame_C_Tick::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_Tick, InDeltaTime) == 0x000038, "Member 'WDG_ReplayInGame_C_Tick::InDeltaTime' has a wrong offset!");

// Function WDG_ReplayInGame.WDG_ReplayInGame_C.OnRemovedFromFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_ReplayInGame_C_OnRemovedFromFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_ReplayInGame_C_OnRemovedFromFocusPath) == 0x000004, "Wrong alignment on WDG_ReplayInGame_C_OnRemovedFromFocusPath");
static_assert(sizeof(WDG_ReplayInGame_C_OnRemovedFromFocusPath) == 0x000008, "Wrong size on WDG_ReplayInGame_C_OnRemovedFromFocusPath");
static_assert(offsetof(WDG_ReplayInGame_C_OnRemovedFromFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_ReplayInGame_C_OnRemovedFromFocusPath::InFocusEvent' has a wrong offset!");

// Function WDG_ReplayInGame.WDG_ReplayInGame_C.OnSaveHighlights
// 0x0004 (0x0004 - 0x0000)
struct WDG_ReplayInGame_C_OnSaveHighlights final
{
public:
	int32                                         ResultCode;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ReplayInGame_C_OnSaveHighlights) == 0x000004, "Wrong alignment on WDG_ReplayInGame_C_OnSaveHighlights");
static_assert(sizeof(WDG_ReplayInGame_C_OnSaveHighlights) == 0x000004, "Wrong size on WDG_ReplayInGame_C_OnSaveHighlights");
static_assert(offsetof(WDG_ReplayInGame_C_OnSaveHighlights, ResultCode) == 0x000000, "Member 'WDG_ReplayInGame_C_OnSaveHighlights::ResultCode' has a wrong offset!");

// Function WDG_ReplayInGame.WDG_ReplayInGame_C.OnSaveReplay
// 0x0004 (0x0004 - 0x0000)
struct WDG_ReplayInGame_C_OnSaveReplay final
{
public:
	int32                                         ResultCode;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ReplayInGame_C_OnSaveReplay) == 0x000004, "Wrong alignment on WDG_ReplayInGame_C_OnSaveReplay");
static_assert(sizeof(WDG_ReplayInGame_C_OnSaveReplay) == 0x000004, "Wrong size on WDG_ReplayInGame_C_OnSaveReplay");
static_assert(offsetof(WDG_ReplayInGame_C_OnSaveReplay, ResultCode) == 0x000000, "Member 'WDG_ReplayInGame_C_OnSaveReplay::ResultCode' has a wrong offset!");

// Function WDG_ReplayInGame.WDG_ReplayInGame_C.GetCurrentTime
// 0x00C8 (0x00C8 - 0x0000)
struct WDG_ReplayInGame_C_GetCurrentTime final
{
public:
	class FText                                   ReturnValue;                                       // 0x0000(0x0018)(Parm, OutParm, ReturnParm)
	int32                                         CallFunc_Divide_IntInt_ReturnValue;                // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Percent_IntInt_ReturnValue;               // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData;              // 0x0020(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData_1;            // 0x0060(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	TArray<struct FFormatArgumentData>            K2Node_MakeArray_Array;                            // 0x00A0(0x0010)(ReferenceParm)
	class FText                                   CallFunc_Format_ReturnValue;                       // 0x00B0(0x0018)()
};
static_assert(alignof(WDG_ReplayInGame_C_GetCurrentTime) == 0x000008, "Wrong alignment on WDG_ReplayInGame_C_GetCurrentTime");
static_assert(sizeof(WDG_ReplayInGame_C_GetCurrentTime) == 0x0000C8, "Wrong size on WDG_ReplayInGame_C_GetCurrentTime");
static_assert(offsetof(WDG_ReplayInGame_C_GetCurrentTime, ReturnValue) == 0x000000, "Member 'WDG_ReplayInGame_C_GetCurrentTime::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_GetCurrentTime, CallFunc_Divide_IntInt_ReturnValue) == 0x000018, "Member 'WDG_ReplayInGame_C_GetCurrentTime::CallFunc_Divide_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_GetCurrentTime, CallFunc_Percent_IntInt_ReturnValue) == 0x00001C, "Member 'WDG_ReplayInGame_C_GetCurrentTime::CallFunc_Percent_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_GetCurrentTime, K2Node_MakeStruct_FormatArgumentData) == 0x000020, "Member 'WDG_ReplayInGame_C_GetCurrentTime::K2Node_MakeStruct_FormatArgumentData' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_GetCurrentTime, K2Node_MakeStruct_FormatArgumentData_1) == 0x000060, "Member 'WDG_ReplayInGame_C_GetCurrentTime::K2Node_MakeStruct_FormatArgumentData_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_GetCurrentTime, K2Node_MakeArray_Array) == 0x0000A0, "Member 'WDG_ReplayInGame_C_GetCurrentTime::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_GetCurrentTime, CallFunc_Format_ReturnValue) == 0x0000B0, "Member 'WDG_ReplayInGame_C_GetCurrentTime::CallFunc_Format_ReturnValue' has a wrong offset!");

// Function WDG_ReplayInGame.WDG_ReplayInGame_C.Get_TotalTime_Text_0
// 0x0018 (0x0018 - 0x0000)
struct WDG_ReplayInGame_C_Get_TotalTime_Text_0 final
{
public:
	class FText                                   ReturnValue;                                       // 0x0000(0x0018)(Parm, OutParm, ReturnParm)
};
static_assert(alignof(WDG_ReplayInGame_C_Get_TotalTime_Text_0) == 0x000008, "Wrong alignment on WDG_ReplayInGame_C_Get_TotalTime_Text_0");
static_assert(sizeof(WDG_ReplayInGame_C_Get_TotalTime_Text_0) == 0x000018, "Wrong size on WDG_ReplayInGame_C_Get_TotalTime_Text_0");
static_assert(offsetof(WDG_ReplayInGame_C_Get_TotalTime_Text_0, ReturnValue) == 0x000000, "Member 'WDG_ReplayInGame_C_Get_TotalTime_Text_0::ReturnValue' has a wrong offset!");

// Function WDG_ReplayInGame.WDG_ReplayInGame_C.Get_TimeMultiplierText_Text_0
// 0x0018 (0x0018 - 0x0000)
struct WDG_ReplayInGame_C_Get_TimeMultiplierText_Text_0 final
{
public:
	class FText                                   ReturnValue;                                       // 0x0000(0x0018)(Parm, OutParm, ReturnParm)
};
static_assert(alignof(WDG_ReplayInGame_C_Get_TimeMultiplierText_Text_0) == 0x000008, "Wrong alignment on WDG_ReplayInGame_C_Get_TimeMultiplierText_Text_0");
static_assert(sizeof(WDG_ReplayInGame_C_Get_TimeMultiplierText_Text_0) == 0x000018, "Wrong size on WDG_ReplayInGame_C_Get_TimeMultiplierText_Text_0");
static_assert(offsetof(WDG_ReplayInGame_C_Get_TimeMultiplierText_Text_0, ReturnValue) == 0x000000, "Member 'WDG_ReplayInGame_C_Get_TimeMultiplierText_Text_0::ReturnValue' has a wrong offset!");

// Function WDG_ReplayInGame.WDG_ReplayInGame_C.OnPreviewKeyDown
// 0x0288 (0x0288 - 0x0000)
struct WDG_ReplayInGame_C_OnPreviewKeyDown final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FKeyEvent                              InKeyEvent;                                        // 0x0038(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm)
	struct FEventReply                            ReturnValue;                                       // 0x0070(0x00B8)(Parm, OutParm, ReturnParm)
	struct FInputAction                           CallFunc_GetInputActionFromKeyEvent_ReturnValue;   // 0x0128(0x0040)()
	bool                                          Temp_bool_True_if_break_was_hit_Variable;          // 0x0168(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0169(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_16A[0x2];                                      // 0x016A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Array_Index_Variable;                     // 0x016C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0170(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0174(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsStandingsFocused_ReturnValue;           // 0x0178(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_HasFocusedDescendants_ReturnValue;        // 0x0179(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_1;                // 0x017A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_2;                // 0x017B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x017C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x017D(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_17E[0x2];                                      // 0x017E(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue;              // 0x0180(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<struct FInputActionKeyMapping>         CallFunc_GetKeysForActions_ReturnValue;            // 0x0188(0x0010)(ReferenceParm)
	struct FInputActionKeyMapping                 CallFunc_Array_Get_Item;                           // 0x0198(0x0028)()
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x01C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue_3;                // 0x01C1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue;            // 0x01C2(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_BoolBool_ReturnValue;          // 0x01C3(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_2;                 // 0x01C4(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1C5[0x3];                                      // 0x01C5(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x01C8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_BooleanAND_ReturnValue_3;                 // 0x01CC(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x01CD(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_4;                 // 0x01CE(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_5;                 // 0x01CF(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	struct FEventReply                            CallFunc_Unhandled_ReturnValue;                    // 0x01D0(0x00B8)()
};
static_assert(alignof(WDG_ReplayInGame_C_OnPreviewKeyDown) == 0x000008, "Wrong alignment on WDG_ReplayInGame_C_OnPreviewKeyDown");
static_assert(sizeof(WDG_ReplayInGame_C_OnPreviewKeyDown) == 0x000288, "Wrong size on WDG_ReplayInGame_C_OnPreviewKeyDown");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, MyGeometry) == 0x000000, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, InKeyEvent) == 0x000038, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::InKeyEvent' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, ReturnValue) == 0x000070, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, CallFunc_GetInputActionFromKeyEvent_ReturnValue) == 0x000128, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::CallFunc_GetInputActionFromKeyEvent_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, Temp_bool_True_if_break_was_hit_Variable) == 0x000168, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::Temp_bool_True_if_break_was_hit_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, CallFunc_Not_PreBool_ReturnValue) == 0x000169, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, Temp_int_Array_Index_Variable) == 0x00016C, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, Temp_int_Loop_Counter_Variable) == 0x000170, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, CallFunc_Add_IntInt_ReturnValue) == 0x000174, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, CallFunc_IsStandingsFocused_ReturnValue) == 0x000178, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::CallFunc_IsStandingsFocused_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, CallFunc_HasFocusedDescendants_ReturnValue) == 0x000179, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::CallFunc_HasFocusedDescendants_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, CallFunc_Not_PreBool_ReturnValue_1) == 0x00017A, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::CallFunc_Not_PreBool_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, CallFunc_Not_PreBool_ReturnValue_2) == 0x00017B, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::CallFunc_Not_PreBool_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, CallFunc_IsValid_ReturnValue) == 0x00017C, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, CallFunc_BooleanAND_ReturnValue) == 0x00017D, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, CallFunc_GetOwningPlayer_ReturnValue) == 0x000180, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::CallFunc_GetOwningPlayer_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, CallFunc_GetKeysForActions_ReturnValue) == 0x000188, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::CallFunc_GetKeysForActions_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, CallFunc_Array_Get_Item) == 0x000198, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, CallFunc_BooleanAND_ReturnValue_1) == 0x0001C0, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, CallFunc_Not_PreBool_ReturnValue_3) == 0x0001C1, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::CallFunc_Not_PreBool_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, CallFunc_EqualEqual_KeyKey_ReturnValue) == 0x0001C2, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::CallFunc_EqualEqual_KeyKey_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, CallFunc_EqualEqual_BoolBool_ReturnValue) == 0x0001C3, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::CallFunc_EqualEqual_BoolBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, CallFunc_BooleanAND_ReturnValue_2) == 0x0001C4, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::CallFunc_BooleanAND_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, CallFunc_Array_Length_ReturnValue) == 0x0001C8, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, CallFunc_BooleanAND_ReturnValue_3) == 0x0001CC, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::CallFunc_BooleanAND_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, CallFunc_Less_IntInt_ReturnValue) == 0x0001CD, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, CallFunc_BooleanAND_ReturnValue_4) == 0x0001CE, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::CallFunc_BooleanAND_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, CallFunc_BooleanAND_ReturnValue_5) == 0x0001CF, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::CallFunc_BooleanAND_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_OnPreviewKeyDown, CallFunc_Unhandled_ReturnValue) == 0x0001D0, "Member 'WDG_ReplayInGame_C_OnPreviewKeyDown::CallFunc_Unhandled_ReturnValue' has a wrong offset!");

// Function WDG_ReplayInGame.WDG_ReplayInGame_C.ShowSaveResultDialog
// 0x0018 (0x0018 - 0x0000)
struct WDG_ReplayInGame_C_ShowSaveResultDialog final
{
public:
	class FText                                   text;                                              // 0x0000(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_ReplayInGame_C_ShowSaveResultDialog) == 0x000008, "Wrong alignment on WDG_ReplayInGame_C_ShowSaveResultDialog");
static_assert(sizeof(WDG_ReplayInGame_C_ShowSaveResultDialog) == 0x000018, "Wrong size on WDG_ReplayInGame_C_ShowSaveResultDialog");
static_assert(offsetof(WDG_ReplayInGame_C_ShowSaveResultDialog, text) == 0x000000, "Member 'WDG_ReplayInGame_C_ShowSaveResultDialog::text' has a wrong offset!");

// Function WDG_ReplayInGame.WDG_ReplayInGame_C.GetPlaybackSpeedTxt
// 0x00A8 (0x00A8 - 0x0000)
struct WDG_ReplayInGame_C_GetPlaybackSpeedTxt final
{
public:
	class FText                                   ReturnValue;                                       // 0x0000(0x0018)(Parm, OutParm, ReturnParm)
	int32                                         CallFunc_Array_Find_ReturnValue;                   // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x001C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1D[0x3];                                       // 0x001D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Array_Get_Item;                           // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_24[0x4];                                       // 0x0024(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_FloatToText_ReturnValue;             // 0x0028(0x0018)()
	struct FFormatArgumentData                    K2Node_MakeStruct_FormatArgumentData;              // 0x0040(0x0040)(UObjectWrapper, HasGetValueTypeHash)
	TArray<struct FFormatArgumentData>            K2Node_MakeArray_Array;                            // 0x0080(0x0010)(ReferenceParm)
	class FText                                   CallFunc_Format_ReturnValue;                       // 0x0090(0x0018)()
};
static_assert(alignof(WDG_ReplayInGame_C_GetPlaybackSpeedTxt) == 0x000008, "Wrong alignment on WDG_ReplayInGame_C_GetPlaybackSpeedTxt");
static_assert(sizeof(WDG_ReplayInGame_C_GetPlaybackSpeedTxt) == 0x0000A8, "Wrong size on WDG_ReplayInGame_C_GetPlaybackSpeedTxt");
static_assert(offsetof(WDG_ReplayInGame_C_GetPlaybackSpeedTxt, ReturnValue) == 0x000000, "Member 'WDG_ReplayInGame_C_GetPlaybackSpeedTxt::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_GetPlaybackSpeedTxt, CallFunc_Array_Find_ReturnValue) == 0x000018, "Member 'WDG_ReplayInGame_C_GetPlaybackSpeedTxt::CallFunc_Array_Find_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_GetPlaybackSpeedTxt, CallFunc_IsValid_ReturnValue) == 0x00001C, "Member 'WDG_ReplayInGame_C_GetPlaybackSpeedTxt::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_GetPlaybackSpeedTxt, CallFunc_Array_Get_Item) == 0x000020, "Member 'WDG_ReplayInGame_C_GetPlaybackSpeedTxt::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_GetPlaybackSpeedTxt, CallFunc_Conv_FloatToText_ReturnValue) == 0x000028, "Member 'WDG_ReplayInGame_C_GetPlaybackSpeedTxt::CallFunc_Conv_FloatToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_GetPlaybackSpeedTxt, K2Node_MakeStruct_FormatArgumentData) == 0x000040, "Member 'WDG_ReplayInGame_C_GetPlaybackSpeedTxt::K2Node_MakeStruct_FormatArgumentData' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_GetPlaybackSpeedTxt, K2Node_MakeArray_Array) == 0x000080, "Member 'WDG_ReplayInGame_C_GetPlaybackSpeedTxt::K2Node_MakeArray_Array' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_GetPlaybackSpeedTxt, CallFunc_Format_ReturnValue) == 0x000090, "Member 'WDG_ReplayInGame_C_GetPlaybackSpeedTxt::CallFunc_Format_ReturnValue' has a wrong offset!");

// Function WDG_ReplayInGame.WDG_ReplayInGame_C.GetCurrentSpeedIndex
// 0x0008 (0x0008 - 0x0000)
struct WDG_ReplayInGame_C_GetCurrentSpeedIndex final
{
public:
	int32                                         Index_0;                                           // 0x0000(0x0004)(Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Find_ReturnValue;                   // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ReplayInGame_C_GetCurrentSpeedIndex) == 0x000004, "Wrong alignment on WDG_ReplayInGame_C_GetCurrentSpeedIndex");
static_assert(sizeof(WDG_ReplayInGame_C_GetCurrentSpeedIndex) == 0x000008, "Wrong size on WDG_ReplayInGame_C_GetCurrentSpeedIndex");
static_assert(offsetof(WDG_ReplayInGame_C_GetCurrentSpeedIndex, Index_0) == 0x000000, "Member 'WDG_ReplayInGame_C_GetCurrentSpeedIndex::Index_0' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_GetCurrentSpeedIndex, CallFunc_Array_Find_ReturnValue) == 0x000004, "Member 'WDG_ReplayInGame_C_GetCurrentSpeedIndex::CallFunc_Array_Find_ReturnValue' has a wrong offset!");

// Function WDG_ReplayInGame.WDG_ReplayInGame_C.ShouldAllowPanelUp
// 0x0003 (0x0003 - 0x0000)
struct WDG_ReplayInGame_C_ShouldAllowPanelUp final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsStandingsFocused_ReturnValue;           // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ReplayInGame_C_ShouldAllowPanelUp) == 0x000001, "Wrong alignment on WDG_ReplayInGame_C_ShouldAllowPanelUp");
static_assert(sizeof(WDG_ReplayInGame_C_ShouldAllowPanelUp) == 0x000003, "Wrong size on WDG_ReplayInGame_C_ShouldAllowPanelUp");
static_assert(offsetof(WDG_ReplayInGame_C_ShouldAllowPanelUp, ReturnValue) == 0x000000, "Member 'WDG_ReplayInGame_C_ShouldAllowPanelUp::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ShouldAllowPanelUp, CallFunc_IsStandingsFocused_ReturnValue) == 0x000001, "Member 'WDG_ReplayInGame_C_ShouldAllowPanelUp::CallFunc_IsStandingsFocused_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_ShouldAllowPanelUp, CallFunc_Not_PreBool_ReturnValue) == 0x000002, "Member 'WDG_ReplayInGame_C_ShouldAllowPanelUp::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");

// Function WDG_ReplayInGame.WDG_ReplayInGame_C.IsStandingsFocused
// 0x0004 (0x0004 - 0x0000)
struct WDG_ReplayInGame_C_IsStandingsFocused final
{
public:
	bool                                          ReturnValue;                                       // 0x0000(0x0001)(Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_HasFocusedDescendants_ReturnValue;        // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_HasAnyUserFocus_ReturnValue;              // 0x0002(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x0003(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ReplayInGame_C_IsStandingsFocused) == 0x000001, "Wrong alignment on WDG_ReplayInGame_C_IsStandingsFocused");
static_assert(sizeof(WDG_ReplayInGame_C_IsStandingsFocused) == 0x000004, "Wrong size on WDG_ReplayInGame_C_IsStandingsFocused");
static_assert(offsetof(WDG_ReplayInGame_C_IsStandingsFocused, ReturnValue) == 0x000000, "Member 'WDG_ReplayInGame_C_IsStandingsFocused::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_IsStandingsFocused, CallFunc_HasFocusedDescendants_ReturnValue) == 0x000001, "Member 'WDG_ReplayInGame_C_IsStandingsFocused::CallFunc_HasFocusedDescendants_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_IsStandingsFocused, CallFunc_HasAnyUserFocus_ReturnValue) == 0x000002, "Member 'WDG_ReplayInGame_C_IsStandingsFocused::CallFunc_HasAnyUserFocus_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ReplayInGame_C_IsStandingsFocused, CallFunc_BooleanOR_ReturnValue) == 0x000003, "Member 'WDG_ReplayInGame_C_IsStandingsFocused::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");

}

