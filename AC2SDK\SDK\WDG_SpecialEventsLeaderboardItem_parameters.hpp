﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SpecialEventsLeaderboardItem

#include "Basic.hpp"

#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_SpecialEventsLeaderboardItem.WDG_SpecialEventsLeaderboardItem_C.ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem
// 0x00D8 (0x00D8 - 0x0000)
struct WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0004(0x0001)(ZeroConstructor, IsPlainOldD<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	uint8                                         Pad_5[0x3];                                        // 0x0005(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateFontInfo                         K2Node_MakeStruct_SlateFontInfo;                   // 0x0008(0x0058)(UObjectWrapper, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0060(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_61[0x7];                                       // 0x0061(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Int32ToLaptimeText_ReturnValue;           // 0x0068(0x0018)()
	class FText                                   CallFunc_Conv_IntToText_ReturnValue;               // 0x0080(0x0018)()
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x0098(0x0018)()
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_1;             // 0x00B0(0x0018)()
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x00C8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_C9[0x3];                                       // 0x00C9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	float                                         CallFunc_Multiply_IntFloat_ReturnValue;            // 0x00CC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x00D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem) == 0x000008, "Wrong alignment on WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem");
static_assert(sizeof(WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem) == 0x0000D8, "Wrong size on WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem");
static_assert(offsetof(WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem, EntryPoint) == 0x000000, "Member 'WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem, CallFunc_MakeLiteralByte_ReturnValue) == 0x000004, "Member 'WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem, K2Node_MakeStruct_SlateFontInfo) == 0x000008, "Member 'WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem::K2Node_MakeStruct_SlateFontInfo' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000060, "Member 'WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem, CallFunc_Int32ToLaptimeText_ReturnValue) == 0x000068, "Member 'WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem::CallFunc_Int32ToLaptimeText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem, CallFunc_Conv_IntToText_ReturnValue) == 0x000080, "Member 'WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem::CallFunc_Conv_IntToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem, CallFunc_Conv_StringToText_ReturnValue) == 0x000098, "Member 'WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem, CallFunc_Conv_IntToText_ReturnValue_1) == 0x0000B0, "Member 'WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem::CallFunc_Conv_IntToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem, CallFunc_Greater_IntInt_ReturnValue) == 0x0000C8, "Member 'WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem, CallFunc_Multiply_IntFloat_ReturnValue) == 0x0000CC, "Member 'WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem::CallFunc_Multiply_IntFloat_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem, CallFunc_PlayAnimation_ReturnValue) == 0x0000D0, "Member 'WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");

}

