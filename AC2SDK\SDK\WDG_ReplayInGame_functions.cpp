﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ReplayInGame

#include "Basic.hpp"

#include "WDG_ReplayInGame_classes.hpp"
#include "WDG_ReplayInGame_parameters.hpp"


namespace SDK
{

// Function WDG_ReplayInGame.WDG_ReplayInGame_C.ExecuteUbergraph_WDG_ReplayInGame
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ReplayInGame_C::ExecuteUbergraph_WDG_ReplayInGame(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "ExecuteUbergraph_WDG_ReplayInGame");

	Params::WDG_ReplayInGame_C_ExecuteUbergraph_WDG_ReplayInGame Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.BndEvt__toggleresults_K2Node_ComponentBoundEvent_0_OnButtonPressed__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayInGame_C::BndEvt__toggleresults_K2Node_ComponentBoundEvent_0_OnButtonPressed__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "BndEvt__toggleresults_K2Node_ComponentBoundEvent_0_OnButtonPressed__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.OnFadeTriggered
// (Event, Public, BlueprintEvent)
// Parameters:
// float                                   Duration                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ReplayInGame_C::OnFadeTriggered(float Duration)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "OnFadeTriggered");

	Params::WDG_ReplayInGame_C_OnFadeTriggered Parms{};

	Parms.Duration = Duration;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.Tick
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// float                                   InDeltaTime                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ReplayInGame_C::Tick(const struct FGeometry& MyGeometry, float InDeltaTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "Tick");

	Params::WDG_ReplayInGame_C_Tick Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InDeltaTime = InDeltaTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.OnRemovedFromFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_ReplayInGame_C::OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "OnRemovedFromFocusPath");

	Params::WDG_ReplayInGame_C_OnRemovedFromFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.CustomEvent
// (BlueprintCallable, BlueprintEvent)

void UWDG_ReplayInGame_C::CustomEvent()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "CustomEvent");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.CustomEvent_0
// (BlueprintCallable, BlueprintEvent)

void UWDG_ReplayInGame_C::CustomEvent_0()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "CustomEvent_0");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.BP_StartPage
// (Event, Public, BlueprintEvent)

void UWDG_ReplayInGame_C::BP_StartPage()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "BP_StartPage");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.Destruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_ReplayInGame_C::Destruct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "Destruct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.OnReplayHUDStarted
// (Event, Public, BlueprintEvent)

void UWDG_ReplayInGame_C::OnReplayHUDStarted()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "OnReplayHUDStarted");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.Remove_PB
// (Event, Public, BlueprintEvent)

void UWDG_ReplayInGame_C::Remove_PB()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "Remove_PB");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.BndEvt__TimeSlider_K2Node_ComponentBoundEvent_69_OnMouseCaptureEndEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayInGame_C::BndEvt__TimeSlider_K2Node_ComponentBoundEvent_69_OnMouseCaptureEndEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "BndEvt__TimeSlider_K2Node_ComponentBoundEvent_69_OnMouseCaptureEndEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.BndEvt__TimeSlider_K2Node_ComponentBoundEvent_21_OnMouseCaptureBeginEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayInGame_C::BndEvt__TimeSlider_K2Node_ComponentBoundEvent_21_OnMouseCaptureBeginEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "BndEvt__TimeSlider_K2Node_ComponentBoundEvent_21_OnMouseCaptureBeginEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.OnQuitRequested
// (Event, Public, BlueprintCallable, BlueprintEvent)

void UWDG_ReplayInGame_C::OnQuitRequested()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "OnQuitRequested");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.BndEvt__btnQuit_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayInGame_C::BndEvt__btnQuit_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "BndEvt__btnQuit_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.BndEvt__btnQuit_K2Node_ComponentBoundEvent_3_OnButtonPressed__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayInGame_C::BndEvt__btnQuit_K2Node_ComponentBoundEvent_3_OnButtonPressed__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "BndEvt__btnQuit_K2Node_ComponentBoundEvent_3_OnButtonPressed__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.BndEvt__confirmationModal_K2Node_ComponentBoundEvent_1_OnNo__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayInGame_C::BndEvt__confirmationModal_K2Node_ComponentBoundEvent_1_OnNo__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "BndEvt__confirmationModal_K2Node_ComponentBoundEvent_1_OnNo__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.BndEvt__confirmationModal_K2Node_ComponentBoundEvent_0_OnYes__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayInGame_C::BndEvt__confirmationModal_K2Node_ComponentBoundEvent_0_OnYes__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "BndEvt__confirmationModal_K2Node_ComponentBoundEvent_0_OnYes__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.BndEvt__btnSpeedIncrease_K2Node_ComponentBoundEvent_17_OnButtonPressed__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayInGame_C::BndEvt__btnSpeedIncrease_K2Node_ComponentBoundEvent_17_OnButtonPressed__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "BndEvt__btnSpeedIncrease_K2Node_ComponentBoundEvent_17_OnButtonPressed__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.BndEvt__btnSpeedDecrease_K2Node_ComponentBoundEvent_16_OnButtonPressed__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayInGame_C::BndEvt__btnSpeedDecrease_K2Node_ComponentBoundEvent_16_OnButtonPressed__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "BndEvt__btnSpeedDecrease_K2Node_ComponentBoundEvent_16_OnButtonPressed__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.BndEvt__togglehud_K2Node_ComponentBoundEvent_0_OnButtonPressed__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayInGame_C::BndEvt__togglehud_K2Node_ComponentBoundEvent_0_OnButtonPressed__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "BndEvt__togglehud_K2Node_ComponentBoundEvent_0_OnButtonPressed__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.BndEvt__btnReverse_K2Node_ComponentBoundEvent_14_OnButtonPressed__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayInGame_C::BndEvt__btnReverse_K2Node_ComponentBoundEvent_14_OnButtonPressed__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "BndEvt__btnReverse_K2Node_ComponentBoundEvent_14_OnButtonPressed__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.BndEvt__btnPlayPause_K2Node_ComponentBoundEvent_2_OnButtonPressed__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayInGame_C::BndEvt__btnPlayPause_K2Node_ComponentBoundEvent_2_OnButtonPressed__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "BndEvt__btnPlayPause_K2Node_ComponentBoundEvent_2_OnButtonPressed__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.BndEvt__btnPhoto_K2Node_ComponentBoundEvent_13_OnButtonClickedEvent__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayInGame_C::BndEvt__btnPhoto_K2Node_ComponentBoundEvent_13_OnButtonClickedEvent__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "BndEvt__btnPhoto_K2Node_ComponentBoundEvent_13_OnButtonClickedEvent__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.BndEvt__btnSaveReplay_K2Node_ComponentBoundEvent_11_OnButtonPressed__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayInGame_C::BndEvt__btnSaveReplay_K2Node_ComponentBoundEvent_11_OnButtonPressed__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "BndEvt__btnSaveReplay_K2Node_ComponentBoundEvent_11_OnButtonPressed__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.BndEvt__btnSaveHighlights_K2Node_ComponentBoundEvent_10_OnButtonPressed__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayInGame_C::BndEvt__btnSaveHighlights_K2Node_ComponentBoundEvent_10_OnButtonPressed__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "BndEvt__btnSaveHighlights_K2Node_ComponentBoundEvent_10_OnButtonPressed__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.BndEvt__btnNextCam_K2Node_ComponentBoundEvent_9_OnButtonPressed__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayInGame_C::BndEvt__btnNextCam_K2Node_ComponentBoundEvent_9_OnButtonPressed__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "BndEvt__btnNextCam_K2Node_ComponentBoundEvent_9_OnButtonPressed__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.BndEvt__btnPrevCam_K2Node_ComponentBoundEvent_8_OnButtonPressed__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayInGame_C::BndEvt__btnPrevCam_K2Node_ComponentBoundEvent_8_OnButtonPressed__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "BndEvt__btnPrevCam_K2Node_ComponentBoundEvent_8_OnButtonPressed__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.BndEvt__btnPrevCar_K2Node_ComponentBoundEvent_7_OnButtonPressed__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayInGame_C::BndEvt__btnPrevCar_K2Node_ComponentBoundEvent_7_OnButtonPressed__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "BndEvt__btnPrevCar_K2Node_ComponentBoundEvent_7_OnButtonPressed__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.BndEvt__btnNextCar_K2Node_ComponentBoundEvent_6_OnButtonPressed__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayInGame_C::BndEvt__btnNextCar_K2Node_ComponentBoundEvent_6_OnButtonPressed__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "BndEvt__btnNextCar_K2Node_ComponentBoundEvent_6_OnButtonPressed__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.BndEvt__btnIncreaseSpeed_K2Node_ComponentBoundEvent_5_OnButtonPressed__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayInGame_C::BndEvt__btnIncreaseSpeed_K2Node_ComponentBoundEvent_5_OnButtonPressed__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "BndEvt__btnIncreaseSpeed_K2Node_ComponentBoundEvent_5_OnButtonPressed__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.BndEvt__ExtendedButton_K2Node_ComponentBoundEvent_4_OnButtonPressed__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayInGame_C::BndEvt__ExtendedButton_K2Node_ComponentBoundEvent_4_OnButtonPressed__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "BndEvt__ExtendedButton_K2Node_ComponentBoundEvent_4_OnButtonPressed__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.BndEvt__btnSlowMotion_K2Node_ComponentBoundEvent_3_OnButtonPressed__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayInGame_C::BndEvt__btnSlowMotion_K2Node_ComponentBoundEvent_3_OnButtonPressed__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "BndEvt__btnSlowMotion_K2Node_ComponentBoundEvent_3_OnButtonPressed__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_12_OnHide__DelegateSignature
// (BlueprintEvent)

void UWDG_ReplayInGame_C::BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_12_OnHide__DelegateSignature()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "BndEvt__fileOperationPopup_K2Node_ComponentBoundEvent_12_OnHide__DelegateSignature");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.OnSaveHighlights
// (Event, Public, BlueprintEvent)
// Parameters:
// int32                                   ResultCode                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ReplayInGame_C::OnSaveHighlights(int32 ResultCode)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "OnSaveHighlights");

	Params::WDG_ReplayInGame_C_OnSaveHighlights Parms{};

	Parms.ResultCode = ResultCode;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.OnSaveReplay
// (Event, Public, BlueprintEvent)
// Parameters:
// int32                                   ResultCode                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ReplayInGame_C::OnSaveReplay(int32 ResultCode)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "OnSaveReplay");

	Params::WDG_ReplayInGame_C_OnSaveReplay Parms{};

	Parms.ResultCode = ResultCode;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.GetCurrentTime
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class FText                             ReturnValue                                            (Parm, OutParm, ReturnParm)

class FText UWDG_ReplayInGame_C::GetCurrentTime()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "GetCurrentTime");

	Params::WDG_ReplayInGame_C_GetCurrentTime Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.Get_TotalTime_Text_0
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class FText                             ReturnValue                                            (Parm, OutParm, ReturnParm)

class FText UWDG_ReplayInGame_C::Get_TotalTime_Text_0()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "Get_TotalTime_Text_0");

	Params::WDG_ReplayInGame_C_Get_TotalTime_Text_0 Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.Get_TimeMultiplierText_Text_0
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class FText                             ReturnValue                                            (Parm, OutParm, ReturnParm)

class FText UWDG_ReplayInGame_C::Get_TimeMultiplierText_Text_0()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "Get_TimeMultiplierText_Text_0");

	Params::WDG_ReplayInGame_C_Get_TimeMultiplierText_Text_0 Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.OnPreviewKeyDown
// (Event, Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FKeyEvent&                 InKeyEvent                                             (BlueprintVisible, BlueprintReadOnly, Parm)
// struct FEventReply                      ReturnValue                                            (Parm, OutParm, ReturnParm)

struct FEventReply UWDG_ReplayInGame_C::OnPreviewKeyDown(const struct FGeometry& MyGeometry, const struct FKeyEvent& InKeyEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "OnPreviewKeyDown");

	Params::WDG_ReplayInGame_C_OnPreviewKeyDown Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InKeyEvent = std::move(InKeyEvent);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.HideSaveResultDialog
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_ReplayInGame_C::HideSaveResultDialog()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "HideSaveResultDialog");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.ShowSaveResultDialog
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const class FText&                      text                                                   (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_ReplayInGame_C::ShowSaveResultDialog(const class FText& text)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "ShowSaveResultDialog");

	Params::WDG_ReplayInGame_C_ShowSaveResultDialog Parms{};

	Parms.text = std::move(text);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.GetPlaybackSpeedTxt
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class FText                             ReturnValue                                            (Parm, OutParm, ReturnParm)

class FText UWDG_ReplayInGame_C::GetPlaybackSpeedTxt()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "GetPlaybackSpeedTxt");

	Params::WDG_ReplayInGame_C_GetPlaybackSpeedTxt Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.GetCurrentSpeedIndex
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// int32*                                  Index_0                                                (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ReplayInGame_C::GetCurrentSpeedIndex(int32* Index_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "GetCurrentSpeedIndex");

	Params::WDG_ReplayInGame_C_GetCurrentSpeedIndex Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Index_0 != nullptr)
		*Index_0 = Parms.Index_0;
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.ShouldAllowPanelUp
// (Event, Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor)

bool UWDG_ReplayInGame_C::ShouldAllowPanelUp()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "ShouldAllowPanelUp");

	Params::WDG_ReplayInGame_C_ShouldAllowPanelUp Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_ReplayInGame.WDG_ReplayInGame_C.IsStandingsFocused
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor)

bool UWDG_ReplayInGame_C::IsStandingsFocused()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayInGame_C", "IsStandingsFocused");

	Params::WDG_ReplayInGame_C_IsStandingsFocused Parms{};

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}

}

