﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RatingDetailTO

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RatingDetailTO.WDG_RatingDetailTO_C
// 0x0018 (0x0650 - 0x0638)
class UWDG_RatingDetailTO_C final : public URatingTODetail
{
public:
	class UWDG_SimpleChartWrapper_C*              ChartWrapperRatingOverTime;                        // 0x0638(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SimpleChartWrapper_C*              ChartWrapperRatings;                               // 0x0640(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldD<PERSON>, <PERSON><PERSON>ki<PERSON>, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgBackground;                                     // 0x0648(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RatingDetailTO_C">();
	}
	static class UWDG_RatingDetailTO_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RatingDetailTO_C>();
	}
};
static_assert(alignof(UWDG_RatingDetailTO_C) == 0x000008, "Wrong alignment on UWDG_RatingDetailTO_C");
static_assert(sizeof(UWDG_RatingDetailTO_C) == 0x000650, "Wrong size on UWDG_RatingDetailTO_C");
static_assert(offsetof(UWDG_RatingDetailTO_C, ChartWrapperRatingOverTime) == 0x000638, "Member 'UWDG_RatingDetailTO_C::ChartWrapperRatingOverTime' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailTO_C, ChartWrapperRatings) == 0x000640, "Member 'UWDG_RatingDetailTO_C::ChartWrapperRatings' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailTO_C, imgBackground) == 0x000648, "Member 'UWDG_RatingDetailTO_C::imgBackground' has a wrong offset!");

}

