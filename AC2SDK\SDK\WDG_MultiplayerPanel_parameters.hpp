﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_MultiplayerPanel

#include "Basic.hpp"


namespace SDK::Params
{

// Function WDG_MultiplayerPanel.WDG_MultiplayerPanel_C.ExecuteUbergraph_WDG_MultiplayerPanel
// 0x0028 (0x0028 - 0x0000)
struct WDG_MultiplayerPanel_C_ExecuteUbergraph_WDG_MultiplayerPanel final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0008(0x0008)(ZeroConstructor, IsPlainOldD<PERSON>, NoD<PERSON>ructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_1;              // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_MultiplayerPanel_C_ExecuteUbergraph_WDG_MultiplayerPanel) == 0x000008, "Wrong alignment on WDG_MultiplayerPanel_C_ExecuteUbergraph_WDG_MultiplayerPanel");
static_assert(sizeof(WDG_MultiplayerPanel_C_ExecuteUbergraph_WDG_MultiplayerPanel) == 0x000028, "Wrong size on WDG_MultiplayerPanel_C_ExecuteUbergraph_WDG_MultiplayerPanel");
static_assert(offsetof(WDG_MultiplayerPanel_C_ExecuteUbergraph_WDG_MultiplayerPanel, EntryPoint) == 0x000000, "Member 'WDG_MultiplayerPanel_C_ExecuteUbergraph_WDG_MultiplayerPanel::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPanel_C_ExecuteUbergraph_WDG_MultiplayerPanel, CallFunc_PlayAnimation_ReturnValue) == 0x000008, "Member 'WDG_MultiplayerPanel_C_ExecuteUbergraph_WDG_MultiplayerPanel::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPanel_C_ExecuteUbergraph_WDG_MultiplayerPanel, K2Node_Event_IsDesignTime) == 0x000010, "Member 'WDG_MultiplayerPanel_C_ExecuteUbergraph_WDG_MultiplayerPanel::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPanel_C_ExecuteUbergraph_WDG_MultiplayerPanel, CallFunc_PlayAnimation_ReturnValue_1) == 0x000018, "Member 'WDG_MultiplayerPanel_C_ExecuteUbergraph_WDG_MultiplayerPanel::CallFunc_PlayAnimation_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerPanel_C_ExecuteUbergraph_WDG_MultiplayerPanel, CallFunc_IsValid_ReturnValue) == 0x000020, "Member 'WDG_MultiplayerPanel_C_ExecuteUbergraph_WDG_MultiplayerPanel::CallFunc_IsValid_ReturnValue' has a wrong offset!");

// Function WDG_MultiplayerPanel.WDG_MultiplayerPanel_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_MultiplayerPanel_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_MultiplayerPanel_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_MultiplayerPanel_C_PreConstruct");
static_assert(sizeof(WDG_MultiplayerPanel_C_PreConstruct) == 0x000001, "Wrong size on WDG_MultiplayerPanel_C_PreConstruct");
static_assert(offsetof(WDG_MultiplayerPanel_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_MultiplayerPanel_C_PreConstruct::IsDesignTime' has a wrong offset!");

}

