﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SpecialEventsLeaderboardItem

#include "Basic.hpp"

#include "WDG_SpecialEventsLeaderboardItem_classes.hpp"
#include "WDG_SpecialEventsLeaderboardItem_parameters.hpp"


namespace SDK
{

// Function WDG_SpecialEventsLeaderboardItem.WDG_SpecialEventsLeaderboardItem_C.ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SpecialEventsLeaderboardItem_C::ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventsLeaderboardItem_C", "ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem");

	Params::WDG_SpecialEventsLeaderboardItem_C_ExecuteUbergraph_WDG_SpecialEventsLeaderboardItem Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEventsLeaderboardItem.WDG_SpecialEventsLeaderboardItem_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_SpecialEventsLeaderboardItem_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventsLeaderboardItem_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}

}

