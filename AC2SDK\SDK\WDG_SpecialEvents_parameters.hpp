﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SpecialEvents

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "UMG_structs.hpp"
#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_SpecialEvents.WDG_SpecialEvents_C.ExecuteUbergraph_WDG_SpecialEvents
// 0x08E0 (0x08E0 - 0x0000)
struct WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0004(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_C[0x4];                                        // 0x000C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UOnlineServices*                        CallFunc_GetOnlineServices_ReturnValue;            // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_ByteByte_ReturnValue;          // 0x0019(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1A[0x6];                                       // 0x001A(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<struct FOnlineServicesHotlapEntry>     K2Node_Event_entries;                              // 0x0020(0x0010)(ConstParm, ReferenceParm)
	TArray<class FText>                           K2Node_Event_deltas;                               // 0x0030(0x0010)(ConstParm, ReferenceParm)
	int32                                         K2Node_Event_ref_id;                               // 0x0040(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Array_IsValidIndex_ReturnValue;           // 0x0044(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_45[0x3];                                       // 0x0045(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0048(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4C[0x4];                                       // 0x004C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FOnlineServicesHotlapEntry             CallFunc_Array_Get_Item;                           // 0x0050(0x0088)()
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x00D8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_2;               // 0x00DC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x00E0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x00E1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_E2[0x6];                                       // 0x00E2(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x00E8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FMargin                                K2Node_MakeStruct_Margin;                          // 0x00F0(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor)
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x0100(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x0104(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_105[0x3];                                      // 0x0105(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue;               // 0x0108(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcPageBase*                            CallFunc_GoToPage_ReturnValue;                     // 0x0110(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_SpecialEventItem_C*                CallFunc_GetSelectedItem_SelectedItem;             // 0x0118(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_SpecialEventsLeaderboardItem_C*    CallFunc_Create_ReturnValue;                       // 0x0120(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue;                     // 0x0128(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UScrollBoxSlot*                         K2Node_DynamicCast_AsScroll_Box_Slot;              // 0x0130(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0138(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_IsValid_ReturnValue_1;                    // 0x0139(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_13A[0x2];                                      // 0x013A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x013C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcPanelBase*                           K2Node_ComponentBoundEvent_Panel_2;                // 0x0140(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0148(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_149[0x3];                                      // 0x0149(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x014C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWidget*                                CallFunc_GetActiveWidget_ReturnValue;              // 0x0150(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ObjectObject_ReturnValue;      // 0x0158(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_159[0x7];                                      // 0x0159(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_SpecialEventItem_C*                K2Node_CustomEvent_Item;                           // 0x0160(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_CustomEvent_Count;                          // 0x0168(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_16C[0x4];                                      // 0x016C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_SpecialEventItem_C*                K2Node_CustomEvent_PreviouslySelected;             // 0x0170(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue_1;             // 0x0178(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_179[0x7];                                      // 0x0179(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<struct FSpecialEventPreset>            CallFunc_GetPresets_SpecialEvents;                 // 0x0180(0x0010)(ReferenceParm)
	TArray<struct FSpecialEventPreset>            CallFunc_GetPresets_SpecialEvents_1;               // 0x0190(0x0010)(ReferenceParm)
	struct FSpecialEventPreset                    K2Node_Event_preset;                               // 0x01A0(0x0240)(ConstParm)
	struct FModelInfo                             K2Node_Event_model_info;                           // 0x03E0(0x01A8)(ConstParm)
	struct FCarInfo                               K2Node_Event_car_info;                             // 0x0588(0x00E0)(ConstParm)
	struct FTeamInfo                              K2Node_Event_team_info;                            // 0x0668(0x0038)(ConstParm)
	struct FCircuitInfo                           K2Node_Event_circuit_info;                         // 0x06A0(0x01F0)(ConstParm)
	TArray<struct FSpecialEventPreset>            CallFunc_GetPresets_SpecialEvents_2;               // 0x0890(0x0010)(ReferenceParm)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_1;              // 0x08A0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_2;              // 0x08A8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcPanelBase*                           K2Node_ComponentBoundEvent_Panel_1;                // 0x08B0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcPanelBase*                           K2Node_ComponentBoundEvent_Panel;                  // 0x08B8(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsAnimationPlaying_ReturnValue;           // 0x08C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_8C1[0x7];                                      // 0x08C1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue_1;             // 0x08C8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcPageBase*                            CallFunc_GoToPage_ReturnValue_1;                   // 0x08D0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_CanPlay_Result;                           // 0x08D8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_CanPlay_Result_1;                         // 0x08D9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents) == 0x000008, "Wrong alignment on WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents");
static_assert(sizeof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents) == 0x0008E0, "Wrong size on WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, EntryPoint) == 0x000000, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, Temp_int_Array_Index_Variable) == 0x000004, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_Add_IntInt_ReturnValue) == 0x000008, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_GetOnlineServices_ReturnValue) == 0x000010, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_GetOnlineServices_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_IsValid_ReturnValue) == 0x000018, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_EqualEqual_ByteByte_ReturnValue) == 0x000019, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_EqualEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, K2Node_Event_entries) == 0x000020, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::K2Node_Event_entries' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, K2Node_Event_deltas) == 0x000030, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::K2Node_Event_deltas' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, K2Node_Event_ref_id) == 0x000040, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::K2Node_Event_ref_id' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_Array_IsValidIndex_ReturnValue) == 0x000044, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_Array_IsValidIndex_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_Array_Length_ReturnValue) == 0x000048, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_Array_Get_Item) == 0x000050, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_Array_Length_ReturnValue_1) == 0x0000D8, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_Array_Length_ReturnValue_2) == 0x0000DC, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_Array_Length_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x0000E0, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_BooleanAND_ReturnValue) == 0x0000E1, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_PlayAnimation_ReturnValue) == 0x0000E8, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, K2Node_MakeStruct_Margin) == 0x0000F0, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::K2Node_MakeStruct_Margin' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_Subtract_IntInt_ReturnValue) == 0x000100, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_Greater_IntInt_ReturnValue) == 0x000104, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_GetMenuManager_ReturnValue) == 0x000108, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_GetMenuManager_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_GoToPage_ReturnValue) == 0x000110, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_GoToPage_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_GetSelectedItem_SelectedItem) == 0x000118, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_GetSelectedItem_SelectedItem' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_Create_ReturnValue) == 0x000120, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_AddChild_ReturnValue) == 0x000128, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_AddChild_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, K2Node_DynamicCast_AsScroll_Box_Slot) == 0x000130, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::K2Node_DynamicCast_AsScroll_Box_Slot' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, K2Node_DynamicCast_bSuccess) == 0x000138, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_IsValid_ReturnValue_1) == 0x000139, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_IsValid_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, Temp_int_Loop_Counter_Variable) == 0x00013C, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, K2Node_ComponentBoundEvent_Panel_2) == 0x000140, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::K2Node_ComponentBoundEvent_Panel_2' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_Less_IntInt_ReturnValue) == 0x000148, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_Add_IntInt_ReturnValue_1) == 0x00014C, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_GetActiveWidget_ReturnValue) == 0x000150, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_GetActiveWidget_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_EqualEqual_ObjectObject_ReturnValue) == 0x000158, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_EqualEqual_ObjectObject_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, K2Node_CustomEvent_Item) == 0x000160, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::K2Node_CustomEvent_Item' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, K2Node_CustomEvent_Count) == 0x000168, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::K2Node_CustomEvent_Count' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, K2Node_CustomEvent_PreviouslySelected) == 0x000170, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::K2Node_CustomEvent_PreviouslySelected' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_Greater_IntInt_ReturnValue_1) == 0x000178, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_Greater_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_GetPresets_SpecialEvents) == 0x000180, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_GetPresets_SpecialEvents' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_GetPresets_SpecialEvents_1) == 0x000190, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_GetPresets_SpecialEvents_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, K2Node_Event_preset) == 0x0001A0, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::K2Node_Event_preset' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, K2Node_Event_model_info) == 0x0003E0, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::K2Node_Event_model_info' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, K2Node_Event_car_info) == 0x000588, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::K2Node_Event_car_info' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, K2Node_Event_team_info) == 0x000668, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::K2Node_Event_team_info' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, K2Node_Event_circuit_info) == 0x0006A0, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::K2Node_Event_circuit_info' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_GetPresets_SpecialEvents_2) == 0x000890, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_GetPresets_SpecialEvents_2' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_PlayAnimation_ReturnValue_1) == 0x0008A0, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_PlayAnimation_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_PlayAnimation_ReturnValue_2) == 0x0008A8, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_PlayAnimation_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, K2Node_ComponentBoundEvent_Panel_1) == 0x0008B0, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::K2Node_ComponentBoundEvent_Panel_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, K2Node_ComponentBoundEvent_Panel) == 0x0008B8, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::K2Node_ComponentBoundEvent_Panel' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_IsAnimationPlaying_ReturnValue) == 0x0008C0, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_IsAnimationPlaying_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_GetMenuManager_ReturnValue_1) == 0x0008C8, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_GetMenuManager_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_GoToPage_ReturnValue_1) == 0x0008D0, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_GoToPage_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_CanPlay_Result) == 0x0008D8, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_CanPlay_Result' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents, CallFunc_CanPlay_Result_1) == 0x0008D9, "Member 'WDG_SpecialEvents_C_ExecuteUbergraph_WDG_SpecialEvents::CallFunc_CanPlay_Result_1' has a wrong offset!");

// Function WDG_SpecialEvents.WDG_SpecialEvents_C.BndEvt__btnOldEvents_K2Node_ComponentBoundEvent_6_OnAcPanelFocusEvent__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_SpecialEvents_C_BndEvt__btnOldEvents_K2Node_ComponentBoundEvent_6_OnAcPanelFocusEvent__DelegateSignature final
{
public:
	class UAcPanelBase*                           panel;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SpecialEvents_C_BndEvt__btnOldEvents_K2Node_ComponentBoundEvent_6_OnAcPanelFocusEvent__DelegateSignature) == 0x000008, "Wrong alignment on WDG_SpecialEvents_C_BndEvt__btnOldEvents_K2Node_ComponentBoundEvent_6_OnAcPanelFocusEvent__DelegateSignature");
static_assert(sizeof(WDG_SpecialEvents_C_BndEvt__btnOldEvents_K2Node_ComponentBoundEvent_6_OnAcPanelFocusEvent__DelegateSignature) == 0x000008, "Wrong size on WDG_SpecialEvents_C_BndEvt__btnOldEvents_K2Node_ComponentBoundEvent_6_OnAcPanelFocusEvent__DelegateSignature");
static_assert(offsetof(WDG_SpecialEvents_C_BndEvt__btnOldEvents_K2Node_ComponentBoundEvent_6_OnAcPanelFocusEvent__DelegateSignature, panel) == 0x000000, "Member 'WDG_SpecialEvents_C_BndEvt__btnOldEvents_K2Node_ComponentBoundEvent_6_OnAcPanelFocusEvent__DelegateSignature::panel' has a wrong offset!");

// Function WDG_SpecialEvents.WDG_SpecialEvents_C.BndEvt__btnRefresh_K2Node_ComponentBoundEvent_5_OnAcPanelFocusEvent__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_SpecialEvents_C_BndEvt__btnRefresh_K2Node_ComponentBoundEvent_5_OnAcPanelFocusEvent__DelegateSignature final
{
public:
	class UAcPanelBase*                           panel;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SpecialEvents_C_BndEvt__btnRefresh_K2Node_ComponentBoundEvent_5_OnAcPanelFocusEvent__DelegateSignature) == 0x000008, "Wrong alignment on WDG_SpecialEvents_C_BndEvt__btnRefresh_K2Node_ComponentBoundEvent_5_OnAcPanelFocusEvent__DelegateSignature");
static_assert(sizeof(WDG_SpecialEvents_C_BndEvt__btnRefresh_K2Node_ComponentBoundEvent_5_OnAcPanelFocusEvent__DelegateSignature) == 0x000008, "Wrong size on WDG_SpecialEvents_C_BndEvt__btnRefresh_K2Node_ComponentBoundEvent_5_OnAcPanelFocusEvent__DelegateSignature");
static_assert(offsetof(WDG_SpecialEvents_C_BndEvt__btnRefresh_K2Node_ComponentBoundEvent_5_OnAcPanelFocusEvent__DelegateSignature, panel) == 0x000000, "Member 'WDG_SpecialEvents_C_BndEvt__btnRefresh_K2Node_ComponentBoundEvent_5_OnAcPanelFocusEvent__DelegateSignature::panel' has a wrong offset!");

// Function WDG_SpecialEvents.WDG_SpecialEvents_C.OnEventSelected
// 0x06F0 (0x06F0 - 0x0000)
struct WDG_SpecialEvents_C_OnEventSelected final
{
public:
	struct FSpecialEventPreset                    Preset;                                            // 0x0000(0x0240)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	struct FModelInfo                             model_info;                                        // 0x0240(0x01A8)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	struct FCarInfo                               car_info;                                          // 0x03E8(0x00E0)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	struct FTeamInfo                              team_info;                                         // 0x04C8(0x0038)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	struct FCircuitInfo                           circuit_info;                                      // 0x0500(0x01F0)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_SpecialEvents_C_OnEventSelected) == 0x000008, "Wrong alignment on WDG_SpecialEvents_C_OnEventSelected");
static_assert(sizeof(WDG_SpecialEvents_C_OnEventSelected) == 0x0006F0, "Wrong size on WDG_SpecialEvents_C_OnEventSelected");
static_assert(offsetof(WDG_SpecialEvents_C_OnEventSelected, Preset) == 0x000000, "Member 'WDG_SpecialEvents_C_OnEventSelected::Preset' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_OnEventSelected, model_info) == 0x000240, "Member 'WDG_SpecialEvents_C_OnEventSelected::model_info' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_OnEventSelected, car_info) == 0x0003E8, "Member 'WDG_SpecialEvents_C_OnEventSelected::car_info' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_OnEventSelected, team_info) == 0x0004C8, "Member 'WDG_SpecialEvents_C_OnEventSelected::team_info' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_OnEventSelected, circuit_info) == 0x000500, "Member 'WDG_SpecialEvents_C_OnEventSelected::circuit_info' has a wrong offset!");

// Function WDG_SpecialEvents.WDG_SpecialEvents_C.OnSpecialEventListPopulated
// 0x0010 (0x0010 - 0x0000)
struct WDG_SpecialEvents_C_OnSpecialEventListPopulated final
{
public:
	int32                                         Count;                                             // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_SpecialEventItem_C*                PreviouslySelected;                                // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SpecialEvents_C_OnSpecialEventListPopulated) == 0x000008, "Wrong alignment on WDG_SpecialEvents_C_OnSpecialEventListPopulated");
static_assert(sizeof(WDG_SpecialEvents_C_OnSpecialEventListPopulated) == 0x000010, "Wrong size on WDG_SpecialEvents_C_OnSpecialEventListPopulated");
static_assert(offsetof(WDG_SpecialEvents_C_OnSpecialEventListPopulated, Count) == 0x000000, "Member 'WDG_SpecialEvents_C_OnSpecialEventListPopulated::Count' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_OnSpecialEventListPopulated, PreviouslySelected) == 0x000008, "Member 'WDG_SpecialEvents_C_OnSpecialEventListPopulated::PreviouslySelected' has a wrong offset!");

// Function WDG_SpecialEvents.WDG_SpecialEvents_C.OnSpecialEventItemAdded
// 0x0008 (0x0008 - 0x0000)
struct WDG_SpecialEvents_C_OnSpecialEventItemAdded final
{
public:
	class UWDG_SpecialEventItem_C*                Item;                                              // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SpecialEvents_C_OnSpecialEventItemAdded) == 0x000008, "Wrong alignment on WDG_SpecialEvents_C_OnSpecialEventItemAdded");
static_assert(sizeof(WDG_SpecialEvents_C_OnSpecialEventItemAdded) == 0x000008, "Wrong size on WDG_SpecialEvents_C_OnSpecialEventItemAdded");
static_assert(offsetof(WDG_SpecialEvents_C_OnSpecialEventItemAdded, Item) == 0x000000, "Member 'WDG_SpecialEvents_C_OnSpecialEventItemAdded::Item' has a wrong offset!");

// Function WDG_SpecialEvents.WDG_SpecialEvents_C.BndEvt__WDG_GenericBarItem_C_0_K2Node_ComponentBoundEvent_2_OnAcPanelFocusEvent__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_SpecialEvents_C_BndEvt__WDG_GenericBarItem_C_0_K2Node_ComponentBoundEvent_2_OnAcPanelFocusEvent__DelegateSignature final
{
public:
	class UAcPanelBase*                           panel;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SpecialEvents_C_BndEvt__WDG_GenericBarItem_C_0_K2Node_ComponentBoundEvent_2_OnAcPanelFocusEvent__DelegateSignature) == 0x000008, "Wrong alignment on WDG_SpecialEvents_C_BndEvt__WDG_GenericBarItem_C_0_K2Node_ComponentBoundEvent_2_OnAcPanelFocusEvent__DelegateSignature");
static_assert(sizeof(WDG_SpecialEvents_C_BndEvt__WDG_GenericBarItem_C_0_K2Node_ComponentBoundEvent_2_OnAcPanelFocusEvent__DelegateSignature) == 0x000008, "Wrong size on WDG_SpecialEvents_C_BndEvt__WDG_GenericBarItem_C_0_K2Node_ComponentBoundEvent_2_OnAcPanelFocusEvent__DelegateSignature");
static_assert(offsetof(WDG_SpecialEvents_C_BndEvt__WDG_GenericBarItem_C_0_K2Node_ComponentBoundEvent_2_OnAcPanelFocusEvent__DelegateSignature, panel) == 0x000000, "Member 'WDG_SpecialEvents_C_BndEvt__WDG_GenericBarItem_C_0_K2Node_ComponentBoundEvent_2_OnAcPanelFocusEvent__DelegateSignature::panel' has a wrong offset!");

// Function WDG_SpecialEvents.WDG_SpecialEvents_C.OnLeaderboardRequested
// 0x0004 (0x0004 - 0x0000)
struct WDG_SpecialEvents_C_OnLeaderboardRequested final
{
public:
	int32                                         ref_id;                                            // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SpecialEvents_C_OnLeaderboardRequested) == 0x000004, "Wrong alignment on WDG_SpecialEvents_C_OnLeaderboardRequested");
static_assert(sizeof(WDG_SpecialEvents_C_OnLeaderboardRequested) == 0x000004, "Wrong size on WDG_SpecialEvents_C_OnLeaderboardRequested");
static_assert(offsetof(WDG_SpecialEvents_C_OnLeaderboardRequested, ref_id) == 0x000000, "Member 'WDG_SpecialEvents_C_OnLeaderboardRequested::ref_id' has a wrong offset!");

// Function WDG_SpecialEvents.WDG_SpecialEvents_C.OnLeaderboardReceived
// 0x0020 (0x0020 - 0x0000)
struct WDG_SpecialEvents_C_OnLeaderboardReceived final
{
public:
	TArray<struct FOnlineServicesHotlapEntry>     entries;                                           // 0x0000(0x0010)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	TArray<class FText>                           deltas;                                            // 0x0010(0x0010)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
};
static_assert(alignof(WDG_SpecialEvents_C_OnLeaderboardReceived) == 0x000008, "Wrong alignment on WDG_SpecialEvents_C_OnLeaderboardReceived");
static_assert(sizeof(WDG_SpecialEvents_C_OnLeaderboardReceived) == 0x000020, "Wrong size on WDG_SpecialEvents_C_OnLeaderboardReceived");
static_assert(offsetof(WDG_SpecialEvents_C_OnLeaderboardReceived, entries) == 0x000000, "Member 'WDG_SpecialEvents_C_OnLeaderboardReceived::entries' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_OnLeaderboardReceived, deltas) == 0x000010, "Member 'WDG_SpecialEvents_C_OnLeaderboardReceived::deltas' has a wrong offset!");

// Function WDG_SpecialEvents.WDG_SpecialEvents_C.Populate
// 0x02C8 (0x02C8 - 0x0000)
struct WDG_SpecialEvents_C_Populate final
{
public:
	TArray<struct FSpecialEventPreset>            SpecialEvents;                                     // 0x0000(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	bool                                          PastEvents;                                        // 0x0010(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_SpecialEventItem_C*                PreviousItem;                                      // 0x0018(0x0008)(Edit, BlueprintVisible, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Count;                                             // 0x0020(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0024(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x002C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_SpecialEventItem_C*                CallFunc_Create_ReturnValue;                       // 0x0030(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_39[0x3];                                       // 0x0039(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable;                                 // 0x003C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWrapBoxSlot*                           CallFunc_AddChildToWrapBox_ReturnValue;            // 0x0040(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue;               // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void(class UWDG_SpecialEventItem_C* Sender, bool ByMouse)> K2Node_CreateDelegate_OutputDelegate; // 0x0050(0x0010)(ZeroConstructor, NoDestructor)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0060(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0068(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6C[0x4];                                       // 0x006C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSpecialEventPreset                    CallFunc_Array_Get_Item;                           // 0x0070(0x0240)()
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x02B0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2B1[0x3];                                      // 0x02B1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void(class UAcPanelBase* Sender)>   K2Node_CreateDelegate_OutputDelegate_1;            // 0x02B4(0x0010)(ZeroConstructor, NoDestructor)
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x02C4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SpecialEvents_C_Populate) == 0x000008, "Wrong alignment on WDG_SpecialEvents_C_Populate");
static_assert(sizeof(WDG_SpecialEvents_C_Populate) == 0x0002C8, "Wrong size on WDG_SpecialEvents_C_Populate");
static_assert(offsetof(WDG_SpecialEvents_C_Populate, SpecialEvents) == 0x000000, "Member 'WDG_SpecialEvents_C_Populate::SpecialEvents' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_Populate, PastEvents) == 0x000010, "Member 'WDG_SpecialEvents_C_Populate::PastEvents' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_Populate, PreviousItem) == 0x000018, "Member 'WDG_SpecialEvents_C_Populate::PreviousItem' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_Populate, Count) == 0x000020, "Member 'WDG_SpecialEvents_C_Populate::Count' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_Populate, Temp_int_Loop_Counter_Variable) == 0x000024, "Member 'WDG_SpecialEvents_C_Populate::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_Populate, CallFunc_Add_IntInt_ReturnValue) == 0x000028, "Member 'WDG_SpecialEvents_C_Populate::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_Populate, CallFunc_Array_Length_ReturnValue) == 0x00002C, "Member 'WDG_SpecialEvents_C_Populate::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_Populate, CallFunc_Create_ReturnValue) == 0x000030, "Member 'WDG_SpecialEvents_C_Populate::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_Populate, CallFunc_Less_IntInt_ReturnValue) == 0x000038, "Member 'WDG_SpecialEvents_C_Populate::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_Populate, Temp_int_Variable) == 0x00003C, "Member 'WDG_SpecialEvents_C_Populate::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_Populate, CallFunc_AddChildToWrapBox_ReturnValue) == 0x000040, "Member 'WDG_SpecialEvents_C_Populate::CallFunc_AddChildToWrapBox_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_Populate, CallFunc_GetMenuManager_ReturnValue) == 0x000048, "Member 'WDG_SpecialEvents_C_Populate::CallFunc_GetMenuManager_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_Populate, K2Node_CreateDelegate_OutputDelegate) == 0x000050, "Member 'WDG_SpecialEvents_C_Populate::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_Populate, CallFunc_PlayAnimation_ReturnValue) == 0x000060, "Member 'WDG_SpecialEvents_C_Populate::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_Populate, Temp_int_Array_Index_Variable) == 0x000068, "Member 'WDG_SpecialEvents_C_Populate::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_Populate, CallFunc_Array_Get_Item) == 0x000070, "Member 'WDG_SpecialEvents_C_Populate::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_Populate, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x0002B0, "Member 'WDG_SpecialEvents_C_Populate::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_Populate, K2Node_CreateDelegate_OutputDelegate_1) == 0x0002B4, "Member 'WDG_SpecialEvents_C_Populate::K2Node_CreateDelegate_OutputDelegate_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_Populate, CallFunc_Add_IntInt_ReturnValue_1) == 0x0002C4, "Member 'WDG_SpecialEvents_C_Populate::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");

// Function WDG_SpecialEvents.WDG_SpecialEvents_C.FocusToItem
// 0x0008 (0x0008 - 0x0000)
struct WDG_SpecialEvents_C_FocusToItem final
{
public:
	class UAcPanelBase*                           Target;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SpecialEvents_C_FocusToItem) == 0x000008, "Wrong alignment on WDG_SpecialEvents_C_FocusToItem");
static_assert(sizeof(WDG_SpecialEvents_C_FocusToItem) == 0x000008, "Wrong size on WDG_SpecialEvents_C_FocusToItem");
static_assert(offsetof(WDG_SpecialEvents_C_FocusToItem, Target) == 0x000000, "Member 'WDG_SpecialEvents_C_FocusToItem::Target' has a wrong offset!");

// Function WDG_SpecialEvents.WDG_SpecialEvents_C.GetPresets
// 0x02C8 (0x02C8 - 0x0000)
struct WDG_SpecialEvents_C_GetPresets final
{
public:
	bool                                          Debug;                                             // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          PastEvents;                                        // 0x0001(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2[0x6];                                        // 0x0002(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<struct FSpecialEventPreset>            SpecialEvents;                                     // 0x0008(0x0010)(Parm, OutParm)
	TArray<struct FSpecialEventPreset>            SpecialEventsArray;                                // 0x0018(0x0010)(Edit, BlueprintVisible)
	class UDataTable*                             DataTable;                                         // 0x0028(0x0008)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0034(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0038(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x003C(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_3D[0x3];                                       // 0x003D(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UOnlineServices*                        CallFunc_GetOnlineServices_ReturnValue;            // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TArray<class FName>                           CallFunc_GetDataTableRowNames_OutRowNames;         // 0x0048(0x0010)(ReferenceParm)
	TArray<struct FSpecialEventPreset>            CallFunc_GetSortedSpecialEventList_List;           // 0x0058(0x0010)(ReferenceParm)
	class FName                                   CallFunc_Array_Get_Item;                           // 0x0068(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSpecialEventPreset                    CallFunc_GetDataTableRowFromName_OutRow;           // 0x0070(0x0240)()
	bool                                          CallFunc_GetDataTableRowFromName_ReturnValue;      // 0x02B0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2B1[0x3];                                      // 0x02B1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x02B4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x02B8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x02B9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_isSpecialEventEnabled_ReturnValue;        // 0x02BA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2BB[0x1];                                      // 0x02BB(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Add_ReturnValue;                    // 0x02BC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x02C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x02C1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SpecialEvents_C_GetPresets) == 0x000008, "Wrong alignment on WDG_SpecialEvents_C_GetPresets");
static_assert(sizeof(WDG_SpecialEvents_C_GetPresets) == 0x0002C8, "Wrong size on WDG_SpecialEvents_C_GetPresets");
static_assert(offsetof(WDG_SpecialEvents_C_GetPresets, Debug) == 0x000000, "Member 'WDG_SpecialEvents_C_GetPresets::Debug' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetPresets, PastEvents) == 0x000001, "Member 'WDG_SpecialEvents_C_GetPresets::PastEvents' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetPresets, SpecialEvents) == 0x000008, "Member 'WDG_SpecialEvents_C_GetPresets::SpecialEvents' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetPresets, SpecialEventsArray) == 0x000018, "Member 'WDG_SpecialEvents_C_GetPresets::SpecialEventsArray' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetPresets, DataTable) == 0x000028, "Member 'WDG_SpecialEvents_C_GetPresets::DataTable' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetPresets, Temp_int_Loop_Counter_Variable) == 0x000030, "Member 'WDG_SpecialEvents_C_GetPresets::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetPresets, CallFunc_Add_IntInt_ReturnValue) == 0x000034, "Member 'WDG_SpecialEvents_C_GetPresets::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetPresets, Temp_int_Array_Index_Variable) == 0x000038, "Member 'WDG_SpecialEvents_C_GetPresets::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetPresets, CallFunc_IsValid_ReturnValue) == 0x00003C, "Member 'WDG_SpecialEvents_C_GetPresets::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetPresets, CallFunc_GetOnlineServices_ReturnValue) == 0x000040, "Member 'WDG_SpecialEvents_C_GetPresets::CallFunc_GetOnlineServices_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetPresets, CallFunc_GetDataTableRowNames_OutRowNames) == 0x000048, "Member 'WDG_SpecialEvents_C_GetPresets::CallFunc_GetDataTableRowNames_OutRowNames' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetPresets, CallFunc_GetSortedSpecialEventList_List) == 0x000058, "Member 'WDG_SpecialEvents_C_GetPresets::CallFunc_GetSortedSpecialEventList_List' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetPresets, CallFunc_Array_Get_Item) == 0x000068, "Member 'WDG_SpecialEvents_C_GetPresets::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetPresets, CallFunc_GetDataTableRowFromName_OutRow) == 0x000070, "Member 'WDG_SpecialEvents_C_GetPresets::CallFunc_GetDataTableRowFromName_OutRow' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetPresets, CallFunc_GetDataTableRowFromName_ReturnValue) == 0x0002B0, "Member 'WDG_SpecialEvents_C_GetPresets::CallFunc_GetDataTableRowFromName_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetPresets, CallFunc_Array_Length_ReturnValue) == 0x0002B4, "Member 'WDG_SpecialEvents_C_GetPresets::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetPresets, CallFunc_Less_IntInt_ReturnValue) == 0x0002B8, "Member 'WDG_SpecialEvents_C_GetPresets::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetPresets, CallFunc_Greater_IntInt_ReturnValue) == 0x0002B9, "Member 'WDG_SpecialEvents_C_GetPresets::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetPresets, CallFunc_isSpecialEventEnabled_ReturnValue) == 0x0002BA, "Member 'WDG_SpecialEvents_C_GetPresets::CallFunc_isSpecialEventEnabled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetPresets, CallFunc_Array_Add_ReturnValue) == 0x0002BC, "Member 'WDG_SpecialEvents_C_GetPresets::CallFunc_Array_Add_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetPresets, CallFunc_Not_PreBool_ReturnValue) == 0x0002C0, "Member 'WDG_SpecialEvents_C_GetPresets::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetPresets, CallFunc_BooleanAND_ReturnValue) == 0x0002C1, "Member 'WDG_SpecialEvents_C_GetPresets::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");

// Function WDG_SpecialEvents.WDG_SpecialEvents_C.ItemSelected
// 0x0050 (0x0050 - 0x0000)
struct WDG_SpecialEvents_C_ItemSelected final
{
public:
	class UWDG_SpecialEventItem_C*                Item;                                              // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          ByMouse;                                           // 0x0008(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0009(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_A[0x2];                                        // 0x000A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Array_Index_Variable;                     // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_isSpecialEventEnabled_ReturnValue;        // 0x0011(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0012(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue_1;                 // 0x0013(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_14[0x4];                                       // 0x0014(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UWidget*>                        CallFunc_GetAllChildren_ReturnValue;               // 0x0018(0x0010)(ReferenceParm, ContainsInstancedReference)
	class UWidget*                                CallFunc_Array_Get_Item;                           // 0x0028(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_SpecialEventItem_C*                K2Node_DynamicCast_AsWDG_Special_Event_Item;       // 0x0030(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_39[0x3];                                       // 0x0039(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x003C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_ObjectObject_ReturnValue;      // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_41[0x3];                                       // 0x0041(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0044(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0048(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_49[0x3];                                       // 0x0049(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x004C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SpecialEvents_C_ItemSelected) == 0x000008, "Wrong alignment on WDG_SpecialEvents_C_ItemSelected");
static_assert(sizeof(WDG_SpecialEvents_C_ItemSelected) == 0x000050, "Wrong size on WDG_SpecialEvents_C_ItemSelected");
static_assert(offsetof(WDG_SpecialEvents_C_ItemSelected, Item) == 0x000000, "Member 'WDG_SpecialEvents_C_ItemSelected::Item' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ItemSelected, ByMouse) == 0x000008, "Member 'WDG_SpecialEvents_C_ItemSelected::ByMouse' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ItemSelected, CallFunc_Not_PreBool_ReturnValue) == 0x000009, "Member 'WDG_SpecialEvents_C_ItemSelected::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ItemSelected, Temp_int_Array_Index_Variable) == 0x00000C, "Member 'WDG_SpecialEvents_C_ItemSelected::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ItemSelected, CallFunc_IsValid_ReturnValue) == 0x000010, "Member 'WDG_SpecialEvents_C_ItemSelected::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ItemSelected, CallFunc_isSpecialEventEnabled_ReturnValue) == 0x000011, "Member 'WDG_SpecialEvents_C_ItemSelected::CallFunc_isSpecialEventEnabled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ItemSelected, CallFunc_BooleanAND_ReturnValue) == 0x000012, "Member 'WDG_SpecialEvents_C_ItemSelected::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ItemSelected, CallFunc_BooleanAND_ReturnValue_1) == 0x000013, "Member 'WDG_SpecialEvents_C_ItemSelected::CallFunc_BooleanAND_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ItemSelected, CallFunc_GetAllChildren_ReturnValue) == 0x000018, "Member 'WDG_SpecialEvents_C_ItemSelected::CallFunc_GetAllChildren_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ItemSelected, CallFunc_Array_Get_Item) == 0x000028, "Member 'WDG_SpecialEvents_C_ItemSelected::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ItemSelected, K2Node_DynamicCast_AsWDG_Special_Event_Item) == 0x000030, "Member 'WDG_SpecialEvents_C_ItemSelected::K2Node_DynamicCast_AsWDG_Special_Event_Item' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ItemSelected, K2Node_DynamicCast_bSuccess) == 0x000038, "Member 'WDG_SpecialEvents_C_ItemSelected::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ItemSelected, CallFunc_Array_Length_ReturnValue) == 0x00003C, "Member 'WDG_SpecialEvents_C_ItemSelected::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ItemSelected, CallFunc_EqualEqual_ObjectObject_ReturnValue) == 0x000040, "Member 'WDG_SpecialEvents_C_ItemSelected::CallFunc_EqualEqual_ObjectObject_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ItemSelected, Temp_int_Loop_Counter_Variable) == 0x000044, "Member 'WDG_SpecialEvents_C_ItemSelected::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ItemSelected, CallFunc_Less_IntInt_ReturnValue) == 0x000048, "Member 'WDG_SpecialEvents_C_ItemSelected::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_ItemSelected, CallFunc_Add_IntInt_ReturnValue) == 0x00004C, "Member 'WDG_SpecialEvents_C_ItemSelected::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");

// Function WDG_SpecialEvents.WDG_SpecialEvents_C.SetEventsAvailable
// 0x0001 (0x0001 - 0x0000)
struct WDG_SpecialEvents_C_SetEventsAvailable final
{
public:
	bool                                          IsAvailable;                                       // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SpecialEvents_C_SetEventsAvailable) == 0x000001, "Wrong alignment on WDG_SpecialEvents_C_SetEventsAvailable");
static_assert(sizeof(WDG_SpecialEvents_C_SetEventsAvailable) == 0x000001, "Wrong size on WDG_SpecialEvents_C_SetEventsAvailable");
static_assert(offsetof(WDG_SpecialEvents_C_SetEventsAvailable, IsAvailable) == 0x000000, "Member 'WDG_SpecialEvents_C_SetEventsAvailable::IsAvailable' has a wrong offset!");

// Function WDG_SpecialEvents.WDG_SpecialEvents_C.SelectAndFocusListToItem
// 0x0010 (0x0010 - 0x0000)
struct WDG_SpecialEvents_C_SelectAndFocusListToItem final
{
public:
	class UWDG_SpecialEventItem_C*                Item;                                              // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0008(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SpecialEvents_C_SelectAndFocusListToItem) == 0x000008, "Wrong alignment on WDG_SpecialEvents_C_SelectAndFocusListToItem");
static_assert(sizeof(WDG_SpecialEvents_C_SelectAndFocusListToItem) == 0x000010, "Wrong size on WDG_SpecialEvents_C_SelectAndFocusListToItem");
static_assert(offsetof(WDG_SpecialEvents_C_SelectAndFocusListToItem, Item) == 0x000000, "Member 'WDG_SpecialEvents_C_SelectAndFocusListToItem::Item' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_SelectAndFocusListToItem, CallFunc_IsValid_ReturnValue) == 0x000008, "Member 'WDG_SpecialEvents_C_SelectAndFocusListToItem::CallFunc_IsValid_ReturnValue' has a wrong offset!");

// Function WDG_SpecialEvents.WDG_SpecialEvents_C.GetSelectedItem
// 0x0058 (0x0058 - 0x0000)
struct WDG_SpecialEvents_C_GetSelectedItem final
{
public:
	class UWDG_SpecialEventItem_C*                SelectedItem;                                      // 0x0000(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_SpecialEventItem_C*                Found;                                             // 0x0008(0x0008)(Edit, BlueprintVisible, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x0010(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0014(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1C[0x4];                                       // 0x001C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<class UWidget*>                        CallFunc_GetAllChildren_ReturnValue;               // 0x0020(0x0010)(ReferenceParm, ContainsInstancedReference)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_34[0x4];                                       // 0x0034(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                CallFunc_Array_Get_Item;                           // 0x0038(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0040(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_41[0x7];                                       // 0x0041(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_SpecialEventItem_C*                K2Node_DynamicCast_AsWDG_Special_Event_Item;       // 0x0048(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0050(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
};
static_assert(alignof(WDG_SpecialEvents_C_GetSelectedItem) == 0x000008, "Wrong alignment on WDG_SpecialEvents_C_GetSelectedItem");
static_assert(sizeof(WDG_SpecialEvents_C_GetSelectedItem) == 0x000058, "Wrong size on WDG_SpecialEvents_C_GetSelectedItem");
static_assert(offsetof(WDG_SpecialEvents_C_GetSelectedItem, SelectedItem) == 0x000000, "Member 'WDG_SpecialEvents_C_GetSelectedItem::SelectedItem' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetSelectedItem, Found) == 0x000008, "Member 'WDG_SpecialEvents_C_GetSelectedItem::Found' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetSelectedItem, Temp_int_Array_Index_Variable) == 0x000010, "Member 'WDG_SpecialEvents_C_GetSelectedItem::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetSelectedItem, Temp_int_Loop_Counter_Variable) == 0x000014, "Member 'WDG_SpecialEvents_C_GetSelectedItem::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetSelectedItem, CallFunc_Add_IntInt_ReturnValue) == 0x000018, "Member 'WDG_SpecialEvents_C_GetSelectedItem::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetSelectedItem, CallFunc_GetAllChildren_ReturnValue) == 0x000020, "Member 'WDG_SpecialEvents_C_GetSelectedItem::CallFunc_GetAllChildren_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetSelectedItem, CallFunc_Array_Length_ReturnValue) == 0x000030, "Member 'WDG_SpecialEvents_C_GetSelectedItem::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetSelectedItem, CallFunc_Array_Get_Item) == 0x000038, "Member 'WDG_SpecialEvents_C_GetSelectedItem::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetSelectedItem, CallFunc_Less_IntInt_ReturnValue) == 0x000040, "Member 'WDG_SpecialEvents_C_GetSelectedItem::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetSelectedItem, K2Node_DynamicCast_AsWDG_Special_Event_Item) == 0x000048, "Member 'WDG_SpecialEvents_C_GetSelectedItem::K2Node_DynamicCast_AsWDG_Special_Event_Item' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_GetSelectedItem, K2Node_DynamicCast_bSuccess) == 0x000050, "Member 'WDG_SpecialEvents_C_GetSelectedItem::K2Node_DynamicCast_bSuccess' has a wrong offset!");

// Function WDG_SpecialEvents.WDG_SpecialEvents_C.DownToSelectedItem
// 0x0020 (0x0020 - 0x0000)
struct WDG_SpecialEvents_C_DownToSelectedItem final
{
public:
	EUINavigation                                 Navigation_0;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                ReturnValue;                                       // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_SpecialEventItem_C*                CallFunc_GetSelectedItem_SelectedItem;             // 0x0010(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0018(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SpecialEvents_C_DownToSelectedItem) == 0x000008, "Wrong alignment on WDG_SpecialEvents_C_DownToSelectedItem");
static_assert(sizeof(WDG_SpecialEvents_C_DownToSelectedItem) == 0x000020, "Wrong size on WDG_SpecialEvents_C_DownToSelectedItem");
static_assert(offsetof(WDG_SpecialEvents_C_DownToSelectedItem, Navigation_0) == 0x000000, "Member 'WDG_SpecialEvents_C_DownToSelectedItem::Navigation_0' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_DownToSelectedItem, ReturnValue) == 0x000008, "Member 'WDG_SpecialEvents_C_DownToSelectedItem::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_DownToSelectedItem, CallFunc_GetSelectedItem_SelectedItem) == 0x000010, "Member 'WDG_SpecialEvents_C_DownToSelectedItem::CallFunc_GetSelectedItem_SelectedItem' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_DownToSelectedItem, CallFunc_IsValid_ReturnValue) == 0x000018, "Member 'WDG_SpecialEvents_C_DownToSelectedItem::CallFunc_IsValid_ReturnValue' has a wrong offset!");

// Function WDG_SpecialEvents.WDG_SpecialEvents_C.OnPreviewKeyDown
// 0x02A0 (0x02A0 - 0x0000)
struct WDG_SpecialEvents_C_OnPreviewKeyDown final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FKeyEvent                              InKeyEvent;                                        // 0x0038(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm)
	struct FEventReply                            ReturnValue;                                       // 0x0070(0x00B8)(Parm, OutParm, ReturnParm)
	struct FEventReply                            CallFunc_Handled_ReturnValue;                      // 0x0128(0x00B8)()
	struct FEventReply                            CallFunc_OnPreviewKeyDown_ReturnValue;             // 0x01E0(0x00B8)()
	bool                                          CallFunc_HasFocusedDescendants_ReturnValue;        // 0x0298(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SpecialEvents_C_OnPreviewKeyDown) == 0x000008, "Wrong alignment on WDG_SpecialEvents_C_OnPreviewKeyDown");
static_assert(sizeof(WDG_SpecialEvents_C_OnPreviewKeyDown) == 0x0002A0, "Wrong size on WDG_SpecialEvents_C_OnPreviewKeyDown");
static_assert(offsetof(WDG_SpecialEvents_C_OnPreviewKeyDown, MyGeometry) == 0x000000, "Member 'WDG_SpecialEvents_C_OnPreviewKeyDown::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_OnPreviewKeyDown, InKeyEvent) == 0x000038, "Member 'WDG_SpecialEvents_C_OnPreviewKeyDown::InKeyEvent' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_OnPreviewKeyDown, ReturnValue) == 0x000070, "Member 'WDG_SpecialEvents_C_OnPreviewKeyDown::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_OnPreviewKeyDown, CallFunc_Handled_ReturnValue) == 0x000128, "Member 'WDG_SpecialEvents_C_OnPreviewKeyDown::CallFunc_Handled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_OnPreviewKeyDown, CallFunc_OnPreviewKeyDown_ReturnValue) == 0x0001E0, "Member 'WDG_SpecialEvents_C_OnPreviewKeyDown::CallFunc_OnPreviewKeyDown_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_OnPreviewKeyDown, CallFunc_HasFocusedDescendants_ReturnValue) == 0x000298, "Member 'WDG_SpecialEvents_C_OnPreviewKeyDown::CallFunc_HasFocusedDescendants_ReturnValue' has a wrong offset!");

// Function WDG_SpecialEvents.WDG_SpecialEvents_C.LeftToCurrentTeam
// 0x0018 (0x0018 - 0x0000)
struct WDG_SpecialEvents_C_LeftToCurrentTeam final
{
public:
	EUINavigation                                 Navigation_0;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWidget*                                ReturnValue;                                       // 0x0008(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsVisible_ReturnValue;                    // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SpecialEvents_C_LeftToCurrentTeam) == 0x000008, "Wrong alignment on WDG_SpecialEvents_C_LeftToCurrentTeam");
static_assert(sizeof(WDG_SpecialEvents_C_LeftToCurrentTeam) == 0x000018, "Wrong size on WDG_SpecialEvents_C_LeftToCurrentTeam");
static_assert(offsetof(WDG_SpecialEvents_C_LeftToCurrentTeam, Navigation_0) == 0x000000, "Member 'WDG_SpecialEvents_C_LeftToCurrentTeam::Navigation_0' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_LeftToCurrentTeam, ReturnValue) == 0x000008, "Member 'WDG_SpecialEvents_C_LeftToCurrentTeam::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_LeftToCurrentTeam, CallFunc_IsVisible_ReturnValue) == 0x000010, "Member 'WDG_SpecialEvents_C_LeftToCurrentTeam::CallFunc_IsVisible_ReturnValue' has a wrong offset!");

// Function WDG_SpecialEvents.WDG_SpecialEvents_C.UnfadeUI
// 0x0010 (0x0010 - 0x0000)
struct WDG_SpecialEvents_C_UnfadeUI final
{
public:
	float                                         CallFunc_PauseAnimation_ReturnValue;               // 0x0000(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsAnimationPlaying_ReturnValue;           // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_5[0x3];                                        // 0x0005(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SpecialEvents_C_UnfadeUI) == 0x000008, "Wrong alignment on WDG_SpecialEvents_C_UnfadeUI");
static_assert(sizeof(WDG_SpecialEvents_C_UnfadeUI) == 0x000010, "Wrong size on WDG_SpecialEvents_C_UnfadeUI");
static_assert(offsetof(WDG_SpecialEvents_C_UnfadeUI, CallFunc_PauseAnimation_ReturnValue) == 0x000000, "Member 'WDG_SpecialEvents_C_UnfadeUI::CallFunc_PauseAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_UnfadeUI, CallFunc_IsAnimationPlaying_ReturnValue) == 0x000004, "Member 'WDG_SpecialEvents_C_UnfadeUI::CallFunc_IsAnimationPlaying_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEvents_C_UnfadeUI, CallFunc_PlayAnimation_ReturnValue) == 0x000008, "Member 'WDG_SpecialEvents_C_UnfadeUI::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");

}

