﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_WeatherForecastBroadcast

#include "Basic.hpp"

#include "WDG_WeatherForecast_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_WeatherForecastBroadcast.WDG_WeatherForecastBroadcast_C
// 0x0000 (0x0748 - 0x0748)
class UWDG_WeatherForecastBroadcast_C final : public UWDG_WeatherForecast_C
{
public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_WeatherForecastBroadcast_C">();
	}
	static class UWDG_WeatherForecastBroadcast_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_WeatherForecastBroadcast_C>();
	}
};
static_assert(alignof(UWDG_WeatherForecastBroadcast_C) == 0x000008, "Wrong alignment on UWDG_WeatherForecastBroadcast_C");
static_assert(sizeof(UWDG_WeatherForecastBroadcast_C) == 0x000748, "Wrong size on UWDG_WeatherForecastBroadcast_C");

}

