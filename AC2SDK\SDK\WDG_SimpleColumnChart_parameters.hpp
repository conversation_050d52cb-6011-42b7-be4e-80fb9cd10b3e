﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SimpleColumnChart

#include "Basic.hpp"

#include "UMG_structs.hpp"
#include "CoreUObject_structs.hpp"


namespace SDK::Params
{

// Function WDG_SimpleColumnChart.WDG_SimpleColumnChart_C.ExecuteUbergraph_WDG_SimpleColumnChart
// 0x0050 (0x0050 - 0x0000)
struct WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_5[0x3];                                        // 0x0005(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         K2Node_Event_id_1;                                 // 0x0008(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_Event_value_1;                              // 0x000C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_isColumnNavigable_1;                  // 0x0010(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_11[0x7];                                       // 0x0011(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue;              // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         K2Node_Event_id;                                   // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         K2Node_Event_value;                                // 0x0024(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_Event_color;                                // 0x0028(0x0010)(ConstParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_Event_isColumnNavigable;                    // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_39[0x7];                                       // 0x0039(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_SimpleColumnChartColumn_C*         CallFunc_Create_ReturnValue;                       // 0x0040(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_SimpleColumnChartColumn_C*         CallFunc_Create_ReturnValue_1;                     // 0x0048(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart) == 0x000008, "Wrong alignment on WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart");
static_assert(sizeof(WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart) == 0x000050, "Wrong size on WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart");
static_assert(offsetof(WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart, EntryPoint) == 0x000000, "Member 'WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart, K2Node_Event_IsDesignTime) == 0x000004, "Member 'WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart, K2Node_Event_id_1) == 0x000008, "Member 'WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart::K2Node_Event_id_1' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart, K2Node_Event_value_1) == 0x00000C, "Member 'WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart::K2Node_Event_value_1' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart, K2Node_Event_isColumnNavigable_1) == 0x000010, "Member 'WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart::K2Node_Event_isColumnNavigable_1' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart, CallFunc_GetOwningPlayer_ReturnValue) == 0x000018, "Member 'WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart::CallFunc_GetOwningPlayer_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart, K2Node_Event_id) == 0x000020, "Member 'WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart::K2Node_Event_id' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart, K2Node_Event_value) == 0x000024, "Member 'WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart::K2Node_Event_value' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart, K2Node_Event_color) == 0x000028, "Member 'WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart::K2Node_Event_color' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart, K2Node_Event_isColumnNavigable) == 0x000038, "Member 'WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart::K2Node_Event_isColumnNavigable' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart, CallFunc_Create_ReturnValue) == 0x000040, "Member 'WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart, CallFunc_Create_ReturnValue_1) == 0x000048, "Member 'WDG_SimpleColumnChart_C_ExecuteUbergraph_WDG_SimpleColumnChart::CallFunc_Create_ReturnValue_1' has a wrong offset!");

// Function WDG_SimpleColumnChart.WDG_SimpleColumnChart_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_SimpleColumnChart_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SimpleColumnChart_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_SimpleColumnChart_C_PreConstruct");
static_assert(sizeof(WDG_SimpleColumnChart_C_PreConstruct) == 0x000001, "Wrong size on WDG_SimpleColumnChart_C_PreConstruct");
static_assert(offsetof(WDG_SimpleColumnChart_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_SimpleColumnChart_C_PreConstruct::IsDesignTime' has a wrong offset!");

// Function WDG_SimpleColumnChart.WDG_SimpleColumnChart_C.AddColumnWithColor
// 0x001C (0x001C - 0x0000)
struct WDG_SimpleColumnChart_C_AddColumnWithColor final
{
public:
	int32                                         ID;                                                // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         Value;                                             // 0x0004(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Color;                                             // 0x0008(0x0010)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ZeroConstructor, ReferenceParm, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          isColumnNavigable;                                 // 0x0018(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SimpleColumnChart_C_AddColumnWithColor) == 0x000004, "Wrong alignment on WDG_SimpleColumnChart_C_AddColumnWithColor");
static_assert(sizeof(WDG_SimpleColumnChart_C_AddColumnWithColor) == 0x00001C, "Wrong size on WDG_SimpleColumnChart_C_AddColumnWithColor");
static_assert(offsetof(WDG_SimpleColumnChart_C_AddColumnWithColor, ID) == 0x000000, "Member 'WDG_SimpleColumnChart_C_AddColumnWithColor::ID' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChart_C_AddColumnWithColor, Value) == 0x000004, "Member 'WDG_SimpleColumnChart_C_AddColumnWithColor::Value' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChart_C_AddColumnWithColor, Color) == 0x000008, "Member 'WDG_SimpleColumnChart_C_AddColumnWithColor::Color' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChart_C_AddColumnWithColor, isColumnNavigable) == 0x000018, "Member 'WDG_SimpleColumnChart_C_AddColumnWithColor::isColumnNavigable' has a wrong offset!");

// Function WDG_SimpleColumnChart.WDG_SimpleColumnChart_C.AddColumn
// 0x000C (0x000C - 0x0000)
struct WDG_SimpleColumnChart_C_AddColumn final
{
public:
	int32                                         ID;                                                // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	float                                         Value;                                             // 0x0004(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          isColumnNavigable;                                 // 0x0008(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SimpleColumnChart_C_AddColumn) == 0x000004, "Wrong alignment on WDG_SimpleColumnChart_C_AddColumn");
static_assert(sizeof(WDG_SimpleColumnChart_C_AddColumn) == 0x00000C, "Wrong size on WDG_SimpleColumnChart_C_AddColumn");
static_assert(offsetof(WDG_SimpleColumnChart_C_AddColumn, ID) == 0x000000, "Member 'WDG_SimpleColumnChart_C_AddColumn::ID' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChart_C_AddColumn, Value) == 0x000004, "Member 'WDG_SimpleColumnChart_C_AddColumn::Value' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChart_C_AddColumn, isColumnNavigable) == 0x000008, "Member 'WDG_SimpleColumnChart_C_AddColumn::isColumnNavigable' has a wrong offset!");

// Function WDG_SimpleColumnChart.WDG_SimpleColumnChart_C.WDG_AddSpacer
// 0x0020 (0x0020 - 0x0000)
struct WDG_SimpleColumnChart_C_WDG_AddSpacer final
{
public:
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue;              // 0x0000(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSlateChildSize                        K2Node_MakeStruct_SlateChildSize;                  // 0x0008(0x0008)(NoDestructor)
	class UWDG_BullcrapSpacer_C*                  CallFunc_Create_ReturnValue;                       // 0x0010(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UHorizontalBoxSlot*                     CallFunc_AddChildToHorizontalBox_ReturnValue;      // 0x0018(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SimpleColumnChart_C_WDG_AddSpacer) == 0x000008, "Wrong alignment on WDG_SimpleColumnChart_C_WDG_AddSpacer");
static_assert(sizeof(WDG_SimpleColumnChart_C_WDG_AddSpacer) == 0x000020, "Wrong size on WDG_SimpleColumnChart_C_WDG_AddSpacer");
static_assert(offsetof(WDG_SimpleColumnChart_C_WDG_AddSpacer, CallFunc_GetOwningPlayer_ReturnValue) == 0x000000, "Member 'WDG_SimpleColumnChart_C_WDG_AddSpacer::CallFunc_GetOwningPlayer_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChart_C_WDG_AddSpacer, K2Node_MakeStruct_SlateChildSize) == 0x000008, "Member 'WDG_SimpleColumnChart_C_WDG_AddSpacer::K2Node_MakeStruct_SlateChildSize' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChart_C_WDG_AddSpacer, CallFunc_Create_ReturnValue) == 0x000010, "Member 'WDG_SimpleColumnChart_C_WDG_AddSpacer::CallFunc_Create_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChart_C_WDG_AddSpacer, CallFunc_AddChildToHorizontalBox_ReturnValue) == 0x000018, "Member 'WDG_SimpleColumnChart_C_WDG_AddSpacer::CallFunc_AddChildToHorizontalBox_ReturnValue' has a wrong offset!");

// Function WDG_SimpleColumnChart.WDG_SimpleColumnChart_C.WDG_AddColumn
// 0x0028 (0x0028 - 0x0000)
struct WDG_SimpleColumnChart_C_WDG_AddColumn final
{
public:
	class UWDG_SimpleColumnChartColumn_C*         col;                                               // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void(class UWDG_SimpleColumnChartColumn_C* selectedColumn)> K2Node_CreateDelegate_OutputDelegate; // 0x0008(0x0010)(ZeroConstructor, NoDestructor)
	struct FSlateChildSize                        K2Node_MakeStruct_SlateChildSize;                  // 0x0018(0x0008)(NoDestructor)
	class UHorizontalBoxSlot*                     CallFunc_AddChildToHorizontalBox_ReturnValue;      // 0x0020(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SimpleColumnChart_C_WDG_AddColumn) == 0x000008, "Wrong alignment on WDG_SimpleColumnChart_C_WDG_AddColumn");
static_assert(sizeof(WDG_SimpleColumnChart_C_WDG_AddColumn) == 0x000028, "Wrong size on WDG_SimpleColumnChart_C_WDG_AddColumn");
static_assert(offsetof(WDG_SimpleColumnChart_C_WDG_AddColumn, col) == 0x000000, "Member 'WDG_SimpleColumnChart_C_WDG_AddColumn::col' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChart_C_WDG_AddColumn, K2Node_CreateDelegate_OutputDelegate) == 0x000008, "Member 'WDG_SimpleColumnChart_C_WDG_AddColumn::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChart_C_WDG_AddColumn, K2Node_MakeStruct_SlateChildSize) == 0x000018, "Member 'WDG_SimpleColumnChart_C_WDG_AddColumn::K2Node_MakeStruct_SlateChildSize' has a wrong offset!");
static_assert(offsetof(WDG_SimpleColumnChart_C_WDG_AddColumn, CallFunc_AddChildToHorizontalBox_ReturnValue) == 0x000020, "Member 'WDG_SimpleColumnChart_C_WDG_AddColumn::CallFunc_AddChildToHorizontalBox_ReturnValue' has a wrong offset!");

// Function WDG_SimpleColumnChart.WDG_SimpleColumnChart_C.OnColumnSelectedCallback
// 0x0008 (0x0008 - 0x0000)
struct WDG_SimpleColumnChart_C_OnColumnSelectedCallback final
{
public:
	class UWDG_SimpleColumnChartColumn_C*         selectedColumn;                                    // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_SimpleColumnChart_C_OnColumnSelectedCallback) == 0x000008, "Wrong alignment on WDG_SimpleColumnChart_C_OnColumnSelectedCallback");
static_assert(sizeof(WDG_SimpleColumnChart_C_OnColumnSelectedCallback) == 0x000008, "Wrong size on WDG_SimpleColumnChart_C_OnColumnSelectedCallback");
static_assert(offsetof(WDG_SimpleColumnChart_C_OnColumnSelectedCallback, selectedColumn) == 0x000000, "Member 'WDG_SimpleColumnChart_C_OnColumnSelectedCallback::selectedColumn' has a wrong offset!");

}

