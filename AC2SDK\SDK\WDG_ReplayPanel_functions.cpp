﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ReplayPanel

#include "Basic.hpp"

#include "WDG_ReplayPanel_classes.hpp"
#include "WDG_ReplayPanel_parameters.hpp"


namespace SDK
{

// Function WDG_ReplayPanel.WDG_ReplayPanel_C.ExecuteUbergraph_WDG_ReplayPanel
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ReplayPanel_C::ExecuteUbergraph_WDG_ReplayPanel(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayPanel_C", "ExecuteUbergraph_WDG_ReplayPanel");

	Params::WDG_ReplayPanel_C_ExecuteUbergraph_WDG_ReplayPanel Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ReplayPanel.WDG_ReplayPanel_C.BP_MouseLeave
// (Event, Public, BlueprintEvent)

void UWDG_ReplayPanel_C::BP_MouseLeave()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayPanel_C", "BP_MouseLeave");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ReplayPanel.WDG_ReplayPanel_C.BP_MouseOver
// (Event, Public, BlueprintEvent)

void UWDG_ReplayPanel_C::BP_MouseOver()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ReplayPanel_C", "BP_MouseOver");

	UObject::ProcessEvent(Func, nullptr);
}

}

