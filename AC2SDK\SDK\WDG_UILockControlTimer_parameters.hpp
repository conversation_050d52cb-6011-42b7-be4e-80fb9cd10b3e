﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_UILockControlTimer

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_UILockControlTimer.WDG_UILockControlTimer_C.ExecuteUbergraph_WDG_UILockControlTimer
// 0x00A8 (0x00A8 - 0x0000)
struct WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_getPlayerLockControlsTimeMs_ReturnValue;  // 0x0004(0x0004)(ZeroConstructor, <PERSON><PERSON><PERSON><PERSON>ldD<PERSON>, NoD<PERSON>ru<PERSON>, HasGetValueTypeHash)
	class FString                                 CallFunc_ConvertInt32ToFormattedTime_ReturnValue;  // 0x0008(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x0018(0x0018)()
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x0030(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          K2Node_Event_IsDesignTime;                         // 0x0031(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_32[0x6];                                       // 0x0032(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_GetText_ReturnValue;                      // 0x0038(0x0018)()
	class FText                                   CallFunc_TextToUpper_ReturnValue;                  // 0x0050(0x0018)()
	class AGameModeBase*                          CallFunc_GetGameMode_ReturnValue;                  // 0x0068(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class AAcRaceGameMode*                        K2Node_DynamicCast_AsAc_Race_Game_Mode;            // 0x0070(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0078(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_79[0x3];                                       // 0x0079(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	TDelegate<void()>                             K2Node_CreateDelegate_OutputDelegate;              // 0x007C(0x0010)(ZeroConstructor, NoDestructor)
	uint8                                         Pad_8C[0x4];                                       // 0x008C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FTimerHandle                           CallFunc_K2_SetTimerDelegate_ReturnValue;          // 0x0090(0x0008)(NoDestructor, HasGetValueTypeHash)
	EPlayerLockType                               CallFunc_getPlayerLockControlsType_ReturnValue;    // 0x0098(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0099(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_9A[0x2];                                       // 0x009A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_getPlayerLockControlsTimeMs_ReturnValue_1; // 0x009C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x00A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_A1[0x3];                                       // 0x00A1(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x00A4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer) == 0x000008, "Wrong alignment on WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer");
static_assert(sizeof(WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer) == 0x0000A8, "Wrong size on WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer");
static_assert(offsetof(WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer, EntryPoint) == 0x000000, "Member 'WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer, CallFunc_getPlayerLockControlsTimeMs_ReturnValue) == 0x000004, "Member 'WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer::CallFunc_getPlayerLockControlsTimeMs_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer, CallFunc_ConvertInt32ToFormattedTime_ReturnValue) == 0x000008, "Member 'WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer::CallFunc_ConvertInt32ToFormattedTime_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer, CallFunc_Conv_StringToText_ReturnValue) == 0x000018, "Member 'WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer, CallFunc_Greater_IntInt_ReturnValue) == 0x000030, "Member 'WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer, K2Node_Event_IsDesignTime) == 0x000031, "Member 'WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer::K2Node_Event_IsDesignTime' has a wrong offset!");
static_assert(offsetof(WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer, CallFunc_GetText_ReturnValue) == 0x000038, "Member 'WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer::CallFunc_GetText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer, CallFunc_TextToUpper_ReturnValue) == 0x000050, "Member 'WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer::CallFunc_TextToUpper_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer, CallFunc_GetGameMode_ReturnValue) == 0x000068, "Member 'WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer::CallFunc_GetGameMode_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer, K2Node_DynamicCast_AsAc_Race_Game_Mode) == 0x000070, "Member 'WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer::K2Node_DynamicCast_AsAc_Race_Game_Mode' has a wrong offset!");
static_assert(offsetof(WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer, K2Node_DynamicCast_bSuccess) == 0x000078, "Member 'WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer, K2Node_CreateDelegate_OutputDelegate) == 0x00007C, "Member 'WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer, CallFunc_K2_SetTimerDelegate_ReturnValue) == 0x000090, "Member 'WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer::CallFunc_K2_SetTimerDelegate_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer, CallFunc_getPlayerLockControlsType_ReturnValue) == 0x000098, "Member 'WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer::CallFunc_getPlayerLockControlsType_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer, K2Node_SwitchEnum_CmpSuccess) == 0x000099, "Member 'WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer, CallFunc_getPlayerLockControlsTimeMs_ReturnValue_1) == 0x00009C, "Member 'WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer::CallFunc_getPlayerLockControlsTimeMs_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer, CallFunc_IsValid_ReturnValue) == 0x0000A0, "Member 'WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer, CallFunc_Add_IntInt_ReturnValue) == 0x0000A4, "Member 'WDG_UILockControlTimer_C_ExecuteUbergraph_WDG_UILockControlTimer::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");

// Function WDG_UILockControlTimer.WDG_UILockControlTimer_C.PreConstruct
// 0x0001 (0x0001 - 0x0000)
struct WDG_UILockControlTimer_C_PreConstruct final
{
public:
	bool                                          IsDesignTime;                                      // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_UILockControlTimer_C_PreConstruct) == 0x000001, "Wrong alignment on WDG_UILockControlTimer_C_PreConstruct");
static_assert(sizeof(WDG_UILockControlTimer_C_PreConstruct) == 0x000001, "Wrong size on WDG_UILockControlTimer_C_PreConstruct");
static_assert(offsetof(WDG_UILockControlTimer_C_PreConstruct, IsDesignTime) == 0x000000, "Member 'WDG_UILockControlTimer_C_PreConstruct::IsDesignTime' has a wrong offset!");

}

