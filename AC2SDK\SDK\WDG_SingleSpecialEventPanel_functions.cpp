﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SingleSpecialEventPanel

#include "Basic.hpp"

#include "WDG_SingleSpecialEventPanel_classes.hpp"
#include "WDG_SingleSpecialEventPanel_parameters.hpp"


namespace SDK
{

// Function WDG_SingleSpecialEventPanel.WDG_SingleSpecialEventPanel_C.ExecuteUbergraph_WDG_SingleSpecialEventPanel
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SingleSpecialEventPanel_C::ExecuteUbergraph_WDG_SingleSpecialEventPanel(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SingleSpecialEventPanel_C", "ExecuteUbergraph_WDG_SingleSpecialEventPanel");

	Params::WDG_SingleSpecialEventPanel_C_ExecuteUbergraph_WDG_SingleSpecialEventPanel Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SingleSpecialEventPanel.WDG_SingleSpecialEventPanel_C.OnPresetSet
// (Event, Protected, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FSpecialEventPreset&       Preset                                                 (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_SingleSpecialEventPanel_C::OnPresetSet(const struct FSpecialEventPreset& Preset)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SingleSpecialEventPanel_C", "OnPresetSet");

	Params::WDG_SingleSpecialEventPanel_C_OnPresetSet Parms{};

	Parms.Preset = std::move(Preset);

	UObject::ProcessEvent(Func, &Parms);
}

}

