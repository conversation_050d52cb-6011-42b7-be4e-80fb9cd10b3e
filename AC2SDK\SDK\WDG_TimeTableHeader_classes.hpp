﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TimeTableHeader

#include "Basic.hpp"

#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_TimeTableHeader.WDG_TimeTableHeader_C
// 0x0040 (0x02A0 - 0x0260)
class UWDG_TimeTableHeader_C final : public UUserWidget
{
public:
	class UHorizontalBox*                         CombinedQual;                                      // 0x0260(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWidgetSwitcher*                        FirstColumnSwitcher;                               // 0x0268(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         FullTimingInfo;                                    // 0x0270(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class USizeBox*                               Handicaps;                                         // 0x0278(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class USizeBox*                               LapCount;                                          // 0x0280(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         RaceTimingInfo;                                    // 0x0288(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWidgetSwitcher*                        TimingSwitcher;                                    // 0x0290(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtLapCount;                                       // 0x0298(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void SetSingleCarViewing(bool InBool);
	void SetShowingLaptimes(bool ShowFullTimingInfo, bool ShowTimingInfo);
	void SetCombinedLaps(bool IsCombinedLaps);
	void SetShowingHandicap(bool ShowHandicaps);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_TimeTableHeader_C">();
	}
	static class UWDG_TimeTableHeader_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_TimeTableHeader_C>();
	}
};
static_assert(alignof(UWDG_TimeTableHeader_C) == 0x000008, "Wrong alignment on UWDG_TimeTableHeader_C");
static_assert(sizeof(UWDG_TimeTableHeader_C) == 0x0002A0, "Wrong size on UWDG_TimeTableHeader_C");
static_assert(offsetof(UWDG_TimeTableHeader_C, CombinedQual) == 0x000260, "Member 'UWDG_TimeTableHeader_C::CombinedQual' has a wrong offset!");
static_assert(offsetof(UWDG_TimeTableHeader_C, FirstColumnSwitcher) == 0x000268, "Member 'UWDG_TimeTableHeader_C::FirstColumnSwitcher' has a wrong offset!");
static_assert(offsetof(UWDG_TimeTableHeader_C, FullTimingInfo) == 0x000270, "Member 'UWDG_TimeTableHeader_C::FullTimingInfo' has a wrong offset!");
static_assert(offsetof(UWDG_TimeTableHeader_C, Handicaps) == 0x000278, "Member 'UWDG_TimeTableHeader_C::Handicaps' has a wrong offset!");
static_assert(offsetof(UWDG_TimeTableHeader_C, LapCount) == 0x000280, "Member 'UWDG_TimeTableHeader_C::LapCount' has a wrong offset!");
static_assert(offsetof(UWDG_TimeTableHeader_C, RaceTimingInfo) == 0x000288, "Member 'UWDG_TimeTableHeader_C::RaceTimingInfo' has a wrong offset!");
static_assert(offsetof(UWDG_TimeTableHeader_C, TimingSwitcher) == 0x000290, "Member 'UWDG_TimeTableHeader_C::TimingSwitcher' has a wrong offset!");
static_assert(offsetof(UWDG_TimeTableHeader_C, txtLapCount) == 0x000298, "Member 'UWDG_TimeTableHeader_C::txtLapCount' has a wrong offset!");

}

