﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_MultiplayerAdvancedSettings

#include "Basic.hpp"


namespace SDK::Params
{

// Function WDG_MultiplayerAdvancedSettings.WDG_MultiplayerAdvancedSettings_C.ExecuteUbergraph_WDG_MultiplayerAdvancedSettings
// 0x0008 (0x0008 - 0x0000)
struct WDG_MultiplayerAdvancedSettings_C_ExecuteUbergraph_WDG_MultiplayerAdvancedSettings final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_CanPlay_Result;                           // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_CanPlay_Result_1;                         // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_CanPlay_Result_2;                         // 0x0006(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_MultiplayerAdvancedSettings_C_ExecuteUbergraph_WDG_MultiplayerAdvancedSettings) == 0x000004, "Wrong alignment on WDG_MultiplayerAdvancedSettings_C_ExecuteUbergraph_WDG_MultiplayerAdvancedSettings");
static_assert(sizeof(WDG_MultiplayerAdvancedSettings_C_ExecuteUbergraph_WDG_MultiplayerAdvancedSettings) == 0x000008, "Wrong size on WDG_MultiplayerAdvancedSettings_C_ExecuteUbergraph_WDG_MultiplayerAdvancedSettings");
static_assert(offsetof(WDG_MultiplayerAdvancedSettings_C_ExecuteUbergraph_WDG_MultiplayerAdvancedSettings, EntryPoint) == 0x000000, "Member 'WDG_MultiplayerAdvancedSettings_C_ExecuteUbergraph_WDG_MultiplayerAdvancedSettings::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerAdvancedSettings_C_ExecuteUbergraph_WDG_MultiplayerAdvancedSettings, CallFunc_CanPlay_Result) == 0x000004, "Member 'WDG_MultiplayerAdvancedSettings_C_ExecuteUbergraph_WDG_MultiplayerAdvancedSettings::CallFunc_CanPlay_Result' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerAdvancedSettings_C_ExecuteUbergraph_WDG_MultiplayerAdvancedSettings, CallFunc_CanPlay_Result_1) == 0x000005, "Member 'WDG_MultiplayerAdvancedSettings_C_ExecuteUbergraph_WDG_MultiplayerAdvancedSettings::CallFunc_CanPlay_Result_1' has a wrong offset!");
static_assert(offsetof(WDG_MultiplayerAdvancedSettings_C_ExecuteUbergraph_WDG_MultiplayerAdvancedSettings, CallFunc_CanPlay_Result_2) == 0x000006, "Member 'WDG_MultiplayerAdvancedSettings_C_ExecuteUbergraph_WDG_MultiplayerAdvancedSettings::CallFunc_CanPlay_Result_2' has a wrong offset!");

}

