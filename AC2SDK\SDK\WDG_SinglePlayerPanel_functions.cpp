﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SinglePlayerPanel

#include "Basic.hpp"

#include "WDG_SinglePlayerPanel_classes.hpp"
#include "WDG_SinglePlayerPanel_parameters.hpp"


namespace SDK
{

// Function WDG_SinglePlayerPanel.WDG_SinglePlayerPanel_C.ExecuteUbergraph_WDG_SinglePlayerPanel
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SinglePlayerPanel_C::ExecuteUbergraph_WDG_SinglePlayerPanel(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPanel_C", "ExecuteUbergraph_WDG_SinglePlayerPanel");

	Params::WDG_SinglePlayerPanel_C_ExecuteUbergraph_WDG_SinglePlayerPanel Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SinglePlayerPanel.WDG_SinglePlayerPanel_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SinglePlayerPanel_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPanel_C", "PreConstruct");

	Params::WDG_SinglePlayerPanel_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SinglePlayerPanel.WDG_SinglePlayerPanel_C.BP_MouseLeave
// (Event, Public, BlueprintEvent)

void UWDG_SinglePlayerPanel_C::BP_MouseLeave()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPanel_C", "BP_MouseLeave");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SinglePlayerPanel.WDG_SinglePlayerPanel_C.BP_MouseOver
// (Event, Public, BlueprintEvent)

void UWDG_SinglePlayerPanel_C::BP_MouseOver()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SinglePlayerPanel_C", "BP_MouseOver");

	UObject::ProcessEvent(Func, nullptr);
}

}

