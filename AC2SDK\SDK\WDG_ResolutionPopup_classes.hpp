﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ResolutionPopup

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ResolutionPopup.WDG_ResolutionPopup_C
// 0x0048 (0x05C8 - 0x0580)
class UWDG_ResolutionPopup_C final : public UResolutionPopup
{
public:
	class UBackgroundBlur*                        BackgroundBlur_22;                                 // 0x0580(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 BackgroundTransparency;                            // 0x0588(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoD<PERSON>ru<PERSON>, PersistentInstance, HasGetValueTypeHash)
	class UWDG_CancelButton_C*                    Cancel;                                            // 0x0590(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           CanvasPosition;                                    // 0x0598(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ConfirmButton_C*                   Confirm;                                           // 0x05A0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             Message;                                           // 0x05A8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             Title;                                             // 0x05B0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	TMulticastInlineDelegate<void(const struct FVector2D& NewParam)> WDG_PopUp_UpdatePosition;       // 0x05B8(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ResolutionPopup_C">();
	}
	static class UWDG_ResolutionPopup_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ResolutionPopup_C>();
	}
};
static_assert(alignof(UWDG_ResolutionPopup_C) == 0x000008, "Wrong alignment on UWDG_ResolutionPopup_C");
static_assert(sizeof(UWDG_ResolutionPopup_C) == 0x0005C8, "Wrong size on UWDG_ResolutionPopup_C");
static_assert(offsetof(UWDG_ResolutionPopup_C, BackgroundBlur_22) == 0x000580, "Member 'UWDG_ResolutionPopup_C::BackgroundBlur_22' has a wrong offset!");
static_assert(offsetof(UWDG_ResolutionPopup_C, BackgroundTransparency) == 0x000588, "Member 'UWDG_ResolutionPopup_C::BackgroundTransparency' has a wrong offset!");
static_assert(offsetof(UWDG_ResolutionPopup_C, Cancel) == 0x000590, "Member 'UWDG_ResolutionPopup_C::Cancel' has a wrong offset!");
static_assert(offsetof(UWDG_ResolutionPopup_C, CanvasPosition) == 0x000598, "Member 'UWDG_ResolutionPopup_C::CanvasPosition' has a wrong offset!");
static_assert(offsetof(UWDG_ResolutionPopup_C, Confirm) == 0x0005A0, "Member 'UWDG_ResolutionPopup_C::Confirm' has a wrong offset!");
static_assert(offsetof(UWDG_ResolutionPopup_C, Message) == 0x0005A8, "Member 'UWDG_ResolutionPopup_C::Message' has a wrong offset!");
static_assert(offsetof(UWDG_ResolutionPopup_C, Title) == 0x0005B0, "Member 'UWDG_ResolutionPopup_C::Title' has a wrong offset!");
static_assert(offsetof(UWDG_ResolutionPopup_C, WDG_PopUp_UpdatePosition) == 0x0005B8, "Member 'UWDG_ResolutionPopup_C::WDG_PopUp_UpdatePosition' has a wrong offset!");

}

