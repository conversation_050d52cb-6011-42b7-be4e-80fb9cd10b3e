﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_YesNoPopup

#include "Basic.hpp"

#include "WDG_YesNoPopup_classes.hpp"
#include "WDG_YesNoPopup_parameters.hpp"


namespace SDK
{

// Function WDG_YesNoPopup.WDG_YesNoPopup_C.ExecuteUbergraph_WDG_YesNoPopup
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_YesNoPopup_C::ExecuteUbergraph_WDG_YesNoPopup(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_YesNoPopup_C", "ExecuteUbergraph_WDG_YesNoPopup");

	Params::WDG_YesNoPopup_C_ExecuteUbergraph_WDG_YesNoPopup Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_YesNoPopup.WDG_YesNoPopup_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_YesNoPopup_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_YesNoPopup_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_YesNoPopup.WDG_YesNoPopup_C.OnAddedToFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_YesNoPopup_C::OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_YesNoPopup_C", "OnAddedToFocusPath");

	Params::WDG_YesNoPopup_C_OnAddedToFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}

}

