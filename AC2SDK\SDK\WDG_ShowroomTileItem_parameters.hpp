﻿#pragma once

/*
* SDK generated by <PERSON><PERSON>-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomTileItem

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "CoreUObject_structs.hpp"
#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_ShowroomTileItem.WDG_ShowroomTileItem_C.ExecuteUbergraph_WDG_ShowroomTileItem
// 0x0220 (0x0220 - 0x0000)
struct WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0004(0x0001)(ZeroConstructor, Is<PERSON>lainOld<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	uint8                                         Pad_5[0x3];                                        // 0x0005(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FModelInfo                             K2Node_Event_ModelInfo;                            // 0x0008(0x01A8)()
	ECarGroup                                     CallFunc_GetCarGroup_ReturnValue;                  // 0x01B0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_NotEqual_ByteByte_ReturnValue;            // 0x01B1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1B2[0x2];                                      // 0x01B2(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           CallFunc_GetCarGroupColor_Color;                   // 0x01B4(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_GetCarGroupColor_Color_1;                 // 0x01C4(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_1D4[0x4];                                      // 0x01D4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_CarGroupToText_Output;                    // 0x01D8(0x0018)()
	struct FSlateColor                            K2Node_MakeStruct_SlateColor;                      // 0x01F0(0x0028)(UObjectWrapper)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0218(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem) == 0x000008, "Wrong alignment on WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem");
static_assert(sizeof(WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem) == 0x000220, "Wrong size on WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem");
static_assert(offsetof(WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem, EntryPoint) == 0x000000, "Member 'WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem, CallFunc_MakeLiteralByte_ReturnValue) == 0x000004, "Member 'WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem, K2Node_Event_ModelInfo) == 0x000008, "Member 'WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem::K2Node_Event_ModelInfo' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem, CallFunc_GetCarGroup_ReturnValue) == 0x0001B0, "Member 'WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem::CallFunc_GetCarGroup_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem, CallFunc_NotEqual_ByteByte_ReturnValue) == 0x0001B1, "Member 'WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem::CallFunc_NotEqual_ByteByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem, CallFunc_GetCarGroupColor_Color) == 0x0001B4, "Member 'WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem::CallFunc_GetCarGroupColor_Color' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem, CallFunc_GetCarGroupColor_Color_1) == 0x0001C4, "Member 'WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem::CallFunc_GetCarGroupColor_Color_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem, CallFunc_CarGroupToText_Output) == 0x0001D8, "Member 'WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem::CallFunc_CarGroupToText_Output' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem, K2Node_MakeStruct_SlateColor) == 0x0001F0, "Member 'WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem::K2Node_MakeStruct_SlateColor' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000218, "Member 'WDG_ShowroomTileItem_C_ExecuteUbergraph_WDG_ShowroomTileItem::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");

// Function WDG_ShowroomTileItem.WDG_ShowroomTileItem_C.UpdateModel
// 0x01A8 (0x01A8 - 0x0000)
struct WDG_ShowroomTileItem_C_UpdateModel final
{
public:
	struct FModelInfo                             ModelInfo_0;                                       // 0x0000(0x01A8)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_ShowroomTileItem_C_UpdateModel) == 0x000008, "Wrong alignment on WDG_ShowroomTileItem_C_UpdateModel");
static_assert(sizeof(WDG_ShowroomTileItem_C_UpdateModel) == 0x0001A8, "Wrong size on WDG_ShowroomTileItem_C_UpdateModel");
static_assert(offsetof(WDG_ShowroomTileItem_C_UpdateModel, ModelInfo_0) == 0x000000, "Member 'WDG_ShowroomTileItem_C_UpdateModel::ModelInfo_0' has a wrong offset!");

}

