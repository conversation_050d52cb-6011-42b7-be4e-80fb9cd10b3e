﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RaceRatingDetailCompetition

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RaceRatingDetailCompetition.WDG_RaceRatingDetailCompetition_C
// 0x0000 (0x0280 - 0x0280)
class UWDG_RaceRatingDetailCompetition_C final : public URatingDetailCompetition
{
public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RaceRatingDetailCompetition_C">();
	}
	static class UWDG_RaceRatingDetailCompetition_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RaceRatingDetailCompetition_C>();
	}
};
static_assert(alignof(UWDG_RaceRatingDetailCompetition_C) == 0x000008, "Wrong alignment on UWDG_RaceRatingDetailCompetition_C");
static_assert(sizeof(UWDG_RaceRatingDetailCompetition_C) == 0x000280, "Wrong size on UWDG_RaceRatingDetailCompetition_C");

}

