﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WPB_RaceTransitionPageNew

#include "Basic.hpp"

#include "WPB_RaceTransitionPageNew_classes.hpp"
#include "WPB_RaceTransitionPageNew_parameters.hpp"


namespace SDK
{

// Function WPB_RaceTransitionPageNew.WPB_RaceTransitionPageNew_C.ExecuteUbergraph_WPB_RaceTransitionPageNew
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWPB_RaceTransitionPageNew_C::ExecuteUbergraph_WPB_RaceTransitionPageNew(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WPB_RaceTransitionPageNew_C", "ExecuteUbergraph_WPB_RaceTransitionPageNew");

	Params::WPB_RaceTransitionPageNew_C_ExecuteUbergraph_WPB_RaceTransitionPageNew Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WPB_RaceTransitionPageNew.WPB_RaceTransitionPageNew_C.BP_StartPage
// (Event, Public, BlueprintEvent)

void UWPB_RaceTransitionPageNew_C::BP_StartPage()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WPB_RaceTransitionPageNew_C", "BP_StartPage");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WPB_RaceTransitionPageNew.WPB_RaceTransitionPageNew_C.Tick
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// float                                   InDeltaTime                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWPB_RaceTransitionPageNew_C::Tick(const struct FGeometry& MyGeometry, float InDeltaTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WPB_RaceTransitionPageNew_C", "Tick");

	Params::WPB_RaceTransitionPageNew_C_Tick Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InDeltaTime = InDeltaTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WPB_RaceTransitionPageNew.WPB_RaceTransitionPageNew_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWPB_RaceTransitionPageNew_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WPB_RaceTransitionPageNew_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}

}

