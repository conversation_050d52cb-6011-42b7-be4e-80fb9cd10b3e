﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_OptionsPanel

#include "Basic.hpp"

#include "WDG_OptionsPanel_classes.hpp"
#include "WDG_OptionsPanel_parameters.hpp"


namespace SDK
{

// Function WDG_OptionsPanel.WDG_OptionsPanel_C.ExecuteUbergraph_WDG_OptionsPanel
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_OptionsPanel_C::ExecuteUbergraph_WDG_OptionsPanel(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_OptionsPanel_C", "ExecuteUbergraph_WDG_OptionsPanel");

	Params::WDG_OptionsPanel_C_ExecuteUbergraph_WDG_OptionsPanel Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_OptionsPanel.WDG_OptionsPanel_C.BP_MouseLeave
// (Event, Public, BlueprintEvent)

void UWDG_OptionsPanel_C::BP_MouseLeave()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_OptionsPanel_C", "BP_MouseLeave");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_OptionsPanel.WDG_OptionsPanel_C.BP_MouseOver
// (Event, Public, BlueprintEvent)

void UWDG_OptionsPanel_C::BP_MouseOver()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_OptionsPanel_C", "BP_MouseOver");

	UObject::ProcessEvent(Func, nullptr);
}

}

