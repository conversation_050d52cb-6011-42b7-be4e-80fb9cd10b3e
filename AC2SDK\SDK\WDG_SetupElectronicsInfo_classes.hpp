﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SetupElectronicsInfo

#include "Basic.hpp"

#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SetupElectronicsInfo.WDG_SetupElectronicsInfo_C
// 0x0050 (0x02B0 - 0x0260)
class UWDG_SetupElectronicsInfo_C final : public UUserWidget
{
public:
	class UImage*                                 background;                                        // 0x0260(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_0;                                           // 0x0268(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_2;                                           // 0x0270(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_146;                                         // 0x0278(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_325;                                         // 0x0280(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_326;                                         // 0x0288(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             NamedSlot_1;                                       // 0x0290(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             OMItitle;                                          // 0x0298(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             PsiTitle;                                          // 0x02A0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             WearSlot;                                          // 0x02A8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SetupElectronicsInfo_C">();
	}
	static class UWDG_SetupElectronicsInfo_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SetupElectronicsInfo_C>();
	}
};
static_assert(alignof(UWDG_SetupElectronicsInfo_C) == 0x000008, "Wrong alignment on UWDG_SetupElectronicsInfo_C");
static_assert(sizeof(UWDG_SetupElectronicsInfo_C) == 0x0002B0, "Wrong size on UWDG_SetupElectronicsInfo_C");
static_assert(offsetof(UWDG_SetupElectronicsInfo_C, background) == 0x000260, "Member 'UWDG_SetupElectronicsInfo_C::background' has a wrong offset!");
static_assert(offsetof(UWDG_SetupElectronicsInfo_C, Image_0) == 0x000268, "Member 'UWDG_SetupElectronicsInfo_C::Image_0' has a wrong offset!");
static_assert(offsetof(UWDG_SetupElectronicsInfo_C, Image_2) == 0x000270, "Member 'UWDG_SetupElectronicsInfo_C::Image_2' has a wrong offset!");
static_assert(offsetof(UWDG_SetupElectronicsInfo_C, Image_146) == 0x000278, "Member 'UWDG_SetupElectronicsInfo_C::Image_146' has a wrong offset!");
static_assert(offsetof(UWDG_SetupElectronicsInfo_C, Image_325) == 0x000280, "Member 'UWDG_SetupElectronicsInfo_C::Image_325' has a wrong offset!");
static_assert(offsetof(UWDG_SetupElectronicsInfo_C, Image_326) == 0x000288, "Member 'UWDG_SetupElectronicsInfo_C::Image_326' has a wrong offset!");
static_assert(offsetof(UWDG_SetupElectronicsInfo_C, NamedSlot_1) == 0x000290, "Member 'UWDG_SetupElectronicsInfo_C::NamedSlot_1' has a wrong offset!");
static_assert(offsetof(UWDG_SetupElectronicsInfo_C, OMItitle) == 0x000298, "Member 'UWDG_SetupElectronicsInfo_C::OMItitle' has a wrong offset!");
static_assert(offsetof(UWDG_SetupElectronicsInfo_C, PsiTitle) == 0x0002A0, "Member 'UWDG_SetupElectronicsInfo_C::PsiTitle' has a wrong offset!");
static_assert(offsetof(UWDG_SetupElectronicsInfo_C, WearSlot) == 0x0002A8, "Member 'UWDG_SetupElectronicsInfo_C::WearSlot' has a wrong offset!");

}

