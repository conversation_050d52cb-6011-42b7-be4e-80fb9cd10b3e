﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RatingDetailPCLeaderboardSeasonItem

#include "Basic.hpp"

#include "WDG_RatingDetailPCLeaderboardSeasonItem_classes.hpp"
#include "WDG_RatingDetailPCLeaderboardSeasonItem_parameters.hpp"


namespace SDK
{

// Function WDG_RatingDetailPCLeaderboardSeasonItem.WDG_RatingDetailPCLeaderboardSeasonItem_C.SetSeasonNo
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// int32                                   Season                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_RatingDetailPCLeaderboardSeasonItem_C::SetSeasonNo(int32 Season)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_RatingDetailPCLeaderboardSeasonItem_C", "SetSeasonNo");

	Params::WDG_RatingDetailPCLeaderboardSeasonItem_C_SetSeasonNo Parms{};

	Parms.Season = Season;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_RatingDetailPCLeaderboardSeasonItem.WDG_RatingDetailPCLeaderboardSeasonItem_C.AddLeaderboard
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FOnlineServicesLeaderboardRank&Rank                                                   (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_RatingDetailPCLeaderboardSeasonItem_C::AddLeaderboard(const struct FOnlineServicesLeaderboardRank& Rank)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_RatingDetailPCLeaderboardSeasonItem_C", "AddLeaderboard");

	Params::WDG_RatingDetailPCLeaderboardSeasonItem_C_AddLeaderboard Parms{};

	Parms.Rank = std::move(Rank);

	UObject::ProcessEvent(Func, &Parms);
}

}

