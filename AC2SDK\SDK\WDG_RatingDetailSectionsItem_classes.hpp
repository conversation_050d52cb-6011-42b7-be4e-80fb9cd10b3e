﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_RatingDetailSectionsItem

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_RatingDetailSectionsItem.WDG_RatingDetailSectionsItem_C
// 0x00B8 (0x0368 - 0x02B0)
class UWDG_RatingDetailSectionsItem_C final : public URatingDetailSectionsItem
{
public:
	class UWidgetAnimation*                       PopupHint;                                         // 0x02B0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 Image_0;                                           // 0x02B8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoD<PERSON>ru<PERSON>, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_1;                                           // 0x02C0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_83;                                          // 0x02C8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_202;                                         // 0x02D0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_203;                                         // 0x02D8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_283;                                         // 0x02E0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_284;                                         // 0x02E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_285;                                         // 0x02F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_286;                                         // 0x02F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_287;                                         // 0x0300(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_288;                                         // 0x0308(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_289;                                         // 0x0310(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_290;                                         // 0x0318(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_291;                                         // 0x0320(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_292;                                         // 0x0328(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_293;                                         // 0x0330(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_294;                                         // 0x0338(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_295;                                         // 0x0340(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_296;                                         // 0x0348(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_297;                                         // 0x0350(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_298;                                         // 0x0358(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_327;                                         // 0x0360(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_RatingDetailSectionsItem_C">();
	}
	static class UWDG_RatingDetailSectionsItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_RatingDetailSectionsItem_C>();
	}
};
static_assert(alignof(UWDG_RatingDetailSectionsItem_C) == 0x000008, "Wrong alignment on UWDG_RatingDetailSectionsItem_C");
static_assert(sizeof(UWDG_RatingDetailSectionsItem_C) == 0x000368, "Wrong size on UWDG_RatingDetailSectionsItem_C");
static_assert(offsetof(UWDG_RatingDetailSectionsItem_C, PopupHint) == 0x0002B0, "Member 'UWDG_RatingDetailSectionsItem_C::PopupHint' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailSectionsItem_C, Image_0) == 0x0002B8, "Member 'UWDG_RatingDetailSectionsItem_C::Image_0' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailSectionsItem_C, Image_1) == 0x0002C0, "Member 'UWDG_RatingDetailSectionsItem_C::Image_1' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailSectionsItem_C, Image_83) == 0x0002C8, "Member 'UWDG_RatingDetailSectionsItem_C::Image_83' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailSectionsItem_C, Image_202) == 0x0002D0, "Member 'UWDG_RatingDetailSectionsItem_C::Image_202' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailSectionsItem_C, Image_203) == 0x0002D8, "Member 'UWDG_RatingDetailSectionsItem_C::Image_203' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailSectionsItem_C, Image_283) == 0x0002E0, "Member 'UWDG_RatingDetailSectionsItem_C::Image_283' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailSectionsItem_C, Image_284) == 0x0002E8, "Member 'UWDG_RatingDetailSectionsItem_C::Image_284' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailSectionsItem_C, Image_285) == 0x0002F0, "Member 'UWDG_RatingDetailSectionsItem_C::Image_285' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailSectionsItem_C, Image_286) == 0x0002F8, "Member 'UWDG_RatingDetailSectionsItem_C::Image_286' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailSectionsItem_C, Image_287) == 0x000300, "Member 'UWDG_RatingDetailSectionsItem_C::Image_287' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailSectionsItem_C, Image_288) == 0x000308, "Member 'UWDG_RatingDetailSectionsItem_C::Image_288' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailSectionsItem_C, Image_289) == 0x000310, "Member 'UWDG_RatingDetailSectionsItem_C::Image_289' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailSectionsItem_C, Image_290) == 0x000318, "Member 'UWDG_RatingDetailSectionsItem_C::Image_290' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailSectionsItem_C, Image_291) == 0x000320, "Member 'UWDG_RatingDetailSectionsItem_C::Image_291' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailSectionsItem_C, Image_292) == 0x000328, "Member 'UWDG_RatingDetailSectionsItem_C::Image_292' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailSectionsItem_C, Image_293) == 0x000330, "Member 'UWDG_RatingDetailSectionsItem_C::Image_293' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailSectionsItem_C, Image_294) == 0x000338, "Member 'UWDG_RatingDetailSectionsItem_C::Image_294' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailSectionsItem_C, Image_295) == 0x000340, "Member 'UWDG_RatingDetailSectionsItem_C::Image_295' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailSectionsItem_C, Image_296) == 0x000348, "Member 'UWDG_RatingDetailSectionsItem_C::Image_296' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailSectionsItem_C, Image_297) == 0x000350, "Member 'UWDG_RatingDetailSectionsItem_C::Image_297' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailSectionsItem_C, Image_298) == 0x000358, "Member 'UWDG_RatingDetailSectionsItem_C::Image_298' has a wrong offset!");
static_assert(offsetof(UWDG_RatingDetailSectionsItem_C, Image_327) == 0x000360, "Member 'UWDG_RatingDetailSectionsItem_C::Image_327' has a wrong offset!");

}

