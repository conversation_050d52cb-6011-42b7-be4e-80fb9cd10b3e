﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SpecialEventItem

#include "Basic.hpp"

#include "WDG_SpecialEventItem_classes.hpp"
#include "WDG_SpecialEventItem_parameters.hpp"


namespace SDK
{

// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.ExecuteUbergraph_WDG_SpecialEventItem
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SpecialEventItem_C::ExecuteUbergraph_WDG_SpecialEventItem(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventItem_C", "ExecuteUbergraph_WDG_SpecialEventItem");

	Params::WDG_SpecialEventItem_C_ExecuteUbergraph_WDG_SpecialEventItem Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.PreConstruct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// bool                                    IsDesignTime                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SpecialEventItem_C::PreConstruct(bool IsDesignTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventItem_C", "PreConstruct");

	Params::WDG_SpecialEventItem_C_PreConstruct Parms{};

	Parms.IsDesignTime = IsDesignTime;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.BP_MouseFocus
// (Event, Public, BlueprintEvent)
// Parameters:
// bool                                    mouse_enter                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SpecialEventItem_C::BP_MouseFocus(bool mouse_enter)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventItem_C", "BP_MouseFocus");

	Params::WDG_SpecialEventItem_C_BP_MouseFocus Parms{};

	Parms.mouse_enter = mouse_enter;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.ForwardM
// (BlueprintCallable, BlueprintEvent)
// Parameters:
// class UAcPanelBase*                     panel                                                  (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    mouse_over                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SpecialEventItem_C::ForwardM(class UAcPanelBase* panel, bool mouse_over)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventItem_C", "ForwardM");

	Params::WDG_SpecialEventItem_C_ForwardM Parms{};

	Parms.panel = panel;
	Parms.mouse_over = mouse_over;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_SpecialEventItem_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventItem_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.OnRemovedFromFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_SpecialEventItem_C::OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventItem_C", "OnRemovedFromFocusPath");

	Params::WDG_SpecialEventItem_C_OnRemovedFromFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.OnAfterConstruct
// (Event, Public, BlueprintEvent)

void UWDG_SpecialEventItem_C::OnAfterConstruct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventItem_C", "OnAfterConstruct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.OnAddedToFocusPath
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FFocusEvent&               InFocusEvent                                           (BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)

void UWDG_SpecialEventItem_C::OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventItem_C", "OnAddedToFocusPath");

	Params::WDG_SpecialEventItem_C_OnAddedToFocusPath Parms{};

	Parms.InFocusEvent = std::move(InFocusEvent);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.BP_MouseLeave
// (Event, Public, BlueprintEvent)

void UWDG_SpecialEventItem_C::BP_MouseLeave()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventItem_C", "BP_MouseLeave");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.BP_MouseOver
// (Event, Public, BlueprintEvent)

void UWDG_SpecialEventItem_C::BP_MouseOver()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventItem_C", "BP_MouseOver");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.GetPresetData
// (Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// class FText*                            Car_Model_Name                                         (Parm, OutParm)
// EBrandType*                             Brand                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// class FText*                            Track_Name                                             (Parm, OutParm)
// class FText*                            GameMode                                               (Parm, OutParm)
// TSoftObjectPtr<class UTexture2D>*       teamLogo                                               (Parm, OutParm, HasGetValueTypeHash)

void UWDG_SpecialEventItem_C::GetPresetData(class FText* Car_Model_Name, EBrandType* Brand, class FText* Track_Name, class FText* GameMode, TSoftObjectPtr<class UTexture2D>* teamLogo)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventItem_C", "GetPresetData");

	Params::WDG_SpecialEventItem_C_GetPresetData Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Car_Model_Name != nullptr)
		*Car_Model_Name = std::move(Parms.Car_Model_Name);

	if (Brand != nullptr)
		*Brand = Parms.Brand;

	if (Track_Name != nullptr)
		*Track_Name = std::move(Parms.Track_Name);

	if (GameMode != nullptr)
		*GameMode = std::move(Parms.GameMode);

	if (teamLogo != nullptr)
		*teamLogo = Parms.teamLogo;
}


// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.SetColors
// (Public, BlueprintCallable, BlueprintEvent)

void UWDG_SpecialEventItem_C::SetColors()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventItem_C", "SetColors");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.GetMainColor
// (Public, HasOutParams, BlueprintCallable, BlueprintEvent, BlueprintPure)
// Parameters:
// struct FLinearColor*                    Value                                                  (Parm, OutParm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SpecialEventItem_C::GetMainColor(struct FLinearColor* Value)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventItem_C", "GetMainColor");

	Params::WDG_SpecialEventItem_C_GetMainColor Parms{};

	UObject::ProcessEvent(Func, &Parms);

	if (Value != nullptr)
		*Value = std::move(Parms.Value);
}


// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.SetBackgroundImage
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)

void UWDG_SpecialEventItem_C::SetBackgroundImage()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventItem_C", "SetBackgroundImage");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.SetSelected
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// bool                                    IsSelected_0                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_SpecialEventItem_C::SetSelected(bool IsSelected_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventItem_C", "SetSelected");

	Params::WDG_SpecialEventItem_C_SetSelected Parms{};

	Parms.IsSelected_0 = IsSelected_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.UpdateFromPreset
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FSpecialEventPreset&       SpecialEvent_0                                         (BlueprintVisible, BlueprintReadOnly, Parm)

void UWDG_SpecialEventItem_C::UpdateFromPreset(const struct FSpecialEventPreset& SpecialEvent_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventItem_C", "UpdateFromPreset");

	Params::WDG_SpecialEventItem_C_UpdateFromPreset Parms{};

	Parms.SpecialEvent_0 = std::move(SpecialEvent_0);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.ResizeTextFont
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UTextBlock*                       text                                                   (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// int32                                   Size_0                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SpecialEventItem_C::ResizeTextFont(class UTextBlock* text, int32 Size_0)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventItem_C", "ResizeTextFont");

	Params::WDG_SpecialEventItem_C_ResizeTextFont Parms{};

	Parms.text = text;
	Parms.Size_0 = Size_0;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.imgZoomedOut
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UImage*                           Target                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SpecialEventItem_C::imgZoomedOut(class UImage* Target)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventItem_C", "imgZoomedOut");

	Params::WDG_SpecialEventItem_C_imgZoomedOut Parms{};

	Parms.Target = Target;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.imgZoomedIn
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UImage*                           Target                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SpecialEventItem_C::imgZoomedIn(class UImage* Target)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventItem_C", "imgZoomedIn");

	Params::WDG_SpecialEventItem_C_imgZoomedIn Parms{};

	Parms.Target = Target;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_0
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UImage*                           Target                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SpecialEventItem_C::SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_0(class UImage* Target)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventItem_C", "SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_0");

	Params::WDG_SpecialEventItem_C_SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_0 Parms{};

	Parms.Target = Target;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SpecialEventItem.WDG_SpecialEventItem_C.SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_1
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UImage*                           Target                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SpecialEventItem_C::SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_1(class UImage* Target)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SpecialEventItem_C", "SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_1");

	Params::WDG_SpecialEventItem_C_SequenceEvent__ENTRYPOINTWDG_SpecialEventItem_1 Parms{};

	Parms.Target = Target;

	UObject::ProcessEvent(Func, &Parms);
}

}

