﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ScalableTextBox

#include "Basic.hpp"

#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_ScalableTextBox.WDG_ScalableTextBox_C.ExecuteUbergraph_WDG_ScalableTextBox
// 0x0004 (0x0004 - 0x0000)
struct WDG_ScalableTextBox_C_ExecuteUbergraph_WDG_ScalableTextBox final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ScalableTextBox_C_ExecuteUbergraph_WDG_ScalableTextBox) == 0x000004, "Wrong alignment on WDG_ScalableTextBox_C_ExecuteUbergraph_WDG_ScalableTextBox");
static_assert(sizeof(WDG_ScalableTextBox_C_ExecuteUbergraph_WDG_ScalableTextBox) == 0x000004, "Wrong size on WDG_ScalableTextBox_C_ExecuteUbergraph_WDG_ScalableTextBox");
static_assert(offsetof(WDG_ScalableTextBox_C_ExecuteUbergraph_WDG_ScalableTextBox, EntryPoint) == 0x000000, "Member 'WDG_ScalableTextBox_C_ExecuteUbergraph_WDG_ScalableTextBox::EntryPoint' has a wrong offset!");

// Function WDG_ScalableTextBox.WDG_ScalableTextBox_C.AddText
// 0x0080 (0x0080 - 0x0000)
struct WDG_ScalableTextBox_C_AddText final
{
public:
	class FText                                   text;                                              // 0x0000(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm)
	int32                                         FontSize;                                          // 0x0018(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         FontDimension;                                     // 0x001C(0x0004)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSlateFontInfo                         K2Node_MakeStruct_SlateFontInfo;                   // 0x0020(0x0058)(UObjectWrapper, HasGetValueTypeHash)
	class UTextBlock*                             CallFunc_SpawnObject_ReturnValue;                  // 0x0078(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ScalableTextBox_C_AddText) == 0x000008, "Wrong alignment on WDG_ScalableTextBox_C_AddText");
static_assert(sizeof(WDG_ScalableTextBox_C_AddText) == 0x000080, "Wrong size on WDG_ScalableTextBox_C_AddText");
static_assert(offsetof(WDG_ScalableTextBox_C_AddText, text) == 0x000000, "Member 'WDG_ScalableTextBox_C_AddText::text' has a wrong offset!");
static_assert(offsetof(WDG_ScalableTextBox_C_AddText, FontSize) == 0x000018, "Member 'WDG_ScalableTextBox_C_AddText::FontSize' has a wrong offset!");
static_assert(offsetof(WDG_ScalableTextBox_C_AddText, FontDimension) == 0x00001C, "Member 'WDG_ScalableTextBox_C_AddText::FontDimension' has a wrong offset!");
static_assert(offsetof(WDG_ScalableTextBox_C_AddText, K2Node_MakeStruct_SlateFontInfo) == 0x000020, "Member 'WDG_ScalableTextBox_C_AddText::K2Node_MakeStruct_SlateFontInfo' has a wrong offset!");
static_assert(offsetof(WDG_ScalableTextBox_C_AddText, CallFunc_SpawnObject_ReturnValue) == 0x000078, "Member 'WDG_ScalableTextBox_C_AddText::CallFunc_SpawnObject_ReturnValue' has a wrong offset!");

// Function WDG_ScalableTextBox.WDG_ScalableTextBox_C.AddToVBox
// 0x0020 (0x0020 - 0x0000)
struct WDG_ScalableTextBox_C_AddToVBox final
{
public:
	class UWidget*                                Content;                                           // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FMargin                                K2Node_MakeStruct_Margin;                          // 0x0008(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	class UVerticalBoxSlot*                       CallFunc_AddChildToVerticalBox_ReturnValue;        // 0x0018(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ScalableTextBox_C_AddToVBox) == 0x000008, "Wrong alignment on WDG_ScalableTextBox_C_AddToVBox");
static_assert(sizeof(WDG_ScalableTextBox_C_AddToVBox) == 0x000020, "Wrong size on WDG_ScalableTextBox_C_AddToVBox");
static_assert(offsetof(WDG_ScalableTextBox_C_AddToVBox, Content) == 0x000000, "Member 'WDG_ScalableTextBox_C_AddToVBox::Content' has a wrong offset!");
static_assert(offsetof(WDG_ScalableTextBox_C_AddToVBox, K2Node_MakeStruct_Margin) == 0x000008, "Member 'WDG_ScalableTextBox_C_AddToVBox::K2Node_MakeStruct_Margin' has a wrong offset!");
static_assert(offsetof(WDG_ScalableTextBox_C_AddToVBox, CallFunc_AddChildToVerticalBox_ReturnValue) == 0x000018, "Member 'WDG_ScalableTextBox_C_AddToVBox::CallFunc_AddChildToVerticalBox_ReturnValue' has a wrong offset!");

}

