﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SectorTimeItem

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SectorTimeItem.WDG_SectorTimeItem_C
// 0x0010 (0x0288 - 0x0278)
class UWDG_SectorTimeItem_C final : public UTimeTableSectorTimeItem
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0278(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	bool                                          IsBold;                                            // 0x0280(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)

public:
	void ExecuteUbergraph_WDG_SectorTimeItem(int32 EntryPoint);
	void PreConstruct(bool IsDesignTime);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SectorTimeItem_C">();
	}
	static class UWDG_SectorTimeItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SectorTimeItem_C>();
	}
};
static_assert(alignof(UWDG_SectorTimeItem_C) == 0x000008, "Wrong alignment on UWDG_SectorTimeItem_C");
static_assert(sizeof(UWDG_SectorTimeItem_C) == 0x000288, "Wrong size on UWDG_SectorTimeItem_C");
static_assert(offsetof(UWDG_SectorTimeItem_C, UberGraphFrame) == 0x000278, "Member 'UWDG_SectorTimeItem_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SectorTimeItem_C, IsBold) == 0x000280, "Member 'UWDG_SectorTimeItem_C::IsBold' has a wrong offset!");

}

