﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_VideoOptionsPage

#include "Basic.hpp"

#include "WDG_VideoOptionsPage_classes.hpp"
#include "WDG_VideoOptionsPage_parameters.hpp"


namespace SDK
{

// Function WDG_VideoOptionsPage.WDG_VideoOptionsPage_C.ExecuteUbergraph_WDG_VideoOptionsPage
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_VideoOptionsPage_C::ExecuteUbergraph_WDG_VideoOptionsPage(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_VideoOptionsPage_C", "ExecuteUbergraph_WDG_VideoOptionsPage");

	Params::WDG_VideoOptionsPage_C_ExecuteUbergraph_WDG_VideoOptionsPage Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_VideoOptionsPage.WDG_VideoOptionsPage_C.BndEvt__filePanel_K2Node_ComponentBoundEvent_10_OnDeleteUserPreset__DelegateSignature
// (BlueprintEvent)
// Parameters:
// const class FString&                    Filename                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, HasGetValueTypeHash)

void UWDG_VideoOptionsPage_C::BndEvt__filePanel_K2Node_ComponentBoundEvent_10_OnDeleteUserPreset__DelegateSignature(const class FString& Filename)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_VideoOptionsPage_C", "BndEvt__filePanel_K2Node_ComponentBoundEvent_10_OnDeleteUserPreset__DelegateSignature");

	Params::WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_10_OnDeleteUserPreset__DelegateSignature Parms{};

	Parms.Filename = std::move(Filename);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_VideoOptionsPage.WDG_VideoOptionsPage_C.OnPanelFocused
// (Event, Public, BlueprintEvent)
// Parameters:
// class UAcPanelBase*                     panelOnFocus                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_VideoOptionsPage_C::OnPanelFocused(class UAcPanelBase* panelOnFocus)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_VideoOptionsPage_C", "OnPanelFocused");

	Params::WDG_VideoOptionsPage_C_OnPanelFocused Parms{};

	Parms.panelOnFocus = panelOnFocus;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_VideoOptionsPage.WDG_VideoOptionsPage_C.OnPopulatePresets
// (BlueprintCallable, BlueprintEvent)

void UWDG_VideoOptionsPage_C::OnPopulatePresets()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_VideoOptionsPage_C", "OnPopulatePresets");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_VideoOptionsPage.WDG_VideoOptionsPage_C.BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class FName                             Filename                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const class FString&                    DisplayName                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, HasGetValueTypeHash)

void UWDG_VideoOptionsPage_C::BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature(class FName Filename, const class FString& DisplayName)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_VideoOptionsPage_C", "BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature");

	Params::WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_5_OnLoadGenericPreset__DelegateSignature Parms{};

	Parms.Filename = Filename;
	Parms.DisplayName = std::move(DisplayName);

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_VideoOptionsPage.WDG_VideoOptionsPage_C.BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnSaveUserPreset__DelegateSignature
// (BlueprintEvent)
// Parameters:
// const class FString&                    Filename                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, HasGetValueTypeHash)
// bool                                    ExistingFile                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_VideoOptionsPage_C::BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnSaveUserPreset__DelegateSignature(const class FString& Filename, bool ExistingFile)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_VideoOptionsPage_C", "BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnSaveUserPreset__DelegateSignature");

	Params::WDG_VideoOptionsPage_C_BndEvt__filePanel_K2Node_ComponentBoundEvent_4_OnSaveUserPreset__DelegateSignature Parms{};

	Parms.Filename = std::move(Filename);
	Parms.ExistingFile = ExistingFile;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_VideoOptionsPage.WDG_VideoOptionsPage_C.BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_3_OnHide__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UAcPanelBase*                     CallingPanel                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    Cancelled                                              (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_VideoOptionsPage_C::BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_3_OnHide__DelegateSignature(class UAcPanelBase* CallingPanel, bool Cancelled)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_VideoOptionsPage_C", "BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_3_OnHide__DelegateSignature");

	Params::WDG_VideoOptionsPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_3_OnHide__DelegateSignature Parms{};

	Parms.CallingPanel = CallingPanel;
	Parms.Cancelled = Cancelled;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_VideoOptionsPage.WDG_VideoOptionsPage_C.BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnShow__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UAcPanelBase*                     CallingPanel                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_VideoOptionsPage_C::BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnShow__DelegateSignature(class UAcPanelBase* CallingPanel)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_VideoOptionsPage_C", "BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnShow__DelegateSignature");

	Params::WDG_VideoOptionsPage_C_BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnShow__DelegateSignature Parms{};

	Parms.CallingPanel = CallingPanel;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_VideoOptionsPage.WDG_VideoOptionsPage_C.BP_OnMenuNavigation
// (Event, Public, BlueprintEvent)
// Parameters:
// const EControllerActionType             Input                                                  (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// bool                                    isReleased                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)

void UWDG_VideoOptionsPage_C::BP_OnMenuNavigation(const EControllerActionType Input, bool isReleased)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_VideoOptionsPage_C", "BP_OnMenuNavigation");

	Params::WDG_VideoOptionsPage_C_BP_OnMenuNavigation Parms{};

	Parms.Input = Input;
	Parms.isReleased = isReleased;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_VideoOptionsPage.WDG_VideoOptionsPage_C.BndEvt__btnPresets_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature
// (BlueprintEvent)
// Parameters:
// class UWDG_GenericBarItem_C*            Sender                                                 (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_VideoOptionsPage_C::BndEvt__btnPresets_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature(class UWDG_GenericBarItem_C* Sender)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_VideoOptionsPage_C", "BndEvt__btnPresets_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature");

	Params::WDG_VideoOptionsPage_C_BndEvt__btnPresets_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature Parms{};

	Parms.Sender = Sender;

	UObject::ProcessEvent(Func, &Parms);
}

}

