﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SpecialEventList

#include "Basic.hpp"

#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_SpecialEventList.WDG_SpecialEventList_C.ExecuteUbergraph_WDG_SpecialEventList
// 0x000C (0x000C - 0x0000)
struct WDG_SpecialEventList_C_ExecuteUbergraph_WDG_SpecialEventList final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FFocusEvent                            K2Node_Event_InFocusEvent;                         // 0x0004(0x0008)(NoDestructor)
};
static_assert(alignof(WDG_SpecialEventList_C_ExecuteUbergraph_WDG_SpecialEventList) == 0x000004, "Wrong alignment on WDG_SpecialEventList_C_ExecuteUbergraph_WDG_SpecialEventList");
static_assert(sizeof(WDG_SpecialEventList_C_ExecuteUbergraph_WDG_SpecialEventList) == 0x00000C, "Wrong size on WDG_SpecialEventList_C_ExecuteUbergraph_WDG_SpecialEventList");
static_assert(offsetof(WDG_SpecialEventList_C_ExecuteUbergraph_WDG_SpecialEventList, EntryPoint) == 0x000000, "Member 'WDG_SpecialEventList_C_ExecuteUbergraph_WDG_SpecialEventList::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventList_C_ExecuteUbergraph_WDG_SpecialEventList, K2Node_Event_InFocusEvent) == 0x000004, "Member 'WDG_SpecialEventList_C_ExecuteUbergraph_WDG_SpecialEventList::K2Node_Event_InFocusEvent' has a wrong offset!");

// Function WDG_SpecialEventList.WDG_SpecialEventList_C.OnAddedToFocusPath
// 0x0008 (0x0008 - 0x0000)
struct WDG_SpecialEventList_C_OnAddedToFocusPath final
{
public:
	struct FFocusEvent                            InFocusEvent;                                      // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
};
static_assert(alignof(WDG_SpecialEventList_C_OnAddedToFocusPath) == 0x000004, "Wrong alignment on WDG_SpecialEventList_C_OnAddedToFocusPath");
static_assert(sizeof(WDG_SpecialEventList_C_OnAddedToFocusPath) == 0x000008, "Wrong size on WDG_SpecialEventList_C_OnAddedToFocusPath");
static_assert(offsetof(WDG_SpecialEventList_C_OnAddedToFocusPath, InFocusEvent) == 0x000000, "Member 'WDG_SpecialEventList_C_OnAddedToFocusPath::InFocusEvent' has a wrong offset!");

}

