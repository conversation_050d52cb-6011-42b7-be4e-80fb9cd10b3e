﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SetupPresetButton

#include "Basic.hpp"

#include "WDG_SetupPresetButton_classes.hpp"
#include "WDG_SetupPresetButton_parameters.hpp"


namespace SDK
{

// Function WDG_SetupPresetButton.WDG_SetupPresetButton_C.ExecuteUbergraph_WDG_SetupPresetButton
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_SetupPresetButton_C::ExecuteUbergraph_WDG_SetupPresetButton(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SetupPresetButton_C", "ExecuteUbergraph_WDG_SetupPresetButton");

	Params::WDG_SetupPresetButton_C_ExecuteUbergraph_WDG_SetupPresetButton Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_SetupPresetButton.WDG_SetupPresetButton_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_SetupPresetButton_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SetupPresetButton_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SetupPresetButton.WDG_SetupPresetButton_C.BP_MouseOver
// (Event, Public, BlueprintEvent)

void UWDG_SetupPresetButton_C::BP_MouseOver()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SetupPresetButton_C", "BP_MouseOver");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_SetupPresetButton.WDG_SetupPresetButton_C.BP_MouseLeave
// (Event, Public, BlueprintEvent)

void UWDG_SetupPresetButton_C::BP_MouseLeave()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_SetupPresetButton_C", "BP_MouseLeave");

	UObject::ProcessEvent(Func, nullptr);
}

}

