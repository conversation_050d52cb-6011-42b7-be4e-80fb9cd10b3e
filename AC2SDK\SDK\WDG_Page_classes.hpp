﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_Page

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "UMG_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_Page.WDG_Page_C
// 0x0078 (0x02D8 - 0x0260)
class UWDG_Page_C final : public UUserWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0260(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UNamedSlot*                             BackgroundSlot;                                    // 0x0268(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBackgroundBlur*                        bgrBlur;                                           // 0x0270(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                bgrDarken;                                         // 0x0278(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             BodySlot;                                          // 0x0280(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             FooterSlot;                                        // 0x0288(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             HeaderSlot;                                        // 0x0290(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             HelpSlot;                                          // 0x0298(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                holo;                                              // 0x02A0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             LeftSlot;                                          // 0x02A8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             ModalSlot;                                         // 0x02B0(0x0008)(ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 OPVideo_Background;                                // 0x02B8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_SessionCountdown_C*                SessionCount;                                      // 0x02C0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtCurrentGameVersion;                             // 0x02C8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	bool                                          EnableSessionCount;                                // 0x02D0(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          EnableGradientBgr;                                 // 0x02D1(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)

public:
	void ExecuteUbergraph_WDG_Page(int32 EntryPoint);
	void PreConstruct(bool IsDesignTime);
	void Construct();
	void UseBlurInsteadOfBackground();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_Page_C">();
	}
	static class UWDG_Page_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_Page_C>();
	}
};
static_assert(alignof(UWDG_Page_C) == 0x000008, "Wrong alignment on UWDG_Page_C");
static_assert(sizeof(UWDG_Page_C) == 0x0002D8, "Wrong size on UWDG_Page_C");
static_assert(offsetof(UWDG_Page_C, UberGraphFrame) == 0x000260, "Member 'UWDG_Page_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_Page_C, BackgroundSlot) == 0x000268, "Member 'UWDG_Page_C::BackgroundSlot' has a wrong offset!");
static_assert(offsetof(UWDG_Page_C, bgrBlur) == 0x000270, "Member 'UWDG_Page_C::bgrBlur' has a wrong offset!");
static_assert(offsetof(UWDG_Page_C, bgrDarken) == 0x000278, "Member 'UWDG_Page_C::bgrDarken' has a wrong offset!");
static_assert(offsetof(UWDG_Page_C, BodySlot) == 0x000280, "Member 'UWDG_Page_C::BodySlot' has a wrong offset!");
static_assert(offsetof(UWDG_Page_C, FooterSlot) == 0x000288, "Member 'UWDG_Page_C::FooterSlot' has a wrong offset!");
static_assert(offsetof(UWDG_Page_C, HeaderSlot) == 0x000290, "Member 'UWDG_Page_C::HeaderSlot' has a wrong offset!");
static_assert(offsetof(UWDG_Page_C, HelpSlot) == 0x000298, "Member 'UWDG_Page_C::HelpSlot' has a wrong offset!");
static_assert(offsetof(UWDG_Page_C, holo) == 0x0002A0, "Member 'UWDG_Page_C::holo' has a wrong offset!");
static_assert(offsetof(UWDG_Page_C, LeftSlot) == 0x0002A8, "Member 'UWDG_Page_C::LeftSlot' has a wrong offset!");
static_assert(offsetof(UWDG_Page_C, ModalSlot) == 0x0002B0, "Member 'UWDG_Page_C::ModalSlot' has a wrong offset!");
static_assert(offsetof(UWDG_Page_C, OPVideo_Background) == 0x0002B8, "Member 'UWDG_Page_C::OPVideo_Background' has a wrong offset!");
static_assert(offsetof(UWDG_Page_C, SessionCount) == 0x0002C0, "Member 'UWDG_Page_C::SessionCount' has a wrong offset!");
static_assert(offsetof(UWDG_Page_C, txtCurrentGameVersion) == 0x0002C8, "Member 'UWDG_Page_C::txtCurrentGameVersion' has a wrong offset!");
static_assert(offsetof(UWDG_Page_C, EnableSessionCount) == 0x0002D0, "Member 'UWDG_Page_C::EnableSessionCount' has a wrong offset!");
static_assert(offsetof(UWDG_Page_C, EnableGradientBgr) == 0x0002D1, "Member 'UWDG_Page_C::EnableGradientBgr' has a wrong offset!");

}

