﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SingleSpecialEventPanel

#include "Basic.hpp"

#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_SingleSpecialEventPanel.WDG_SingleSpecialEventPanel_C.ExecuteUbergraph_WDG_SingleSpecialEventPanel
// 0x0250 (0x0250 - 0x0000)
struct WDG_SingleSpecialEventPanel_C_ExecuteUbergraph_WDG_SingleSpecialEventPanel final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0004(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0005(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6[0x2];                                        // 0x0006(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSpecialEventPreset                    K2Node_Event_preset;                               // 0x0008(0x0240)()
	bool                                          CallFunc_CanPlay_Result;                           // 0x0248(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_SingleSpecialEventPanel_C_ExecuteUbergraph_WDG_SingleSpecialEventPanel) == 0x000008, "Wrong alignment on WDG_SingleSpecialEventPanel_C_ExecuteUbergraph_WDG_SingleSpecialEventPanel");
static_assert(sizeof(WDG_SingleSpecialEventPanel_C_ExecuteUbergraph_WDG_SingleSpecialEventPanel) == 0x000250, "Wrong size on WDG_SingleSpecialEventPanel_C_ExecuteUbergraph_WDG_SingleSpecialEventPanel");
static_assert(offsetof(WDG_SingleSpecialEventPanel_C_ExecuteUbergraph_WDG_SingleSpecialEventPanel, EntryPoint) == 0x000000, "Member 'WDG_SingleSpecialEventPanel_C_ExecuteUbergraph_WDG_SingleSpecialEventPanel::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_SingleSpecialEventPanel_C_ExecuteUbergraph_WDG_SingleSpecialEventPanel, CallFunc_MakeLiteralByte_ReturnValue) == 0x000004, "Member 'WDG_SingleSpecialEventPanel_C_ExecuteUbergraph_WDG_SingleSpecialEventPanel::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SingleSpecialEventPanel_C_ExecuteUbergraph_WDG_SingleSpecialEventPanel, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000005, "Member 'WDG_SingleSpecialEventPanel_C_ExecuteUbergraph_WDG_SingleSpecialEventPanel::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SingleSpecialEventPanel_C_ExecuteUbergraph_WDG_SingleSpecialEventPanel, K2Node_Event_preset) == 0x000008, "Member 'WDG_SingleSpecialEventPanel_C_ExecuteUbergraph_WDG_SingleSpecialEventPanel::K2Node_Event_preset' has a wrong offset!");
static_assert(offsetof(WDG_SingleSpecialEventPanel_C_ExecuteUbergraph_WDG_SingleSpecialEventPanel, CallFunc_CanPlay_Result) == 0x000248, "Member 'WDG_SingleSpecialEventPanel_C_ExecuteUbergraph_WDG_SingleSpecialEventPanel::CallFunc_CanPlay_Result' has a wrong offset!");

// Function WDG_SingleSpecialEventPanel.WDG_SingleSpecialEventPanel_C.OnPresetSet
// 0x0240 (0x0240 - 0x0000)
struct WDG_SingleSpecialEventPanel_C_OnPresetSet final
{
public:
	struct FSpecialEventPreset                    Preset;                                            // 0x0000(0x0240)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_SingleSpecialEventPanel_C_OnPresetSet) == 0x000008, "Wrong alignment on WDG_SingleSpecialEventPanel_C_OnPresetSet");
static_assert(sizeof(WDG_SingleSpecialEventPanel_C_OnPresetSet) == 0x000240, "Wrong size on WDG_SingleSpecialEventPanel_C_OnPresetSet");
static_assert(offsetof(WDG_SingleSpecialEventPanel_C_OnPresetSet, Preset) == 0x000000, "Member 'WDG_SingleSpecialEventPanel_C_OnPresetSet::Preset' has a wrong offset!");

}

