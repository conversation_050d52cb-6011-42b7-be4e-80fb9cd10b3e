﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PitstopInfo

#include "Basic.hpp"

#include "WDG_PitstopInfo_classes.hpp"
#include "WDG_PitstopInfo_parameters.hpp"


namespace SDK
{

// Function WDG_PitstopInfo.WDG_PitstopInfo_C.ExecuteUbergraph_WDG_PitstopInfo
// (Final, UbergraphFunction, HasDefaults)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_PitstopInfo_C::ExecuteUbergraph_WDG_PitstopInfo(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PitstopInfo_C", "ExecuteUbergraph_WDG_PitstopInfo");

	Params::WDG_PitstopInfo_C_ExecuteUbergraph_WDG_PitstopInfo Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_PitstopInfo.WDG_PitstopInfo_C.OnAfterConstruct
// (Event, Public, BlueprintEvent)

void UWDG_PitstopInfo_C::OnAfterConstruct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PitstopInfo_C", "OnAfterConstruct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_PitstopInfo.WDG_PitstopInfo_C.Tick
// (BlueprintCosmetic, Event, Public, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// float                                   InDeltaTime                                            (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_PitstopInfo_C::Tick(const struct FGeometry& MyGeometry, float InDeltaTime)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_PitstopInfo_C", "Tick");

	Params::WDG_PitstopInfo_C_Tick Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.InDeltaTime = InDeltaTime;

	UObject::ProcessEvent(Func, &Parms);
}

}

