﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_OptionsGenericPanel

#include "Basic.hpp"

#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_OptionsGenericPanel.WDG_OptionsGenericPanel_C
// 0x0020 (0x0688 - 0x0668)
class UWDG_OptionsGenericPanel_C final : public UOptionsGenericPanel
{
public:
	class UWidgetAnimation*                       Opacity;                                           // 0x0668(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       Scale;                                             // 0x0670(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 HoverImageBox;                                     // 0x0678(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 NormalImageBox;                                    // 0x0680(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_OptionsGenericPanel_C">();
	}
	static class UWDG_OptionsGenericPanel_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_OptionsGenericPanel_C>();
	}
};
static_assert(alignof(UWDG_OptionsGenericPanel_C) == 0x000008, "Wrong alignment on UWDG_OptionsGenericPanel_C");
static_assert(sizeof(UWDG_OptionsGenericPanel_C) == 0x000688, "Wrong size on UWDG_OptionsGenericPanel_C");
static_assert(offsetof(UWDG_OptionsGenericPanel_C, Opacity) == 0x000668, "Member 'UWDG_OptionsGenericPanel_C::Opacity' has a wrong offset!");
static_assert(offsetof(UWDG_OptionsGenericPanel_C, Scale) == 0x000670, "Member 'UWDG_OptionsGenericPanel_C::Scale' has a wrong offset!");
static_assert(offsetof(UWDG_OptionsGenericPanel_C, HoverImageBox) == 0x000678, "Member 'UWDG_OptionsGenericPanel_C::HoverImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_OptionsGenericPanel_C, NormalImageBox) == 0x000680, "Member 'UWDG_OptionsGenericPanel_C::NormalImageBox' has a wrong offset!");

}

