﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomPage

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"
#include "ShowroomTileType_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ShowroomPage.WDG_ShowroomPage_C
// 0x0220 (0x1238 - 0x1018)
class UWDG_ShowroomPage_C final : public UShowroom
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x1018(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       ShowroomFadeOutText;                               // 0x1020(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoD<PERSON><PERSON><PERSON>, HasGetValueTypeHash)
	class UWidgetAnimation*                       ShowroomOverlayFade;                               // 0x1028(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       NewAnimation;                                      // 0x1030(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UImage*                                 ACCLogoShowroom;                                   // 0x1038(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         allDrivers;                                        // 0x1040(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_FooterButton_C*                    Back;                                              // 0x1048(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                borderConfirm;                                     // 0x1050(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         boxDrivers;                                        // 0x1058(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         boxShowroomText;                                   // 0x1060(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_CarBrandWidget_C*                  brandShowroom;                                     // 0x1068(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnAddCustom;                                      // 0x1070(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnCameraToggle;                                   // 0x1078(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnConfirm;                                        // 0x1080(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnCustomDelete;                                   // 0x1088(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnCustomSave;                                     // 0x1090(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnCustomSaveAsNew;                                // 0x1098(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnDoorLeft;                                       // 0x10A0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnDoorRight;                                      // 0x10A8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnEditCustom;                                     // 0x10B0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnIndicators;                                     // 0x10B8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnPlaySequence;                                   // 0x10C0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnRainLight;                                      // 0x10C8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           canvasCameraControls;                              // 0x10D0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           canvasCarSelection;                                // 0x10D8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           canvasCustomEditor;                                // 0x10E0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           canvasDriverBio;                                   // 0x10E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           CanvasPanel_3;                                     // 0x10F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UScrollBox*                             carEntries;                                        // 0x10F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomCarGroupFilter_C*          CarGroupFilter;                                    // 0x1100(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      CurrentDriver;                                     // 0x1108(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      CurrentModel;                                      // 0x1110(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      CurrentTeam;                                       // 0x1118(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      CustomCarModel;                                    // 0x1120(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomCustomCarOptions_C*        customCarOptions;                                  // 0x1128(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomFilter_C*                  Filter;                                            // 0x1130(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Footer_C*                          Footer;                                            // 0x1138(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         hboxCustomControls;                                // 0x1140(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Header_C*                          Header;                                            // 0x1148(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgLineSelection;                                  // 0x1150(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgLocked;                                         // 0x1158(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgTerminal;                                       // 0x1160(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_InformationBox_C*                  infoBox;                                           // 0x1168(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_TextInput_C*                       inputRaceNumber;                                   // 0x1170(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_TextInput_C*                       inputTeamName;                                     // 0x1178(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomModal_C*                   modalOverlay;                                      // 0x1180(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UOverlay*                               overlayMousePreventer;                             // 0x1188(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_BasicMessagePopup_C*               popupCarSaved;                                     // 0x1190(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_BasicMessagePopup_C*               popupChanges;                                      // 0x1198(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_BasicMessagePopup_C*               popupDelete;                                       // 0x11A0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_BasicMessagePopup_C*               popupOverwrite;                                    // 0x11A8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             PreviousPage;                                      // 0x11B0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomSeasonTypeFilter_C*        SeasonTypeFilter;                                  // 0x11B8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomTileSelector_C*            SelectorModel;                                     // 0x11C0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomTileSelector_C*            SelectorTeam;                                      // 0x11C8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UScaleBox*                              showroomOverlay;                                   // 0x11D0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class USizeBox*                               sizeCarEntries;                                    // 0x11D8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_HorizontalSlider_C*                sliderCarLights;                                   // 0x11E0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWidgetSwitcher*                        switcherMode;                                      // 0x11E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWidgetSwitcher*                        switcherTileType;                                  // 0x11F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Page_C*                            Template;                                          // 0x11F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             textDriverBio;                                     // 0x1200(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      tileDriverBio;                                     // 0x1208(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtShowRoomCarInfo;                                // 0x1210(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UScrollBox*                             variantEntries;                                    // 0x1218(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class AShowRoomCameraActor_C*                 ShowRoomCamera;                                    // 0x1220(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          EditingCustom;                                     // 0x1228(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          AddingCustom;                                      // 0x1229(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	bool                                          IsLeftDoorOpen;                                    // 0x122A(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	bool                                          IsRightDoorOpen;                                   // 0x122B(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	bool                                          IsRainLightOn;                                     // 0x122C(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	bool                                          IsIndicatorsOn;                                    // 0x122D(0x0001)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor)
	uint8                                         Pad_122E[0x2];                                     // 0x122E(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	class ABP_MenuController_C*                   MenuController;                                    // 0x1230(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnTemplate, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_ShowroomPage(int32 EntryPoint);
	void BndEvt__WDG_ShowroomPage_SeasonTypeFilter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature(ESeasonType activeFilter_0);
	void BndEvt__WDG_ShowroomPage_CarGroupFilter_K2Node_ComponentBoundEvent_9_OnFilterSelected__DelegateSignature(ECarGroup activeFilter_0);
	void BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_7_OnItemPrevious__DelegateSignature(class FName Key, class UWDG_ShowroomTileItemHorizontal_C* Sender);
	void BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_5_OnItemNext__DelegateSignature(class FName Key, class UWDG_ShowroomTileItemHorizontal_C* Sender);
	void BndEvt__CurrentModel_K2Node_ComponentBoundEvent_3_OnItemPrevious__DelegateSignature(class FName Key, class UWDG_ShowroomTileItemHorizontal_C* Sender);
	void BndEvt__CurrentModel_K2Node_ComponentBoundEvent_1_OnItemNext__DelegateSignature(class FName Key, class UWDG_ShowroomTileItemHorizontal_C* Sender);
	void OnEntryListSeason(ESeasonType Season);
	void BndEvt__btnSelect_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__Back_K2Node_ComponentBoundEvent_0_OnClicked__DelegateSignature();
	void OnFilterApplied(EShowroomCarFilterType Filter_0);
	void BndEvt__Filter_K2Node_ComponentBoundEvent_2_OnFilterSelected__DelegateSignature(EShowroomCarFilterType activeFilter_0);
	void OnPageReady();
	void BP_OnBackward();
	void BndEvt__ModalOverlay_K2Node_ComponentBoundEvent_2_OnCancel__DelegateSignature(class UAcPanelBase* CallingPanel);
	void BndEvt__modalOverlay_K2Node_ComponentBoundEvent_1_OnShow__DelegateSignature(class UAcPanelBase* CallingPanel);
	void BndEvt__modalOverlay_K2Node_ComponentBoundEvent_0_OnHide__DelegateSignature(class UAcPanelBase* CallingPanel, bool Cancelled);
	void BndEvt__WDG_ModelSelectorItemHorizontal_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature(class UWDG_ShowroomTileBase_C* Sender, class FName Key, int32 KeyInt);
	void Update_Model(const struct FModelInfo& ModelInfo);
	void BndEvt__SelectorModel_K2Node_ComponentBoundEvent_1_OnModelSelected__DelegateSignature(const struct FModelInfo& ModelInfo);
	void OnModelUpdate(const struct FModelInfo& ModelInfo);
	void OnPreviousCarEntry(class FName Key, class UWDG_ShowroomTileItemHorizontal_C* Sender);
	void OnNextCarEntry(class FName Key, class UWDG_ShowroomTileItemHorizontal_C* Sender);
	void BndEvt__SelectorTeam_K2Node_ComponentBoundEvent_2_OnTeamSelected__DelegateSignature(const struct FTeamInfo& TeamInfo);
	void BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature(class UWDG_ShowroomTileBase_C* Sender, class FName Key, int32 KeyInt);
	void BndEvt__CurrentTeam_K2Node_ComponentBoundEvent_0_OnItemFocused__DelegateSignature(class UWDG_ShowroomTileBase_C* Sender);
	void OnCarItemSelected(class UWDG_ShowroomTileBase_C* Sender, class FName Key, int32 KeyInt);
	void Update_Team(const struct FTeamInfo& TeamInfo);
	void OnTeamUpdate(const struct FTeamInfo& TeamInfo);
	void PreviousVariant(class FName Key, class UWDG_ShowroomTileItemHorizontal_C* Sender);
	void NextVariant(class FName Key, class UWDG_ShowroomTileItemHorizontal_C* Sender);
	void VariantSelected(class UWDG_ShowroomTileItemHorizontal_C* Sender, int32 ColorCode, const struct FLinearColor& Color);
	void OnVariantUpdate(int32 variant_key);
	void OnPopulateVariants();
	void OnDriverItemSelected(class UWDG_ShowroomTileBase_C* Sender, class FName Key, int32 KeyInt);
	void OnCarUpdate(const struct FCarInfo& CarInfo, class FName CarKey);
	void BndEvt__CurrentDriver_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature(class UWDG_ShowroomTileBase_C* Sender, class FName Key, int32 KeyInt);
	void OnDriverUpdate(const struct FDriverInfo& DriverInfo, class FName DriverKey);
	void OnHideCustomEditor();
	void OnShowCustomEditor();
	void BndEvt__popupChanges_K2Node_ComponentBoundEvent_6_OnNo__DelegateSignature();
	void BndEvt__popupChanges_K2Node_ComponentBoundEvent_1_OnYes__DelegateSignature();
	void BndEvt__inputTeamName_K2Node_ComponentBoundEvent_1_AcTextInputChanged__DelegateSignature(const class FText& text);
	void BndEvt__customCarOptions_K2Node_ComponentBoundEvent_8_OnValuesUpdated__DelegateSignature(const struct FCarInfo& CarInfo);
	void BndEvt__inputRaceNumber_K2Node_ComponentBoundEvent_3_AcTextInputChanged__DelegateSignature(const class FText& text);
	void BndEvt__CustomCarModel_K2Node_ComponentBoundEvent_2_OnItemForward__DelegateSignature(class UWDG_ShowroomTileBase_C* Sender, class FName Key, int32 KeyInt);
	void OnCustomModelUpdate(const struct FModelInfo& ModelInfo);
	void BndEvt__btnEditCustom_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature(class UWDG_GenericBarItem_C* Sender);
	void BndEvt__btnAddCustom_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature(class UWDG_GenericBarItem_C* Sender);
	void BndEvt__popupOverwrite_K2Node_ComponentBoundEvent_8_OnNo__DelegateSignature();
	void BndEvt__popupOverwrite_K2Node_ComponentBoundEvent_7_OnYes__DelegateSignature();
	void BndEvt__btnCustomSaveAsNew_K2Node_ComponentBoundEvent_3_OnItemForward__DelegateSignature(class UWDG_GenericBarItem_C* Sender);
	void BndEvt__popupDelete_K2Node_ComponentBoundEvent_2_OnNo__DelegateSignature();
	void BndEvt__popupDelete_K2Node_ComponentBoundEvent_0_OnYes__DelegateSignature();
	void BndEvt__btnCustomDelete_K2Node_ComponentBoundEvent_1_OnItemForward__DelegateSignature(class UWDG_GenericBarItem_C* Sender);
	void BndEvt__popupCarSaved_K2Node_ComponentBoundEvent_6_OnHide__DelegateSignature();
	void BndEvt__popupCarSaved_K2Node_ComponentBoundEvent_4_OnShow__DelegateSignature(bool HasConfirmation);
	void BndEvt__btnCustomSave_K2Node_ComponentBoundEvent_7_OnItemForward__DelegateSignature(class UWDG_GenericBarItem_C* Sender);
	void OnAnimationFinished(const class UWidgetAnimation* Animation);
	void StopSequence();
	void BndEvt__btnPlaySequence_K2Node_ComponentBoundEvent_6_OnAcPanelForwardEvent__DelegateSignature();
	void PlaySequence();
	void BndEvt__btnDoorRight_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__btnDoorLeft_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature();
	void OnPostCarUpdate();
	void BndEvt__WDG_HorizontalSlider_K2Node_ComponentBoundEvent_1_SelectorChanged__DelegateSignature(class UGenericSelectorItem* Source, int32 current_index, int32 current_value);
	void BndEvt__WDG_GenericBarItem_C_1_K2Node_ComponentBoundEvent_2_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__btnToggleDoors_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature();
	void OnCameraToggle();
	void BndEvt__btnCameraToggle_K2Node_ComponentBoundEvent_7_OnAcPanelForwardEvent__DelegateSignature();
	void ResetView(bool ResetOnlyOffset);
	void SetPreviousPageTitle();
	struct FEventReply OnMouseButtonUp(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent);
	struct FEventReply OnMouseMove(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent);
	void SetShowroomCamera();
	struct FEventReply OnMouseWheel(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent);
	struct FEventReply OnMouseButtonDoubleClick(const struct FGeometry& InMyGeometry, const struct FPointerEvent& InMouseEvent);
	void ShowModalOverlay(EShowroomTileType Type, class UAcPanelBase* CallingPanel);
	void HideModalOverlay(bool Cancelled);
	struct FEventReply OnPreviewKeyDown(const struct FGeometry& MyGeometry, const struct FKeyEvent& InKeyEvent);
	void UpdateModelTiles(bool All_Models);
	void UpdateTeamTiles();
	void FocusToSelectedCarEntry();
	class UWidget* GoToCurrentDriver(EUINavigation Navigation_0);
	void PrepareCustomEditor(const struct FCarInfo& CarInfo, ECarModelType CarModel);
	void ReturnToCallingPage();
	void CheckEnableHideFileOpButtons();
	void ReturnFromCustomEditor();
	void FindSelectedEntry(bool ScrollToOnFind, class UWDG_ShowroomTileItemHorizontal_C** AsWDG_Showroom_Tile_Item_Horizontal);
	bool IsShowroomPlaying();
	struct FEventReply OnPreviewMouseButtonDown(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent);
	void GetCarSystems(class UAcCarSystems** carSystems);
	void SetCarLights(int32 Stage);
	void GetCarAnimations(class UAcCarAnimations** CarAnimations);
	void ToggleUIOpacity();
	struct FEventReply OnKeyUp(const struct FGeometry& MyGeometry, const struct FKeyEvent& InKeyEvent);
	void FindSelectedVariant(bool ScrollToOnFind, class UWDG_ShowroomTileItemHorizontal_C** AsWDG_Showroom_Tile_Item_Horizontal);
	void IsCurrentCarCustom(bool* isCustom);
	void UpdateShowroomLabels(class FName ModelName, EBrandType Brand);
	void ProceedToNextPage();
	void ToggleLockIcon(const struct FCarInfo& CarInfo);
	void ShowCustomEditor(bool isCustom);
	bool IsInCustomEditor();
	void getCarAvatar(class ACarAvatar** currentShowroomCar);
	void GoToDriverCustomization();
	void Show_UI_If_Hidden(bool* WasHidden);
	struct FEventReply OnKeyDown(const struct FGeometry& MyGeometry, const struct FKeyEvent& InKeyEvent);
	class UWidget* GoUpFromCustomEdit(EUINavigation Navigation_0);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ShowroomPage_C">();
	}
	static class UWDG_ShowroomPage_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ShowroomPage_C>();
	}
};
static_assert(alignof(UWDG_ShowroomPage_C) == 0x000008, "Wrong alignment on UWDG_ShowroomPage_C");
static_assert(sizeof(UWDG_ShowroomPage_C) == 0x001238, "Wrong size on UWDG_ShowroomPage_C");
static_assert(offsetof(UWDG_ShowroomPage_C, UberGraphFrame) == 0x001018, "Member 'UWDG_ShowroomPage_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, ShowroomFadeOutText) == 0x001020, "Member 'UWDG_ShowroomPage_C::ShowroomFadeOutText' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, ShowroomOverlayFade) == 0x001028, "Member 'UWDG_ShowroomPage_C::ShowroomOverlayFade' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, NewAnimation) == 0x001030, "Member 'UWDG_ShowroomPage_C::NewAnimation' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, ACCLogoShowroom) == 0x001038, "Member 'UWDG_ShowroomPage_C::ACCLogoShowroom' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, allDrivers) == 0x001040, "Member 'UWDG_ShowroomPage_C::allDrivers' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, Back) == 0x001048, "Member 'UWDG_ShowroomPage_C::Back' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, borderConfirm) == 0x001050, "Member 'UWDG_ShowroomPage_C::borderConfirm' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, boxDrivers) == 0x001058, "Member 'UWDG_ShowroomPage_C::boxDrivers' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, boxShowroomText) == 0x001060, "Member 'UWDG_ShowroomPage_C::boxShowroomText' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, brandShowroom) == 0x001068, "Member 'UWDG_ShowroomPage_C::brandShowroom' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, btnAddCustom) == 0x001070, "Member 'UWDG_ShowroomPage_C::btnAddCustom' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, btnCameraToggle) == 0x001078, "Member 'UWDG_ShowroomPage_C::btnCameraToggle' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, btnConfirm) == 0x001080, "Member 'UWDG_ShowroomPage_C::btnConfirm' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, btnCustomDelete) == 0x001088, "Member 'UWDG_ShowroomPage_C::btnCustomDelete' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, btnCustomSave) == 0x001090, "Member 'UWDG_ShowroomPage_C::btnCustomSave' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, btnCustomSaveAsNew) == 0x001098, "Member 'UWDG_ShowroomPage_C::btnCustomSaveAsNew' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, btnDoorLeft) == 0x0010A0, "Member 'UWDG_ShowroomPage_C::btnDoorLeft' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, btnDoorRight) == 0x0010A8, "Member 'UWDG_ShowroomPage_C::btnDoorRight' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, btnEditCustom) == 0x0010B0, "Member 'UWDG_ShowroomPage_C::btnEditCustom' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, btnIndicators) == 0x0010B8, "Member 'UWDG_ShowroomPage_C::btnIndicators' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, btnPlaySequence) == 0x0010C0, "Member 'UWDG_ShowroomPage_C::btnPlaySequence' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, btnRainLight) == 0x0010C8, "Member 'UWDG_ShowroomPage_C::btnRainLight' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, canvasCameraControls) == 0x0010D0, "Member 'UWDG_ShowroomPage_C::canvasCameraControls' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, canvasCarSelection) == 0x0010D8, "Member 'UWDG_ShowroomPage_C::canvasCarSelection' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, canvasCustomEditor) == 0x0010E0, "Member 'UWDG_ShowroomPage_C::canvasCustomEditor' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, canvasDriverBio) == 0x0010E8, "Member 'UWDG_ShowroomPage_C::canvasDriverBio' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, CanvasPanel_3) == 0x0010F0, "Member 'UWDG_ShowroomPage_C::CanvasPanel_3' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, carEntries) == 0x0010F8, "Member 'UWDG_ShowroomPage_C::carEntries' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, CarGroupFilter) == 0x001100, "Member 'UWDG_ShowroomPage_C::CarGroupFilter' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, CurrentDriver) == 0x001108, "Member 'UWDG_ShowroomPage_C::CurrentDriver' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, CurrentModel) == 0x001110, "Member 'UWDG_ShowroomPage_C::CurrentModel' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, CurrentTeam) == 0x001118, "Member 'UWDG_ShowroomPage_C::CurrentTeam' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, CustomCarModel) == 0x001120, "Member 'UWDG_ShowroomPage_C::CustomCarModel' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, customCarOptions) == 0x001128, "Member 'UWDG_ShowroomPage_C::customCarOptions' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, Filter) == 0x001130, "Member 'UWDG_ShowroomPage_C::Filter' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, Footer) == 0x001138, "Member 'UWDG_ShowroomPage_C::Footer' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, hboxCustomControls) == 0x001140, "Member 'UWDG_ShowroomPage_C::hboxCustomControls' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, Header) == 0x001148, "Member 'UWDG_ShowroomPage_C::Header' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, imgLineSelection) == 0x001150, "Member 'UWDG_ShowroomPage_C::imgLineSelection' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, imgLocked) == 0x001158, "Member 'UWDG_ShowroomPage_C::imgLocked' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, imgTerminal) == 0x001160, "Member 'UWDG_ShowroomPage_C::imgTerminal' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, infoBox) == 0x001168, "Member 'UWDG_ShowroomPage_C::infoBox' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, inputRaceNumber) == 0x001170, "Member 'UWDG_ShowroomPage_C::inputRaceNumber' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, inputTeamName) == 0x001178, "Member 'UWDG_ShowroomPage_C::inputTeamName' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, modalOverlay) == 0x001180, "Member 'UWDG_ShowroomPage_C::modalOverlay' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, overlayMousePreventer) == 0x001188, "Member 'UWDG_ShowroomPage_C::overlayMousePreventer' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, popupCarSaved) == 0x001190, "Member 'UWDG_ShowroomPage_C::popupCarSaved' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, popupChanges) == 0x001198, "Member 'UWDG_ShowroomPage_C::popupChanges' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, popupDelete) == 0x0011A0, "Member 'UWDG_ShowroomPage_C::popupDelete' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, popupOverwrite) == 0x0011A8, "Member 'UWDG_ShowroomPage_C::popupOverwrite' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, PreviousPage) == 0x0011B0, "Member 'UWDG_ShowroomPage_C::PreviousPage' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, SeasonTypeFilter) == 0x0011B8, "Member 'UWDG_ShowroomPage_C::SeasonTypeFilter' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, SelectorModel) == 0x0011C0, "Member 'UWDG_ShowroomPage_C::SelectorModel' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, SelectorTeam) == 0x0011C8, "Member 'UWDG_ShowroomPage_C::SelectorTeam' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, showroomOverlay) == 0x0011D0, "Member 'UWDG_ShowroomPage_C::showroomOverlay' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, sizeCarEntries) == 0x0011D8, "Member 'UWDG_ShowroomPage_C::sizeCarEntries' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, sliderCarLights) == 0x0011E0, "Member 'UWDG_ShowroomPage_C::sliderCarLights' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, switcherMode) == 0x0011E8, "Member 'UWDG_ShowroomPage_C::switcherMode' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, switcherTileType) == 0x0011F0, "Member 'UWDG_ShowroomPage_C::switcherTileType' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, Template) == 0x0011F8, "Member 'UWDG_ShowroomPage_C::Template' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, textDriverBio) == 0x001200, "Member 'UWDG_ShowroomPage_C::textDriverBio' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, tileDriverBio) == 0x001208, "Member 'UWDG_ShowroomPage_C::tileDriverBio' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, txtShowRoomCarInfo) == 0x001210, "Member 'UWDG_ShowroomPage_C::txtShowRoomCarInfo' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, variantEntries) == 0x001218, "Member 'UWDG_ShowroomPage_C::variantEntries' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, ShowRoomCamera) == 0x001220, "Member 'UWDG_ShowroomPage_C::ShowRoomCamera' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, EditingCustom) == 0x001228, "Member 'UWDG_ShowroomPage_C::EditingCustom' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, AddingCustom) == 0x001229, "Member 'UWDG_ShowroomPage_C::AddingCustom' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, IsLeftDoorOpen) == 0x00122A, "Member 'UWDG_ShowroomPage_C::IsLeftDoorOpen' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, IsRightDoorOpen) == 0x00122B, "Member 'UWDG_ShowroomPage_C::IsRightDoorOpen' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, IsRainLightOn) == 0x00122C, "Member 'UWDG_ShowroomPage_C::IsRainLightOn' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, IsIndicatorsOn) == 0x00122D, "Member 'UWDG_ShowroomPage_C::IsIndicatorsOn' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomPage_C, MenuController) == 0x001230, "Member 'UWDG_ShowroomPage_C::MenuController' has a wrong offset!");

}

