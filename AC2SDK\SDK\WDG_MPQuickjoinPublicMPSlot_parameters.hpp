﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_MPQuickjoinPublicMPSlot

#include "Basic.hpp"

#include "AC2_structs.hpp"


namespace SDK::Params
{

// Function WDG_MPQuickjoinPublicMPSlot.WDG_MPQuickjoinPublicMPSlot_C.ExecuteUbergraph_WDG_MPQuickjoinPublicMPSlot
// 0x0028 (0x0028 - 0x0000)
struct WDG_MPQuickjoinPublicMPSlot_C_ExecuteUbergraph_WDG_MPQuickjoinPublicMPSlot final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationForward_ReturnValue;         // 0x0008(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationForward_ReturnValue_1;       // 0x0010(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationReverse_ReturnValue;         // 0x0018(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationReverse_ReturnValue_1;       // 0x0020(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_MPQuickjoinPublicMPSlot_C_ExecuteUbergraph_WDG_MPQuickjoinPublicMPSlot) == 0x000008, "Wrong alignment on WDG_MPQuickjoinPublicMPSlot_C_ExecuteUbergraph_WDG_MPQuickjoinPublicMPSlot");
static_assert(sizeof(WDG_MPQuickjoinPublicMPSlot_C_ExecuteUbergraph_WDG_MPQuickjoinPublicMPSlot) == 0x000028, "Wrong size on WDG_MPQuickjoinPublicMPSlot_C_ExecuteUbergraph_WDG_MPQuickjoinPublicMPSlot");
static_assert(offsetof(WDG_MPQuickjoinPublicMPSlot_C_ExecuteUbergraph_WDG_MPQuickjoinPublicMPSlot, EntryPoint) == 0x000000, "Member 'WDG_MPQuickjoinPublicMPSlot_C_ExecuteUbergraph_WDG_MPQuickjoinPublicMPSlot::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinPublicMPSlot_C_ExecuteUbergraph_WDG_MPQuickjoinPublicMPSlot, CallFunc_PlayAnimationForward_ReturnValue) == 0x000008, "Member 'WDG_MPQuickjoinPublicMPSlot_C_ExecuteUbergraph_WDG_MPQuickjoinPublicMPSlot::CallFunc_PlayAnimationForward_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinPublicMPSlot_C_ExecuteUbergraph_WDG_MPQuickjoinPublicMPSlot, CallFunc_PlayAnimationForward_ReturnValue_1) == 0x000010, "Member 'WDG_MPQuickjoinPublicMPSlot_C_ExecuteUbergraph_WDG_MPQuickjoinPublicMPSlot::CallFunc_PlayAnimationForward_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinPublicMPSlot_C_ExecuteUbergraph_WDG_MPQuickjoinPublicMPSlot, CallFunc_PlayAnimationReverse_ReturnValue) == 0x000018, "Member 'WDG_MPQuickjoinPublicMPSlot_C_ExecuteUbergraph_WDG_MPQuickjoinPublicMPSlot::CallFunc_PlayAnimationReverse_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinPublicMPSlot_C_ExecuteUbergraph_WDG_MPQuickjoinPublicMPSlot, CallFunc_PlayAnimationReverse_ReturnValue_1) == 0x000020, "Member 'WDG_MPQuickjoinPublicMPSlot_C_ExecuteUbergraph_WDG_MPQuickjoinPublicMPSlot::CallFunc_PlayAnimationReverse_ReturnValue_1' has a wrong offset!");

// Function WDG_MPQuickjoinPublicMPSlot.WDG_MPQuickjoinPublicMPSlot_C.SetQuickjoinInfo
// 0x0138 (0x0138 - 0x0000)
struct WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo final
{
public:
	struct FOnlineServicesMPQuickjoinPanelInfo    quickjoinInfo;                                     // 0x0000(0x0028)(BlueprintVisible, BlueprintReadOnly, Parm, NoDestructor)
	class FText                                   waitingIndicatorText;                              // 0x0028(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm)
	struct FOnlineServicesMPQuickjoinPanelInfo    Info;                                              // 0x0040(0x0028)(Edit, BlueprintVisible, NoDestructor)
	bool                                          CallFunc_TextIsEmpty_ReturnValue;                  // 0x0068(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0069(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x006A(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x006B(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_6C[0x4];                                       // 0x006C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_IntToText_ReturnValue;               // 0x0070(0x0018)()
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_1;             // 0x0088(0x0018)()
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_2;             // 0x00A0(0x0018)()
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_3;             // 0x00B8(0x0018)()
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x00D0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_D4[0x4];                                       // 0x00D4(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_4;             // 0x00D8(0x0018)()
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_5;             // 0x00F0(0x0018)()
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_6;             // 0x0108(0x0018)()
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_7;             // 0x0120(0x0018)()
};
static_assert(alignof(WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo) == 0x000008, "Wrong alignment on WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo");
static_assert(sizeof(WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo) == 0x000138, "Wrong size on WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo");
static_assert(offsetof(WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo, quickjoinInfo) == 0x000000, "Member 'WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo::quickjoinInfo' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo, waitingIndicatorText) == 0x000028, "Member 'WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo::waitingIndicatorText' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo, Info) == 0x000040, "Member 'WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo::Info' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo, CallFunc_TextIsEmpty_ReturnValue) == 0x000068, "Member 'WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo::CallFunc_TextIsEmpty_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo, CallFunc_Not_PreBool_ReturnValue) == 0x000069, "Member 'WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo, CallFunc_MakeLiteralByte_ReturnValue) == 0x00006A, "Member 'WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x00006B, "Member 'WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo, CallFunc_Conv_IntToText_ReturnValue) == 0x000070, "Member 'WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo::CallFunc_Conv_IntToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo, CallFunc_Conv_IntToText_ReturnValue_1) == 0x000088, "Member 'WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo::CallFunc_Conv_IntToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo, CallFunc_Conv_IntToText_ReturnValue_2) == 0x0000A0, "Member 'WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo::CallFunc_Conv_IntToText_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo, CallFunc_Conv_IntToText_ReturnValue_3) == 0x0000B8, "Member 'WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo::CallFunc_Conv_IntToText_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo, CallFunc_Subtract_IntInt_ReturnValue) == 0x0000D0, "Member 'WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo, CallFunc_Conv_IntToText_ReturnValue_4) == 0x0000D8, "Member 'WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo::CallFunc_Conv_IntToText_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo, CallFunc_Conv_IntToText_ReturnValue_5) == 0x0000F0, "Member 'WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo::CallFunc_Conv_IntToText_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo, CallFunc_Conv_IntToText_ReturnValue_6) == 0x000108, "Member 'WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo::CallFunc_Conv_IntToText_ReturnValue_6' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo, CallFunc_Conv_IntToText_ReturnValue_7) == 0x000120, "Member 'WDG_MPQuickjoinPublicMPSlot_C_SetQuickjoinInfo::CallFunc_Conv_IntToText_ReturnValue_7' has a wrong offset!");

}

