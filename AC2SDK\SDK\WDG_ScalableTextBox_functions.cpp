﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ScalableTextBox

#include "Basic.hpp"

#include "WDG_ScalableTextBox_classes.hpp"
#include "WDG_ScalableTextBox_parameters.hpp"


namespace SDK
{

// Function WDG_ScalableTextBox.WDG_ScalableTextBox_C.ExecuteUbergraph_WDG_ScalableTextBox
// (Final, UbergraphFunction)
// Parameters:
// int32                                   EntryPoint                                             (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ScalableTextBox_C::ExecuteUbergraph_WDG_ScalableTextBox(int32 EntryPoint)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ScalableTextBox_C", "ExecuteUbergraph_WDG_ScalableTextBox");

	Params::WDG_ScalableTextBox_C_ExecuteUbergraph_WDG_ScalableTextBox Parms{};

	Parms.EntryPoint = EntryPoint;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ScalableTextBox.WDG_ScalableTextBox_C.Construct
// (BlueprintCosmetic, Event, Public, BlueprintEvent)

void UWDG_ScalableTextBox_C::Construct()
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ScalableTextBox_C", "Construct");

	UObject::ProcessEvent(Func, nullptr);
}


// Function WDG_ScalableTextBox.WDG_ScalableTextBox_C.AddText
// (Public, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const class FText&                      text                                                   (BlueprintVisible, BlueprintReadOnly, Parm)
// int32                                   FontSize                                               (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ScalableTextBox_C::AddText(const class FText& text, int32 FontSize)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ScalableTextBox_C", "AddText");

	Params::WDG_ScalableTextBox_C_AddText Parms{};

	Parms.text = std::move(text);
	Parms.FontSize = FontSize;

	UObject::ProcessEvent(Func, &Parms);
}


// Function WDG_ScalableTextBox.WDG_ScalableTextBox_C.AddToVBox
// (Public, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UWidget*                          Content                                                (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

void UWDG_ScalableTextBox_C::AddToVBox(class UWidget* Content)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_ScalableTextBox_C", "AddToVBox");

	Params::WDG_ScalableTextBox_C_AddToVBox Parms{};

	Parms.Content = Content;

	UObject::ProcessEvent(Func, &Parms);
}

}

