﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_PlayerDataPage

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_PlayerDataPage.WDG_PlayerDataPage_C
// 0x0068 (0x05C0 - 0x0558)
class UWDG_PlayerDataPage_C final : public UPlayerDataPage
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0558(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWDG_FooterButton_C*                    Back;                                              // 0x0560(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_FooterButton_C*                    Confirm;                                           // 0x0568(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Footer_C*                          Footer;                                            // 0x0570(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_107;                                         // 0x0578(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_108;                                         // 0x0580(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_109;                                         // 0x0588(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 Image_435;                                         // 0x0590(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 imgBannerBottom;                                   // 0x0598(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Page_C*                            PageBase;                                          // 0x05A0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             TextBlock_6;                                       // 0x05A8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtCurrentGameVersion;                             // 0x05B0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ShowroomLogo_C*                    WDG_ShowroomLogo;                                  // 0x05B8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_PlayerDataPage(int32 EntryPoint);
	void BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_5_OnEditableTextCommittedEvent__DelegateSignature(const class FText& text, ETextCommit CommitMethod);
	void BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_4_OnEditableTextCommittedEvent__DelegateSignature(const class FText& text, ETextCommit CommitMethod);
	void BndEvt__txtNameValue_K2Node_ComponentBoundEvent_3_OnEditableTextCommittedEvent__DelegateSignature(const class FText& text, ETextCommit CommitMethod);
	void Construct();
	void BndEvt__txtNicknameValue_K2Node_ComponentBoundEvent_2_OnEditableTextChangedEvent__DelegateSignature(const class FText& text);
	void BndEvt__txtLastnameValue_K2Node_ComponentBoundEvent_1_OnEditableTextChangedEvent__DelegateSignature(const class FText& text);
	void BndEvt__txtNameValue_K2Node_ComponentBoundEvent_0_OnEditableTextChangedEvent__DelegateSignature(const class FText& text);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_PlayerDataPage_C">();
	}
	static class UWDG_PlayerDataPage_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_PlayerDataPage_C>();
	}
};
static_assert(alignof(UWDG_PlayerDataPage_C) == 0x000008, "Wrong alignment on UWDG_PlayerDataPage_C");
static_assert(sizeof(UWDG_PlayerDataPage_C) == 0x0005C0, "Wrong size on UWDG_PlayerDataPage_C");
static_assert(offsetof(UWDG_PlayerDataPage_C, UberGraphFrame) == 0x000558, "Member 'UWDG_PlayerDataPage_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerDataPage_C, Back) == 0x000560, "Member 'UWDG_PlayerDataPage_C::Back' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerDataPage_C, Confirm) == 0x000568, "Member 'UWDG_PlayerDataPage_C::Confirm' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerDataPage_C, Footer) == 0x000570, "Member 'UWDG_PlayerDataPage_C::Footer' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerDataPage_C, Image_107) == 0x000578, "Member 'UWDG_PlayerDataPage_C::Image_107' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerDataPage_C, Image_108) == 0x000580, "Member 'UWDG_PlayerDataPage_C::Image_108' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerDataPage_C, Image_109) == 0x000588, "Member 'UWDG_PlayerDataPage_C::Image_109' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerDataPage_C, Image_435) == 0x000590, "Member 'UWDG_PlayerDataPage_C::Image_435' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerDataPage_C, imgBannerBottom) == 0x000598, "Member 'UWDG_PlayerDataPage_C::imgBannerBottom' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerDataPage_C, PageBase) == 0x0005A0, "Member 'UWDG_PlayerDataPage_C::PageBase' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerDataPage_C, TextBlock_6) == 0x0005A8, "Member 'UWDG_PlayerDataPage_C::TextBlock_6' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerDataPage_C, txtCurrentGameVersion) == 0x0005B0, "Member 'UWDG_PlayerDataPage_C::txtCurrentGameVersion' has a wrong offset!");
static_assert(offsetof(UWDG_PlayerDataPage_C, WDG_ShowroomLogo) == 0x0005B8, "Member 'UWDG_PlayerDataPage_C::WDG_ShowroomLogo' has a wrong offset!");

}

