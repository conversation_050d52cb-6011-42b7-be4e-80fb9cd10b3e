﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SpecialEventsPage

#include "Basic.hpp"

#include "AC2_structs.hpp"
#include "SlateCore_structs.hpp"
#include "UMG_structs.hpp"


namespace SDK::Params
{

// Function WDG_SpecialEventsPage.WDG_SpecialEventsPage_C.ExecuteUbergraph_WDG_SpecialEventsPage
// 0x0448 (0x0448 - 0x0000)
struct WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateColor                            K2Node_MakeStruct_SlateColor;                      // 0x0008(0x0028)(UObjectWrapper)
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_1;                    // 0x0030(0x0028)(UObjectWrapper)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue;                // 0x0058(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimation_ReturnValue_1;              // 0x0060(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0068(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0069(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_6A[0x2];                                       // 0x006A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	struct FWidgetTransform                       K2Node_MakeStruct_WidgetTransform;                 // 0x006C(0x001C)(NoDestructor, UObjectWrapper)
	class FText                                   CallFunc_FindTextInLocalizationTable_OutText;      // 0x0088(0x0018)()
	bool                                          CallFunc_FindTextInLocalizationTable_ReturnValue;  // 0x00A0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_A1[0x7];                                       // 0x00A1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_FindTextInLocalizationTable_OutText_1;    // 0x00A8(0x0018)()
	bool                                          CallFunc_FindTextInLocalizationTable_ReturnValue_1; // 0x00C0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_C1[0x7];                                       // 0x00C1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_AppendToFText_ReturnValue;                // 0x00C8(0x0018)()
	class FText                                   CallFunc_AppendToFText_ReturnValue_1;              // 0x00E0(0x0018)()
	class FText                                   CallFunc_FindTextInLocalizationTable_OutText_2;    // 0x00F8(0x0018)()
	bool                                          CallFunc_FindTextInLocalizationTable_ReturnValue_2; // 0x0110(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_111[0x7];                                      // 0x0111(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_AppendToFText_ReturnValue_2;              // 0x0118(0x0018)()
	class FText                                   CallFunc_FindTextInLocalizationTable_OutText_3;    // 0x0130(0x0018)()
	bool                                          CallFunc_FindTextInLocalizationTable_ReturnValue_3; // 0x0148(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_149[0x7];                                      // 0x0149(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_AppendToFText_ReturnValue_3;              // 0x0150(0x0018)()
	class FText                                   CallFunc_FindTextInLocalizationTable_OutText_4;    // 0x0168(0x0018)()
	bool                                          CallFunc_FindTextInLocalizationTable_ReturnValue_4; // 0x0180(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_181[0x7];                                      // 0x0181(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSpecialEventPreset                    K2Node_Event_preset;                               // 0x0188(0x0240)()
	class FText                                   CallFunc_AppendToFText_ReturnValue_4;              // 0x03C8(0x0018)()
	bool                                          CallFunc_CanPlay_Result;                           // 0x03E0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_3E1[0x7];                                      // 0x03E1(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_2;                    // 0x03E8(0x0028)(UObjectWrapper)
	class UMenuManager*                           CallFunc_GetMenuManager_ReturnValue;               // 0x0410(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcPageBase*                            CallFunc_GoToPage_ReturnValue;                     // 0x0418(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_3;                    // 0x0420(0x0028)(UObjectWrapper)
};
static_assert(alignof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage) == 0x000008, "Wrong alignment on WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage");
static_assert(sizeof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage) == 0x000448, "Wrong size on WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, EntryPoint) == 0x000000, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, K2Node_MakeStruct_SlateColor) == 0x000008, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::K2Node_MakeStruct_SlateColor' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, K2Node_MakeStruct_SlateColor_1) == 0x000030, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::K2Node_MakeStruct_SlateColor_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, CallFunc_PlayAnimation_ReturnValue) == 0x000058, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::CallFunc_PlayAnimation_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, CallFunc_PlayAnimation_ReturnValue_1) == 0x000060, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::CallFunc_PlayAnimation_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, Temp_bool_Variable) == 0x000068, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, CallFunc_Not_PreBool_ReturnValue) == 0x000069, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, K2Node_MakeStruct_WidgetTransform) == 0x00006C, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::K2Node_MakeStruct_WidgetTransform' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, CallFunc_FindTextInLocalizationTable_OutText) == 0x000088, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::CallFunc_FindTextInLocalizationTable_OutText' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, CallFunc_FindTextInLocalizationTable_ReturnValue) == 0x0000A0, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::CallFunc_FindTextInLocalizationTable_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, CallFunc_FindTextInLocalizationTable_OutText_1) == 0x0000A8, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::CallFunc_FindTextInLocalizationTable_OutText_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, CallFunc_FindTextInLocalizationTable_ReturnValue_1) == 0x0000C0, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::CallFunc_FindTextInLocalizationTable_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, CallFunc_AppendToFText_ReturnValue) == 0x0000C8, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::CallFunc_AppendToFText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, CallFunc_AppendToFText_ReturnValue_1) == 0x0000E0, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::CallFunc_AppendToFText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, CallFunc_FindTextInLocalizationTable_OutText_2) == 0x0000F8, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::CallFunc_FindTextInLocalizationTable_OutText_2' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, CallFunc_FindTextInLocalizationTable_ReturnValue_2) == 0x000110, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::CallFunc_FindTextInLocalizationTable_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, CallFunc_AppendToFText_ReturnValue_2) == 0x000118, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::CallFunc_AppendToFText_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, CallFunc_FindTextInLocalizationTable_OutText_3) == 0x000130, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::CallFunc_FindTextInLocalizationTable_OutText_3' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, CallFunc_FindTextInLocalizationTable_ReturnValue_3) == 0x000148, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::CallFunc_FindTextInLocalizationTable_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, CallFunc_AppendToFText_ReturnValue_3) == 0x000150, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::CallFunc_AppendToFText_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, CallFunc_FindTextInLocalizationTable_OutText_4) == 0x000168, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::CallFunc_FindTextInLocalizationTable_OutText_4' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, CallFunc_FindTextInLocalizationTable_ReturnValue_4) == 0x000180, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::CallFunc_FindTextInLocalizationTable_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, K2Node_Event_preset) == 0x000188, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::K2Node_Event_preset' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, CallFunc_AppendToFText_ReturnValue_4) == 0x0003C8, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::CallFunc_AppendToFText_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, CallFunc_CanPlay_Result) == 0x0003E0, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::CallFunc_CanPlay_Result' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, K2Node_MakeStruct_SlateColor_2) == 0x0003E8, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::K2Node_MakeStruct_SlateColor_2' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, CallFunc_GetMenuManager_ReturnValue) == 0x000410, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::CallFunc_GetMenuManager_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, CallFunc_GoToPage_ReturnValue) == 0x000418, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::CallFunc_GoToPage_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage, K2Node_MakeStruct_SlateColor_3) == 0x000420, "Member 'WDG_SpecialEventsPage_C_ExecuteUbergraph_WDG_SpecialEventsPage::K2Node_MakeStruct_SlateColor_3' has a wrong offset!");

// Function WDG_SpecialEventsPage.WDG_SpecialEventsPage_C.OnPresetSelected
// 0x0240 (0x0240 - 0x0000)
struct WDG_SpecialEventsPage_C_OnPresetSelected final
{
public:
	struct FSpecialEventPreset                    Preset;                                            // 0x0000(0x0240)(BlueprintVisible, BlueprintReadOnly, Parm)
};
static_assert(alignof(WDG_SpecialEventsPage_C_OnPresetSelected) == 0x000008, "Wrong alignment on WDG_SpecialEventsPage_C_OnPresetSelected");
static_assert(sizeof(WDG_SpecialEventsPage_C_OnPresetSelected) == 0x000240, "Wrong size on WDG_SpecialEventsPage_C_OnPresetSelected");
static_assert(offsetof(WDG_SpecialEventsPage_C_OnPresetSelected, Preset) == 0x000000, "Member 'WDG_SpecialEventsPage_C_OnPresetSelected::Preset' has a wrong offset!");

}

