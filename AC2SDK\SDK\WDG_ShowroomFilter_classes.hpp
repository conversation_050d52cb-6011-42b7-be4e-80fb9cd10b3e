﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomFilter

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"
#include "CoreUObject_structs.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ShowroomFilter.WDG_ShowroomFilter_C
// 0x00D8 (0x06B8 - 0x05E0)
class UWDG_ShowroomFilter_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UVerticalBox*                           boxBody;                                           // 0x05E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                brdActiveFilter;                                   // 0x05F0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnAll;                                            // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnCustom;                                         // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnEndurance;                                      // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnOfficial;                                       // 0x0610(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnSprint;                                         // 0x0618(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                Indicator;                                         // 0x0620(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtActiveFilter;                                   // 0x0628(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	TArray<EShowroomCarFilterType>                AvailableFilters;                                  // 0x0630(0x0010)(Edit, BlueprintVisible)
	EShowroomCarFilterType                        activeFilter;                                      // 0x0640(0x0001)(Edit, BlueprintVisible, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_641[0x3];                                      // 0x0641(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	struct FLinearColor                           OverColor;                                         // 0x0644(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_654[0x4];                                      // 0x0654(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	TMulticastInlineDelegate<void(EShowroomCarFilterType activeFilter)> OnFilterSelected;            // 0x0658(0x0010)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, BlueprintAssignable, BlueprintCallable)
	TMap<EShowroomCarFilterType, class FText>     FilterTypeTextMap;                                 // 0x0668(0x0050)(Edit, BlueprintVisible)

public:
	void ExecuteUbergraph_WDG_ShowroomFilter(int32 EntryPoint);
	void BndEvt__btnOfficial_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature();
	void OnRemovedFromFocusPath(const struct FFocusEvent& InFocusEvent);
	void OnAddedToFocusPath(const struct FFocusEvent& InFocusEvent);
	void OnForward_Event_0();
	void Construct();
	void BndEvt__btnAll_K2Node_ComponentBoundEvent_3_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__btnSprint_K2Node_ComponentBoundEvent_2_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__btnEndurance_K2Node_ComponentBoundEvent_1_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__btnCustom_K2Node_ComponentBoundEvent_0_OnAcPanelForwardEvent__DelegateSignature();
	void Hide();
	void show();
	void PreConstruct(bool IsDesignTime);
	void SetFilters();
	void UpdateTextLabel();
	void SetActiveFilter(EShowroomCarFilterType activeFilter_0);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ShowroomFilter_C">();
	}
	static class UWDG_ShowroomFilter_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ShowroomFilter_C>();
	}
};
static_assert(alignof(UWDG_ShowroomFilter_C) == 0x000008, "Wrong alignment on UWDG_ShowroomFilter_C");
static_assert(sizeof(UWDG_ShowroomFilter_C) == 0x0006B8, "Wrong size on UWDG_ShowroomFilter_C");
static_assert(offsetof(UWDG_ShowroomFilter_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_ShowroomFilter_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomFilter_C, boxBody) == 0x0005E8, "Member 'UWDG_ShowroomFilter_C::boxBody' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomFilter_C, brdActiveFilter) == 0x0005F0, "Member 'UWDG_ShowroomFilter_C::brdActiveFilter' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomFilter_C, btnAll) == 0x0005F8, "Member 'UWDG_ShowroomFilter_C::btnAll' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomFilter_C, btnCustom) == 0x000600, "Member 'UWDG_ShowroomFilter_C::btnCustom' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomFilter_C, btnEndurance) == 0x000608, "Member 'UWDG_ShowroomFilter_C::btnEndurance' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomFilter_C, btnOfficial) == 0x000610, "Member 'UWDG_ShowroomFilter_C::btnOfficial' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomFilter_C, btnSprint) == 0x000618, "Member 'UWDG_ShowroomFilter_C::btnSprint' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomFilter_C, Indicator) == 0x000620, "Member 'UWDG_ShowroomFilter_C::Indicator' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomFilter_C, txtActiveFilter) == 0x000628, "Member 'UWDG_ShowroomFilter_C::txtActiveFilter' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomFilter_C, AvailableFilters) == 0x000630, "Member 'UWDG_ShowroomFilter_C::AvailableFilters' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomFilter_C, activeFilter) == 0x000640, "Member 'UWDG_ShowroomFilter_C::activeFilter' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomFilter_C, OverColor) == 0x000644, "Member 'UWDG_ShowroomFilter_C::OverColor' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomFilter_C, OnFilterSelected) == 0x000658, "Member 'UWDG_ShowroomFilter_C::OnFilterSelected' has a wrong offset!");
static_assert(offsetof(UWDG_ShowroomFilter_C, FilterTypeTextMap) == 0x000668, "Member 'UWDG_ShowroomFilter_C::FilterTypeTextMap' has a wrong offset!");

}

