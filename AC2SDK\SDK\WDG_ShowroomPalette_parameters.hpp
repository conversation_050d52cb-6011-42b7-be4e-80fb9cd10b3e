﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ShowroomPalette

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "InputCore_structs.hpp"
#include "UMG_structs.hpp"
#include "AC2_structs.hpp"
#include "CoreUObject_structs.hpp"


namespace SDK::Params
{

// Function WDG_ShowroomPalette.WDG_ShowroomPalette_C.ExecuteUbergraph_WDG_ShowroomPalette
// 0x0018 (0x0018 - 0x0000)
struct WDG_ShowroomPalette_C_ExecuteUbergraph_WDG_ShowroomPalette final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4[0x4];                                        // 0x0004(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UAcPanelBase*                           K2Node_Event_Child;                                // 0x0008(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  K2Node_ComponentBoundEvent_Sender;                 // 0x0010(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPalette_C_ExecuteUbergraph_WDG_ShowroomPalette) == 0x000008, "Wrong alignment on WDG_ShowroomPalette_C_ExecuteUbergraph_WDG_ShowroomPalette");
static_assert(sizeof(WDG_ShowroomPalette_C_ExecuteUbergraph_WDG_ShowroomPalette) == 0x000018, "Wrong size on WDG_ShowroomPalette_C_ExecuteUbergraph_WDG_ShowroomPalette");
static_assert(offsetof(WDG_ShowroomPalette_C_ExecuteUbergraph_WDG_ShowroomPalette, EntryPoint) == 0x000000, "Member 'WDG_ShowroomPalette_C_ExecuteUbergraph_WDG_ShowroomPalette::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_ExecuteUbergraph_WDG_ShowroomPalette, K2Node_Event_Child) == 0x000008, "Member 'WDG_ShowroomPalette_C_ExecuteUbergraph_WDG_ShowroomPalette::K2Node_Event_Child' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_ExecuteUbergraph_WDG_ShowroomPalette, K2Node_ComponentBoundEvent_Sender) == 0x000010, "Member 'WDG_ShowroomPalette_C_ExecuteUbergraph_WDG_ShowroomPalette::K2Node_ComponentBoundEvent_Sender' has a wrong offset!");

// Function WDG_ShowroomPalette.WDG_ShowroomPalette_C.BndEvt__btnExit_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomPalette_C_BndEvt__btnExit_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature final
{
public:
	class UWDG_GenericBarItem_C*                  Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPalette_C_BndEvt__btnExit_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature) == 0x000008, "Wrong alignment on WDG_ShowroomPalette_C_BndEvt__btnExit_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature");
static_assert(sizeof(WDG_ShowroomPalette_C_BndEvt__btnExit_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature) == 0x000008, "Wrong size on WDG_ShowroomPalette_C_BndEvt__btnExit_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature");
static_assert(offsetof(WDG_ShowroomPalette_C_BndEvt__btnExit_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature, Sender) == 0x000000, "Member 'WDG_ShowroomPalette_C_BndEvt__btnExit_K2Node_ComponentBoundEvent_0_OnItemForward__DelegateSignature::Sender' has a wrong offset!");

// Function WDG_ShowroomPalette.WDG_ShowroomPalette_C.BP_OnChildrenBackward
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomPalette_C_BP_OnChildrenBackward final
{
public:
	class UAcPanelBase*                           Child;                                             // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPalette_C_BP_OnChildrenBackward) == 0x000008, "Wrong alignment on WDG_ShowroomPalette_C_BP_OnChildrenBackward");
static_assert(sizeof(WDG_ShowroomPalette_C_BP_OnChildrenBackward) == 0x000008, "Wrong size on WDG_ShowroomPalette_C_BP_OnChildrenBackward");
static_assert(offsetof(WDG_ShowroomPalette_C_BP_OnChildrenBackward, Child) == 0x000000, "Member 'WDG_ShowroomPalette_C_BP_OnChildrenBackward::Child' has a wrong offset!");

// Function WDG_ShowroomPalette.WDG_ShowroomPalette_C.Populate
// 0x0350 (0x0350 - 0x0000)
struct WDG_ShowroomPalette_C_Populate final
{
public:
	bool                                          Special;                                           // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<struct FSkinColor>                     colors;                                            // 0x0008(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0018(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x001C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x0020(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0024(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_25[0x3];                                       // 0x0025(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x0028(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x002C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_1;                   // 0x0030(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_34[0x4];                                       // 0x0034(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	struct FSkinColor                             CallFunc_Array_Get_Item;                           // 0x0038(0x0160)()
	struct FSkinColor                             CallFunc_Array_Get_Item_1;                         // 0x0198(0x0160)()
	bool                                          CallFunc_NotEqual_IntInt_ReturnValue;              // 0x02F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_NotEqual_IntInt_ReturnValue_1;            // 0x02F9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_2FA[0x6];                                      // 0x02FA(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomTileItemHorizontal_C*      CallFunc_CreateColorTile_ReturnValue;              // 0x0300(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      CallFunc_CreateColorTile_ReturnValue_1;            // 0x0308(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_SetTileColorByCode_Color;                 // 0x0310(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           CallFunc_SetTileColorByCode_Color_1;               // 0x0320(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue;                     // 0x0330(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UPanelSlot*                             CallFunc_AddChild_ReturnValue_1;                   // 0x0338(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Loop_Counter_Variable_1;                  // 0x0340(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue_1;                // 0x0344(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_345[0x3];                                      // 0x0345(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x0348(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPalette_C_Populate) == 0x000008, "Wrong alignment on WDG_ShowroomPalette_C_Populate");
static_assert(sizeof(WDG_ShowroomPalette_C_Populate) == 0x000350, "Wrong size on WDG_ShowroomPalette_C_Populate");
static_assert(offsetof(WDG_ShowroomPalette_C_Populate, Special) == 0x000000, "Member 'WDG_ShowroomPalette_C_Populate::Special' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Populate, colors) == 0x000008, "Member 'WDG_ShowroomPalette_C_Populate::colors' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Populate, CallFunc_Array_Length_ReturnValue) == 0x000018, "Member 'WDG_ShowroomPalette_C_Populate::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Populate, CallFunc_Array_Length_ReturnValue_1) == 0x00001C, "Member 'WDG_ShowroomPalette_C_Populate::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Populate, Temp_int_Loop_Counter_Variable) == 0x000020, "Member 'WDG_ShowroomPalette_C_Populate::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Populate, CallFunc_Less_IntInt_ReturnValue) == 0x000024, "Member 'WDG_ShowroomPalette_C_Populate::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Populate, CallFunc_Add_IntInt_ReturnValue) == 0x000028, "Member 'WDG_ShowroomPalette_C_Populate::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Populate, Temp_int_Array_Index_Variable) == 0x00002C, "Member 'WDG_ShowroomPalette_C_Populate::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Populate, Temp_int_Array_Index_Variable_1) == 0x000030, "Member 'WDG_ShowroomPalette_C_Populate::Temp_int_Array_Index_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Populate, CallFunc_Array_Get_Item) == 0x000038, "Member 'WDG_ShowroomPalette_C_Populate::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Populate, CallFunc_Array_Get_Item_1) == 0x000198, "Member 'WDG_ShowroomPalette_C_Populate::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Populate, CallFunc_NotEqual_IntInt_ReturnValue) == 0x0002F8, "Member 'WDG_ShowroomPalette_C_Populate::CallFunc_NotEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Populate, CallFunc_NotEqual_IntInt_ReturnValue_1) == 0x0002F9, "Member 'WDG_ShowroomPalette_C_Populate::CallFunc_NotEqual_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Populate, CallFunc_CreateColorTile_ReturnValue) == 0x000300, "Member 'WDG_ShowroomPalette_C_Populate::CallFunc_CreateColorTile_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Populate, CallFunc_CreateColorTile_ReturnValue_1) == 0x000308, "Member 'WDG_ShowroomPalette_C_Populate::CallFunc_CreateColorTile_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Populate, CallFunc_SetTileColorByCode_Color) == 0x000310, "Member 'WDG_ShowroomPalette_C_Populate::CallFunc_SetTileColorByCode_Color' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Populate, CallFunc_SetTileColorByCode_Color_1) == 0x000320, "Member 'WDG_ShowroomPalette_C_Populate::CallFunc_SetTileColorByCode_Color_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Populate, CallFunc_AddChild_ReturnValue) == 0x000330, "Member 'WDG_ShowroomPalette_C_Populate::CallFunc_AddChild_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Populate, CallFunc_AddChild_ReturnValue_1) == 0x000338, "Member 'WDG_ShowroomPalette_C_Populate::CallFunc_AddChild_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Populate, Temp_int_Loop_Counter_Variable_1) == 0x000340, "Member 'WDG_ShowroomPalette_C_Populate::Temp_int_Loop_Counter_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Populate, CallFunc_Less_IntInt_ReturnValue_1) == 0x000344, "Member 'WDG_ShowroomPalette_C_Populate::CallFunc_Less_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Populate, CallFunc_Add_IntInt_ReturnValue_1) == 0x000348, "Member 'WDG_ShowroomPalette_C_Populate::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");

// Function WDG_ShowroomPalette.WDG_ShowroomPalette_C.CreateColorTile
// 0x01A8 (0x01A8 - 0x0000)
struct WDG_ShowroomPalette_C_CreateColorTile final
{
public:
	struct FSkinColor                             SkinColor;                                         // 0x0000(0x0160)(ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
	class UWDG_ShowroomTileItemHorizontal_C*      ReturnValue;                                       // 0x0160(0x0008)(Parm, OutParm, ZeroConstructor, ReturnParm, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	TDelegate<void(class UWDG_ShowroomTileItemHorizontal_C* Sender, int32 ColorCode, const struct FLinearColor& Color)> K2Node_CreateDelegate_OutputDelegate; // 0x0168(0x0010)(ZeroConstructor, NoDestructor)
	TDelegate<void(class UWDG_ShowroomTileBase_C* Sender)> K2Node_CreateDelegate_OutputDelegate_1;   // 0x0178(0x0010)(ZeroConstructor, NoDestructor)
	class APlayerController*                      CallFunc_GetOwningPlayer_ReturnValue;              // 0x0188(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           K2Node_MakeStruct_LinearColor;                     // 0x0190(0x0010)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      CallFunc_Create_ReturnValue;                       // 0x01A0(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPalette_C_CreateColorTile) == 0x000008, "Wrong alignment on WDG_ShowroomPalette_C_CreateColorTile");
static_assert(sizeof(WDG_ShowroomPalette_C_CreateColorTile) == 0x0001A8, "Wrong size on WDG_ShowroomPalette_C_CreateColorTile");
static_assert(offsetof(WDG_ShowroomPalette_C_CreateColorTile, SkinColor) == 0x000000, "Member 'WDG_ShowroomPalette_C_CreateColorTile::SkinColor' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_CreateColorTile, ReturnValue) == 0x000160, "Member 'WDG_ShowroomPalette_C_CreateColorTile::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_CreateColorTile, K2Node_CreateDelegate_OutputDelegate) == 0x000168, "Member 'WDG_ShowroomPalette_C_CreateColorTile::K2Node_CreateDelegate_OutputDelegate' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_CreateColorTile, K2Node_CreateDelegate_OutputDelegate_1) == 0x000178, "Member 'WDG_ShowroomPalette_C_CreateColorTile::K2Node_CreateDelegate_OutputDelegate_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_CreateColorTile, CallFunc_GetOwningPlayer_ReturnValue) == 0x000188, "Member 'WDG_ShowroomPalette_C_CreateColorTile::CallFunc_GetOwningPlayer_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_CreateColorTile, K2Node_MakeStruct_LinearColor) == 0x000190, "Member 'WDG_ShowroomPalette_C_CreateColorTile::K2Node_MakeStruct_LinearColor' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_CreateColorTile, CallFunc_Create_ReturnValue) == 0x0001A0, "Member 'WDG_ShowroomPalette_C_CreateColorTile::CallFunc_Create_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPalette.WDG_ShowroomPalette_C.ColorSelected
// 0x0020 (0x0020 - 0x0000)
struct WDG_ShowroomPalette_C_ColorSelected final
{
public:
	class UWDG_ShowroomTileItemHorizontal_C*      Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Key;                                               // 0x0008(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FLinearColor                           Color;                                             // 0x000C(0x0010)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPalette_C_ColorSelected) == 0x000008, "Wrong alignment on WDG_ShowroomPalette_C_ColorSelected");
static_assert(sizeof(WDG_ShowroomPalette_C_ColorSelected) == 0x000020, "Wrong size on WDG_ShowroomPalette_C_ColorSelected");
static_assert(offsetof(WDG_ShowroomPalette_C_ColorSelected, Sender) == 0x000000, "Member 'WDG_ShowroomPalette_C_ColorSelected::Sender' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_ColorSelected, Key) == 0x000008, "Member 'WDG_ShowroomPalette_C_ColorSelected::Key' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_ColorSelected, Color) == 0x00000C, "Member 'WDG_ShowroomPalette_C_ColorSelected::Color' has a wrong offset!");

// Function WDG_ShowroomPalette.WDG_ShowroomPalette_C.Open
// 0x0080 (0x0080 - 0x0000)
struct WDG_ShowroomPalette_C_Open final
{
public:
	bool                                          for_aux_lights;                                    // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1[0x7];                                        // 0x0001(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomMaterialColorSelector_C*   Sender;                                            // 0x0008(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      FocusedTile;                                       // 0x0010(0x0008)(Parm, OutParm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWDG_ShowroomTileItemHorizontal_C*      TargetTile;                                        // 0x0018(0x0008)(Edit, BlueprintVisible, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          Temp_bool_Variable;                                // 0x0020(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x0021(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_22[0x2];                                       // 0x0022(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Variable;                                 // 0x0024(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_IsValid_ReturnValue;                      // 0x0028(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_29[0x3];                                       // 0x0029(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x002C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UWidget*                                CallFunc_GetChildAt_ReturnValue;                   // 0x0030(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_GetChildrenCount_ReturnValue;             // 0x0038(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3C[0x4];                                       // 0x003C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UWDG_ShowroomTileItemHorizontal_C*      K2Node_DynamicCast_AsWDG_Showroom_Tile_Item_Horizontal; // 0x0040(0x0008)(ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x0048(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	uint8                                         Pad_49[0x3];                                       // 0x0049(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Subtract_IntInt_ReturnValue;              // 0x004C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_LessEqual_IntInt_ReturnValue;             // 0x0050(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanAND_ReturnValue;                   // 0x0051(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_IntInt_ReturnValue;            // 0x0052(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_53[0x1];                                       // 0x0053(0x0001)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x0054(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_IntInt_ReturnValue;               // 0x0058(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_59[0x7];                                       // 0x0059(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	TArray<struct FSkinColor>                     CallFunc_getSkinColorsSorted_normal_colors;        // 0x0060(0x0010)(ReferenceParm)
	TArray<struct FSkinColor>                     CallFunc_getSkinColorsSorted_special_colors;       // 0x0070(0x0010)(ReferenceParm)
};
static_assert(alignof(WDG_ShowroomPalette_C_Open) == 0x000008, "Wrong alignment on WDG_ShowroomPalette_C_Open");
static_assert(sizeof(WDG_ShowroomPalette_C_Open) == 0x000080, "Wrong size on WDG_ShowroomPalette_C_Open");
static_assert(offsetof(WDG_ShowroomPalette_C_Open, for_aux_lights) == 0x000000, "Member 'WDG_ShowroomPalette_C_Open::for_aux_lights' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Open, Sender) == 0x000008, "Member 'WDG_ShowroomPalette_C_Open::Sender' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Open, FocusedTile) == 0x000010, "Member 'WDG_ShowroomPalette_C_Open::FocusedTile' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Open, TargetTile) == 0x000018, "Member 'WDG_ShowroomPalette_C_Open::TargetTile' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Open, Temp_bool_Variable) == 0x000020, "Member 'WDG_ShowroomPalette_C_Open::Temp_bool_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Open, CallFunc_Not_PreBool_ReturnValue) == 0x000021, "Member 'WDG_ShowroomPalette_C_Open::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Open, Temp_int_Variable) == 0x000024, "Member 'WDG_ShowroomPalette_C_Open::Temp_int_Variable' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Open, CallFunc_IsValid_ReturnValue) == 0x000028, "Member 'WDG_ShowroomPalette_C_Open::CallFunc_IsValid_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Open, CallFunc_Add_IntInt_ReturnValue) == 0x00002C, "Member 'WDG_ShowroomPalette_C_Open::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Open, CallFunc_GetChildAt_ReturnValue) == 0x000030, "Member 'WDG_ShowroomPalette_C_Open::CallFunc_GetChildAt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Open, CallFunc_GetChildrenCount_ReturnValue) == 0x000038, "Member 'WDG_ShowroomPalette_C_Open::CallFunc_GetChildrenCount_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Open, K2Node_DynamicCast_AsWDG_Showroom_Tile_Item_Horizontal) == 0x000040, "Member 'WDG_ShowroomPalette_C_Open::K2Node_DynamicCast_AsWDG_Showroom_Tile_Item_Horizontal' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Open, K2Node_DynamicCast_bSuccess) == 0x000048, "Member 'WDG_ShowroomPalette_C_Open::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Open, CallFunc_Subtract_IntInt_ReturnValue) == 0x00004C, "Member 'WDG_ShowroomPalette_C_Open::CallFunc_Subtract_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Open, CallFunc_LessEqual_IntInt_ReturnValue) == 0x000050, "Member 'WDG_ShowroomPalette_C_Open::CallFunc_LessEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Open, CallFunc_BooleanAND_ReturnValue) == 0x000051, "Member 'WDG_ShowroomPalette_C_Open::CallFunc_BooleanAND_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Open, CallFunc_EqualEqual_IntInt_ReturnValue) == 0x000052, "Member 'WDG_ShowroomPalette_C_Open::CallFunc_EqualEqual_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Open, CallFunc_Array_Length_ReturnValue) == 0x000054, "Member 'WDG_ShowroomPalette_C_Open::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Open, CallFunc_Greater_IntInt_ReturnValue) == 0x000058, "Member 'WDG_ShowroomPalette_C_Open::CallFunc_Greater_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Open, CallFunc_getSkinColorsSorted_normal_colors) == 0x000060, "Member 'WDG_ShowroomPalette_C_Open::CallFunc_getSkinColorsSorted_normal_colors' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Open, CallFunc_getSkinColorsSorted_special_colors) == 0x000070, "Member 'WDG_ShowroomPalette_C_Open::CallFunc_getSkinColorsSorted_special_colors' has a wrong offset!");

// Function WDG_ShowroomPalette.WDG_ShowroomPalette_C.Close
// 0x0002 (0x0002 - 0x0000)
struct WDG_ShowroomPalette_C_Close final
{
public:
	bool                                          Cancel;                                            // 0x0000(0x0001)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_IsVisible_ReturnValue;                    // 0x0001(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPalette_C_Close) == 0x000001, "Wrong alignment on WDG_ShowroomPalette_C_Close");
static_assert(sizeof(WDG_ShowroomPalette_C_Close) == 0x000002, "Wrong size on WDG_ShowroomPalette_C_Close");
static_assert(offsetof(WDG_ShowroomPalette_C_Close, Cancel) == 0x000000, "Member 'WDG_ShowroomPalette_C_Close::Cancel' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_Close, CallFunc_IsVisible_ReturnValue) == 0x000001, "Member 'WDG_ShowroomPalette_C_Close::CallFunc_IsVisible_ReturnValue' has a wrong offset!");

// Function WDG_ShowroomPalette.WDG_ShowroomPalette_C.ColorFocused
// 0x0008 (0x0008 - 0x0000)
struct WDG_ShowroomPalette_C_ColorFocused final
{
public:
	class UWDG_ShowroomTileBase_C*                Sender;                                            // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_ShowroomPalette_C_ColorFocused) == 0x000008, "Wrong alignment on WDG_ShowroomPalette_C_ColorFocused");
static_assert(sizeof(WDG_ShowroomPalette_C_ColorFocused) == 0x000008, "Wrong size on WDG_ShowroomPalette_C_ColorFocused");
static_assert(offsetof(WDG_ShowroomPalette_C_ColorFocused, Sender) == 0x000000, "Member 'WDG_ShowroomPalette_C_ColorFocused::Sender' has a wrong offset!");

// Function WDG_ShowroomPalette.WDG_ShowroomPalette_C.OnPreviewKeyDown
// 0x02C0 (0x02C0 - 0x0000)
struct WDG_ShowroomPalette_C_OnPreviewKeyDown final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	struct FKeyEvent                              InKeyEvent;                                        // 0x0038(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm)
	struct FEventReply                            ReturnValue;                                       // 0x0070(0x00B8)(Parm, OutParm, ReturnParm)
	struct FKey                                   CallFunc_GetKey_ReturnValue;                       // 0x0128(0x0018)(HasGetValueTypeHash)
	struct FEventReply                            CallFunc_Handled_ReturnValue;                      // 0x0140(0x00B8)()
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue;            // 0x01F8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue_1;          // 0x01F9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_EqualEqual_KeyKey_ReturnValue_2;          // 0x01FA(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1FB[0x5];                                      // 0x01FB(0x0005)(Fixing Size After Last Property [ Dumper-7 ])
	struct FEventReply                            CallFunc_Unhandled_ReturnValue;                    // 0x0200(0x00B8)()
	bool                                          CallFunc_BooleanOR_ReturnValue;                    // 0x02B8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_BooleanOR_ReturnValue_1;                  // 0x02B9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_ShowroomPalette_C_OnPreviewKeyDown) == 0x000008, "Wrong alignment on WDG_ShowroomPalette_C_OnPreviewKeyDown");
static_assert(sizeof(WDG_ShowroomPalette_C_OnPreviewKeyDown) == 0x0002C0, "Wrong size on WDG_ShowroomPalette_C_OnPreviewKeyDown");
static_assert(offsetof(WDG_ShowroomPalette_C_OnPreviewKeyDown, MyGeometry) == 0x000000, "Member 'WDG_ShowroomPalette_C_OnPreviewKeyDown::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_OnPreviewKeyDown, InKeyEvent) == 0x000038, "Member 'WDG_ShowroomPalette_C_OnPreviewKeyDown::InKeyEvent' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_OnPreviewKeyDown, ReturnValue) == 0x000070, "Member 'WDG_ShowroomPalette_C_OnPreviewKeyDown::ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_OnPreviewKeyDown, CallFunc_GetKey_ReturnValue) == 0x000128, "Member 'WDG_ShowroomPalette_C_OnPreviewKeyDown::CallFunc_GetKey_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_OnPreviewKeyDown, CallFunc_Handled_ReturnValue) == 0x000140, "Member 'WDG_ShowroomPalette_C_OnPreviewKeyDown::CallFunc_Handled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_OnPreviewKeyDown, CallFunc_EqualEqual_KeyKey_ReturnValue) == 0x0001F8, "Member 'WDG_ShowroomPalette_C_OnPreviewKeyDown::CallFunc_EqualEqual_KeyKey_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_OnPreviewKeyDown, CallFunc_EqualEqual_KeyKey_ReturnValue_1) == 0x0001F9, "Member 'WDG_ShowroomPalette_C_OnPreviewKeyDown::CallFunc_EqualEqual_KeyKey_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_OnPreviewKeyDown, CallFunc_EqualEqual_KeyKey_ReturnValue_2) == 0x0001FA, "Member 'WDG_ShowroomPalette_C_OnPreviewKeyDown::CallFunc_EqualEqual_KeyKey_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_OnPreviewKeyDown, CallFunc_Unhandled_ReturnValue) == 0x000200, "Member 'WDG_ShowroomPalette_C_OnPreviewKeyDown::CallFunc_Unhandled_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_OnPreviewKeyDown, CallFunc_BooleanOR_ReturnValue) == 0x0002B8, "Member 'WDG_ShowroomPalette_C_OnPreviewKeyDown::CallFunc_BooleanOR_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_ShowroomPalette_C_OnPreviewKeyDown, CallFunc_BooleanOR_ReturnValue_1) == 0x0002B9, "Member 'WDG_ShowroomPalette_C_OnPreviewKeyDown::CallFunc_BooleanOR_ReturnValue_1' has a wrong offset!");

}

