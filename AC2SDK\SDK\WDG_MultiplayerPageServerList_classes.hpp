﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_MultiplayerPageServerList

#include "Basic.hpp"

#include "SlateCore_structs.hpp"
#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_MultiplayerPageServerList.WDG_MultiplayerPageServerList_C
// 0x0088 (0x0998 - 0x0910)
class UWDG_MultiplayerPageServerList_C final : public UMultiplayerPageServerList
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0910(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UImage*                                 bgImage;                                           // 0x0918(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, Is<PERSON>lainOldD<PERSON>, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                brdFilterWrapper;                                  // 0x0920(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnJoinable;                                       // 0x0928(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnSortDrivers;                                    // 0x0930(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericBarItem_C*                  btnSortPing;                                       // 0x0938(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_MPCarGroupSelector_C*              carGroupSelector;                                  // 0x0940(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Footer_C*                          Footer;                                            // 0x0948(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Header_C*                          Header;                                            // 0x0950(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_Page_C*                            PageContainer;                                     // 0x0958(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_DLCWarningPopup_C*                 popupDLC;                                          // 0x0960(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_HorizontalSlider_C*                sliderPingLimit;                                   // 0x0968(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtPingCount;                                      // 0x0970(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             txtPinging;                                        // 0x0978(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         txtPingWrapper;                                    // 0x0980(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_KSONConnectionState_C*             WDG_KSONConnectionState;                           // 0x0988(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ServerInfoItem_C*                  WDG_ServerInfoItem;                                // 0x0990(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_MultiplayerPageServerList(int32 EntryPoint);
	void BndEvt__ServerList_K2Node_ComponentBoundEvent_3_OnUserScrolledEvent__DelegateSignature(float CurrentOffset);
	void ServerPinged(int32 number_of_servers_pings, int32 total_servers);
	void ListReceived();
	void ListSorted();
	void ListTimedOut();
	void ListRequested();
	void BndEvt__btnJoinable_K2Node_ComponentBoundEvent_12_OnToggled__DelegateSignature(bool isOn);
	void BndEvt__sliderPingLimit_K2Node_ComponentBoundEvent_11_SelectorChanged__DelegateSignature(class UGenericSelectorItem* Source, int32 current_index, int32 current_value);
	void BndEvt__btnSortPing_K2Node_ComponentBoundEvent_10_OnToggled__DelegateSignature(bool isOn);
	void BndEvt__btnSortDrivers_K2Node_ComponentBoundEvent_9_OnToggled__DelegateSignature(bool isOn);
	void BndEvt__btnAdvanced_K2Node_ComponentBoundEvent_8_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__btnCarSelection_K2Node_ComponentBoundEvent_7_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__btnConnect_K2Node_ComponentBoundEvent_6_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__btnQuickjoin_K2Node_ComponentBoundEvent_5_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__btnRefreshLAN_K2Node_ComponentBoundEvent_4_OnAcPanelForwardEvent__DelegateSignature();
	void BndEvt__btnRefresh_K2Node_ComponentBoundEvent_3_OnAcPanelForwardEvent__DelegateSignature();
	void OnServerDisconnection(EDisconnectionReason reason);
	void BndEvt__carGroupSelector_K2Node_ComponentBoundEvent_2_OnCarGroupSelected__DelegateSignature(EMPCarGroup CarGroup);
	void BndEvt__popupDLC_K2Node_ComponentBoundEvent_1_OnYes__DelegateSignature();
	void BndEvt__popupDLC_K2Node_ComponentBoundEvent_0_OnNo__DelegateSignature();
	void OnSeasonUnavailable(EContentType content_type);
	void BP_StartPage();
	void BP_JoinServerFailed();
	class UWidget* FocusToConnect(EUINavigation Navigation_0);
	class UWidget* FocusToRefresh(EUINavigation Navigation_0);
	bool PreConnectionCheck(const struct FCarInfo& Car, const struct FCircuitInfo& circuit);
	void ShowErrorMessage(const class FText& MessageText);
	void HideErrorMessage();
	struct FEventReply OnPreviewKeyDown(const struct FGeometry& MyGeometry, const struct FKeyEvent& InKeyEvent);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_MultiplayerPageServerList_C">();
	}
	static class UWDG_MultiplayerPageServerList_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_MultiplayerPageServerList_C>();
	}
};
static_assert(alignof(UWDG_MultiplayerPageServerList_C) == 0x000008, "Wrong alignment on UWDG_MultiplayerPageServerList_C");
static_assert(sizeof(UWDG_MultiplayerPageServerList_C) == 0x000998, "Wrong size on UWDG_MultiplayerPageServerList_C");
static_assert(offsetof(UWDG_MultiplayerPageServerList_C, UberGraphFrame) == 0x000910, "Member 'UWDG_MultiplayerPageServerList_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPageServerList_C, bgImage) == 0x000918, "Member 'UWDG_MultiplayerPageServerList_C::bgImage' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPageServerList_C, brdFilterWrapper) == 0x000920, "Member 'UWDG_MultiplayerPageServerList_C::brdFilterWrapper' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPageServerList_C, btnJoinable) == 0x000928, "Member 'UWDG_MultiplayerPageServerList_C::btnJoinable' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPageServerList_C, btnSortDrivers) == 0x000930, "Member 'UWDG_MultiplayerPageServerList_C::btnSortDrivers' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPageServerList_C, btnSortPing) == 0x000938, "Member 'UWDG_MultiplayerPageServerList_C::btnSortPing' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPageServerList_C, carGroupSelector) == 0x000940, "Member 'UWDG_MultiplayerPageServerList_C::carGroupSelector' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPageServerList_C, Footer) == 0x000948, "Member 'UWDG_MultiplayerPageServerList_C::Footer' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPageServerList_C, Header) == 0x000950, "Member 'UWDG_MultiplayerPageServerList_C::Header' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPageServerList_C, PageContainer) == 0x000958, "Member 'UWDG_MultiplayerPageServerList_C::PageContainer' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPageServerList_C, popupDLC) == 0x000960, "Member 'UWDG_MultiplayerPageServerList_C::popupDLC' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPageServerList_C, sliderPingLimit) == 0x000968, "Member 'UWDG_MultiplayerPageServerList_C::sliderPingLimit' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPageServerList_C, txtPingCount) == 0x000970, "Member 'UWDG_MultiplayerPageServerList_C::txtPingCount' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPageServerList_C, txtPinging) == 0x000978, "Member 'UWDG_MultiplayerPageServerList_C::txtPinging' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPageServerList_C, txtPingWrapper) == 0x000980, "Member 'UWDG_MultiplayerPageServerList_C::txtPingWrapper' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPageServerList_C, WDG_KSONConnectionState) == 0x000988, "Member 'UWDG_MultiplayerPageServerList_C::WDG_KSONConnectionState' has a wrong offset!");
static_assert(offsetof(UWDG_MultiplayerPageServerList_C, WDG_ServerInfoItem) == 0x000990, "Member 'UWDG_MultiplayerPageServerList_C::WDG_ServerInfoItem' has a wrong offset!");

}

