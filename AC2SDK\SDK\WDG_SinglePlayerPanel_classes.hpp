﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_SinglePlayerPanel

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_SinglePlayerPanel.WDG_SinglePlayerPanel_C
// 0x0060 (0x0640 - 0x05E0)
class UWDG_SinglePlayerPanel_C final : public UAcPanelBase
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x05E0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UWidgetAnimation*                       OpacityAnim;                                       // 0x05E8(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UWidgetAnimation*                       ScaleAnim;                                         // 0x05F0(0x0008)(BlueprintVisible, BlueprintReadOnly, ZeroConstructor, Transient, IsPlainOldData, RepSkip, NoDestructor, HasGetValueTypeHash)
	class UNamedSlot*                             CarModelSlot;                                      // 0x05F8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 HoverImageBox;                                     // 0x0600(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UImage*                                 NormalImageBox;                                    // 0x0608(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           PanelToHide;                                       // 0x0610(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             Singleplayer_lbl;                                  // 0x0618(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             SubTitleSlot;                                      // 0x0620(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UNamedSlot*                             TrackSlot;                                         // 0x0628(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UCanvasPanel*                           WhiteText;                                         // 0x0630(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UObject*                                SaveMenu;                                          // 0x0638(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, IsPlainOldData, NoDestructor, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_SinglePlayerPanel(int32 EntryPoint);
	void PreConstruct(bool IsDesignTime);
	void BP_MouseLeave();
	void BP_MouseOver();

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_SinglePlayerPanel_C">();
	}
	static class UWDG_SinglePlayerPanel_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_SinglePlayerPanel_C>();
	}
};
static_assert(alignof(UWDG_SinglePlayerPanel_C) == 0x000008, "Wrong alignment on UWDG_SinglePlayerPanel_C");
static_assert(sizeof(UWDG_SinglePlayerPanel_C) == 0x000640, "Wrong size on UWDG_SinglePlayerPanel_C");
static_assert(offsetof(UWDG_SinglePlayerPanel_C, UberGraphFrame) == 0x0005E0, "Member 'UWDG_SinglePlayerPanel_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPanel_C, OpacityAnim) == 0x0005E8, "Member 'UWDG_SinglePlayerPanel_C::OpacityAnim' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPanel_C, ScaleAnim) == 0x0005F0, "Member 'UWDG_SinglePlayerPanel_C::ScaleAnim' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPanel_C, CarModelSlot) == 0x0005F8, "Member 'UWDG_SinglePlayerPanel_C::CarModelSlot' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPanel_C, HoverImageBox) == 0x000600, "Member 'UWDG_SinglePlayerPanel_C::HoverImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPanel_C, NormalImageBox) == 0x000608, "Member 'UWDG_SinglePlayerPanel_C::NormalImageBox' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPanel_C, PanelToHide) == 0x000610, "Member 'UWDG_SinglePlayerPanel_C::PanelToHide' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPanel_C, Singleplayer_lbl) == 0x000618, "Member 'UWDG_SinglePlayerPanel_C::Singleplayer_lbl' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPanel_C, SubTitleSlot) == 0x000620, "Member 'UWDG_SinglePlayerPanel_C::SubTitleSlot' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPanel_C, TrackSlot) == 0x000628, "Member 'UWDG_SinglePlayerPanel_C::TrackSlot' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPanel_C, WhiteText) == 0x000630, "Member 'UWDG_SinglePlayerPanel_C::WhiteText' has a wrong offset!");
static_assert(offsetof(UWDG_SinglePlayerPanel_C, SaveMenu) == 0x000638, "Member 'UWDG_SinglePlayerPanel_C::SaveMenu' has a wrong offset!");

}

