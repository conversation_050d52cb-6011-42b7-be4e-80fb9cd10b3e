﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TVSessionTimeTable

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_TVSessionTimeTable.WDG_TVSessionTimeTable_C
// 0x0010 (0x0670 - 0x0660)
class UWDG_TVSessionTimeTable_C final : public UTVSessionTimeTableWidget
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x0660(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UInvalidationBox*                       InvalidationBox_0;                                 // 0x0668(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)

public:
	void ExecuteUbergraph_WDG_TVSessionTimeTable(int32 EntryPoint);
	void OnHudVisibility(bool IsWidgetVisible);
	void Construct();
	struct FEventReply OnMouseWheel(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_TVSessionTimeTable_C">();
	}
	static class UWDG_TVSessionTimeTable_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_TVSessionTimeTable_C>();
	}
};
static_assert(alignof(UWDG_TVSessionTimeTable_C) == 0x000008, "Wrong alignment on UWDG_TVSessionTimeTable_C");
static_assert(sizeof(UWDG_TVSessionTimeTable_C) == 0x000670, "Wrong size on UWDG_TVSessionTimeTable_C");
static_assert(offsetof(UWDG_TVSessionTimeTable_C, UberGraphFrame) == 0x000660, "Member 'UWDG_TVSessionTimeTable_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_TVSessionTimeTable_C, InvalidationBox_0) == 0x000668, "Member 'UWDG_TVSessionTimeTable_C::InvalidationBox_0' has a wrong offset!");

}

