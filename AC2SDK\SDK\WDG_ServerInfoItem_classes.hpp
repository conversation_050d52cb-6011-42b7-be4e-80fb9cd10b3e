﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_ServerInfoItem

#include "Basic.hpp"

#include "Engine_structs.hpp"
#include "AC2_structs.hpp"
#include "AC2_classes.hpp"


namespace SDK
{

// WidgetBlueprintGeneratedClass WDG_ServerInfoItem.WDG_ServerInfoItem_C
// 0x0238 (0x0AE8 - 0x08B0)
class UWDG_ServerInfoItem_C final : public UServerInfoItem
{
public:
	struct FPointerToUberGraphFrame               UberGraphFrame;                                    // 0x08B0(0x0008)(ZeroConstructor, Transient, DuplicateTransient)
	class UCanvasPanel*                           CanvasPanel_0;                                     // 0x08B8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_GenericMappedLabel_C*              lblCarGroup;                                       // 0x08C0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UTextBlock*                             lblServerPassword;                                 // 0x08C8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         Main;                                              // 0x08D0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UBorder*                                PasswordContainer;                                 // 0x08D8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UHorizontalBox*                         sessions;                                          // 0x08E0(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class USpacer*                                Spacer_68;                                         // 0x08E8(0x0008)(BlueprintVisible, ExportObject, ZeroConstructor, InstancedReference, IsPlainOldData, RepSkip, NoDestructor, PersistentInstance, HasGetValueTypeHash)
	class UWDG_ServerInfoSessionItem_C*           SessionInfoItemClass;                              // 0x08F0(0x0008)(Edit, BlueprintVisible, ZeroConstructor, DisableEditOnInstance, InstancedReference, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FCircuitInfo                           circuit;                                           // 0x08F8(0x01F0)(Edit, BlueprintVisible, DisableEditOnInstance)

public:
	void ExecuteUbergraph_WDG_ServerInfoItem(int32 EntryPoint);
	void Tick(const struct FGeometry& MyGeometry, float InDeltaTime);
	void BP_MouseFocus(bool mouse_enter);
	void OnSetServerInfoComplete(const struct FOnlineServicesMPServerInfo& receivedServerInfo);

public:
	static class UClass* StaticClass()
	{
		return StaticBPGeneratedClassImpl<"WDG_ServerInfoItem_C">();
	}
	static class UWDG_ServerInfoItem_C* GetDefaultObj()
	{
		return GetDefaultObjImpl<UWDG_ServerInfoItem_C>();
	}
};
static_assert(alignof(UWDG_ServerInfoItem_C) == 0x000008, "Wrong alignment on UWDG_ServerInfoItem_C");
static_assert(sizeof(UWDG_ServerInfoItem_C) == 0x000AE8, "Wrong size on UWDG_ServerInfoItem_C");
static_assert(offsetof(UWDG_ServerInfoItem_C, UberGraphFrame) == 0x0008B0, "Member 'UWDG_ServerInfoItem_C::UberGraphFrame' has a wrong offset!");
static_assert(offsetof(UWDG_ServerInfoItem_C, CanvasPanel_0) == 0x0008B8, "Member 'UWDG_ServerInfoItem_C::CanvasPanel_0' has a wrong offset!");
static_assert(offsetof(UWDG_ServerInfoItem_C, lblCarGroup) == 0x0008C0, "Member 'UWDG_ServerInfoItem_C::lblCarGroup' has a wrong offset!");
static_assert(offsetof(UWDG_ServerInfoItem_C, lblServerPassword) == 0x0008C8, "Member 'UWDG_ServerInfoItem_C::lblServerPassword' has a wrong offset!");
static_assert(offsetof(UWDG_ServerInfoItem_C, Main) == 0x0008D0, "Member 'UWDG_ServerInfoItem_C::Main' has a wrong offset!");
static_assert(offsetof(UWDG_ServerInfoItem_C, PasswordContainer) == 0x0008D8, "Member 'UWDG_ServerInfoItem_C::PasswordContainer' has a wrong offset!");
static_assert(offsetof(UWDG_ServerInfoItem_C, sessions) == 0x0008E0, "Member 'UWDG_ServerInfoItem_C::sessions' has a wrong offset!");
static_assert(offsetof(UWDG_ServerInfoItem_C, Spacer_68) == 0x0008E8, "Member 'UWDG_ServerInfoItem_C::Spacer_68' has a wrong offset!");
static_assert(offsetof(UWDG_ServerInfoItem_C, SessionInfoItemClass) == 0x0008F0, "Member 'UWDG_ServerInfoItem_C::SessionInfoItemClass' has a wrong offset!");
static_assert(offsetof(UWDG_ServerInfoItem_C, circuit) == 0x0008F8, "Member 'UWDG_ServerInfoItem_C::circuit' has a wrong offset!");

}

