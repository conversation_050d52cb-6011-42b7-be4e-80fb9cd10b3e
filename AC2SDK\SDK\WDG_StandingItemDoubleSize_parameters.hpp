﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_StandingItemDoubleSize

#include "Basic.hpp"


namespace SDK::Params
{

// Function WDG_StandingItemDoubleSize.WDG_StandingItemDoubleSize_C.ExecuteUbergraph_WDG_StandingItemDoubleSize
// 0x0004 (0x0004 - 0x0000)
struct WDG_StandingItemDoubleSize_C_ExecuteUbergraph_WDG_StandingItemDoubleSize final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_StandingItemDoubleSize_C_ExecuteUbergraph_WDG_StandingItemDoubleSize) == 0x000004, "Wrong alignment on WDG_StandingItemDoubleSize_C_ExecuteUbergraph_WDG_StandingItemDoubleSize");
static_assert(sizeof(WDG_StandingItemDoubleSize_C_ExecuteUbergraph_WDG_StandingItemDoubleSize) == 0x000004, "Wrong size on WDG_StandingItemDoubleSize_C_ExecuteUbergraph_WDG_StandingItemDoubleSize");
static_assert(offsetof(WDG_StandingItemDoubleSize_C_ExecuteUbergraph_WDG_StandingItemDoubleSize, EntryPoint) == 0x000000, "Member 'WDG_StandingItemDoubleSize_C_ExecuteUbergraph_WDG_StandingItemDoubleSize::EntryPoint' has a wrong offset!");

}

