﻿#pragma once

/*
* SDK generated by Dumper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_TrackMapFull

#include "Basic.hpp"

#include "WDG_TrackMapFull_classes.hpp"
#include "WDG_TrackMapFull_parameters.hpp"


namespace SDK
{

// Function WDG_TrackMapFull.WDG_TrackMapFull_C.OnMouseButtonDown
// (BlueprintCosmetic, Event, Public, HasOutParams, HasDefaults, BlueprintCallable, BlueprintEvent)
// Parameters:
// const struct FGeometry&                 MyGeometry                                             (BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
// const struct FPointerEvent&             MouseEvent                                             (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm)
// struct FEventReply                      ReturnValue                                            (Parm, OutParm, ReturnParm)

struct FEventReply UWDG_TrackMapFull_C::OnMouseButtonDown(const struct FGeometry& MyGeometry, const struct FPointerEvent& MouseEvent)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TrackMapFull_C", "OnMouseButtonDown");

	Params::WDG_TrackMapFull_C_OnMouseButtonDown Parms{};

	Parms.MyGeometry = std::move(MyGeometry);
	Parms.MouseEvent = std::move(MouseEvent);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}


// Function WDG_TrackMapFull.WDG_TrackMapFull_C.IsWidgetDefinitionEnabled
// (Event, Public, HasOutParams, BlueprintCallable, BlueprintEvent)
// Parameters:
// class UAcGameInstance*                  GameInstance                                           (BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
// const struct FHUDOptions&               HUDOptions                                             (ConstParm, BlueprintVisible, BlueprintReadOnly, Parm, OutParm, ReferenceParm, NoDestructor)
// bool                                    ReturnValue                                            (Parm, OutParm, ZeroConstructor, ReturnParm, IsPlainOldData, NoDestructor)

bool UWDG_TrackMapFull_C::IsWidgetDefinitionEnabled(class UAcGameInstance* GameInstance, const struct FHUDOptions& HUDOptions)
{
	static class UFunction* Func = nullptr;

	if (Func == nullptr)
		Func = Class->GetFunction("WDG_TrackMapFull_C", "IsWidgetDefinitionEnabled");

	Params::WDG_TrackMapFull_C_IsWidgetDefinitionEnabled Parms{};

	Parms.GameInstance = GameInstance;
	Parms.HUDOptions = std::move(HUDOptions);

	UObject::ProcessEvent(Func, &Parms);

	return Parms.ReturnValue;
}

}

