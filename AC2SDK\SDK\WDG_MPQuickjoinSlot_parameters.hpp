﻿#pragma once

/*
* SDK generated by <PERSON>mper-7
*
* https://github.com/Encryqed/Dumper-7
*/

// Package: WDG_MPQuickjoinSlot

#include "Basic.hpp"

#include "CoreUObject_structs.hpp"
#include "AC2_structs.hpp"
#include "SlateCore_structs.hpp"


namespace SDK::Params
{

// Function WDG_MPQuickjoinSlot.WDG_MPQuickjoinSlot_C.ExecuteUbergraph_WDG_MPQuickjoinSlot
// 0x00B0 (0x00B0 - 0x0000)
struct WDG_MPQuickjoinSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlot final
{
public:
	int32                                         EntryPoint;                                        // 0x0000(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FGeometry                              K2Node_Event_MyGeometry;                           // 0x0004(0x0038)(IsPlainOldD<PERSON>, NoDestructor)
	float                                         K2Node_Event_InDeltaTime;                          // 0x003C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationForward_ReturnValue;         // 0x0040(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationReverse_ReturnValue;         // 0x0048(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationForward_ReturnValue_1;       // 0x0050(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UUMGSequencePlayer*                     CallFunc_PlayAnimationReverse_ReturnValue_1;       // 0x0058(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FSlateColor                            K2Node_MakeStruct_SlateColor;                      // 0x0060(0x0028)(UObjectWrapper)
	struct FSlateColor                            K2Node_MakeStruct_SlateColor_1;                    // 0x0088(0x0028)(UObjectWrapper)
};
static_assert(alignof(WDG_MPQuickjoinSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlot) == 0x000008, "Wrong alignment on WDG_MPQuickjoinSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlot");
static_assert(sizeof(WDG_MPQuickjoinSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlot) == 0x0000B0, "Wrong size on WDG_MPQuickjoinSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlot");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlot, EntryPoint) == 0x000000, "Member 'WDG_MPQuickjoinSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlot::EntryPoint' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlot, K2Node_Event_MyGeometry) == 0x000004, "Member 'WDG_MPQuickjoinSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlot::K2Node_Event_MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlot, K2Node_Event_InDeltaTime) == 0x00003C, "Member 'WDG_MPQuickjoinSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlot::K2Node_Event_InDeltaTime' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlot, CallFunc_PlayAnimationForward_ReturnValue) == 0x000040, "Member 'WDG_MPQuickjoinSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlot::CallFunc_PlayAnimationForward_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlot, CallFunc_PlayAnimationReverse_ReturnValue) == 0x000048, "Member 'WDG_MPQuickjoinSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlot::CallFunc_PlayAnimationReverse_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlot, CallFunc_PlayAnimationForward_ReturnValue_1) == 0x000050, "Member 'WDG_MPQuickjoinSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlot::CallFunc_PlayAnimationForward_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlot, CallFunc_PlayAnimationReverse_ReturnValue_1) == 0x000058, "Member 'WDG_MPQuickjoinSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlot::CallFunc_PlayAnimationReverse_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlot, K2Node_MakeStruct_SlateColor) == 0x000060, "Member 'WDG_MPQuickjoinSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlot::K2Node_MakeStruct_SlateColor' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlot, K2Node_MakeStruct_SlateColor_1) == 0x000088, "Member 'WDG_MPQuickjoinSlot_C_ExecuteUbergraph_WDG_MPQuickjoinSlot::K2Node_MakeStruct_SlateColor_1' has a wrong offset!");

// Function WDG_MPQuickjoinSlot.WDG_MPQuickjoinSlot_C.Tick
// 0x003C (0x003C - 0x0000)
struct WDG_MPQuickjoinSlot_C_Tick final
{
public:
	struct FGeometry                              MyGeometry;                                        // 0x0000(0x0038)(BlueprintVisible, BlueprintReadOnly, Parm, IsPlainOldData, NoDestructor)
	float                                         InDeltaTime;                                       // 0x0038(0x0004)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
};
static_assert(alignof(WDG_MPQuickjoinSlot_C_Tick) == 0x000004, "Wrong alignment on WDG_MPQuickjoinSlot_C_Tick");
static_assert(sizeof(WDG_MPQuickjoinSlot_C_Tick) == 0x00003C, "Wrong size on WDG_MPQuickjoinSlot_C_Tick");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_Tick, MyGeometry) == 0x000000, "Member 'WDG_MPQuickjoinSlot_C_Tick::MyGeometry' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_Tick, InDeltaTime) == 0x000038, "Member 'WDG_MPQuickjoinSlot_C_Tick::InDeltaTime' has a wrong offset!");

// Function WDG_MPQuickjoinSlot.WDG_MPQuickjoinSlot_C.SetServer
// 0x04E8 (0x04E8 - 0x0000)
struct WDG_MPQuickjoinSlot_C_SetServer final
{
public:
	struct FOnlineServicesMPServerInfo            ServerInfo;                                        // 0x0000(0x00B0)(BlueprintVisible, BlueprintReadOnly, Parm)
	class FText                                   waitingIndicatorText;                              // 0x00B0(0x0018)(BlueprintVisible, BlueprintReadOnly, Parm)
	bool                                          CallFunc_TextIsEmpty_ReturnValue;                  // 0x00C8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          CallFunc_Not_PreBool_ReturnValue;                  // 0x00C9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_CA[0x2];                                       // 0x00CA(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Loop_Counter_Variable;                    // 0x00CC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue;                   // 0x00D0(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable;                     // 0x00D4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_1;                   // 0x00D8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         Temp_int_Array_Index_Variable_2;                   // 0x00DC(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FOnlineServicesMPServerSessionInfo     CallFunc_Array_Get_Item;                           // 0x00E0(0x000C)(NoDestructor)
	struct FOnlineServicesMPServerSessionInfo     CallFunc_Array_Get_Item_1;                         // 0x00EC(0x000C)(NoDestructor)
	class FText                                   CallFunc_Conv_IntToText_ReturnValue;               // 0x00F8(0x0018)()
	class FText                                   CallFunc_AppendToFText_ReturnValue;                // 0x0110(0x0018)()
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_1;             // 0x0128(0x0018)()
	class FText                                   CallFunc_AppendToFText_ReturnValue_1;              // 0x0140(0x0018)()
	bool                                          K2Node_SwitchEnum_CmpSuccess;                      // 0x0158(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	bool                                          K2Node_SwitchEnum_CmpSuccess_1;                    // 0x0159(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_15A[0x2];                                      // 0x015A(0x0002)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_Array_Length_ReturnValue;                 // 0x015C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_1;               // 0x0160(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_Less_IntInt_ReturnValue;                  // 0x0164(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_165[0x3];                                      // 0x0165(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         Temp_int_Loop_Counter_Variable_1;                  // 0x0168(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Add_IntInt_ReturnValue_1;                 // 0x016C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FOnlineServicesMPServerSessionInfo     CallFunc_Array_Get_Item_2;                         // 0x0170(0x000C)(NoDestructor)
	uint8                                         Pad_17C[0x4];                                      // 0x017C(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x0180(0x0018)()
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_2;             // 0x0198(0x0018)()
	class FText                                   CallFunc_AppendToFText_ReturnValue_2;              // 0x01B0(0x0018)()
	bool                                          K2Node_SwitchEnum_CmpSuccess_2;                    // 0x01C8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_1C9[0x3];                                      // 0x01C9(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	class FName                                   CallFunc_Conv_StringToName_ReturnValue;            // 0x01CC(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_Array_Length_ReturnValue_2;               // 0x01D4(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	struct FCircuitInfo                           K2Node_MakeStruct_CircuitInfo;                     // 0x01D8(0x01F0)(UObjectWrapper)
	bool                                          CallFunc_Less_IntInt_ReturnValue_1;                // 0x03C8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_3C9[0x7];                                      // 0x03C9(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_StringToText_ReturnValue_1;          // 0x03D0(0x0018)()
	bool                                          CallFunc_Less_IntInt_ReturnValue_2;                // 0x03E8(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         CallFunc_Conv_IntToByte_ReturnValue;               // 0x03E9(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_3EA[0x6];                                      // 0x03EA(0x0006)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_ByteToText_ReturnValue;              // 0x03F0(0x0018)()
	class FText                                   CallFunc_AppendToFText_ReturnValue_3;              // 0x0408(0x0018)()
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue;              // 0x0420(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_421[0x7];                                      // 0x0421(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_ByteToText_ReturnValue_1;            // 0x0428(0x0018)()
	uint8                                         CallFunc_MakeLiteralByte_ReturnValue_1;            // 0x0440(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_441[0x7];                                      // 0x0441(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_3;             // 0x0448(0x0018)()
	int32                                         Temp_int_Loop_Counter_Variable_2;                  // 0x0460(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_464[0x4];                                      // 0x0464(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Conv_IntToString_ReturnValue;             // 0x0468(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_IntToText_ReturnValue_4;             // 0x0478(0x0018)()
	bool                                          CallFunc_Less_IntInt_ReturnValue_3;                // 0x0490(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_491[0x7];                                      // 0x0491(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FText                                   CallFunc_AppendToFText_ReturnValue_4;              // 0x0498(0x0018)()
	class FText                                   CallFunc_AppendToFText_ReturnValue_5;              // 0x04B0(0x0018)()
	int32                                         CallFunc_Add_IntInt_ReturnValue_2;                 // 0x04C8(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	uint8                                         Pad_4CC[0x4];                                      // 0x04CC(0x0004)(Fixing Size After Last Property [ Dumper-7 ])
	class UGameInstance*                          CallFunc_GetGameInstance_ReturnValue;              // 0x04D0(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class UAcGameInstance*                        K2Node_DynamicCast_AsAc_Game_Instance;             // 0x04D8(0x0008)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper, HasGetValueTypeHash)
	bool                                          K2Node_DynamicCast_bSuccess;                       // 0x04E0(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor, UObjectWrapper)
	bool                                          CallFunc_GetCircuitInfo_ReturnValue;               // 0x04E1(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
};
static_assert(alignof(WDG_MPQuickjoinSlot_C_SetServer) == 0x000008, "Wrong alignment on WDG_MPQuickjoinSlot_C_SetServer");
static_assert(sizeof(WDG_MPQuickjoinSlot_C_SetServer) == 0x0004E8, "Wrong size on WDG_MPQuickjoinSlot_C_SetServer");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, ServerInfo) == 0x000000, "Member 'WDG_MPQuickjoinSlot_C_SetServer::ServerInfo' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, waitingIndicatorText) == 0x0000B0, "Member 'WDG_MPQuickjoinSlot_C_SetServer::waitingIndicatorText' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_TextIsEmpty_ReturnValue) == 0x0000C8, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_TextIsEmpty_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Not_PreBool_ReturnValue) == 0x0000C9, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Not_PreBool_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, Temp_int_Loop_Counter_Variable) == 0x0000CC, "Member 'WDG_MPQuickjoinSlot_C_SetServer::Temp_int_Loop_Counter_Variable' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Add_IntInt_ReturnValue) == 0x0000D0, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Add_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, Temp_int_Array_Index_Variable) == 0x0000D4, "Member 'WDG_MPQuickjoinSlot_C_SetServer::Temp_int_Array_Index_Variable' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, Temp_int_Array_Index_Variable_1) == 0x0000D8, "Member 'WDG_MPQuickjoinSlot_C_SetServer::Temp_int_Array_Index_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, Temp_int_Array_Index_Variable_2) == 0x0000DC, "Member 'WDG_MPQuickjoinSlot_C_SetServer::Temp_int_Array_Index_Variable_2' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Array_Get_Item) == 0x0000E0, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Array_Get_Item' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Array_Get_Item_1) == 0x0000EC, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Array_Get_Item_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Conv_IntToText_ReturnValue) == 0x0000F8, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Conv_IntToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_AppendToFText_ReturnValue) == 0x000110, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_AppendToFText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Conv_IntToText_ReturnValue_1) == 0x000128, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Conv_IntToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_AppendToFText_ReturnValue_1) == 0x000140, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_AppendToFText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, K2Node_SwitchEnum_CmpSuccess) == 0x000158, "Member 'WDG_MPQuickjoinSlot_C_SetServer::K2Node_SwitchEnum_CmpSuccess' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, K2Node_SwitchEnum_CmpSuccess_1) == 0x000159, "Member 'WDG_MPQuickjoinSlot_C_SetServer::K2Node_SwitchEnum_CmpSuccess_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Array_Length_ReturnValue) == 0x00015C, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Array_Length_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Array_Length_ReturnValue_1) == 0x000160, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Array_Length_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Less_IntInt_ReturnValue) == 0x000164, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Less_IntInt_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, Temp_int_Loop_Counter_Variable_1) == 0x000168, "Member 'WDG_MPQuickjoinSlot_C_SetServer::Temp_int_Loop_Counter_Variable_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Add_IntInt_ReturnValue_1) == 0x00016C, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Add_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Array_Get_Item_2) == 0x000170, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Array_Get_Item_2' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Conv_StringToText_ReturnValue) == 0x000180, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Conv_IntToText_ReturnValue_2) == 0x000198, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Conv_IntToText_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_AppendToFText_ReturnValue_2) == 0x0001B0, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_AppendToFText_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, K2Node_SwitchEnum_CmpSuccess_2) == 0x0001C8, "Member 'WDG_MPQuickjoinSlot_C_SetServer::K2Node_SwitchEnum_CmpSuccess_2' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Conv_StringToName_ReturnValue) == 0x0001CC, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Conv_StringToName_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Array_Length_ReturnValue_2) == 0x0001D4, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Array_Length_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, K2Node_MakeStruct_CircuitInfo) == 0x0001D8, "Member 'WDG_MPQuickjoinSlot_C_SetServer::K2Node_MakeStruct_CircuitInfo' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Less_IntInt_ReturnValue_1) == 0x0003C8, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Less_IntInt_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Conv_StringToText_ReturnValue_1) == 0x0003D0, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Conv_StringToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Less_IntInt_ReturnValue_2) == 0x0003E8, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Less_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Conv_IntToByte_ReturnValue) == 0x0003E9, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Conv_IntToByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Conv_ByteToText_ReturnValue) == 0x0003F0, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Conv_ByteToText_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_AppendToFText_ReturnValue_3) == 0x000408, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_AppendToFText_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_MakeLiteralByte_ReturnValue) == 0x000420, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_MakeLiteralByte_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Conv_ByteToText_ReturnValue_1) == 0x000428, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Conv_ByteToText_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_MakeLiteralByte_ReturnValue_1) == 0x000440, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_MakeLiteralByte_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Conv_IntToText_ReturnValue_3) == 0x000448, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Conv_IntToText_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, Temp_int_Loop_Counter_Variable_2) == 0x000460, "Member 'WDG_MPQuickjoinSlot_C_SetServer::Temp_int_Loop_Counter_Variable_2' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Conv_IntToString_ReturnValue) == 0x000468, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Conv_IntToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Conv_IntToText_ReturnValue_4) == 0x000478, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Conv_IntToText_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Less_IntInt_ReturnValue_3) == 0x000490, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Less_IntInt_ReturnValue_3' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_AppendToFText_ReturnValue_4) == 0x000498, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_AppendToFText_ReturnValue_4' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_AppendToFText_ReturnValue_5) == 0x0004B0, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_AppendToFText_ReturnValue_5' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_Add_IntInt_ReturnValue_2) == 0x0004C8, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_Add_IntInt_ReturnValue_2' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_GetGameInstance_ReturnValue) == 0x0004D0, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_GetGameInstance_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, K2Node_DynamicCast_AsAc_Game_Instance) == 0x0004D8, "Member 'WDG_MPQuickjoinSlot_C_SetServer::K2Node_DynamicCast_AsAc_Game_Instance' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, K2Node_DynamicCast_bSuccess) == 0x0004E0, "Member 'WDG_MPQuickjoinSlot_C_SetServer::K2Node_DynamicCast_bSuccess' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_SetServer, CallFunc_GetCircuitInfo_ReturnValue) == 0x0004E1, "Member 'WDG_MPQuickjoinSlot_C_SetServer::CallFunc_GetCircuitInfo_ReturnValue' has a wrong offset!");

// Function WDG_MPQuickjoinSlot.WDG_MPQuickjoinSlot_C.GetMinutesRemainingText
// 0x00A0 (0x00A0 - 0x0000)
struct WDG_MPQuickjoinSlot_C_GetMinutesRemainingText final
{
public:
	struct FDateTime                              SessionEndUtc;                                     // 0x0000(0x0008)(BlueprintVisible, BlueprintReadOnly, Parm, ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	class FText                                   SessionEndText;                                    // 0x0008(0x0018)(Parm, OutParm)
	struct FDateTime                              CallFunc_DateTimeMinValue_ReturnValue;             // 0x0020(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FDateTime                              CallFunc_UtcNow_ReturnValue;                       // 0x0028(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	struct FTimespan                              CallFunc_Subtract_DateTimeDateTime_ReturnValue;    // 0x0030(0x0008)(ZeroConstructor, NoDestructor, HasGetValueTypeHash)
	bool                                          CallFunc_EqualEqual_DateTimeDateTime_ReturnValue;  // 0x0038(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_39[0x3];                                       // 0x0039(0x0003)(Fixing Size After Last Property [ Dumper-7 ])
	int32                                         CallFunc_BreakTimespan_Days;                       // 0x003C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakTimespan_Hours;                      // 0x0040(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakTimespan_Minutes;                    // 0x0044(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakTimespan_Seconds;                    // 0x0048(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	int32                                         CallFunc_BreakTimespan_Milliseconds;               // 0x004C(0x0004)(ZeroConstructor, IsPlainOldData, NoDestructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Conv_IntToString_ReturnValue;             // 0x0050(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	bool                                          CallFunc_Greater_DateTimeDateTime_ReturnValue;     // 0x0060(0x0001)(ZeroConstructor, IsPlainOldData, NoDestructor)
	uint8                                         Pad_61[0x7];                                       // 0x0061(0x0007)(Fixing Size After Last Property [ Dumper-7 ])
	class FString                                 CallFunc_Concat_StrStr_ReturnValue;                // 0x0068(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FString                                 CallFunc_Concat_StrStr_ReturnValue_1;              // 0x0078(0x0010)(ZeroConstructor, HasGetValueTypeHash)
	class FText                                   CallFunc_Conv_StringToText_ReturnValue;            // 0x0088(0x0018)()
};
static_assert(alignof(WDG_MPQuickjoinSlot_C_GetMinutesRemainingText) == 0x000008, "Wrong alignment on WDG_MPQuickjoinSlot_C_GetMinutesRemainingText");
static_assert(sizeof(WDG_MPQuickjoinSlot_C_GetMinutesRemainingText) == 0x0000A0, "Wrong size on WDG_MPQuickjoinSlot_C_GetMinutesRemainingText");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_GetMinutesRemainingText, SessionEndUtc) == 0x000000, "Member 'WDG_MPQuickjoinSlot_C_GetMinutesRemainingText::SessionEndUtc' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_GetMinutesRemainingText, SessionEndText) == 0x000008, "Member 'WDG_MPQuickjoinSlot_C_GetMinutesRemainingText::SessionEndText' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_GetMinutesRemainingText, CallFunc_DateTimeMinValue_ReturnValue) == 0x000020, "Member 'WDG_MPQuickjoinSlot_C_GetMinutesRemainingText::CallFunc_DateTimeMinValue_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_GetMinutesRemainingText, CallFunc_UtcNow_ReturnValue) == 0x000028, "Member 'WDG_MPQuickjoinSlot_C_GetMinutesRemainingText::CallFunc_UtcNow_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_GetMinutesRemainingText, CallFunc_Subtract_DateTimeDateTime_ReturnValue) == 0x000030, "Member 'WDG_MPQuickjoinSlot_C_GetMinutesRemainingText::CallFunc_Subtract_DateTimeDateTime_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_GetMinutesRemainingText, CallFunc_EqualEqual_DateTimeDateTime_ReturnValue) == 0x000038, "Member 'WDG_MPQuickjoinSlot_C_GetMinutesRemainingText::CallFunc_EqualEqual_DateTimeDateTime_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_GetMinutesRemainingText, CallFunc_BreakTimespan_Days) == 0x00003C, "Member 'WDG_MPQuickjoinSlot_C_GetMinutesRemainingText::CallFunc_BreakTimespan_Days' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_GetMinutesRemainingText, CallFunc_BreakTimespan_Hours) == 0x000040, "Member 'WDG_MPQuickjoinSlot_C_GetMinutesRemainingText::CallFunc_BreakTimespan_Hours' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_GetMinutesRemainingText, CallFunc_BreakTimespan_Minutes) == 0x000044, "Member 'WDG_MPQuickjoinSlot_C_GetMinutesRemainingText::CallFunc_BreakTimespan_Minutes' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_GetMinutesRemainingText, CallFunc_BreakTimespan_Seconds) == 0x000048, "Member 'WDG_MPQuickjoinSlot_C_GetMinutesRemainingText::CallFunc_BreakTimespan_Seconds' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_GetMinutesRemainingText, CallFunc_BreakTimespan_Milliseconds) == 0x00004C, "Member 'WDG_MPQuickjoinSlot_C_GetMinutesRemainingText::CallFunc_BreakTimespan_Milliseconds' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_GetMinutesRemainingText, CallFunc_Conv_IntToString_ReturnValue) == 0x000050, "Member 'WDG_MPQuickjoinSlot_C_GetMinutesRemainingText::CallFunc_Conv_IntToString_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_GetMinutesRemainingText, CallFunc_Greater_DateTimeDateTime_ReturnValue) == 0x000060, "Member 'WDG_MPQuickjoinSlot_C_GetMinutesRemainingText::CallFunc_Greater_DateTimeDateTime_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_GetMinutesRemainingText, CallFunc_Concat_StrStr_ReturnValue) == 0x000068, "Member 'WDG_MPQuickjoinSlot_C_GetMinutesRemainingText::CallFunc_Concat_StrStr_ReturnValue' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_GetMinutesRemainingText, CallFunc_Concat_StrStr_ReturnValue_1) == 0x000078, "Member 'WDG_MPQuickjoinSlot_C_GetMinutesRemainingText::CallFunc_Concat_StrStr_ReturnValue_1' has a wrong offset!");
static_assert(offsetof(WDG_MPQuickjoinSlot_C_GetMinutesRemainingText, CallFunc_Conv_StringToText_ReturnValue) == 0x000088, "Member 'WDG_MPQuickjoinSlot_C_GetMinutesRemainingText::CallFunc_Conv_StringToText_ReturnValue' has a wrong offset!");

}

